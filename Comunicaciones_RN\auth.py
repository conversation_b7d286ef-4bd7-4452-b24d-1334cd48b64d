# auth.py 
 
from flask import Blueprint, render_template, redirect, url_for, request, flash, make_response, jsonify
from werkzeug.security import check_password_hash
from flask_login import login_user, logout_user, login_required, current_user
from extensions import db, socketio  # Import socketio
from models import User
import logging
import uuid
from globals import connected_users  # Importar `connected_users` desde `globals.py`

# Configurar logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/')
def index():
    return redirect(url_for('auth.login'))

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'GET':
        return render_template('login.html')

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        logger.debug(f"Attempting login for user: {username}")
        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            logger.debug("Password correct. Logging in user.")

            login_user(user, remember=True)

            # --- MODIFICACIÓN AQUÍ: Usar request.sid (disponible en el contexto de SocketIO) ---
            # connected_users[user.id] = {'username': user.username, 'node_id': None}  # Ya no
            # El 'node_id' se asignará cuando el usuario se conecte a un nodo específico.

            logger.debug(f"User {username} logged in successfully.")
            response = make_response(redirect(url_for('views.dashboard')))
            return response
        else:
            logger.debug("Invalid username or password.")
            flash('Invalid username or password')
            return render_template('login.html')  # Asegúrate de devolver una respuesta válida

@auth_bp.route('/logout')
@login_required
def logout():
    user = current_user

    # Verifica si el usuario está autenticado antes de acceder a 'username'
    if user.is_authenticated:
        logger.debug(f"User {user.username} logged out successfully.")

        # --- MODIFICACIÓN AQUÍ: Buscar y eliminar por SID (si existe) ---
        for sid, user_info in list(connected_users.items()):  # Usar list() para iterar sobre una copia
            if user_info['username'] == user.username:
                # Desconectar usando SocketIO (más limpio)
                socketio.disconnect(sid=sid, namespace='/node')  # Desconectar de /node
                socketio.disconnect(sid=sid, namespace='/private') # Desconectar de /private

                del connected_users[sid]
                break # Salir del bucle una vez encontrado


    else:
        logger.debug("Anonymous user attempted to log out.")

    logout_user()
    response = make_response(redirect(url_for('auth.login')))
    return response


#@auth_bp.route('/connected_users', methods=['GET'])
#@login_required
#def list_connected_users():
    # No es necesario importar 'connected_users' desde 'app' aquí, ya que lo importaste globalmente

    # Crear un nuevo diccionario que solo incluya el usuario y el nodo.
    # Ya no se filtra por session_token, así que todos los usuarios conectados
    # deben aparecer en la lista.
#    filtered_users = [
#        {"username": user_info['username'], "node_id": user_info['node_id']}
#        for user_info in connected_users.values()
#    ]
#    return jsonify(filtered_users)