#!/usr/bin/env python3
import os
import sys
import psycopg2
from psycopg2 import sql
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Configuración de la base de datos
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "taxi_app_db"
DB_USER = "joacoabe"
DB_PASSWORD = "isaias52"

def run_sql_file(file_path):
    """Ejecuta un archivo SQL en la base de datos."""
    try:
        # Conectar a la base de datos
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        # Crear un cursor
        cursor = conn.cursor()
        
        # Leer el archivo SQL
        with open(file_path, 'r') as f:
            sql_script = f.read()
        
        # Ejecutar el script SQL
        cursor.execute(sql_script)
        
        # Cerrar el cursor y la conexión
        cursor.close()
        conn.close()
        
        print(f"Migración ejecutada con éxito: {file_path}")
        return True
    except Exception as e:
        print(f"Error al ejecutar la migración {file_path}: {e}")
        return False

def main():
    """Función principal que ejecuta todas las migraciones."""
    # Directorio donde se encuentran los archivos de migración
    migrations_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Lista de archivos SQL a ejecutar en orden
    sql_files = [
        "add_category_to_vehicles.sql"
    ]
    
    # Ejecutar cada archivo SQL
    for sql_file in sql_files:
        file_path = os.path.join(migrations_dir, sql_file)
        if os.path.exists(file_path):
            success = run_sql_file(file_path)
            if not success:
                print(f"Error al ejecutar {sql_file}. Abortando.")
                sys.exit(1)
        else:
            print(f"Archivo no encontrado: {file_path}")
            sys.exit(1)
    
    print("Todas las migraciones se ejecutaron correctamente.")

if __name__ == "__main__":
    main()
