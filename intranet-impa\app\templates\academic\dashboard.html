{% extends "base.html" %}

{% block title %}Sistema Académico - Intranet IMPA{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-graduation-cap"></i> Sistema Académico
            </h1>
        </div>
    </div>
    
    <!-- Estadísticas principales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Escuelas Bíblicas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.total_schools }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-school fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Programas Pastorales
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.total_programs }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-graduate fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Estudiantes Activos
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.active_students }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pastores en Formación
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.pastoral_students }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Alertas de matrículas pendientes -->
    {% if stats.pending_approvals > 0 and current_user.role in ['instituto', 'rector'] %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Atención:</strong> Hay {{ stats.pending_approvals }} matrícula(s) pastoral(es) pendiente(s) de aprobación.
                <a href="{{ url_for('routes.pastoral_programs') }}" class="alert-link">Ver matrículas pendientes</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Acciones rápidas -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-school"></i> Escuelas Bíblicas
                        {% if current_user.role in ['administrador', 'secretaria'] %}
                        <a href="{{ url_for('routes.create_academic_school') }}" class="btn btn-primary btn-sm float-right">
                            <i class="fas fa-plus"></i> Nueva Escuela
                        </a>
                        {% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text">Gestiona las escuelas bíblicas de las iglesias locales.</p>
                    <div class="row">
                        <div class="col-6">
                            <a href="{{ url_for('routes.academic_schools') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-list"></i> Ver Escuelas
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ url_for('routes.academic_reports') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-chart-bar"></i> Reportes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-graduate"></i> Formación Pastoral
                        {% if current_user.role in ['administrador', 'instituto', 'rector'] %}
                        <a href="{{ url_for('routes.create_pastoral_program') }}" class="btn btn-success btn-sm float-right">
                            <i class="fas fa-plus"></i> Nuevo Programa
                        </a>
                        {% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text">Programas de formación continua para pastores y líderes.</p>
                    <div class="row">
                        <div class="col-6">
                            <a href="{{ url_for('routes.pastoral_programs') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-graduation-cap"></i> Ver Programas
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ url_for('routes.academic_reports') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-certificate"></i> Certificados
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Lista de escuelas recientes -->
    {% if schools %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Escuelas Bíblicas Activas</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Escuela</th>
                                    <th>Iglesia</th>
                                    <th>Director</th>
                                    <th>Estudiantes</th>
                                    <th>Estado</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for school in schools[:5] %}
                                <tr>
                                    <td>
                                        <strong>{{ school.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ school.description }}</small>
                                    </td>
                                    <td>{{ school.church.name }}</td>
                                    <td>
                                        {% if school.director %}
                                        {{ school.director.first_name }} {{ school.director.last_name }}
                                        {% else %}
                                        <span class="text-muted">Sin asignar</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ school.enrollments|selectattr("status", "equalto", "active")|list|length }} activos
                                        </span>
                                    </td>
                                    <td>
                                        {% if school.is_active %}
                                        <span class="badge badge-success">Activa</span>
                                        {% else %}
                                        <span class="badge badge-secondary">Inactiva</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('routes.academic_school_detail', school_id=school.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Ver
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if schools|length > 5 %}
                    <div class="text-center">
                        <a href="{{ url_for('routes.academic_schools') }}" class="btn btn-outline-primary">
                            Ver todas las escuelas ({{ schools|length }})
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Lista de programas pastorales -->
    {% if pastoral_programs %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Programas Pastorales Activos</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Programa</th>
                                    <th>Tipo</th>
                                    <th>Duración</th>
                                    <th>Estudiantes</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for program in pastoral_programs[:5] %}
                                <tr>
                                    <td>
                                        <strong>{{ program.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ program.description }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ program.program_type|title }}</span>
                                    </td>
                                    <td>{{ program.duration_months }} meses</td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ program.enrollments|selectattr("status", "equalto", "active")|list|length }}/{{ program.max_students }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if current_user.role in ['pastorado', 'superintendente'] %}
                                        <a href="{{ url_for('routes.enroll_in_pastoral_program', program_id=program.id) }}" 
                                           class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-user-plus"></i> Matricularme
                                        </a>
                                        {% endif %}
                                        {% if current_user.role in ['administrador', 'instituto', 'rector'] %}
                                        <a href="#" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Ver
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if pastoral_programs|length > 5 %}
                    <div class="text-center">
                        <a href="{{ url_for('routes.pastoral_programs') }}" class="btn btn-outline-success">
                            Ver todos los programas ({{ pastoral_programs|length }})
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
