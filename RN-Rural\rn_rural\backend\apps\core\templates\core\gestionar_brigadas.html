{# File: backend/apps/core/templates/core/gestionar_brigadas.html #}
{% extends "core/base.html" %}

{% block title %}Gestionar Brigadas - RN-Rural{% endblock %}

{% block extra_head %}
<style>
    .card-dashboard {
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    .card-dashboard:hover {
        transform: translateY(-5px);
    }
    .card-header-custom {
        border-radius: 10px 10px 0 0;
        padding: 15px;
        font-weight: bold;
    }
    .table-custom {
        border-collapse: separate;
        border-spacing: 0 8px;
    }
    .table-custom thead th {
        border-bottom: none;
        background-color: #f8f9fa;
        padding: 12px 15px;
        font-weight: 600;
    }
    .table-custom tbody tr {
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border-radius: 8px;
        background-color: white;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    .table-custom tbody tr:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .table-custom td {
        padding: 15px;
        vertical-align: middle;
        border-top: none;
    }
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    .search-box {
        position: relative;
        margin-bottom: 20px;
    }
    .search-box input {
        padding-left: 40px;
        border-radius: 20px;
    }
    .search-box i {
        position: absolute;
        left: 15px;
        top: 10px;
        color: #6c757d;
    }
    .status-badge {
        font-size: 0.85rem;
        padding: 6px 10px;
        border-radius: 20px;
    }
    .status-active {
        background-color: #198754;
        color: white;
    }
    .status-inactive {
        background-color: #6c757d;
        color: white;
    }
    #mapBrigadas {
        height: 400px;
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .map-legend {
        position: absolute;
        bottom: 30px;
        right: 10px;
        z-index: 1000;
        background: white;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        font-size: 0.8rem;
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    .legend-color {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        border-radius: 50%;
    }
    .legend-brigada-activa { background-color: #198754; }
    .legend-brigada-inactiva { background-color: #6c757d; }
</style>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-3">Gestionar Brigadas</h2>
    <hr>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Mapa de Brigadas</h4>
                    </div>
                </div>
                <div class="card-body p-0 position-relative">
                    <div id="mapBrigadas"></div>

                    <div class="map-legend">
                        <h6 class="mb-2">Leyenda</h6>
                        <div class="legend-item">
                            <div class="legend-color legend-brigada-activa"></div>
                            <span>Brigada Activa</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-brigada-inactiva"></div>
                            <span>Brigada Inactiva</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-users"></i> Lista de Brigadas</h4>
                        <a href="{% url 'user_add' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nueva Brigada
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" class="form-control" placeholder="Buscar brigada...">
                    </div>

                    <div class="table-responsive">
                        <table class="table table-custom" id="brigadasTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Usuario</th>
                                    <th>Estado</th>
                                    <th>Última Actualización</th>
                                    <th>Incidencias Asignadas</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for brigada in brigadas %}
                                <tr>
                                    <td><strong>#{{ brigada.id }}</strong></td>
                                    <td>
                                        <i class="fas fa-user"></i> {{ brigada.username }}
                                    </td>
                                    <td>
                                        {% if brigada.is_active %}
                                            <span class="badge status-badge status-active">Activa</span>
                                        {% else %}
                                            <span class="badge status-badge status-inactive">Inactiva</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <i class="fas fa-clock"></i>
                                        {% if brigada.ubicacion.ultima_actualizacion %}
                                            {{ brigada.ubicacion.ultima_actualizacion|date:"d/m/Y H:i" }}
                                        {% else %}
                                            Sin datos
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ brigada.incidencias_count }}</span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{% url 'user_edit' brigada.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> Editar
                                            </a>
                                            <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=derivadas&brigada={{ brigada.id }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-clipboard-list"></i> Ver Incidencias
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center py-3">
                                        <i class="fas fa-info-circle"></i> No hay brigadas registradas.
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar el mapa
        const map = L.map('mapBrigadas').setView([-40.8, -63.0], 6); // Río Negro aprox

        // Añadir capa de mapa base
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Añadir marcadores para las brigadas
        {% for brigada in brigadas %}
            {% if brigada.ubicacion and brigada.ubicacion.posicion_actual %}
                // Determinar el color según el estado
                const iconColor = {% if brigada.is_active %}'#198754'{% else %}'#6c757d'{% endif %};

                // Crear icono personalizado
                const customIcon = L.divIcon({
                    className: 'custom-div-icon',
                    html: `<div style="background-color:${iconColor}; width:30px; height:30px; border-radius:50%; border:3px solid white; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;">${{ brigada.id }}</div>`,
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                });

                // Añadir marcador
                const marker = L.marker([{{ brigada.ubicacion.posicion_actual.y }}, {{ brigada.ubicacion.posicion_actual.x }}], { icon: customIcon }).addTo(map);

                // Añadir popup
                marker.bindPopup(`
                    <div style="text-align:center;">
                        <h5 style="margin-bottom:8px;">Brigada #{{ brigada.id }}</h5>
                        <p><strong>Usuario:</strong> {{ brigada.username }}</p>
                        <p><strong>Estado:</strong> {% if brigada.is_active %}Activa{% else %}Inactiva{% endif %}</p>
                        <p><strong>Incidencias asignadas:</strong> {{ brigada.incidencias_count }}</p>
                        <a href="{% url 'user_edit' brigada.id %}" class="btn btn-sm btn-primary">
                            Editar
                        </a>
                    </div>
                `);
            {% endif %}
        {% endfor %}

        // Búsqueda en tiempo real
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('#brigadasTable tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // Ya no necesitamos este código porque los enlaces ahora son directos
        // y no usan JavaScript para la navegación
    });
</script>
{% endblock %}
