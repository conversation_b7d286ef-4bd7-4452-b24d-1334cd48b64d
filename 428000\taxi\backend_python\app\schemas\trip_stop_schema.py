from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from app.models.trip_stop import TripStopStatusEnum

class TripStopBase(BaseModel):
    """Esquema base para paradas de viaje."""
    latitude: str
    longitude: str
    address: Optional[str] = None
    order: int
    wait_time_seconds: Optional[int] = 60
    notes: Optional[str] = None

class TripStopCreate(TripStopBase):
    """Esquema para crear una parada de viaje."""
    pass

class TripStopUpdate(BaseModel):
    """Esquema para actualizar una parada de viaje."""
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    address: Optional[str] = None
    order: Optional[int] = None
    status: Optional[TripStopStatusEnum] = None
    wait_time_seconds: Optional[int] = None
    notes: Optional[str] = None

class TripStop(TripStopBase):
    """Esquema para devolver una parada de viaje."""
    id: int
    trip_id: int
    status: TripStopStatusEnum
    estimated_arrival_time: Optional[datetime] = None
    actual_arrival_time: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
