{# File: backend/apps/core/templates/core/base.html #}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RN-Rural{% endblock %}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🌲</text></svg>" type="image/svg+xml">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    {% block extra_head %}{% endblock %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            padding-top: 70px;
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        main { /* Aplicar directamente a main, no a main.container para más flexibilidad */
            min-height: calc(100vh - 70px - 73px);
        }
        .footer {
            background-color: #343a40;
            color: white;
            padding: 1rem 0;
            text-align: center;
        }
        .navbar {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .card-dashboard {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card-dashboard:hover {
            transform: translateY(-5px);
        }
        .card-header-custom {
            border-radius: 10px 10px 0 0;
            padding: 15px;
            font-weight: bold;
        }
        .bg-nueva { background-color: #0dcaf0; color: white; }
        .bg-proceso { background-color: #ffc107; color: black; }
        .bg-derivada { background-color: #fd7e14; color: white; }
        .bg-resuelta { background-color: #198754; color: white; }
        .bg-cerrada { background-color: #6c757d; color: white; }

        .alert {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .btn {
            border-radius: 6px;
            font-weight: 500;
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{% if user.is_authenticated %}{% if user.role == 'CIUDADANO' %}{% url 'dashboard_ciudadano' %}{% elif user.role == 'OPERADOR' %}{% url 'dashboard_operador' %}{% elif user.role == 'BRIGADA' %}{% url 'dashboard_brigada' %}{% else %}{% url 'login' %}{% endif %}{% else %}{% url 'login' %}{% endif %}">
                RN-Rural
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <span class="navbar-text me-3">
                                Bienvenido, {{ user.username }} ({{ user.get_role_display }})
                            </span>
                        </li>
                        {% if user.role == "CIUDADANO" %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard_ciudadano' %}active{% endif %}" href="{% url 'dashboard_ciudadano' %}">Mi Portal</a>
                        </li>
                        {% elif user.role == "OPERADOR" %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard_operador' %}active{% endif %}" href="{% url 'dashboard_operador' %}">Panel Operador</a>
                        </li>
                        {% elif user.role == "BRIGADA" %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard_brigada' %}active{% endif %}" href="{% url 'dashboard_brigada' %}">Panel Brigada</a>
                        </li>
                        {% endif %}
                        {% if user.is_superuser %}
                            <li class="nav-item"><a class="nav-link {% if request.resolver_match.url_name == 'user_list' %}active{% endif %}" href="{% url 'user_list' %}">Gestionar Usuarios</a></li>
                            <li class="nav-item"><a class="nav-link {% if request.resolver_match.app_name == 'admin' %}active{% endif %}" href="{% url 'admin:index' %}">Admin Django</a></li>
                        {% elif user.is_staff %}
                             <li class="nav-item"><a class="nav-link {% if request.resolver_match.app_name == 'admin' %}active{% endif %}" href="{% url 'admin:index' %}">Admin Django</a></li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'logout' %}">Cerrar Sesión</a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'login' %}active{% endif %}" href="{% url 'login' %}">Iniciar Sesión</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'register' %}active{% endif %}" href="{% url 'register' %}">Registrarse</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    {# El comentario problemático estaba probablemente aquí, fuera de un bloque. Lo he quitado. #}
    {# Los mensajes de Django se mostrarán DENTRO del bloque de contenido de la página hija o en un lugar específico aquí. #}
    {# Para mostrarlos consistentemente arriba, podríamos ponerlos aquí, ANTES del bloque de contenido principal. #}
    <div class="container mt-3"> {# Contenedor para los mensajes, para que no ocupen todo el ancho si el main es fluid #}
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <main role="main"> {# Quitamos 'class="container"' de aquí para dar flexibilidad a las plantillas hijas #}
        {% block content %}
            <!-- El contenido específico de cada página irá aquí.
                 La plantilla hija (ej. dashboard_operador.html) DEBE usar
                 <div class="container"> o <div class="container-fluid">
                 según necesite para su propio contenido.
            -->
        {% endblock %}
    </main>

    <footer class="footer mt-auto py-3">
        <div class="container">
            <span class="text-light">© RN-Rural {% now "Y" %}</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>