<!-- app/templates/cameras/camera_detail.html -->
{% extends "base.html" %}
{% block title %}Detalle Cámara - {{ super() }}{% endblock %}

{% block content %}
<h1>Detalle de la Cámara #{{ camera.id }}</h1>

<p><strong>Tipo:</strong> {{ camera.type }}</p>
<p><strong>Dirección:</strong> {{ camera.direction }}</p>
<p><strong>Punto Asociado:</strong> 
    <a href="{{ url_for('points.detail', point_id=camera.point_id) }}">#{{ camera.point_id }}</a>
</p>
<p><strong>Creada:</strong> {{ camera.created_at.strftime('%d/%m/%Y %H:%M') }}</p>
<p><strong>Última Actualización:</strong> {{ camera.updated_at.strftime('%d/%m/%Y %H:%M') }}</p>

{% if camera.get_photo_url() %}
    <p><strong>Foto:</strong></p>
    <img src="{{ camera.get_photo_url() }}" class="img-fluid" alt="Foto de cámara">
{% else %}
    <p class="text-muted">No hay foto disponible.</p>
{% endif %}

<hr>
<a href="{{ url_for('points.list_all') }}" class="btn btn-secondary">Volver</a>
{% endblock %}
