#!/usr/bin/env python3
# config.py

import json
import os
import pyaudio
from flask import Flask, render_template, request, redirect, url_for

CONFIG_FILE = 'config.json'
app = Flask(__name__)

def load_config():
    if not os.path.exists(CONFIG_FILE):
        return {}
    else:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config

def save_config(config):
    config['node_url'] = "https://192.168.1.82/node"  # Valor predefinido
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)

@app.route('/')
def index():
    config = load_config()
    input_devices = list_audio_devices()
    return render_template('config_form.html', config=config, input_devices=input_devices)

@app.route('/save_config', methods=['POST'])
def save_user_config():
    config = {
        "username": request.form['username'],
        "password": request.form['password'],
        "node_id": request.form['node_id'],
        "input_device_index": int(request.form['input_device_index']),
        "volume_level": int(request.form['volume_level']),
        "port_number": request.form['port_number']
    }
    save_config(config)
    return redirect(url_for('index'))

def list_audio_devices():
    audio = pyaudio.PyAudio()
    input_devices = []
    for i in range(audio.get_device_count()):
        info = audio.get_device_info_by_index(i)
        if info["maxInputChannels"] > 0:
            input_devices.append((i, info['name']))
    audio.terminate()
    if not input_devices:
        input_devices = [(0, "Dispositivo predeterminado")]
    return input_devices

if __name__ == '__main__':
    app.run(debug=True)
