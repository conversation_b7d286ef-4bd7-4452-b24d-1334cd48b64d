---

### ✅ Paso 1: Crear de nuevo `instance/` y `migrations/`

Desde la raíz del proyecto (donde está `app/`):

```bash
mkdir -p instance
mkdir -p app/static/uploads
```

---

### ✅ Paso 2: Crear la base de datos con los modelos

```bash
(relevamiento) python
```

Y en la consola de Python:

```python
from app import create_app, db
app = create_app()
app.app_context().push()
db.create_all()
exit()
```

Esto creará `instance/vhf.db` vacío con las tablas definidas en tus modelos (`User`, `Point`, `Image`, `Camera`, etc.).

---

### ✅ Paso 3: Inicializar y generar migraciones (opcional pero recomendable)

Si usás Flask-Migrate y querés poder hacer migraciones a futuro:

```bash
(relevamiento) flask db init       # solo si no existe 'migrations/'
(relevamiento) flask db migrate -m "Inicial"
(relevamiento) flask db upgrade
```

---

### ✅ Paso 4: Confirmar que funciona

Podés correr tu aplicación y visitar `/points/list` o hacer un `import_csv` para empezar desde cero.

---

### ✅ Paso 5: Crear Usuario

Crear un usuario desde el sitio
httpXXXXXXXXXXXXXXX:5006/auth/register
No es necesario agregar el correo electronico

---

### ✅ Paso 6: Exportar Puntos

Desde el sitio
httpXXXXXXXXXXXXXXX5006/points/list
En la parte superior se selecciona un archivo csv con el siguiente formato

id	name	city	latitude	longitude	status	description	source

es importante respetar las columnas
---