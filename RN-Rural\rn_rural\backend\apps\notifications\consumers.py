# File: backend/apps/notifications/consumers.py
# -----------------------------------------------

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model  # Mantener la importación

# NO llamar a get_user_model() aquí a nivel de módulo
# User = get_user_model()  # ELIMINAR ESTA LÍNEA
logger = logging.getLogger(__name__)

class NotificationConsumer(AsyncWebsocketConsumer):
    """
    Consumidor WebSocket para notificaciones en tiempo real.
    """
    
    async def connect(self):
        """
        Método llamado cuando un cliente WebSocket se conecta.
        """
        self.user_id = self.scope['url_route']['kwargs']['user_id']
        self.notification_group_name = f'notifications_{self.user_id}'
        
        # Verificar que el usuario existe
        user_exists = await self.check_user_exists(self.user_id)
        if not user_exists:
            logger.warning(f"Intento de conexión con ID de usuario inválido: {self.user_id}")
            await self.close()
            return
        
        # Unirse al grupo de notificaciones del usuario
        await self.channel_layer.group_add(
            self.notification_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"Usuario {self.user_id} conectado a WebSocket")
        
        # Enviar mensaje de conexión exitosa
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Conectado al servidor de notificaciones'
        }))
    
    async def disconnect(self, close_code):
        """
        Método llamado cuando un cliente WebSocket se desconecta.
        """
        # Abandonar el grupo de notificaciones
        await self.channel_layer.group_discard(
            self.notification_group_name,
            self.channel_name
        )
        logger.info(f"Usuario {self.user_id} desconectado de WebSocket")
    
    async def receive(self, text_data):
        """
        Método llamado cuando se recibe un mensaje del cliente WebSocket.
        """
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                # Responder a los pings para mantener la conexión viva
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'message': 'Pong'
                }))
            else:
                logger.warning(f"Mensaje de tipo desconocido recibido: {message_type}")
        except json.JSONDecodeError:
            logger.error(f"Error al decodificar JSON: {text_data}")
        except Exception as e:
            logger.error(f"Error al procesar mensaje: {str(e)}")
    
    async def notification_message(self, event):
        """
        Método llamado cuando se recibe un mensaje del grupo de notificaciones.
        """
        # Enviar el mensaje al cliente WebSocket
        await self.send(text_data=json.dumps(event))
    
    @database_sync_to_async
    def check_user_exists(self, user_id):
        """
        Verificar que el usuario existe en la base de datos.
        """
        try:
            # Mover la llamada a get_user_model() dentro del método
            User = get_user_model()
            return User.objects.filter(id=user_id).exists()
        except Exception as e:
            logger.error(f"Error al verificar usuario: {str(e)}")
            return False