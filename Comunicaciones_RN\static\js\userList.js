// userList.js

import { socket, safeEmit, logToFile } from './socketConnection.js';

// Obtener elementos del DOM
const userListDiv = document.getElementById('userListDiv');
const nodeId = parseInt(document.body.dataset.nodeId);

// --- Función para ACTUALIZAR la lista de usuarios conectados ---
function updateConnectedUsers(users) {
    logToFile("updateConnectedUsers called with:" + JSON.stringify(users));  // Asegúrate de que logToFile esté accesible
    if (userListDiv) {
        userListDiv.innerHTML = '<h3>Usuarios Conectados</h3>';
        if (users.length === 0) {
            userListDiv.innerHTML += '<p>No hay usuarios conectados a este nodo.</p>';
        } else {
            users.forEach(user => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.innerHTML = `<a href="/private_chat/${user}">${user}</a> <img src="/static/audio-icon.png" class="audio-icon" id="audio-${user}">`;
                userListDiv.appendChild(userItem);
            });
        }
    } else {
        console.error('userListDiv not found.');
    }
}

// --- Evento 'update_users' (lista de usuarios) ---
socket.on('update_users', function(users) {
    logToFile('Datos recibidos (update_users):' + JSON.stringify(users));
    updateConnectedUsers(users);
});

// --- Solicitar lista inicial de usuarios al conectarse ---
socket.on('connect', () => {
    console.log('Conectado a SocketIO (namespace /node), solicitando lista inicial');
    safeEmit('get_connected_users', { node_id: nodeId });
});

// Este módulo no necesita exportar nada, ya que la funcionalidad
// se activa a través de los listeners de Socket.IO.