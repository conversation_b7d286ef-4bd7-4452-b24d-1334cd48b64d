# app/pdf_generator.py

import os
import json
import math
from io import BytesIO
from flask import current_app # <-- Necesario para logger
from reportlab.platypus import (SimpleDocTemplate, Paragraph, Spacer, PageBreak, Flowable)
from reportlab.platypus import Image as ReportLabImage
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.lib.units import inch, cm
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.utils import ImageReader
from PIL import Image as PILImage, ImageDraw, UnidentifiedImageError

# --- Importaciones de la app ---
from app import db
from .models import Point, Image, Camera # Asumiendo que models está en el mismo directorio o accesible

# --- Estilos ---
styles = getSampleStyleSheet()
styles.add(ParagraphStyle(name='Center', alignment=TA_CENTER))
styles.add(ParagraphStyle(name='LeftSmall', alignment=TA_LEFT, fontSize=9, leading=11))
styles.add(ParagraphStyle(name='RightSmall', alignment=TA_RIGHT, fontSize=9, leading=11))
styles.add(ParagraphStyle(name='PointTitle', fontSize=14, alignment=TA_LEFT, spaceAfter=10, leading=18))
styles.add(ParagraphStyle(name='DetailHeader', fontSize=12, alignment=TA_LEFT, spaceBefore=10, spaceAfter=5, fontName='Helvetica-Bold'))
styles.add(ParagraphStyle(name='DetailItem', fontSize=10, alignment=TA_LEFT, leftIndent=20, spaceAfter=3, leading=12))
styles.add(ParagraphStyle(name='ImageNotes', parent=styles['Normal'], fontSize=9, spaceBefore=6))
styles.add(ParagraphStyle(name='ImageMeta', parent=styles['Italic'], fontSize=8, spaceBefore=3, textColor=colors.grey))

# --- Cabecera y Pie de Página (sin cambios) ---
class PageNumCanvas(Flowable):
    """ Clase para dibujar número de página """
    def __init__(self, doc):
        Flowable.__init__(self)
        self.doc = doc

    def draw(self, canvas, doc):
        canvas.saveState()
        canvas.setFont('Helvetica', 9)
        canvas.drawString(A4[0] - 1*inch, 0.75 * inch, f"Página {doc.page}")
        canvas.restoreState()

class HeaderCanvas(Flowable):
    """ Clase para dibujar la cabecera con logo y título """
    def __init__(self, title="Subsecretaría de Seguridad Ciudadana"):
        Flowable.__init__(self)
        self.title = title
        # Corregido: Usar current_app.static_folder
        self.logo_path = os.path.join(current_app.static_folder, 'img', 'logo_rn.png')

    def draw(self, canvas, doc):
        canvas.saveState()
        page_width = doc.pagesize[0]
        margin = doc.leftMargin

        logo_width = 1.5 * inch
        logo_height = 0.5 * inch
        final_height = 0 # Inicializar
        if os.path.exists(self.logo_path):
            try:
                logo = ImageReader(self.logo_path)
                img_w, img_h = logo.getSize()
                aspect = img_h / float(img_w) if img_w else 1
                final_height = logo_width * aspect
                if final_height > logo_height:
                    final_height = logo_height
                    logo_width = final_height / aspect if aspect else logo_width

                canvas.drawImage(logo, margin, doc.pagesize[1] - margin - final_height,
                                 width=logo_width, height=final_height, mask='auto')
            except Exception as e:
                 current_app.logger.error(f"Error cargando logo para PDF ({self.logo_path}): {e}")
                 logo_width = 0
                 final_height = 0
        else:
             current_app.logger.warning(f"Logo no encontrado en {self.logo_path}")
             logo_width = 0
             final_height = 0

        title_x = margin + logo_width + (0.2 * inch if logo_width > 0 else 0)
        title_y = doc.pagesize[1] - margin - (final_height / 2 if final_height > 0 else 0) - 5 # Ajuste vertical
        canvas.setFont('Helvetica-Bold', 10)
        canvas.drawString(title_x, title_y, self.title)

        # Línea separadora
        line_y = doc.pagesize[1] - margin - final_height - (0.1 * inch if final_height > 0 else 0.2 * inch)
        canvas.setStrokeColor(colors.grey)
        canvas.setLineWidth(0.5)
        canvas.line(margin, line_y, page_width - margin, line_y)
        canvas.restoreState()

# --- FUNCIÓN AUXILIAR PARA DIBUJAR ANOTACIONES (CON CORRECCIÓN DE FLECHA) ---
def draw_annotations_on_image(pil_image, annotations_json_str):
    """
    Toma una imagen de Pillow y un string JSON de anotaciones Konva,
    y devuelve una NUEVA imagen de Pillow con las anotaciones dibujadas.
    Devuelve la imagen original si no hay anotaciones o hay error.
    """
    if not annotations_json_str:
        current_app.logger.debug("draw_annotations_on_image: No hay JSON string, devolviendo imagen original.")
        return pil_image

    try:
        img_with_annotations = pil_image.copy().convert("RGB") # Convertir a RGB para asegurar compatibilidad
        draw = ImageDraw.Draw(img_with_annotations)
        width, height = img_with_annotations.size
        current_app.logger.debug(f"draw_annotations_on_image: Procesando imagen tamaño {width}x{height}")

        try:
            annotations_data = json.loads(annotations_json_str)
            current_app.logger.debug(f"draw_annotations_on_image: Datos de anotaciones parseados para PDF: {json.dumps(annotations_data, indent=2)}")
        except json.JSONDecodeError as json_err:
            current_app.logger.error(f"Error parseando JSON de anotaciones para dibujar en PDF: {json_err}. JSON recibido: '{annotations_json_str}'")
            return pil_image

        if not isinstance(annotations_data, dict) or 'children' not in annotations_data or not isinstance(annotations_data['children'], list):
            current_app.logger.warning(f"JSON de anotaciones no tiene la estructura esperada (capa con children): {annotations_data}")
            return pil_image

        shapes = [child for child in annotations_data.get('children', []) if isinstance(child, dict) and child.get('className') != 'Transformer']
        current_app.logger.debug(f"draw_annotations_on_image: Se encontraron {len(shapes)} formas para dibujar (excluyendo Transformer).")

        for i, shape_data in enumerate(shapes):
             current_app.logger.debug(f"draw_annotations_on_image: Procesando shape #{i}: Class={shape_data.get('className')}, Attrs={shape_data.get('attrs')}, RelData={ {k:v for k,v in shape_data.items() if k not in ['attrs', 'className']} }")

             attrs = shape_data.get('attrs', {})
             shape_class = shape_data.get('className')

             if not shape_class or not attrs:
                current_app.logger.warning(f"draw_annotations_on_image: Forma #{i} inválida o sin atributos, saltando.")
                continue

             stroke = attrs.get('stroke', 'red')
             # Asegurarse que stroke_width sea entero para ImageDraw
             stroke_width = int(attrs.get('strokeWidth', 2))

             # Obtener posición/offset principal (relativo)
             rel_x = shape_data.get('relX', 0)
             rel_y = shape_data.get('relY', 0)
             x = rel_x * width   # x absoluto principal
             y = rel_y * height  # y absoluto principal

             # Ignorar scaleX/scaleY por ahora, ya que PIL no lo aplica directamente
             # scaleX = attrs.get('scaleX', 1)
             # scaleY = attrs.get('scaleY', 1)

             try:
                 if shape_class == 'Rect':
                     rel_w = shape_data.get('relWidth', 0)
                     rel_h = shape_data.get('relHeight', 0)
                     rect_w = rel_w * width # * scaleX # Multiplicar por scale si se implementa
                     rect_h = rel_h * height # * scaleY # Multiplicar por scale si se implementa
                     current_app.logger.debug(f"  -> Dibujando Rect en PDF: x={x:.2f}, y={y:.2f}, w={rect_w:.2f}, h={rect_h:.2f}, stroke={stroke}, width={stroke_width}")
                     x1 = x + rect_w
                     y1 = y + rect_h
                     draw.rectangle([min(x, x1), min(y, y1), max(x, x1), max(y, y1)], outline=stroke, width=stroke_width)

                 elif shape_class == 'Circle':
                     rel_r = shape_data.get('relRadius', 0)
                     # Usar min(width, height) para radio relativo es más robusto
                     radius = rel_r * min(width, height) # * max(scaleX, scaleY) # Afectar por escala si se implementa
                     left, top = x - radius, y - radius
                     right, bottom = x + radius, y + radius
                     current_app.logger.debug(f"  -> Dibujando Circle en PDF: cx={x:.2f}, cy={y:.2f}, r={radius:.2f}, stroke={stroke}, width={stroke_width}")
                     if radius > 0.5:
                        draw.ellipse([left, top, right, bottom], outline=stroke, width=stroke_width)
                     else:
                        current_app.logger.debug(f"  -> Omitiendo círculo con radio muy pequeño o cero: {radius:.2f}")

                 elif shape_class == 'Arrow':
                     rel_points = shape_data.get('relPoints')
                     if isinstance(rel_points, list) and len(rel_points) >= 4:
                         # === INICIO CORRECCIÓN FLECHA ===
                         # 1. Calcular puntos absolutos locales (sin el offset x,y de la flecha)
                         abs_points_local = [p * width if i % 2 == 0 else p * height for i, p in enumerate(rel_points)]

                         # 2. Calcular el offset absoluto de la flecha (su propia x,y)
                         offset_x = x # Ya calculamos x = rel_x * width antes
                         offset_y = y # Ya calculamos y = rel_y * height antes

                         # 3. Aplicar el offset a cada punto local para obtener coords globales
                         #    También aplicar la escala si existiera (multiplicar coordenadas locales por scale antes de sumar offset)
                         abs_points_global = [
                             (abs_points_local[i]) + offset_x if i % 2 == 0 else (abs_points_local[i]) + offset_y
                             for i in range(len(abs_points_local))
                         ]
                         current_app.logger.debug(f"  -> Dibujando Arrow Line en PDF: offset=({offset_x:.2f},{offset_y:.2f}) global_points={abs_points_global}, fill={stroke}, width={stroke_width}")

                         # 4. Dibujar la línea principal con los puntos globales
                         draw.line(abs_points_global, fill=stroke, width=stroke_width)
                         # === FIN CORRECCIÓN FLECHA (LÍNEA) ===

                         # --- Dibujo de la punta (usa puntos globales) ---
                         pointer_length = attrs.get('pointerLength', 10) # * max(scaleX, scaleY) # Escalar punta si es necesario
                         if len(abs_points_global) >= 4:
                            p_start_x, p_start_y = abs_points_global[-4], abs_points_global[-3]
                            p_end_x, p_end_y = abs_points_global[-2], abs_points_global[-1]
                            dx = p_end_x - p_start_x
                            dy = p_end_y - p_start_y
                            if dx == 0 and dy == 0:
                                current_app.logger.warning("  -> No se puede dibujar punta de flecha (puntos inicio/fin globales iguales)")
                            else:
                                angle = math.atan2(dy, dx)
                                angle1 = angle + math.pi * 0.85 # Ángulo para una pata de la punta
                                angle2 = angle - math.pi * 0.85 # Ángulo para la otra pata
                                head_x1 = p_end_x - pointer_length * math.cos(angle1)
                                head_y1 = p_end_y - pointer_length * math.sin(angle1)
                                head_x2 = p_end_x - pointer_length * math.cos(angle2)
                                head_y2 = p_end_y - pointer_length * math.sin(angle2)
                                current_app.logger.debug(f"  -> Dibujando Arrow Head en PDF: end=({p_end_x:.2f},{p_end_y:.2f}), head1=({head_x1:.2f},{head_y1:.2f}), head2=({head_x2:.2f},{head_y2:.2f})")
                                # Dibujar las dos líneas que forman la punta
                                draw.line([p_end_x, p_end_y, head_x1, head_y1], fill=stroke, width=stroke_width)
                                draw.line([p_end_x, p_end_y, head_x2, head_y2], fill=stroke, width=stroke_width)
                         else:
                              current_app.logger.warning(f"  -> No hay suficientes puntos globales ({len(abs_points_global)}) para dibujar punta de flecha.")
                     else:
                         current_app.logger.warning(f"  -> Datos de puntos inválidos para Arrow (relPoints no encontrado o no es lista): {shape_data.get('relPoints')}")

                 else:
                     current_app.logger.warning(f"draw_annotations_on_image: Forma desconocida o no soportada para dibujar en PDF: {shape_class}")

             except Exception as draw_err:
                 current_app.logger.error(f"Error dibujando forma {shape_class} (índice {i}) en PDF: {draw_err}. Attrs: {attrs}, RelData: { {k:v for k,v in shape_data.items() if k not in ['attrs', 'className']} }", exc_info=True)

        current_app.logger.debug("draw_annotations_on_image: Dibujo de formas completado.")
        return img_with_annotations

    except Exception as e:
        current_app.logger.error(f"Error general en draw_annotations_on_image: {e}", exc_info=True)
        return pil_image # Devolver original en caso de error mayor

# --- Función generate_point_report_pdf ---
def generate_point_report_pdf(point_id):
    """
    Genera un reporte PDF para un punto específico, incluyendo imagen anotada y notas.
    Retorna un objeto BytesIO con el contenido del PDF.
    """
    point = db.session.get(Point, point_id)
    if not point:
        raise ValueError(f"Punto con ID {point_id} no encontrado.")

    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4,
                            leftMargin=0.75*inch, rightMargin=0.75*inch,
                            topMargin=1.2*inch, bottomMargin=1.2*inch) # Márgenes ajustados para cabecera/pie
    story = []

    # Función para dibujar cabecera/pie en cada página
    def on_page(canvas, doc):
        HeaderCanvas().draw(canvas, doc)
        PageNumCanvas(doc).draw(canvas, doc)

    # --- Contenido del Punto ---
    story.append(Paragraph(f"Seguimiento de trámite", styles['PointTitle']))
    story.append(Spacer(1, 0.2*inch))
    point_title = f"• {point.name or f'Punto ID: {point.id}'}"
    if point.city: point_title += f" ({point.city})"
    story.append(Paragraph(point_title, styles['Normal'])) # Usar estilo Normal o uno específico
    story.append(Spacer(1, 0.3*inch))

    # --- Imagen Asociada (Tomar la primera o la más relevante) ---
    # Ordenar por timestamp ascendente para obtener la primera subida
    first_image = point.images.order_by(Image.upload_timestamp.asc()).first()

    if first_image:
        image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], first_image.filename)
        pil_image_original = None
        image_added_to_story = False

        if os.path.exists(image_path):
            try:
                current_app.logger.info(f"generate_point_report_pdf: Procesando imagen {first_image.filename} para punto {point_id}")
                pil_image_original = PILImage.open(image_path)

                # << --- LLAMADA A LA FUNCIÓN CORREGIDA PARA DIBUJAR --- >>
                pil_image_to_render = draw_annotations_on_image(pil_image_original, first_image.annotations_json)
                current_app.logger.info(f"generate_point_report_pdf: Imagen procesada por draw_annotations_on_image.")

                # Guardar imagen (con anotaciones) en un buffer en memoria
                img_buffer = BytesIO()
                # Determinar formato (JPEG preferido si no hay transparencia, sino PNG)
                img_format = pil_image_to_render.format or 'PNG' # Formato original si existe
                save_format = 'JPEG' if img_format.upper() == 'JPEG' or pil_image_to_render.mode == 'RGB' else 'PNG'

                # Convertir a RGB si se guarda como JPEG y no lo es
                if save_format == 'JPEG' and pil_image_to_render.mode != 'RGB':
                     current_app.logger.debug(f"Convirtiendo imagen a RGB para guardar como JPEG (modo actual: {pil_image_to_render.mode})")
                     pil_image_to_render = pil_image_to_render.convert('RGB')

                pil_image_to_render.save(img_buffer, format=save_format, quality=85 if save_format == 'JPEG' else None, optimize=True if save_format == 'JPEG' else False)
                img_buffer.seek(0) # Rebobinar el buffer

                # Crear objeto ReportLabImage desde el buffer
                img_reportlab = ReportLabImage(img_buffer)

                # Ajustar tamaño para que quepa en la página (manteniendo proporción)
                page_width = A4[0] - doc.leftMargin - doc.rightMargin
                img_width_pil, img_height_pil = pil_image_to_render.size # Usar tamaño de la imagen renderizada
                aspect = img_height_pil / float(img_width_pil) if img_width_pil else 1

                # Ajustar ancho al 80% del ancho de página disponible
                display_width = page_width * 0.8
                display_height = display_width * aspect

                # Limitar altura máxima (ej. 50% de la altura de la página)
                max_height = (A4[1] - doc.topMargin - doc.bottomMargin) * 0.6 # 60% del alto disponible
                if display_height > max_height:
                    display_height = max_height
                    display_width = display_height / aspect if aspect else display_width

                img_reportlab.drawWidth = display_width
                img_reportlab.drawHeight = display_height

                story.append(img_reportlab)
                image_added_to_story = True
                current_app.logger.info(f"generate_point_report_pdf: Imagen añadida al PDF.")

            except UnidentifiedImageError:
                 current_app.logger.error(f"Error al procesar imagen {first_image.filename}: Formato no identificado.", exc_info=True)
                 story.append(Paragraph(f"[Error: Formato de imagen no reconocido - {first_image.original_filename or first_image.id}]", styles['LeftSmall']))
            except Exception as e:
                current_app.logger.error(f"Error al procesar/añadir imagen {first_image.filename} al PDF: {e}", exc_info=True)
                story.append(Paragraph(f"[Error al cargar imagen: {first_image.original_filename or first_image.id}]", styles['LeftSmall']))
            finally:
                # Cerrar el archivo de imagen original si se abrió
                if pil_image_original:
                    try:
                        pil_image_original.close()
                    except Exception as close_err:
                        current_app.logger.warning(f"Error menor cerrando imagen original {first_image.filename}: {close_err}")
        else:
            # Si el archivo no existe en disco
            story.append(Paragraph(f"[Imagen no encontrada en disco: {first_image.original_filename or first_image.id}]", styles['LeftSmall']))
            current_app.logger.warning(f"Archivo de imagen no encontrado en disco para PDF: {image_path}")

        # Notas y Metadatos de la Imagen (si se añadió o si no se encontró el archivo)
        if image_added_to_story or not os.path.exists(image_path):
            if first_image.notes:
                # Usar nl2br del contexto Jinja si estuviera disponible o reemplazar manualmente
                notes_html = first_image.notes.replace('\r\n', '<br/>').replace('\n', '<br/>').replace('\r', '<br/>')
                story.append(Paragraph(f"Notas: {notes_html}", styles['ImageNotes']))
            else:
                story.append(Paragraph("Notas: Ninguna", styles['ImageNotes']))

            uploader_name = first_image.uploader.username if first_image.uploader else 'Desconocido'
            upload_time_str = first_image.upload_timestamp.strftime('%d/%m/%y %H:%M') if first_image.upload_timestamp else 'N/A'
            meta_text = f"Subida por: {uploader_name} el {upload_time_str}"
            story.append(Paragraph(meta_text, styles['ImageMeta']))
            story.append(Spacer(1, 0.3*inch))

    else:
        # Si no hay ninguna imagen asociada al punto
        story.append(Paragraph("[No hay imagen asociada a este punto]", styles['LeftSmall']))
        story.append(Spacer(1, 0.3*inch))

    # --- Sección DETALLE ---
    story.append(Paragraph("DETALLE:", styles['DetailHeader']))
    if point.description:
        # Reemplazar saltos de línea por <br/> para ReportLab
        desc_text = point.description.replace('\n', '<br/>')
        story.append(Paragraph(f"Descripción General: {desc_text}", styles['DetailItem']))
    story.append(Paragraph(f"Estado Actual: {point.status.capitalize()}", styles['DetailItem']))
    story.append(Paragraph(f"Coordenadas: ({point.latitude:.6f}, {point.longitude:.6f})", styles['DetailItem']))
    if point.source:
        story.append(Paragraph(f"Origen/Capa: {point.source}", styles['DetailItem']))

    # --- Listado de Cámaras ---
    cameras = point.cameras.order_by(Camera.created_at.asc()).all()
    if cameras:
         story.append(Spacer(1, 0.1*inch)) # Pequeño espacio antes de la lista de cámaras
         story.append(Paragraph("Cámaras Asociadas:", styles['DetailItem'])) # Título opcional
         for i, cam in enumerate(cameras):
             letter = chr(ord('A') + i) # Enumerar con A, B, C...
             camera_detail = f"    {letter}- Cámara {cam.type.capitalize()}: {cam.direction}" # Indentación con  
             if cam.photo_filename:
                 camera_detail += " (con foto adjunta en sistema)"
             story.append(Paragraph(camera_detail, styles['DetailItem'])) # Usar DetailItem u otro estilo
    else:
         story.append(Paragraph("No hay cámaras asociadas.", styles['DetailItem']))

    # Construir el PDF
    try:
        doc.build(story, onFirstPage=on_page, onLaterPages=on_page)
        current_app.logger.info(f"generate_point_report_pdf: PDF construido exitosamente para punto {point_id}.")
    except Exception as build_err:
        current_app.logger.error(f"Error construyendo el PDF para punto {point_id}: {build_err}", exc_info=True)
        raise # Relanzar la excepción para que el controlador la maneje

    buffer.seek(0) # Rebobinar el buffer para leerlo
    return buffer