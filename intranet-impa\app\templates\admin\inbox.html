<!-- templates/admin/inbox.html -->
{% extends "base.html" %}
{% block title %}<PERSON><PERSON><PERSON> Mensajes{% endblock %}

{% block content %}
<div class="container my-4">
  <h1><PERSON><PERSON><PERSON> de Mensajes</h1>
  {% include '_search_form.html' %}
  {% include '_pagination.html' %}
  <hr>
  {% if messages %}
    {% for message in messages %}
      <div class="card mb-3 {% if not message.is_read %}border-warning{% endif %}">
        <div class="card-header">
          <div class="row">
            <div class="col-md-8">
              <strong>De:</strong> {{ message.sender.full_name }}
            </div>
            <div class="col-md-4 text-right">
              <small>{{ message.sent_at|to_local }}</small>
            </div>
          </div>
          <div class="mt-2">
            <strong>Asunto:</strong> {{ message.subject or "Sin asunto" }}
          </div>
        </div>
        <div class="card-body">
          <p>{{ message.message_text }}</p>
        </div>
        <div class="card-footer text-right">
          <a href="{{ url_for('routes.reply_message', message_id=message.id) }}" class="btn btn-sm btn-primary">Responder</a>
          <a href="{{ url_for('routes.forward_message', message_id=message.id) }}" class="btn btn-sm btn-secondary">Reenviar</a>
          {% if current_user.role == 'pastorado' %}
            <a href="{{ url_for('routes.pastor_member_detail', user_id=message.sender_id) }}" class="btn btn-sm btn-info">Ver Detalles</a>
          {% else %}
            <a href="{{ url_for('routes.user_detail', user_id=message.sender_id) }}" class="btn btn-sm btn-info">Ver Detalles</a>
          {% endif %}
          <!-- Formulario para eliminar el mensaje -->
          <form action="{{ url_for('routes.delete_message', message_id=message.id) }}" method="POST" style="display:inline;">
            <button type="submit" class="btn btn-sm btn-danger">Eliminar</button>
          </form>
        </div>
      </div>
    {% endfor %}
  {% else %}
    <p>No tienes mensajes.</p>
  {% endif %}
</div>
{% endblock %}
