# server_websocket.py para pruebas
import eventlet
eventlet.monkey_patch()  # Esto debe estar antes de cualquier otra importación

import logging
from flask import Flask, send_from_directory
from flask_socketio import SocketIO, emit
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socketio = SocketIO(app, cors_allowed_origins="*")

# Configuración de logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s %(levelname)s: %(message)s')

@app.route('/prueba_websocket')
def prueba_websocket():
    logging.debug("Serving prueba_websocket.html")
    return send_from_directory(os.path.abspath(os.getcwd()), 'prueba_websocket.html')

@socketio.on('connect')
def handle_connect():
    logging.info("Client connected")

@socketio.on('message')
def handle_message(data):
    logging.info(f"Received message: {data}")
    emit('response', f"Echo: {data}")

@socketio.on('disconnect')
def handle_disconnect():
    logging.info("Client disconnected")

if __name__ == '__main__':
    logging.info("Starting server on port 5000 without SSL")
    try:
        # Ejecutar el servidor sin SSL en el puerto 5000
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        logging.error(f"Failed to start server: {e}")
