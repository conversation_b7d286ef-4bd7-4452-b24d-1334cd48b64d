<!-- /app/templates/admin/list_reviews.html -->
{% extends "base.html" %}
{% block title %}Histórico de Reseñas{% endblock %}

{% block content %}
<div class="container my-4">
  <h1>Histórico de Reseñas</h1>

  {# Incluir búsqueda si es necesario #}
  {# {% include '_search_form.html' %} #}
  {# Incluir paginación si es necesario (y si la ruta la implementa) #}
  {# {% from "_pagination.html" import render_pagination %} #}
  {# {{ render_pagination(pagination, 'routes.list_reviews', filters=filters) }} #}

  <hr>

  {% if reviews %}
    <ul class="list-group">
      {% for review in reviews %}
        <li class="list-group-item">
          <div class="d-flex justify-content-between">
            <div>
              {# Quién escribió la reseña #}
              <strong>De:</strong> {{ review.reviewer.first_name if review.reviewer else 'Usuario Eliminado' }} {{ review.reviewer.last_name if review.reviewer else '' }}<br>
              {# A quién se reseñó - CORREGIDO: usar review.user_reviewed #}
              <strong>Para:</strong>
              {% if review.user_reviewed %} {# Verificar que el usuario reseñado aún existe #}
                <a href="{{ url_for('routes.user_detail', user_id=review.user_reviewed.id) }}">
                  {{ review.user_reviewed.first_name }} {{ review.user_reviewed.last_name }}
                </a>
              {% else %}
                Usuario Eliminado (ID: {{ review.user_id }})
              {% endif %}
            </div>
            <div>
              <small>{{ review.created_at|to_local }}</small>
            </div>
          </div>
          <p class="mt-2">{{ review.review_text }}</p>
          {# Botones Editar/Eliminar #}
          {# Asegurarse que el usuario reseñado exista antes de permitir acciones? #}
          {% if review.user_reviewed and (current_user.role in ['administrador', 'secretaria'] or (current_user.role == 'pastorado' and review.user_reviewed.role == 'miembro' and review.user_reviewed.church_id == current_user.church_id)) %}
            <div class="mt-2">
                <a href="{{ url_for('routes.edit_review', review_id=review.id) }}" class="btn btn-sm btn-primary">Editar</a>
                <form action="{{ url_for('routes.delete_review', review_id=review.id) }}" method="POST" style="display:inline;" onsubmit="return confirm('¿Estás seguro de eliminar esta reseña?');">
                  <button type="submit" class="btn btn-sm btn-danger">Eliminar</button>
                </form>
            </div>
          {% endif %}
        </li>
      {% endfor %}
    </ul>
  {% else %}
    <p>No hay reseñas registradas.</p>
  {% endif %}

</div>
{% endblock %}