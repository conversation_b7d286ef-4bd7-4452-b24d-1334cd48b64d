<!-- /app/templates/secretaria/dashboard.html -->
{% extends "base.html" %}
{% block title %}Dashboard Secretaria{% endblock %}

{% block content %}
<div class="container my-4">
    <h1><i class="fas fa-clipboard-list mr-2"></i>Escritorio Secretaria</h1>
    <p>B<PERSON><PERSON>ida, {{ view_user.first_name }}!</p>

    <!-- <PERSON>la de Widgets -->
    <div class="row mb-4">
        <!-- Widget Total Usuarios -->
        <div class="col-md-3">
            <div class="card text-white bg-primary mb-3 shadow">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-users mr-2"></i>Total Usuarios</h5>
                    <p class="card-text display-4">{{ total_users_count if total_users_count is defined else 'N/A' }}</p>
                </div>
                 <a href="{{ url_for('routes.list_users') }}" class="card-footer text-white">Ver detalles <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Docs Pendientes -->
        <div class="col-md-3">
            <div class="card text-white bg-warning mb-3 shadow">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-file-alt mr-2"></i>Docs Pendientes</h5>
                    <p class="card-text display-4">{{ pending_docs_count if pending_docs_count is defined else 'N/A' }}</p>
                    <small>Esperando aprobación</small>
                </div>
                 <a href="{{ url_for('routes.list_documents', status='pendiente') }}" class="card-footer text-white">Gestionar <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Mensajes Nuevos -->
        <div class="col-md-3">
            <div class="card text-white bg-info mb-3 shadow">
                 <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-envelope-open-text mr-2"></i>Mensajes Nuevos</h5>
                    <p class="card-text display-4">{{ unread_count if unread_count is defined else 'N/A' }}</p>
                    <small>Sin leer</small>
                </div>
                <a href="{{ url_for('routes.inbox') }}" class="card-footer text-white">Ir al Buzón <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Cumpleaños -->
        <div class="col-md-3">
            <div class="card bg-light mb-3 shadow">
                <div class="card-header"><i class="fas fa-birthday-cake mr-2"></i>Próximos Cumpleaños</div>
                <div class="card-body" style="max-height: 150px; overflow-y: auto;">
                    {% if upcoming_birthdays %}
                        <ul class="list-unstyled mb-0">
                            {% for user_bday in upcoming_birthdays %}
                                <li class="mb-1">
                                    <a href="{{ url_for('routes.user_detail', user_id=user_bday.id) }}">{{ user_bday.full_name }}</a>
                                    <span class="badge badge-pill badge-secondary float-right">{{ user_bday.date_of_birth.strftime('%d/%m') }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted small">No hay cumpleaños próximos.</p>
                    {% endif %}
                </div>
                 <a href="{{ url_for('routes.calendar_view') }}" class="card-footer text-muted">Ver Calendario <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>
    <!-- Fin Fila de Widgets -->

    <hr>

    <!-- Fila de Acciones Rápidas y Anuncios -->
    <div class="row mb-4">
        <!-- Columna Acciones Rápidas -->
        <div class="col-lg-4">
             <div class="card shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <i class="fas fa-bolt mr-2"></i>Acciones Rápidas
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('routes.register') }}" class="list-group-item list-group-item-action"><i class="fas fa-user-plus fa-fw mr-2 text-success"></i>Registrar Usuario</a>
                    <a href="{{ url_for('routes.create_church') }}" class="list-group-item list-group-item-action"><i class="fas fa-church fa-fw mr-2 text-primary"></i>Crear Iglesia</a>
                    <a href="{{ url_for('routes.upload_document') }}" class="list-group-item list-group-item-action"><i class="fas fa-file-upload fa-fw mr-2 text-info"></i>Subir Documento</a>
                     <a href="{{ url_for('routes.manage_announcement') }}" class="list-group-item list-group-item-action"><i class="fas fa-bullhorn fa-fw mr-2 text-warning"></i>Crear Anuncio</a>
                     <a href="{{ url_for('routes.list_documents', status='pendiente') }}" class="list-group-item list-group-item-action"><i class="fas fa-check-circle fa-fw mr-2 text-danger"></i>Aprobar Documentos</a>
                     <a href="{{ url_for('routes.asignar_jerarquias') }}" class="list-group-item list-group-item-action"><i class="fas fa-sitemap fa-fw mr-2 text-secondary"></i>Asignar Jerarquías</a>
                     <a href="{{ url_for('routes.choose_review_user') }}" class="list-group-item list-group-item-action"><i class="fas fa-history fa-fw mr-2 text-danger"></i>Agregar Histórico</a>
                </div>
            </div>
        </div>

        <!-- Columna Anuncios -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                 <div class="card-header bg-light">
                    <i class="fas fa-bullhorn mr-2"></i>Anuncios Recientes
                 </div>
                 <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% if announcements %}
                        {% for announcement in announcements %}
                            <div class="alert alert-{{ 'secondary' if loop.index % 2 == 0 else 'light' }} border mb-2" role="alert">
                                <h5 class="alert-heading">{{ announcement.title }}</h5>
                                <p class="mb-1 small">{{ announcement.content | nl2br | safe }}</p>
                                <hr class="my-1">
                                <p class="mb-0 small text-muted d-flex justify-content-between">
                                   <span>
                                        <i class="fas fa-user fa-fw"></i> {{ announcement.author.full_name if announcement.author else 'Sistema' }}
                                        <i class="far fa-clock fa-fw ml-2"></i> {{ announcement.created_at | to_local }}
                                    </span>
                                    {% if current_user.role in ['administrador', 'secretaria'] or (announcement.author_id == current_user.id) %}
                                     <span>
                                         <a href="{{ url_for('routes.manage_announcement', announcement_id=announcement.id) }}" class="badge badge-primary"><i class="fas fa-edit"></i> Editar</a>
                                     </span>
                                    {% endif %}
                                </p>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No hay anuncios para mostrar.</p>
                    {% endif %}
                 </div>
             </div>
        </div>
    </div>
    <!-- Fin Fila Acciones/Anuncios -->

     <hr>
    <h2>Otras Secciones</h2>
    {# Mantener las cards originales como enlaces detallados #}
     <div class="row mt-4">
         <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Miembros</div>
            <div class="card-body">
              <a href="{{ url_for('routes.list_users') }}" class="btn btn-outline-primary btn-block">Listar/Buscar Usuarios</a>
              <a href="{{ url_for('routes.church_members_count') }}" class="btn btn-outline-secondary btn-block">Miembros por Iglesia</a>
            </div>
          </div>
        </div>
         <div class="col-md-3">
            <div class="card mb-3">
                <div class="card-header bg-light">Pastores</div>
                <div class="card-body">
                <a href="{{ url_for('routes.list_pastores') }}" class="btn btn-outline-primary btn-block">Listado</a>
                <a href="{{ url_for('routes.seleccionar_pastor_credencial') }}" class="btn btn-outline-secondary btn-block">Generar Credencial</a>
                </div>
            </div>
         </div>
            <div class="col-md-3">
            <div class="card mb-3">
                <div class="card-header bg-light">Iglesia</div>
                <div class="card-body">
                <a href="{{ url_for('routes.list_churches') }}" class="btn btn-outline-primary btn-block">Ver Iglesias</a>
                <a href="{{ url_for('routes.list_inventories') }}" class="btn btn-outline-info btn-block">Inventario</a>
                </div>
            </div>
            </div>
             <!-- Jerarquías ya está en Acciones Rápidas
             <div class="col-md-3">...Jerarquías...</div>
             -->
            <div class="col-md-3">
                <div class="card mb-3">
                <div class="card-header bg-light">Correo</div>
                <div class="card-body">
                    <a href="{{ url_for('routes.send_message') }}" class="btn btn-outline-primary btn-block">Enviar Mensaje</a>
                    <a href="{{ url_for('routes.inbox') }}" class="btn btn-outline-secondary btn-block">Consultar Mensajes</a>
                </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-header bg-light">Documentación</div>
                    <div class="card-body">
                    <a href="{{ url_for('routes.list_documents') }}" class="btn btn-outline-primary btn-block">Ver Documentación</a>
                    <a href="{{ url_for('routes.diploma_preview') }}" class="btn btn-outline-secondary btn-block">Diplomas</a>
                    <a href="{{ url_for('routes.acta_preview') }}" class="btn btn-outline-secondary btn-block">Actas</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-header bg-light">Mapa</div>
                    <div class="card-body">
                    <a href="{{ url_for('routes.map_view') }}" class="btn btn-outline-dark btn-block">Ver Mapa</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-header bg-light">Calendario</div>
                    <div class="card-body">
                    <a href="{{ url_for('routes.calendar_view') }}" class="btn btn-outline-dark btn-block">Ver Calendario</a>
                    </div>
                </div>
            </div>
             <!-- Histórico ya está en Acciones Rápidas
            <div class="col-md-3">...Histórico...</div>
            -->
      </div>

</div>
{% endblock %}