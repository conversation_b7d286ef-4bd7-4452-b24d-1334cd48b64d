<!-- /app/templates/admin/user_detail.html -->
{% extends "base.html" %}

{% block title %}Detalles de Usuario{% endblock %}

{% block content %}
  <h1>Detalles de Usuario: {{ user.first_name }} {{ user.last_name }}</h1>
  
  <h3>Información Personal</h3>
  <p><strong>ID:</strong> {{ user.id }}</p>
  <p><strong>Nombre de Usuario:</strong> {{ user.username }}</p>
  <p><strong>Nombre Completo:</strong> {{ user.first_name }} {{ user.last_name }}</p>
  <p><strong><PERSON><PERSON><PERSON> Electrónico:</strong> {{ user.email }}</p>
  <p><strong>DNI:</strong> {{ user.dni if user.dni else '-' }}</p>
  <p><strong><PERSON><PERSON> de Nacimiento:</strong> {{ user.date_of_birth if user.date_of_birth else '-' }}</p>
  <p><strong>Teléfono:</strong> {{ user.phone_number if user.phone_number else '-' }}</p>
  <p><strong><PERSON><PERSON><PERSON><PERSON>:</strong> {{ user.address if user.address else '-' }}</p>
  <p><strong>Rol Principal:</strong> {{ user.role }}</p>
  <p><strong>Iglesia:</strong> {{ user.church.name if user.church else 'Ninguna' }}</p>
  
  {% if user.role == 'pastorado' %}
    <h3>Información del Pastor</h3>
    <p><strong>Grado Pastoral:</strong>
        {{ user.pastor.grado|title if user.pastor and user.pastor.grado else '-' }}
    </p>
    <p><strong>Matrícula:</strong>
        {{ user.pastor.matricula if user.pastor and user.pastor.matricula else '-' }}
    </p>
    <p><strong>Funciones Pastorales:</strong>
      {% if user.pastor_roles %}
          {{ user.pastor_roles | map(attribute='role_name') | map('title') | join(', ') }}
      {% else %}
         -
      {% endif %}
    </p>
  {% elif member %}
    <h3>Información del Miembro</h3>
    <p><strong>Funciones:</strong>
      {% if member.functions %}
        {{ member.functions | map(attribute='name') | join(', ') }}
      {% else %}
        Sin Función
      {% endif %}
    </p>
  {% endif %}
  
  <a href="{{ url_for('routes.edit_profile', user_id=user.id) }}" class="btn btn-primary btn-sm">
    Editar Información
  </a>
  
  <h2>Relaciones Familiares</h2>
  <ul>
    {% for rel in relationships %}
      {% if rel.user_id_1 == user.id %}
        {% set other_user = rel.user2 %}
      {% else %}
        {% set other_user = rel.user1 %}
      {% endif %}
      <li>
        <a href="{{ url_for('routes.user_detail', user_id=other_user.id) }}">
          {{ describe_relationship(rel, user.id) }}
        </a>
      </li>
    {% endfor %}
  </ul>
  <a href="{{ url_for('routes.member_relationships', user_id=user.id) }}" class="btn btn-secondary btn-sm">
    Editar Relaciones
  </a>

  {% if reviews %}
    <h3>Histórico de Reseñas</h3>
    <ul class="list-group">
      {% for review in reviews %}
        <li class="list-group-item">
          <strong>Por:</strong> {{ review.reviewer.first_name }} {{ review.reviewer.last_name }} -
          <small>{{ review.created_at|to_local }}</small>
          <p>{{ review.review_text }}</p>
          {% if current_user.role in ['administrador', 'secretaria'] or (current_user.role == 'pastorado' and user.role == 'miembro' and user.church_id == current_user.church_id) %}
            <a href="{{ url_for('routes.edit_review', review_id=review.id) }}" class="btn btn-sm btn-primary">Editar</a>
            <form action="{{ url_for('routes.delete_review', review_id=review.id) }}" method="POST" style="display:inline;">
              <button type="submit" class="btn btn-sm btn-danger">Eliminar</button>
            </form>
          {% endif %}
        </li>
      {% endfor %}
    </ul>
  {% else %}
    <p>No hay reseñas registradas.</p>
  {% endif %}
{% endblock %}
