# File: backend/apps/incidents/consumers.py
# -----------------------------------------------

import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

class IncidentsConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # self.room_name = self.scope['url_route']['kwargs']['room_name']
        # self.room_group_name = f'chat_{self.room_name}'
        # await self.channel_layer.group_add(
        #     self.room_group_name,
        #     self.channel_name
        # )
        await self.accept()
        await self.send(text_data=json.dumps({
            'message': 'Connected to Incidents WebSocket!'
        }))


    async def disconnect(self, close_code):
        # await self.channel_layer.group_discard(
        #     self.room_group_name,
        #     self.channel_name
        # )
        print(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # # Send message to room group
        # await self.channel_layer.group_send(
        #     self.room_group_name,
        #     {
        #         'type': 'chat_message', # Corresponde a un método `async def chat_message(self, event)`
        #         'message': message
        #     }
        # )
        print(f"Received message: {message}")
        await self.send(text_data=json.dumps({
            'response_message': f"Server received: {message}"
        }))

    # # Ejemplo de manejador de mensajes de grupo
    # async def chat_message(self, event):
    #     message = event['message']
    #     # Send message to WebSocket
    #     await self.send(text_data=json.dumps({
    #         'message': message
    #     }))


class IncidenciaConsumer(AsyncWebsocketConsumer):
    """
    Consumidor WebSocket para actualizar la ubicación de la brigada en tiempo real.
    """

    async def connect(self):
        """
        Conectar al WebSocket y unirse al grupo de la incidencia.
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()

        self.incidencia_id = self.scope['url_route']['kwargs']['incidencia_id']
        self.incidencia_group_name = f'incidencia_{self.incidencia_id}'

        # Verificar que el usuario tenga permiso para ver esta incidencia
        user = self.scope['user']
        if not user.is_authenticated:
            await self.close()
            return

        # Verificar que la incidencia exista y el usuario tenga permiso
        incidencia = await self.get_incidencia()
        if not incidencia:
            await self.close()
            return

        # Verificar que el usuario sea el que reportó la incidencia o sea staff
        if user != incidencia.usuario_reporta and not user.is_staff and user != incidencia.brigada_asignada:
            await self.close()
            return

        # Unirse al grupo de la incidencia
        await self.channel_layer.group_add(
            self.incidencia_group_name,
            self.channel_name
        )

        await self.accept()

        # Si hay una brigada asignada, enviar su ubicación actual
        if incidencia.brigada_asignada and (incidencia.estado == 'DERIVADA_BRIGADA' or incidencia.estado == 'EN_PROCESO_BRIGADA'):
            brigada_ubicacion = await self.get_brigada_ubicacion(incidencia.brigada_asignada)
            if brigada_ubicacion and brigada_ubicacion.posicion_actual:
                await self.send(text_data=json.dumps({
                    'type': 'brigada_location_update',
                    'lat': brigada_ubicacion.posicion_actual.y,
                    'lng': brigada_ubicacion.posicion_actual.x,
                    'ultima_actualizacion': brigada_ubicacion.fecha_actualizacion.strftime("%d/%m/%Y %H:%M")
                }))

    async def disconnect(self, close_code):
        """
        Desconectar del WebSocket y salir del grupo de la incidencia.
        """
        # Salir del grupo de la incidencia
        await self.channel_layer.group_discard(
            self.incidencia_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        """
        Recibir mensaje del WebSocket.
        """
        data = json.loads(text_data)
        message_type = data.get('type')

        # Si es una actualización de ubicación de la brigada
        if message_type == 'brigada_location_update':
            user = self.scope['user']
            incidencia = await self.get_incidencia()

            # Verificar que el usuario sea la brigada asignada o staff
            if user != incidencia.brigada_asignada and not user.is_staff:
                return

            # Actualizar la ubicación de la brigada
            lat = data.get('lat')
            lng = data.get('lng')

            if lat is not None and lng is not None:
                # Actualizar la ubicación en la base de datos
                await self.update_brigada_ubicacion(user, lat, lng)

                # Enviar la actualización a todos los clientes en el grupo
                await self.channel_layer.group_send(
                    self.incidencia_group_name,
                    {
                        'type': 'brigada_location_update',
                        'lat': lat,
                        'lng': lng,
                        'ultima_actualizacion': data.get('ultima_actualizacion', '')
                    }
                )

    async def brigada_location_update(self, event):
        """
        Enviar actualización de ubicación de la brigada a los clientes WebSocket.
        """
        await self.send(text_data=json.dumps({
            'type': 'brigada_location_update',
            'lat': event['lat'],
            'lng': event['lng'],
            'ultima_actualizacion': event['ultima_actualizacion']
        }))

    @database_sync_to_async
    def get_incidencia(self):
        """
        Obtener la incidencia de la base de datos.
        """
        from apps.incidents.models import Incidencia
        try:
            return Incidencia.objects.get(id=self.incidencia_id)
        except Incidencia.DoesNotExist:
            return None

    @database_sync_to_async
    def get_brigada_ubicacion(self, brigada):
        """
        Obtener la ubicación de la brigada de la base de datos.
        """
        from apps.locations.models import UbicacionUsuario
        try:
            return UbicacionUsuario.objects.get(usuario=brigada)
        except UbicacionUsuario.DoesNotExist:
            return None

    @database_sync_to_async
    def update_brigada_ubicacion(self, brigada, lat, lng):
        """
        Actualizar la ubicación de la brigada en la base de datos.
        """
        from django.contrib.gis.geos import Point
        from apps.locations.models import UbicacionUsuario

        try:
            ubicacion, created = UbicacionUsuario.objects.get_or_create(
                usuario=brigada,
                defaults={'posicion_actual': Point(lng, lat)}
            )

            if not created:
                ubicacion.posicion_actual = Point(lng, lat)
                ubicacion.save()

            return ubicacion
        except Exception as e:
            print(f"Error al actualizar ubicación de brigada: {e}")
            return None
