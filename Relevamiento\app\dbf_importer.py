# app/dbf_importer.py ---

import click
from flask.cli import with_appcontext
from flask import current_app
# Corrección: Se eliminó FieldParserError de la importación
from dbfread import DBF, DBFNotFound, MissingMemoFile
import os
from app import db
from app.models import Point

# --- ¡¡¡ CONFIGURACIÓN CRÍTICA - BASADA EN TU IMAGEN !!! ---
# Revisa y ajusta si es necesario para tu archivo ACTUALES.dbf
GPS_FIELD_NAME = 'GPS'          # Columna que contiene "latitud,longitud"
NAME_FIELD_NAME = 'DIRECCION'   # Columna a usar para el nombre del punto
DESCRIPTION_FIELD_NAME = 'layer' # Opcional: Columna para la descripción (o poner None)
ID_FIELD_NAME = 'ID'            # Opcional: Columna ID para usar en logs o fallback de nombre
# -----------------------------------------------------------

# --- Codificación del archivo DBF ---
# Prueba 'utf-8', 'cp1252' si 'latin1' da problemas con acentos/caracteres especiales
DBF_ENCODING = 'latin1'
# -----------------------------------

def import_points_from_dbf(dbf_path):
    """
    Lee un archivo DBF, parsea un campo GPS combinado ("lat,lon"),
    e importa los puntos a la base de datos.
    """
    logger = current_app.logger

    if not os.path.exists(dbf_path):
        logger.error(f"Error: El archivo DBF no se encuentra en {dbf_path}")
        click.echo(f"Error: El archivo DBF no se encuentra en {dbf_path}", err=True)
        return

    logger.info(f"Iniciando importación de puntos desde: {dbf_path} con codificación {DBF_ENCODING}")
    click.echo(f"Importando puntos desde {dbf_path}...")

    count = 0
    added_count = 0
    skipped_invalid_coords = 0
    skipped_parsing_errors = 0
    points_to_add = [] # Acumular puntos para inserción masiva

    try:
        # Carga la tabla DBF
        table = DBF(dbf_path, encoding=DBF_ENCODING, ignore_missing_memofile=True)
        logger.info(f"Archivo DBF abierto. Campos encontrados: {table.field_names}")

        # Verifica si los campos configurados existen
        required_config_fields = [GPS_FIELD_NAME, NAME_FIELD_NAME] # Campos que *configuramos* para usar
        if DESCRIPTION_FIELD_NAME: required_config_fields.append(DESCRIPTION_FIELD_NAME)
        if ID_FIELD_NAME: required_config_fields.append(ID_FIELD_NAME)

        missing_fields = [f for f in required_config_fields if f and f not in table.field_names]
        if missing_fields:
            logger.error(f"Error Crítico: Faltan campos configurados en el DBF: {', '.join(missing_fields)}")
            logger.error(f"Campos disponibles en el DBF: {', '.join(table.field_names)}")
            logger.error(f"Por favor, verifica la configuración de campos en 'app/dbf_importer.py' (GPS_FIELD_NAME, NAME_FIELD_NAME, etc.)")
            click.echo(f"Error: Faltan campos configurados ({', '.join(missing_fields)}) en el DBF. Revisa 'app/dbf_importer.py'.", err=True)
            return

        # --- Inicio del Bucle de Procesamiento de Registros ---
        for record in table:
            count += 1
            record_id_info = f"Registro {count}" # Info para logs
            if ID_FIELD_NAME and ID_FIELD_NAME in record:
                record_id_info += f" (ID: {record.get(ID_FIELD_NAME, 'N/A')})"

            try:
                # 1. Obtener y parsear el campo GPS combinado
                gps_str = record.get(GPS_FIELD_NAME)
                lat = None
                lon = None

                if isinstance(gps_str, str) and ',' in gps_str:
                    parts = gps_str.split(',')
                    if len(parts) == 2:
                        try:
                            # Intentar convertir a float, quitando espacios extra
                            lat_val = float(parts[0].strip())
                            lon_val = float(parts[1].strip())

                            # Validar rango de coordenadas GPS
                            if -90 <= lat_val <= 90 and -180 <= lon_val <= 180:
                                lat = lat_val
                                lon = lon_val
                            else:
                                logger.warning(f"{record_id_info} omitido: Coordenadas GPS fuera de rango ({lat_val}, {lon_val}) en campo '{GPS_FIELD_NAME}'. Valor: '{gps_str}'")
                                skipped_invalid_coords += 1
                                continue # Saltar al siguiente registro
                        except (ValueError, TypeError):
                            logger.error(f"Error de conversión numérica en {record_id_info} para campo '{GPS_FIELD_NAME}'. Valor: '{gps_str}'")
                            skipped_parsing_errors += 1
                            continue # Saltar al siguiente registro
                    else:
                        logger.error(f"Error de formato en {record_id_info}: Campo '{GPS_FIELD_NAME}' no tiene dos partes separadas por coma. Valor: '{gps_str}'")
                        skipped_parsing_errors += 1
                        continue # Saltar al siguiente registro
                else:
                    # Manejar caso donde el campo GPS no sea string o no tenga coma
                    logger.error(f"Error de formato o tipo en {record_id_info}: Campo '{GPS_FIELD_NAME}' no es un string válido o no contiene coma. Valor: '{gps_str}' (Tipo: {type(gps_str)})")
                    skipped_parsing_errors += 1
                    continue # Saltar al siguiente registro

                # Si lat o lon siguen siendo None después del intento de parseo, es un error inesperado
                if lat is None or lon is None:
                    logger.error(f"Error inesperado en {record_id_info}: Lat ({lat}) o Lon ({lon}) son None después del parseo. Valor GPS original: '{gps_str}'")
                    skipped_parsing_errors += 1
                    continue # Saltar al siguiente registro

                # 2. Obtener Nombre (DIRECCION)
                name_val = record.get(NAME_FIELD_NAME)
                name = str(name_val).strip() if name_val is not None else None
                if not name: # Si name es None o string vacío ""
                    fallback_id = record.get(ID_FIELD_NAME, count) if ID_FIELD_NAME else count
                    name = f'Punto Sin Dirección (ID: {fallback_id})'
                    logger.warning(f"Usando nombre por defecto '{name}' para {record_id_info} porque el campo '{NAME_FIELD_NAME}' estaba vacío.")

                # 3. Obtener Descripción (layer, opcional)
                description = None
                if DESCRIPTION_FIELD_NAME and DESCRIPTION_FIELD_NAME in record:
                    desc_val = record.get(DESCRIPTION_FIELD_NAME)
                    description = str(desc_val).strip() if desc_val is not None else None
                    if description == '': description = None # Tratar string vacío como None

                # 4. Crear el objeto Point para añadirlo a la lista
                point = Point(
                    latitude=lat,
                    longitude=lon,
                    name=name,
                    description=description
                )
                points_to_add.append(point)
                added_count += 1

                # Opcional: Imprimir progreso cada N registros
                if count % 100 == 0:
                    click.echo(f"Procesados {count} registros...")

            except Exception as e: # Captura errores generales inesperados procesando un registro
                logger.exception(f"Error inesperado procesando {record_id_info}: {e}. Registro completo: {record}")
                skipped_parsing_errors += 1
                continue # Saltar al siguiente registro

        # --- Fin del Bucle ---

        # Añadir todos los puntos acumulados a la base de datos en una transacción
        if points_to_add:
            logger.info(f"Añadiendo {len(points_to_add)} puntos válidos a la base de datos...")
            try:
                db.session.bulk_save_objects(points_to_add)
                db.session.commit()
                logger.info("Puntos añadidos y cambios guardados en la BD.")
            except Exception as commit_error:
                db.session.rollback()
                logger.exception(f"Error al guardar puntos en la BD: {commit_error}")
                click.echo(f"Error Crítico: No se pudieron guardar los puntos en la base de datos. Ver logs.", err=True)
                return # Detener si falla el commit masivo
        else:
            logger.info("No se encontraron puntos válidos para añadir en el archivo.")
            # No necesitamos rollback si no intentamos añadir nada

        # --- Resumen Final ---
        click.echo("-" * 30)
        click.echo(f"Importación Finalizada.")
        click.echo(f"Total de registros leídos en el DBF: {count}")
        click.echo(f"Puntos válidos añadidos a la BD: {added_count}")
        click.echo(f"Registros omitidos (coords fuera de rango): {skipped_invalid_coords}")
        click.echo(f"Registros omitidos (error formato/parseo): {skipped_parsing_errors}")
        click.echo("-" * 30)

    # --- Manejo de Errores Generales de Apertura o Lectura del DBF ---
    except DBFNotFound as e:
        logger.error(f"Error: Archivo DBF o asociado no encontrado: {e}")
        click.echo(f"Error: Archivo DBF no encontrado: {e}", err=True)
        db.session.rollback()
    except MissingMemoFile as e:
         logger.warning(f"Advertencia: Archivo Memo (.dbt o .fpt) no encontrado: {e}. Los campos Memo estarán vacíos.")
         # Continúa la importación sin los campos memo si se configuró ignore_missing_memofile=True
    # Corrección: Bloque except FieldParserError eliminado
    except Exception as e: # Este bloque capturará errores de parseo si dbfread los lanza como Exception
        db.session.rollback()
        logger.exception(f"Error inesperado durante la importación general: {e}")
        click.echo(f"Error inesperado durante la importación: {e}", err=True)

# --- Comandos Flask CLI ---

# Comando Flask CLI: flask import-dbf <ruta_al_dbf>
@click.command('import-dbf')
@click.argument('dbf_path', type=click.Path(exists=True, dir_okay=False, resolve_path=True))
@with_appcontext
def import_dbf_command(dbf_path):
    """Importa puntos desde un archivo DBF especificado, parseando campo GPS="lat,lon"."""
    import_points_from_dbf(dbf_path)

# Comando Flask CLI: flask init-db
@click.command('init-db')
@with_appcontext
def init_db_command():
    """Borra los datos existentes y crea nuevas tablas."""
    try:
        click.echo("Borrando tablas existentes (si existen)...")
        db.drop_all()
        click.echo("Creando nuevas tablas...")
        db.create_all()
        click.echo("Base de datos inicializada.")
        current_app.logger.info("Base de datos inicializada mediante comando init-db.")
    except Exception as e:
        db.session.rollback()
        click.echo(f"Error al inicializar la base de datos: {e}", err=True)
        current_app.logger.error(f"Error al inicializar la base de datos: {e}")