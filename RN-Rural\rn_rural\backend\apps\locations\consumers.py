# File: backend/apps/locations/consumers.py
# -----------------------------------------------

import json
from channels.generic.websocket import AsyncWebsocketConsumer

class LocationsConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # self.room_name = self.scope['url_route']['kwargs']['room_name']
        # self.room_group_name = f'chat_{self.room_name}'
        # await self.channel_layer.group_add(
        #     self.room_group_name,
        #     self.channel_name
        # )
        await self.accept()
        await self.send(text_data=json.dumps({
            'message': 'Connected to Locations WebSocket!'
        }))


    async def disconnect(self, close_code):
        # await self.channel_layer.group_discard(
        #     self.room_group_name,
        #     self.channel_name
        # )
        print(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # # Send message to room group
        # await self.channel_layer.group_send(
        #     self.room_group_name,
        #     {
        #         'type': 'chat_message', # Corresponde a un método `async def chat_message(self, event)`
        #         'message': message
        #     }
        # )
        print(f"Received message: {message}")
        await self.send(text_data=json.dumps({
            'response_message': f"Server received: {message}"
        }))

    # # Ejemplo de manejador de mensajes de grupo
    # async def chat_message(self, event):
    #     message = event['message']
    #     # Send message to WebSocket
    #     await self.send(text_data=json.dumps({
    #         'message': message
    #     }))
