<!-- prueba_websocket.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Prueba WebSocket</title>
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
</head>
<body>
    <h1>Prueba WebSocket</h1>
    <script>
        const socket = io('https://rncom.patagoniaservers.com.ar', {
            transports: ['websocket']
        });

        socket.on('connect', () => {
            console.log('Conectado al servidor');
            socket.send('Hola servidor desde el navegador!');
        });

        socket.on('response', (data) => {
            console.log('Respuesta del servidor:', data);
        });

        socket.on('disconnect', () => {
            console.log('Desconectado del servidor');
        });

        socket.on('connect_error', (error) => {
            console.error('Error al conectar:', error);
        });
    </script>
</body>
</html>
