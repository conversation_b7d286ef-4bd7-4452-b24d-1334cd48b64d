# File: backend/apps/incidents/forms.py
# -----------------------------------------------
from django import forms
from django.contrib.gis.forms import PointField # Para el campo de ubicación
from django.contrib.auth import get_user_model
from .models import Incidencia

User = get_user_model()

class IncidenciaForm(forms.ModelForm):
    # Si quieres que la ubicación sea un campo que el usuario pueda llenar
    # manualmente por ahora (o que se llene con JS después),
    # puedes definirlo aquí explícitamente.
    # Si omites `ubicacion_incidencia` aquí, y está en Meta.fields,
    # Django usará el widget por defecto para PointField, que es un campo de texto
    # esperando WKT (Well-Known Text) o GeoJSON, lo cual no es muy amigable.
    # Por ahora, para simplificar y probar el flujo, podrías omitirlo del formulario
    # y asignarlo en la vista, o hacerlo un campo oculto que llenarás con JS.

    # Para una entrada manual simple (no recomendado para producción sin JS para mapa):
    # latitud = forms.FloatField(label="Latitud", required=False) # Podrías usar estos y construir el Point en la vista
    # longitud = forms.FloatField(label="Longitud", required=False)

    # Para la subida de archivos (no te preocupes por esto en el primer paso de la funcionalidad)
    # audio_file = forms.FileField(required=False, label="Grabar Audio")
    # foto_file = forms.ImageField(required=False, label="Adjuntar Foto")

    class Meta:
        model = Incidencia
        fields = ['descripcion_texto'] # Empecemos solo con el texto
        # Más adelante podrías añadir: 'ubicacion_incidencia', y luego manejar audio/foto
        widgets = {
            'descripcion_texto': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Describe la incidencia...'}),
        }

    # (Más adelante, si usas campos latitud/longitud separados):
    # def save(self, commit=True):
    #     instance = super().save(commit=False)
    #     lat = self.cleaned_data.get('latitud')
    #     lon = self.cleaned_data.get('longitud')
    #     if lat is not None and lon is not None:
    #         instance.ubicacion_incidencia = Point(lon, lat, srid=4326)
    #     if commit:
    #         instance.save()
    #         self.save_m2m() # Si tuvieras ManyToManyFields
    #     return instance

class AsignarBrigadaForm(forms.Form):
    """
    Formulario para asignar una brigada a una incidencia.
    """
    brigada = forms.ModelChoiceField(
        queryset=User.objects.filter(role='BRIGADA', is_active=True),
        empty_label="Seleccionar brigada",
        required=True,
        label="Asignar a Brigada"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Personalizar el campo de selección de brigada
        self.fields['brigada'].widget.attrs.update({
            'class': 'form-select',
            'id': 'id_brigada'
        })

class CambiarEstadoIncidenciaForm(forms.Form):
    """
    Formulario para cambiar el estado de una incidencia.
    """
    ESTADOS_OPERADOR = [
        ('', 'Seleccionar nuevo estado'),
        ('ASIGNADA_OPERADOR', 'Asignada a Operador'),
        ('EN_PROCESO_OPERADOR', 'En Proceso por Operador'),
        ('DERIVADA_BRIGADA', 'Derivada a Brigada'),
        ('CERRADA_RESUELTA', 'Cerrada - Resuelta'),
        ('CERRADA_NO_RESUELTA', 'Cerrada - No Resuelta')
    ]

    nuevo_estado = forms.ChoiceField(
        choices=ESTADOS_OPERADOR,
        required=True,
        label="Cambiar Estado"
    )

    def __init__(self, *args, **kwargs):
        # Obtener el estado actual de la incidencia para filtrar las opciones
        estado_actual = kwargs.pop('estado_actual', None)
        super().__init__(*args, **kwargs)

        # Personalizar el campo de selección de estado
        self.fields['nuevo_estado'].widget.attrs.update({
            'class': 'form-select',
            'id': 'id_nuevo_estado'
        })

        # Filtrar las opciones según el estado actual
        if estado_actual:
            opciones_validas = []
            if estado_actual == 'NUEVA':
                opciones_validas = [
                    'ASIGNADA_OPERADOR'
                ]
            elif estado_actual == 'ASIGNADA_OPERADOR':
                opciones_validas = [
                    'EN_PROCESO_OPERADOR',
                    'CERRADA_RESUELTA',
                    'CERRADA_NO_RESUELTA'
                ]
            elif estado_actual == 'EN_PROCESO_OPERADOR':
                opciones_validas = [
                    'DERIVADA_BRIGADA',
                    'CERRADA_RESUELTA',
                    'CERRADA_NO_RESUELTA'
                ]

            # Filtrar las opciones
            if opciones_validas:
                self.fields['nuevo_estado'].choices = [
                    ('', 'Seleccionar nuevo estado')
                ] + [(estado, label) for estado, label in self.ESTADOS_OPERADOR[1:] if estado in opciones_validas]