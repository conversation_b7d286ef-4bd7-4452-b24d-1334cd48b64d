#!/usr/bin/env python3
# --- Archivo: force_schema_refresh.py ---
# <PERSON>ript para forzar a SQLAlchemy a refrescar el esquema

import os
import sys
import sqlite3

def clear_sqlalchemy_metadata():
    """Limpiar metadatos de SQLAlchemy."""
    print("🧹 Limpiando metadatos de SQLAlchemy...")
    
    try:
        # Eliminar archivos de cache de SQLAlchemy si existen
        cache_files = [
            'app.db-shm',
            'app.db-wal',
            '.sqlalchemy_cache'
        ]
        
        for cache_file in cache_files:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                print(f"  🗑️  Eliminado: {cache_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error limpiando metadatos: {e}")
        return False

def recreate_database_with_correct_schema():
    """Recrear la base de datos con el esquema correcto."""
    print("🔄 Recreando base de datos con esquema correcto...")
    
    try:
        # Hacer backup de datos
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Extraer datos de usuario
        cursor.execute("SELECT username, email, password_hash FROM user;")
        user_data = cursor.fetchall()
        print(f"💾 Datos de usuario extraídos: {len(user_data)} usuarios")
        
        conn.close()
        
        # Eliminar base de datos actual
        os.remove('app.db')
        print("🗑️  Base de datos anterior eliminada")
        
        # Crear nueva base de datos
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Crear tabla user con esquema correcto
        cursor.execute('''
            CREATE TABLE user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(64) NOT NULL UNIQUE,
                email VARCHAR(120) UNIQUE,
                password_hash VARCHAR(256) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'administrador',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("✅ Tabla user creada con esquema correcto")
        
        # Restaurar datos
        for user in user_data:
            cursor.execute('''
                INSERT INTO user (username, email, password_hash, role, is_active, created_at, updated_at)
                VALUES (?, ?, ?, 'administrador', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', user)
        
        print(f"✅ {len(user_data)} usuarios restaurados")
        
        # Crear otras tablas necesarias
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS point (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100),
                latitude FLOAT,
                longitude FLOAT,
                status VARCHAR(20) DEFAULT 'azul',
                city VARCHAR(100),
                source VARCHAR(100),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS image (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                point_id INTEGER,
                user_id INTEGER,
                annotations_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (point_id) REFERENCES point (id),
                FOREIGN KEY (user_id) REFERENCES user (id)
            );
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS camera (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                point_id INTEGER,
                type VARCHAR(20) DEFAULT 'otra',
                direction FLOAT,
                photo_filename VARCHAR(255),
                latitude FLOAT,
                longitude FLOAT,
                location_source VARCHAR(50),
                location_accuracy FLOAT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (point_id) REFERENCES point (id)
            );
        ''')
        
        # Crear tablas de permisos
        cursor.execute('''
            CREATE TABLE user_city_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                city VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                UNIQUE(user_id, city)
            );
        ''')
        
        cursor.execute('''
            CREATE TABLE user_source_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                source VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                UNIQUE(user_id, source)
            );
        ''')
        
        cursor.execute('''
            CREATE TABLE user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission_type VARCHAR(50) NOT NULL,
                permission_value BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                UNIQUE(user_id, permission_type)
            );
        ''')
        
        print("✅ Todas las tablas creadas")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error recreando base de datos: {e}")
        return False

def test_new_schema():
    """Probar que el nuevo esquema funcione."""
    print("🧪 Probando nuevo esquema...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Verificar esquema
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = ['id', 'username', 'email', 'password_hash', 'role', 'is_active', 'created_by', 'created_at', 'updated_at']
        missing_columns = set(required_columns) - set(column_names)
        
        if missing_columns:
            print(f"❌ Columnas faltantes: {missing_columns}")
            return False
        
        print("✅ Esquema correcto")
        
        # Verificar datos
        cursor.execute("SELECT username, role FROM user;")
        users = cursor.fetchall()
        
        print(f"👥 Usuarios: {len(users)}")
        for user in users:
            print(f"  - {user[0]} ({user[1]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error probando esquema: {e}")
        return False

def test_sqlalchemy_with_new_schema():
    """Probar SQLAlchemy con el nuevo esquema."""
    print("🧪 Probando SQLAlchemy con nuevo esquema...")
    
    try:
        # Limpiar módulos importados
        modules_to_remove = []
        for module_name in sys.modules:
            if module_name.startswith('app'):
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            del sys.modules[module_name]
        
        # Importar de nuevo
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            # Forzar recreación de metadatos
            db.metadata.clear()
            db.metadata.reflect(bind=db.engine)
            
            # Probar consulta
            users = User.query.all()
            print(f"✅ SQLAlchemy funciona: {len(users)} usuarios")
            
            for user in users:
                print(f"  👤 {user.username} ({user.role}) - Activo: {user.is_active}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error con SQLAlchemy: {e}")
        return False

def main():
    """Función principal."""
    print("🔄 Forzador de Refresh de Esquema")
    print("=" * 40)
    
    # Limpiar metadatos
    clear_sqlalchemy_metadata()
    print()
    
    # Recrear base de datos
    if not recreate_database_with_correct_schema():
        print("❌ No se pudo recrear la base de datos")
        sys.exit(1)
    print()
    
    # Probar esquema
    if not test_new_schema():
        print("❌ El nuevo esquema no es correcto")
        sys.exit(1)
    print()
    
    # Probar SQLAlchemy
    if test_sqlalchemy_with_new_schema():
        print("\n🎉 ¡Esquema refrescado exitosamente!")
        print("\n🚀 Próximos pasos:")
        print("   1. Reiniciar app: systemctl restart relevamiento")
        print("   2. Probar login: python3 test_login.py")
        print("\n🔑 Credenciales:")
        print("   Usuario: admin")
        print("   Contraseña: isaias52")
    else:
        print("\n❌ SQLAlchemy aún tiene problemas")

if __name__ == "__main__":
    main()
