# websocket_client.py para pruebas
import socketio

# Crear una instancia del cliente Socket.IO
sio = socketio.AsyncClient()

@sio.event
async def connect():
    print("Conectado al servidor")

@sio.event
async def connect_error(data):
    print(f"Error al conectar: {data}")

@sio.event
async def disconnect():
    print("Desconectado del servidor")

@sio.on('response')
async def on_response(data):
    print(f"Respuesta del servidor: {data}")

async def main():
    try:
        # Conectar al servidor sin usar el argumento 'ssl_verify'
        await sio.connect('https://rncom.patagoniaservers.com.ar', transports=['websocket'])
        print("Conexión establecida")
        await sio.emit('message', 'Hola servidor!')
        # Espera indefinidamente para recibir eventos
        await sio.wait()
    except Exception as e:
        print(f"Error al conectar o durante la comunicación: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
