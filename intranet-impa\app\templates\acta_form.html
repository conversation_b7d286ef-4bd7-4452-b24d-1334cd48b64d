<!-- /app/templates/acta_form.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}
{% block title %}Generar Acta de Miembros{% endblock %}

{% block content %}
<div class="container my-4">
  <h1>Generar Acta de Miembros</h1>
  <form method="POST" action="">
    {{ form.hidden_tag() }}
    <div class="form-group">
      {{ render_field(form.acta_number, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.city, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.province, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.day, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.month, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.year, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.time, class="form-control") }}
    </div>
    <div class="form-group">
        <label for="participants">Seleccione los participantes</label>
        <select id="participants" name="participants" class="form-control select2" multiple="multiple">
            {% for value, label in form.participants.choices %}
                <option value="{{ value }}" {% if value in form.participants.data %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
        </select>
        {% if form.participants.errors %}
            <ul class="errors">
                {% for error in form.participants.errors %}
                    <li>{{ error }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
    <div class="form-group">
      {{ render_field(form.act_description, class="form-control", rows=3) }}
    </div>
    <button type="submit" class="btn btn-primary">{{ form.submit.label }}</button>
    <a href="{{ url_for('routes.dashboard') }}" class="btn btn-secondary">Volver</a>
  </form>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}  <!--  Importante para heredar los scripts de base.html -->
<script>
    $(document).ready(function() {
        $('.select2').select2({
            placeholder: 'Seleccione participantes', //Texto de muestra
            allowClear: true, //Boton para limpiar
            width: '100%', //Ancho del select
        });
    });
</script>
{% endblock %}