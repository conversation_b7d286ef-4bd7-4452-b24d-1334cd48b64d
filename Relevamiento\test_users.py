#!/usr/bin/env python3
"""
Script de prueba para verificar que la gestión de usuarios funciona correctamente.
"""

import sys
import os

# Agregar el directorio actual al path para importar la app
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User

def test_users():
    """Prueba básica de la funcionalidad de usuarios."""
    app = create_app()
    
    with app.app_context():
        try:
            # Verificar que la base de datos existe
            users = User.query.all()
            print(f"✅ Base de datos conectada. Usuarios encontrados: {len(users)}")
            
            # Mostrar usuarios existentes
            for user in users:
                print(f"  - {user.username} ({user.role}) - {'Activo' if user.is_active else 'Inactivo'}")
            
            # Verificar que el usuario admin existe
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print(f"✅ Usuario admin encontrado: {admin.username} - {admin.role}")
                
                # Verificar métodos de permisos
                if hasattr(admin, 'can_manage_users'):
                    can_manage = admin.can_manage_users()
                    print(f"✅ Método can_manage_users funciona: {can_manage}")
                else:
                    print("❌ Método can_manage_users no encontrado")
            else:
                print("❌ Usuario admin no encontrado")
            
            print("✅ Prueba de usuarios completada exitosamente")
            return True
            
        except Exception as e:
            print(f"❌ Error en prueba de usuarios: {e}")
            return False

if __name__ == '__main__':
    success = test_users()
    sys.exit(0 if success else 1)
