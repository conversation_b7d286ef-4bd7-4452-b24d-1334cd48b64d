"""Add regional units and police access to users

Revision ID: 2976d5302661
Revises: 
Create Date: 2024-07-03 19:11:28.894373

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2976d5302661'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('regional_units', sa.String(length=500), nullable=True))
        batch_op.add_column(sa.Column('has_police_access', sa.<PERSON>(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('has_police_access')
        batch_op.drop_column('regional_units')

    # ### end Alembic commands ###
