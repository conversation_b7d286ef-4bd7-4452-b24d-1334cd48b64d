from http.server import HTTPServer, SimpleHTTPRequestHandler
import ssl

# Crear una instancia de SSLContext
context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
context.load_cert_chain(certfile='localhost.pem', keyfile='localhost-key.pem')

# Configurar el servidor HTTP para usar SSL
httpd = HTTPServer(('localhost', 4443), SimpleHTTPRequestHandler)
httpd.socket = context.wrap_socket(httpd.socket, server_side=True)

print("Serving on https://localhost:4443")
httpd.serve_forever()
