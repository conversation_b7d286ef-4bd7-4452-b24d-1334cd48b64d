{% extends "base_layout_operator.html" %}

{% block title %}Detalles del Viaje - Panel de Operador{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .trip-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }
    .trip-status {
        font-size: 1.2rem;
        font-weight: bold;
        padding: 8px 15px;
        border-radius: 5px;
        display: inline-block;
        margin-bottom: 15px;
    }
    .trip-status.solicitado { background-color: #dc3545; color: white; }
    .trip-status.aceptado { background-color: #ffc107; color: black; }
    .trip-status.en_camino_pasajero { background-color: #fd7e14; color: white; }
    .trip-status.en_destino_pasajero { background-color: #0dcaf0; color: black; }
    .trip-status.en_viaje { background-color: #0d6efd; color: white; }
    .trip-status.completado { background-color: #198754; color: white; }
    .trip-status.cancelado_pasajero,
    .trip-status.cancelado_conductor { background-color: #6c757d; color: white; }

    .timeline {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #dee2e6;
    }
    .timeline-item {
        position: relative;
        padding-bottom: 20px;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -30px;
        top: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #0d6efd;
        border: 3px solid white;
    }
    .timeline-item.completed::before {
        background-color: #198754;
    }
    .timeline-item.pending::before {
        background-color: #ffc107;
    }
    .timeline-item.cancelled::before {
        background-color: #dc3545;
    }
    .timeline-date {
        font-size: 0.8rem;
        color: #6c757d;
    }
    .timeline-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .driver-selection {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('operator_dashboard_route') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('operator_trips_route') }}">Viajes</a></li>
                <li class="breadcrumb-item active" aria-current="page">Viaje #{{ trip_id }}</li>
            </ol>
        </nav>
        <h1 class="display-5 mb-4">Detalles del Viaje #{{ trip_id }}</h1>
    </div>
</div>

{% if not trip %}
<div class="alert alert-warning" role="alert">
    <h4 class="alert-heading">Viaje no encontrado</h4>
    <p>No se encontró información para el viaje #{{ trip_id }}.</p>
    <hr>
    <p class="mb-0">Vuelve a la lista de viajes e intenta con otro ID.</p>
</div>
{% else %}

<div class="row">
    <!-- Mapa del Viaje -->
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mapa del Viaje</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
            </div>
        </div>
    </div>

    <!-- Información del Viaje -->
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Información del Viaje</h5>
            </div>
            <div class="card-body">
                <div class="trip-info">
                    <div class="trip-status {{ trip.status }}">
                        {{ trip.status|upper }}
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Pasajero</h6>
                            <p>{{ trip.passenger_name|default('No asignado') }}</p>
                            <p><small>Tel: {{ trip.passenger_phone|default('No disponible') }}</small></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Conductor</h6>
                            <p>{{ trip.driver_name|default('No asignado') }}</p>
                            <p><small>Tel: {{ trip.driver_phone|default('No disponible') }}</small></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Origen</h6>
                            <p>{{ trip.origin_address|default('No especificado') }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Destino</h6>
                            <p>{{ trip.destination_address|default('No especificado') }}</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <h6>Tarifa Estimada</h6>
                            <p>${{ trip.estimated_fare|default('0') }}</p>
                        </div>
                        <div class="col-md-4">
                            <h6>Tarifa Final</h6>
                            <p>${{ trip.actual_fare|default('Pendiente') }}</p>
                        </div>
                        <div class="col-md-4">
                            <h6>Distancia</h6>
                            <p>{{ (trip.estimated_distance_meters|default(0) / 1000)|round(1) }} km</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Solicitado</h6>
                            <p>{{ trip.requested_at|default('') }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Completado</h6>
                            <p>{{ trip.completed_at|default('Pendiente') }}</p>
                        </div>
                    </div>

                    <!-- Línea de tiempo del viaje -->
                    <h6 class="mt-4 mb-3">Línea de Tiempo</h6>
                    <div class="timeline">
                        <div class="timeline-item completed">
                            <div class="timeline-date">{{ trip.requested_at|default('') }}</div>
                            <div class="timeline-title">Viaje Solicitado</div>
                            <div class="timeline-content">El pasajero solicitó un viaje.</div>
                        </div>

                        {% if trip.accepted_at %}
                        <div class="timeline-item completed">
                            <div class="timeline-date">{{ trip.accepted_at }}</div>
                            <div class="timeline-title">Viaje Aceptado</div>
                            <div class="timeline-content">El conductor aceptó el viaje.</div>
                        </div>
                        {% else %}
                        <div class="timeline-item pending">
                            <div class="timeline-date">Pendiente</div>
                            <div class="timeline-title">Viaje Aceptado</div>
                            <div class="timeline-content">Esperando que un conductor acepte el viaje.</div>
                        </div>
                        {% endif %}

                        {% if trip.started_at %}
                        <div class="timeline-item completed">
                            <div class="timeline-date">{{ trip.started_at }}</div>
                            <div class="timeline-title">Viaje Iniciado</div>
                            <div class="timeline-content">El pasajero subió al vehículo.</div>
                        </div>
                        {% elif trip.accepted_at %}
                        <div class="timeline-item pending">
                            <div class="timeline-date">Pendiente</div>
                            <div class="timeline-title">Viaje Iniciado</div>
                            <div class="timeline-content">El conductor está en camino.</div>
                        </div>
                        {% endif %}

                        {% if trip.completed_at %}
                        <div class="timeline-item completed">
                            <div class="timeline-date">{{ trip.completed_at }}</div>
                            <div class="timeline-title">Viaje Completado</div>
                            <div class="timeline-content">El pasajero llegó a su destino.</div>
                        </div>
                        {% elif trip.cancelled_at %}
                        <div class="timeline-item cancelled">
                            <div class="timeline-date">{{ trip.cancelled_at }}</div>
                            <div class="timeline-title">Viaje Cancelado</div>
                            <div class="timeline-content">
                                {% if trip.status == 'cancelado_pasajero' %}
                                El pasajero canceló el viaje.
                                {% else %}
                                El conductor canceló el viaje.
                                {% endif %}
                            </div>
                        </div>
                        {% elif trip.started_at %}
                        <div class="timeline-item pending">
                            <div class="timeline-date">Pendiente</div>
                            <div class="timeline-title">Viaje Completado</div>
                            <div class="timeline-content">El viaje está en curso.</div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Asignación de Conductor (si no hay conductor asignado) -->
                {% if not trip.driver_id and trip.status == 'solicitado' %}
                <div class="driver-selection">
                    <h6 class="mb-3">Asignar Conductor</h6>
                    <form id="assign-driver-form">
                        <div class="mb-3">
                            <label for="driver-select" class="form-label">Seleccionar Conductor</label>
                            <select id="driver-select" class="form-select">
                                <option value="">Seleccione un conductor...</option>
                                {% for driver in available_drivers|default([]) %}
                                <option value="{{ driver.id }}">{{ driver.name }} ({{ driver.vehicle }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="assignDriver()">Asignar Conductor</button>
                    </form>
                </div>
                {% endif %}

                <!-- Acciones del Viaje -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('operator_trips_route') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Volver a Viajes
                    </a>

                    <div>
                        {% if trip.status == 'solicitado' %}
                        <button class="btn btn-danger" onclick="cancelTrip()">
                            <i class="bi bi-x-circle"></i> Cancelar Viaje
                        </button>
                        {% elif trip.status == 'aceptado' or trip.status == 'en_camino_pasajero' or trip.status == 'en_destino_pasajero' or trip.status == 'en_viaje' %}
                        <button class="btn btn-warning" onclick="contactDriver()">
                            <i class="bi bi-telephone"></i> Contactar Conductor
                        </button>
                        <button class="btn btn-info" onclick="contactPassenger()">
                            <i class="bi bi-telephone"></i> Contactar Pasajero
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 12); // Buenos Aires como ejemplo

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Iconos personalizados
    const originIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    const destinationIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon-2x.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    const driverIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon-red.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    // Cargar datos del viaje en el mapa
    function loadTripMap() {
        // Obtener coordenadas del viaje
        const originLat = {{ trip.origin_latitude|default('null') }};
        const originLng = {{ trip.origin_longitude|default('null') }};
        const destLat = {{ trip.destination_latitude|default('null') }};
        const destLng = {{ trip.destination_longitude|default('null') }};
        const driverLat = {{ trip.driver_latitude|default('null') }};
        const driverLng = {{ trip.driver_longitude|default('null') }};

        // Añadir marcadores si hay coordenadas
        if (originLat && originLng) {
            const originMarker = L.marker([originLat, originLng], {icon: originIcon})
                .addTo(map)
                .bindPopup("Origen: {{ trip.origin_address|default('No especificado') }}");
        }

        if (destLat && destLng) {
            const destMarker = L.marker([destLat, destLng], {icon: destinationIcon})
                .addTo(map)
                .bindPopup("Destino: {{ trip.destination_address|default('No especificado') }}");
        }

        if (driverLat && driverLng) {
            const driverMarker = L.marker([driverLat, driverLng], {icon: driverIcon})
                .addTo(map)
                .bindPopup("Conductor: {{ trip.driver_name|default('No asignado') }}");
        }

        // Dibujar ruta si hay origen y destino
        if (originLat && originLng && destLat && destLng) {
            const routeLine = L.polyline([
                [originLat, originLng],
                [destLat, destLng]
            ], {
                color: getColorByStatus('{{ trip.status }}'),
                weight: 3,
                opacity: 0.7,
                dashArray: '5, 10'
            }).addTo(map);

            // Ajustar vista para mostrar toda la ruta
            map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });
        } else if (originLat && originLng) {
            map.setView([originLat, originLng], 15);
        } else if (destLat && destLng) {
            map.setView([destLat, destLng], 15);
        }
    }

    // Función para obtener color según estado
    function getColorByStatus(status) {
        switch(status) {
            case 'solicitado': return '#dc3545'; // Rojo
            case 'aceptado': return '#ffc107'; // Amarillo
            case 'en_camino_pasajero': return '#fd7e14'; // Naranja
            case 'en_destino_pasajero': return '#0dcaf0'; // Cyan
            case 'en_viaje': return '#0d6efd'; // Azul
            case 'completado': return '#198754'; // Verde
            case 'cancelado_pasajero':
            case 'cancelado_conductor': return '#6c757d'; // Gris
            default: return '#6c757d'; // Gris por defecto
        }
    }

    // Función para asignar conductor
    async function assignDriver() {
        const driverId = document.getElementById('driver-select').value;
        if (!driverId) {
            alert('Por favor seleccione un conductor');
            return;
        }

        try {
            const response = await fetchWithAuth(`/api/v1/trips/{{ trip.id }}/assign`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ driver_id: driverId })
            });

            if (response.ok) {
                alert('Conductor asignado correctamente');
                window.location.reload();
            } else {
                const error = await response.json();
                alert('Error al asignar conductor: ' + (error.detail || 'Error desconocido'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error de red al asignar conductor');
        }
    }

    // Función para cancelar viaje
    async function cancelTrip() {
        if (confirm('¿Está seguro de que desea cancelar este viaje?')) {
            try {
                const response = await fetchWithAuth(`/api/v1/trips/{{ trip.id }}/cancel`, {
                    method: 'POST'
                });

                if (response.ok) {
                    alert('Viaje cancelado correctamente');
                    window.location.reload();
                } else {
                    const error = await response.json();
                    alert('Error al cancelar viaje: ' + (error.detail || 'Error desconocido'));
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error de red al cancelar viaje');
            }
        }
    }

    // Función para contactar al conductor
    function contactDriver() {
        const driverPhone = '{{ trip.driver_phone|default("") }}';
        if (driverPhone) {
            alert(`Llamando al conductor: ${driverPhone}`);
            // Aquí se podría implementar la funcionalidad real de llamada
        } else {
            alert('No hay número de teléfono disponible para el conductor');
        }
    }

    // Función para contactar al pasajero
    function contactPassenger() {
        const passengerPhone = '{{ trip.passenger_phone|default("") }}';
        if (passengerPhone) {
            alert(`Llamando al pasajero: ${passengerPhone}`);
            // Aquí se podría implementar la funcionalidad real de llamada
        } else {
            alert('No hay número de teléfono disponible para el pasajero');
        }
    }

    // Cargar mapa al iniciar
    document.addEventListener('DOMContentLoaded', function() {
        loadTripMap();
    });
</script>
{% endblock %}
