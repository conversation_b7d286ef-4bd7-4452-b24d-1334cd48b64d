# File: backend/apps/core/urls.py
# -----------------------------------------------

from django.urls import path
from . import views
from . import views_map
from . import views_manage # Asegúrate que este import esté si usas UserListView etc. desde aquí

urlpatterns = [
    # URLs de autenticación y dashboards principales
    path('', views.custom_login_view, name='login'),            # <--- CAMBIADO AQUÍ
    path('logout/', views.custom_logout_view, name='logout'),
    path('registrarse/', views.register_view, name='register'),
    path('panel-operador/', views.dashboard_operador_view, name='dashboard_operador'),
    path('mi-portal/', views.dashboard_ciudadano_view, name='dashboard_ciudadano'),
    path('panel-brigada/', views.dashboard_brigada_view, name='dashboard_brigada'),
    path('gestionar-brigadas/', views.gestionar_brigadas_view, name='gestionar_brigadas'),
    path('estadisticas/', views.estadisticas_view, name='estadisticas'),

    # URLs para el CRUD de usuarios (panel de administración personalizado)
    path('admin-panel/', views_manage.UserListView.as_view(), name='user_list'),
    path('admin-panel/add/', views_manage.UserCreateView.as_view(), name='user_add'),
    path('admin-panel/<int:pk>/edit/', views_manage.UserUpdateView.as_view(), name='user_edit'),
    path('admin-panel/<int:pk>/delete/', views_manage.UserDeleteView.as_view(), name='user_delete'),

    # URLs para la API de datos del mapa
    path('api/map/operador/', views_map.operador_map_data, name='map_operador'),
    path('api/map/brigada/',  views_map.brigada_map_data,  name='map_brigada'),
    path('api/map/brigada/actualizar-estado/', views_map.actualizar_estado_incidencia, name='actualizar_estado_incidencia'),
    path('api/map/calcular-ruta/', views_map.calcular_ruta, name='calcular_ruta'),
]