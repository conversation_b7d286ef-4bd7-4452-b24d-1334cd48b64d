/* /app/static/css/style.css */

/* ---  <PERSON><PERSON><PERSON> de Colores (Tonos Tierra)  --- */
:root {
    --primary: #e8e0d1;       /* <PERSON><PERSON> claro - Fondo principal, cálido */
    --primary-rgb: 232, 224, 209;
    --primary-hover: #d6cebf;  /* Beige un poco más oscuro (hover) */
    --secondary: #6c757d;      /* <PERSON><PERSON> (Bootstrap) - Para elementos secundarios*/
    --light: #f8f9fa;       /* <PERSON><PERSON> claro (Bootstrap) - Lo usamos para fondos de tarjetas */
    --dark: #444;        /* <PERSON><PERSON> muy oscuro (casi negro) - Para texto */
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
    --white: #fff;
    --accent: #20B2AA;       /* Turquesa de tu logo - ¡USAR COMO ACENTO! */
    --link-color: #0056b3;   /* Azul oscuro para enlaces (contraste) */
    --link-hover-color: #003d7a;
}

/* Navbar - Usamos el color de acento para que el logo se integre */
.navbar {
    background-color: var(--accent) !important;  /* Fondo turquesa */
}

.navbar-brand,
.navbar-nav .nav-link {
    color: var(--white-color) !important;   /* Texto blanco */
}
.navbar-nav .nav-link:hover {
  color: #ddd !important; /*Color al pasar el mouse*/
}

/* ---  Botones  --- */
.btn-primary {
    background-color: var(--accent) !important; /* Botones turquesa */
    border-color: var(--accent) !important;
    color: var(--white) !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
  color: var(--white) !important;
}

/* ---  Fondo del Contenedor Principal  --- */
.container {
    background-color: var(--primary); /* Usamos el beige claro */
    /* ... resto de tus estilos del container ... */
}
/* ---  Tipografía  --- */
body {
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--dark);          /* Texto oscuro */
    background-color: var(--light); /* Fondo claro */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary);      /* Encabezados en marrón principal */
}

/* ---  Contenedor Principal  --- */
.container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

/* ---  Navbar  --- */
.navbar {
    background-color: var(--primary) !important;  /* Fondo marrón */
    margin-bottom: 3rem;
    padding: 0.8rem 1rem;
    border-radius: 0;
}

.navbar-brand,
.navbar-nav .nav-link {
    color: var(--white-color) !important;  /* Texto blanco */
    font-weight: 500;
}

.navbar-nav .nav-link:hover {
    color: #ddd !important; /* Color al pasar el mouse (un gris claro)*/
    text-decoration: underline;
}

.navbar-toggler {
    border-color: rgba(255, 255, 255, 0.5) !important;
}
.navbar-toggler-icon {
    /*Usar el color blanco, para que se vea*/
    background-color: rgba(255, 255, 255, 0.5) !important;
}

/* ---  Botones  --- */
/* Usamos las variables de Bootstrap directamente, y las personalizamos aquí */
.btn-primary {
    background-color: var(--primary) !important;
    border-color: var(--primary) !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--primary-hover) !important; /* Un tono más oscuro al interactuar */
    border-color: var(--primary-hover) !important;
}

/* ---  Tablas  --- */
.table {
    background-color: var(--white-color);
    border-collapse: collapse;
}

.table th {
    background-color: var(--light);
    border-bottom: 2px solid var(--primary);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(139, 69, 19, 0.05);  /* Un marrón muy claro, sutil */
}

/* ---  Tarjetas (Cards) --- */
.card {
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    border-radius: 5px;
}

.card-header {
    font-weight: 600;
    padding: 0.75rem 1.25rem;
    /*  El color de fondo se define con clases de Bootstrap (bg-*) en el HTML */
}

.card-body {
    padding: 1.25rem;
}

/* ---  Alertas (Flashes)  --- */
.alert {
    border-radius: 5px;
    padding: 0.8rem 1.2rem;
    margin-bottom: 1rem;
}

/* ---  Formularios  --- */
.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 5px;
    padding: 0.6rem 1rem;
    border: 1px solid #ccc;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25); /* Sombra marrón al hacer foco */
}

.form-check-input:checked + .form-check-label {
    color: var(--primary);
    font-weight: bold;
}

.form-check {
    margin-bottom: 0.5rem;
}
label {
    font-weight: 600;
}

/* ---  Enlaces --- */
a { /*Este a se aplica a todos los enlaces, para que no haya problemas con otros colores*/
    color: var(--link-color);
    text-decoration: none;
}
a:hover {
    color: var(--link-hover-color);
    text-decoration: underline;
}
/* ---  Select 2 --- */
.select2-container--default .select2-selection--multiple{
	border: 1px solid #ced4da;
	border-radius: 0.25rem
}
.select2-container--default.select2-container--focus .select2-selection--multiple{
	border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}