{# File: backend/apps/core/templates/core/dashboard_operador.html #}
{% extends "core/base.html" %}

{% block title %}Panel Operador - RN-Rural{% endblock %}

{% block extra_head %} {# Bloque para añadir CSS/JS específicos en el <head> #}
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <style>
    #mapOperador {
      height: 65vh; /* Ajusta la altura como necesites */
      width: 100%;
      margin-bottom: 20px;
      border: 1px solid #ddd; /* Opcional: para ver mejor el contenedor del mapa */
    }
    .dashboard-actions {
      margin-bottom: 20px;
    }
    .content-map-full-width { /* Podrías necesitar una clase así si el layout general es muy restrictivo */
        /* width: 100%; */ /* O un ancho específico */
    }
    .card-dashboard {
      margin-bottom: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }
    .card-dashboard:hover {
      transform: translateY(-5px);
    }
    .card-header-custom {
      border-radius: 10px 10px 0 0;
      padding: 15px;
      font-weight: bold;
    }
    .bg-nueva { background-color: #0dcaf0; color: white; }
    .bg-proceso { background-color: #ffc107; color: black; }
    .bg-derivada { background-color: #fd7e14; color: white; }
    .bg-resuelta { background-color: #198754; color: white; }
    .bg-cerrada { background-color: #6c757d; color: white; }
    .incidencia-count {
      font-size: 2rem;
      font-weight: bold;
      text-align: center;
      padding: 15px 0;
    }
    .map-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;
      background: white;
      padding: 10px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .map-legend {
      position: absolute;
      bottom: 30px;
      right: 10px;
      z-index: 1000;
      background: white;
      padding: 10px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      font-size: 0.8rem;
    }
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }
    .legend-color {
      width: 20px;
      height: 20px;
      margin-right: 5px;
      border-radius: 50%;
    }
    .legend-nueva { background-color: #0dcaf0; }
    .legend-proceso { background-color: #ffc107; }
    .legend-derivada { background-color: #fd7e14; }
    .legend-brigada { background-color: #ff7800; }
  </style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <h2 class="mb-3">Panel de Operador: {{ user.username }}</h2>
            <hr>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-9 col-md-8 order-md-1">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <h4 class="mb-0">Mapa de Situación</h4>
                </div>
                <div class="card-body p-0 position-relative">
                    <div id="mapOperador" class="content-map-full-width"></div>

                    <div class="map-controls">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="mostrarIncidencias" checked>
                            <label class="form-check-label" for="mostrarIncidencias">
                                Mostrar Incidencias
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="mostrarBrigadas" checked>
                            <label class="form-check-label" for="mostrarBrigadas">
                                Mostrar Brigadas
                            </label>
                        </div>
                    </div>

                    <div class="map-legend">
                        <h6 class="mb-2">Leyenda</h6>
                        <div class="legend-item">
                            <div class="legend-color legend-nueva"></div>
                            <span>Incidencia Nueva</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-proceso"></div>
                            <span>Incidencia En Proceso</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-derivada"></div>
                            <span>Incidencia Derivada</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-brigada"></div>
                            <span>Brigada</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-4 order-md-2">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <h4 class="mb-0">Acciones Rápidas</h4>
                </div>
                <div class="card-body">
                    <div class="list-group dashboard-actions">
                        <a href="{% url 'incidents:lista_incidencias_operador' %}" class="list-group-item list-group-item-action list-group-item-primary">
                            <i class="fas fa-list-ul"></i> Ver Todas las Incidencias
                        </a>
                        <a href="{% url 'incidents:buscar_incidencia' %}" class="list-group-item list-group-item-action list-group-item-success">
                            <i class="fas fa-search"></i> Buscar Incidencia
                        </a>
                        <a href="{% url 'gestionar_brigadas' %}" class="list-group-item list-group-item-action list-group-item-warning">
                            <i class="fas fa-users"></i> Gestionar Brigadas
                        </a>
                        <a href="{% url 'estadisticas' %}" class="list-group-item list-group-item-action list-group-item-info">
                            <i class="fas fa-chart-bar"></i> Ver Estadísticas
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-6">
                    <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=nuevas" class="text-decoration-none">
                        <div class="card card-dashboard">
                            <div class="card-header card-header-custom bg-nueva">
                                Nuevas
                            </div>
                            <div class="card-body p-0">
                                <div class="incidencia-count" id="count-nuevas">{{ count_nuevas|default:"0" }}</div>
                            </div>
                            <div class="card-footer text-center bg-light">
                                <small class="text-muted">Ver todas <i class="fas fa-arrow-right"></i></small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6">
                    <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=proceso" class="text-decoration-none">
                        <div class="card card-dashboard">
                            <div class="card-header card-header-custom bg-proceso">
                                En Proceso
                            </div>
                            <div class="card-body p-0">
                                <div class="incidencia-count" id="count-proceso">{{ count_proceso|default:"0" }}</div>
                            </div>
                            <div class="card-footer text-center bg-light">
                                <small class="text-muted">Ver todas <i class="fas fa-arrow-right"></i></small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6">
                    <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=derivadas" class="text-decoration-none">
                        <div class="card card-dashboard">
                            <div class="card-header card-header-custom bg-derivada">
                                Derivadas
                            </div>
                            <div class="card-body p-0">
                                <div class="incidencia-count" id="count-derivadas">{{ count_derivadas|default:"0" }}</div>
                            </div>
                            <div class="card-footer text-center bg-light">
                                <small class="text-muted">Ver todas <i class="fas fa-arrow-right"></i></small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6">
                    <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=resueltas" class="text-decoration-none">
                        <div class="card card-dashboard">
                            <div class="card-header card-header-custom bg-resuelta">
                                Resueltas
                            </div>
                            <div class="card-body p-0">
                                <div class="incidencia-count" id="count-resueltas">{{ count_resueltas|default:"0" }}</div>
                            </div>
                            <div class="card-footer text-center bg-light">
                                <small class="text-muted">Ver todas <i class="fas fa-arrow-right"></i></small>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <a href="{% url 'gestionar_brigadas' %}" class="text-decoration-none" id="link-brigadas-activas">
                <div class="card card-dashboard mt-3">
                    <div class="card-header card-header-custom bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">Brigadas Activas</h4>
                            <span class="badge bg-light text-dark rounded-pill" id="count-brigadas">0</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="brigadas-activas-list">
                            <div class="text-center py-3" id="no-brigadas">
                                <p class="text-muted">No hay brigadas activas en este momento.</p>
                            </div>
                            <ul class="list-group" id="brigadas-list">
                                <!-- Las brigadas se cargarán dinámicamente aquí -->
                            </ul>
                        </div>
                    </div>
                    <div class="card-footer text-center bg-light">
                        <small class="text-muted">Ver todas las brigadas <i class="fas fa-arrow-right"></i></small>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %} {# Bloque para añadir JS específicos al final del <body> #}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // === INICIO DE LA CORRECCIÓN PARA ICONOS DE LEAFLET ===
        try {
            delete L.Icon.Default.prototype._getIconUrl;
        } catch (e) {
            console.warn("No se pudo borrar L.Icon.Default.prototype._getIconUrl, puede que no sea necesario.");
        }

        L.Icon.Default.mergeOptions({
            iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
            iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
            shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
        });
        // === FIN DE LA CORRECCIÓN PARA ICONOS DE LEAFLET ===

        // Iconos personalizados para incidencias según su estado
        const iconoNueva = L.divIcon({
            className: 'custom-div-icon',
            html: `<div style="background-color:#0dcaf0; width:20px; height:20px; border-radius:50%; border:2px solid white;"></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        });

        const iconoProceso = L.divIcon({
            className: 'custom-div-icon',
            html: `<div style="background-color:#ffc107; width:20px; height:20px; border-radius:50%; border:2px solid white;"></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        });

        const iconoDerivada = L.divIcon({
            className: 'custom-div-icon',
            html: `<div style="background-color:#fd7e14; width:20px; height:20px; border-radius:50%; border:2px solid white;"></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        });

        const mapElement = document.getElementById('mapOperador');
        if (!mapElement) {
            console.error("Elemento del mapa #mapOperador no encontrado.");
            return;
        }

        const map = L.map(mapElement).setView([-40.8, -63.0], 6); // Río Negro aprox

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 18,
            attribution: 'Map data © <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        let incidenciasLayer = L.layerGroup().addTo(map);
        let brigadasLayer = L.layerGroup().addTo(map);

        // Controles para mostrar/ocultar capas
        const mostrarIncidencias = document.getElementById('mostrarIncidencias');
        const mostrarBrigadas = document.getElementById('mostrarBrigadas');

        mostrarIncidencias.addEventListener('change', function() {
            if (this.checked) {
                map.addLayer(incidenciasLayer);
            } else {
                map.removeLayer(incidenciasLayer);
            }
        });

        mostrarBrigadas.addEventListener('change', function() {
            if (this.checked) {
                map.addLayer(brigadasLayer);
            } else {
                map.removeLayer(brigadasLayer);
            }
        });

        function actualizarMarcadores() {
            fetch("{% url 'map_operador' %}")
                .then(response => {
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error('Respuesta de red no fue ok: ' + response.status + " " + response.statusText + ". Cuerpo: " + text);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    incidenciasLayer.clearLayers();
                    brigadasLayer.clearLayers();

                    // Actualizar contadores
                    let countNuevas = 0;
                    let countProceso = 0;
                    let countDerivadas = 0;
                    let countResueltas = 0;

                    // Limpiar lista de brigadas
                    const brigadasList = document.getElementById('brigadas-list');
                    const noBrigadas = document.getElementById('no-brigadas');
                    brigadasList.innerHTML = '';

                    if (data.incidencias && Array.isArray(data.incidencias)) {
                        data.incidencias.forEach(i => {
                            if (typeof i.lat === 'number' && typeof i.lng === 'number') {
                                // Seleccionar icono según estado
                                let icono;
                                if (i.estado === 'NUEVA') {
                                    icono = iconoNueva;
                                    countNuevas++;
                                } else if (i.estado === 'ASIGNADA_OPERADOR' || i.estado === 'EN_PROCESO_OPERADOR') {
                                    icono = iconoProceso;
                                    countProceso++;
                                } else if (i.estado === 'DERIVADA_BRIGADA') {
                                    icono = iconoDerivada;
                                    countDerivadas++;
                                } else if (i.estado.startsWith('CERRADA_')) {
                                    countResueltas++;
                                    return; // No mostrar incidencias cerradas en el mapa
                                } else {
                                    icono = iconoNueva; // Icono por defecto
                                }

                                const marker = L.marker([i.lat, i.lng], { icon: icono })
                                    .bindPopup(`
                                        <div class="text-center">
                                            <h5>Incidencia #${i.id}</h5>
                                            <span class="badge bg-info">${i.estado || 'N/A'}</span>
                                            <hr>
                                            <a href="/incidencias/operador/detalle/${i.id}/" class="btn btn-sm btn-primary">Ver Detalle</a>
                                        </div>
                                    `)
                                    .addTo(incidenciasLayer);
                            }
                        });
                    }

                    if (data.brigadas && Array.isArray(data.brigadas)) {
                        if (data.brigadas.length > 0) {
                            noBrigadas.style.display = 'none';
                            brigadasList.style.display = 'block';
                        } else {
                            noBrigadas.style.display = 'block';
                            brigadasList.style.display = 'none';
                        }

                        data.brigadas.forEach(b => {
                           if (typeof b.lat === 'number' && typeof b.lng === 'number') {
                                // Añadir marcador al mapa
                                L.circleMarker([b.lat, b.lng], {
                                    radius: 8,
                                    fillColor: "#ff7800",
                                    color: "#000",
                                    weight: 1,
                                    opacity: 1,
                                    fillOpacity: 0.8
                                })
                                .bindPopup(`
                                    <div class="text-center">
                                        <h5>Brigada: ${b.nombre || 'N/A'}</h5>
                                        <small>Actualizado: ${b.ts ? new Date(b.ts).toLocaleString() : 'N/A'}</small>
                                    </div>
                                `)
                                .addTo(brigadasLayer);

                                // Añadir a la lista de brigadas activas
                                const li = document.createElement('li');
                                li.className = 'list-group-item d-flex justify-content-between align-items-center';
                                li.innerHTML = `
                                    ${b.nombre || 'Brigada sin nombre'}
                                    <span class="badge bg-success rounded-pill">Activa</span>
                                `;
                                brigadasList.appendChild(li);
                            }
                        });
                    }

                    // Actualizar contadores en la UI
                    document.getElementById('count-nuevas').textContent = countNuevas;
                    document.getElementById('count-proceso').textContent = countProceso;
                    document.getElementById('count-derivadas').textContent = countDerivadas;
                    document.getElementById('count-resueltas').textContent = countResueltas;
                })
                .catch(error => {
                    console.error('Error al cargar o procesar datos del mapa:', error);
                });
        }

        actualizarMarcadores();
        setInterval(actualizarMarcadores, 15000);
    });
</script>
{% endblock %}