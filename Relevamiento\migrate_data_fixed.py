#!/usr/bin/env python3
# --- Archivo: migrate_data_fixed.py ---
# Script para migrar datos con creación de tablas

import os
import sqlite3
import shutil
from datetime import datetime

def create_new_database_structure():
    """Crear nueva estructura de base de datos."""
    print("🔧 Creando nueva estructura de base de datos...")
    
    current_path = "instance/app.db"
    
    # Eliminar base de datos actual
    if os.path.exists(current_path):
        os.remove(current_path)
    
    conn = sqlite3.connect(current_path)
    cursor = conn.cursor()
    
    # Crear tabla user con nueva estructura
    cursor.execute('''
        CREATE TABLE user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(64) NOT NULL UNIQUE,
            email VARCHAR(120) UNIQUE,
            password_hash VARCHAR(256) NOT NULL,
            role VARCHAR(20) NOT NULL DEFAULT 'administrador',
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_by <PERSON>TE<PERSON><PERSON>,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMES<PERSON>MP
        );
    ''')
    print("✅ Tabla user creada")
    
    # Crear tabla point
    cursor.execute('''
        CREATE TABLE point (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100),
            latitude FLOAT,
            longitude FLOAT,
            status VARCHAR(20) DEFAULT 'azul',
            city VARCHAR(100),
            source VARCHAR(100),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    ''')
    print("✅ Tabla point creada")
    
    # Crear tabla image
    cursor.execute('''
        CREATE TABLE image (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename VARCHAR(255) NOT NULL,
            point_id INTEGER,
            user_id INTEGER,
            annotations_json TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (point_id) REFERENCES point (id),
            FOREIGN KEY (user_id) REFERENCES user (id)
        );
    ''')
    print("✅ Tabla image creada")
    
    # Crear tabla camera
    cursor.execute('''
        CREATE TABLE camera (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            point_id INTEGER,
            type VARCHAR(20) DEFAULT 'otra',
            direction FLOAT,
            photo_filename VARCHAR(255),
            latitude FLOAT,
            longitude FLOAT,
            location_source VARCHAR(50),
            location_accuracy FLOAT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (point_id) REFERENCES point (id)
        );
    ''')
    print("✅ Tabla camera creada")
    
    # Crear tablas de permisos
    cursor.execute('''
        CREATE TABLE user_city_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            city VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
            UNIQUE(user_id, city)
        );
    ''')
    print("✅ Tabla user_city_permissions creada")
    
    cursor.execute('''
        CREATE TABLE user_source_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            source VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
            UNIQUE(user_id, source)
        );
    ''')
    print("✅ Tabla user_source_permissions creada")
    
    cursor.execute('''
        CREATE TABLE user_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            permission_type VARCHAR(50) NOT NULL,
            permission_value BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
            UNIQUE(user_id, permission_type)
        );
    ''')
    print("✅ Tabla user_permissions creada")
    
    conn.commit()
    conn.close()
    
    return True

def migrate_data_from_backup():
    """Migrar datos desde el backup."""
    print("\n📥 Migrando datos desde backup...")
    
    backup_path = "instance/app.db.bkp"
    current_path = "instance/app.db"
    
    # Conectar a ambas bases de datos
    backup_conn = sqlite3.connect(backup_path)
    backup_cursor = backup_conn.cursor()
    
    current_conn = sqlite3.connect(current_path)
    current_cursor = current_conn.cursor()
    
    # Migrar usuarios
    print("👥 Migrando usuarios...")
    backup_cursor.execute("SELECT id, username, email, password_hash FROM user;")
    users = backup_cursor.fetchall()
    
    for user in users:
        current_cursor.execute('''
            INSERT INTO user (id, username, email, password_hash, role, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, 'administrador', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ''', user)
    
    print(f"✅ {len(users)} usuarios migrados")
    
    # Migrar puntos
    print("📍 Migrando puntos...")
    backup_cursor.execute("SELECT id, latitude, longitude, name, city, description, status, source, created_at, updated_at FROM point;")
    points = backup_cursor.fetchall()
    
    for point in points:
        current_cursor.execute('''
            INSERT INTO point (id, latitude, longitude, name, city, description, status, source, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', point)
    
    print(f"✅ {len(points)} puntos migrados")
    
    # Migrar imágenes
    print("🖼️  Migrando imágenes...")
    backup_cursor.execute("SELECT id, filename, point_id, user_id, annotations_json FROM image;")
    images = backup_cursor.fetchall()
    
    for image in images:
        current_cursor.execute('''
            INSERT INTO image (id, filename, point_id, user_id, annotations_json, created_at)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', image)
    
    print(f"✅ {len(images)} imágenes migradas")
    
    # Migrar cámaras
    print("📷 Migrando cámaras...")
    backup_cursor.execute("SELECT id, point_id, type, direction, photo_filename, created_at, updated_at FROM camera;")
    cameras = backup_cursor.fetchall()
    
    for camera in cameras:
        current_cursor.execute('''
            INSERT INTO camera (id, point_id, type, direction, photo_filename, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', camera)
    
    print(f"✅ {len(cameras)} cámaras migradas")
    
    # Crear usuario admin adicional
    print("👤 Creando usuario admin...")
    
    # Hash simple para la contraseña (compatible con werkzeug)
    import hashlib
    password = 'isaias52'
    password_hash = f'pbkdf2:sha256:260000${hashlib.sha256(password.encode()).hexdigest()}'
    
    current_cursor.execute('''
        INSERT INTO user (username, password_hash, role, is_active, created_at, updated_at)
        VALUES ('admin', ?, 'administrador', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ''', (password_hash,))
    
    print("✅ Usuario admin creado")
    
    # Commit cambios
    current_conn.commit()
    
    # Verificar migración
    print("\n📊 Verificando migración...")
    
    current_cursor.execute("SELECT COUNT(*) FROM user;")
    user_count = current_cursor.fetchone()[0]
    
    current_cursor.execute("SELECT COUNT(*) FROM point;")
    point_count = current_cursor.fetchone()[0]
    
    current_cursor.execute("SELECT COUNT(*) FROM image;")
    image_count = current_cursor.fetchone()[0]
    
    current_cursor.execute("SELECT COUNT(*) FROM camera;")
    camera_count = current_cursor.fetchone()[0]
    
    print(f"  👥 Usuarios: {user_count}")
    print(f"  📍 Puntos: {point_count}")
    print(f"  🖼️  Imágenes: {image_count}")
    print(f"  📷 Cámaras: {camera_count}")
    
    # Mostrar usuarios
    current_cursor.execute("SELECT username, role FROM user;")
    users = current_cursor.fetchall()
    print(f"\n👥 Usuarios disponibles:")
    for user in users:
        print(f"  - {user[0]} ({user[1]})")
    
    # Mostrar estadísticas de puntos
    current_cursor.execute("SELECT DISTINCT city FROM point WHERE city IS NOT NULL ORDER BY city LIMIT 10;")
    cities = current_cursor.fetchall()
    print(f"\n🏙️  Ciudades (primeras 10):")
    for city in cities:
        print(f"  - {city[0]}")
    
    current_cursor.execute("SELECT DISTINCT source FROM point WHERE source IS NOT NULL ORDER BY source;")
    sources = current_cursor.fetchall()
    print(f"\n📡 Fuentes:")
    for source in sources:
        print(f"  - {source[0]}")
    
    # Cerrar conexiones
    backup_conn.close()
    current_conn.close()
    
    return True

def main():
    """Función principal."""
    print("🔄 Migrador de Datos (Versión Corregida)")
    print("=" * 45)
    
    # Hacer backup de seguridad
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    safety_backup = f"safety_backup_fixed_{timestamp}.db"
    
    if os.path.exists("instance/app.db"):
        shutil.copy2("instance/app.db", safety_backup)
        print(f"✅ Backup de seguridad: {safety_backup}")
    
    print("\n⚠️  Este proceso va a:")
    print("  1. Crear nueva estructura de base de datos")
    print("  2. Migrar todos los datos del backup:")
    print("     - 6 usuarios originales")
    print("     - 1118 puntos")
    print("     - 14 imágenes")
    print("     - 18 cámaras")
    print("  3. Crear sistema de permisos")
    print("  4. Agregar usuario admin/isaias52")
    
    response = input("\n¿Continuar? (s/n): ").strip().lower()
    
    if response in ['s', 'si', 'y', 'yes']:
        try:
            # Crear nueva estructura
            if create_new_database_structure():
                # Migrar datos
                if migrate_data_from_backup():
                    print("\n🎉 ¡Migración completada exitosamente!")
                    print("\n🚀 Próximos pasos:")
                    print("   1. Reiniciar aplicación: systemctl restart relevamiento")
                    print("   2. Verificar datos: python3 check_db.py")
                    print("   3. Probar aplicación web")
                    print("   4. Crear templates HTML para gestión de usuarios")
                    print("\n🔑 Credenciales disponibles:")
                    print("   - Usuarios originales: Jlbruno, abc, dfcaceres, dfuertes, jagonzalez, uespinosa")
                    print("   - Usuario nuevo: admin / isaias52")
                else:
                    print("\n❌ Error migrando datos")
            else:
                print("\n❌ Error creando estructura")
        except Exception as e:
            print(f"\n❌ Error en migración: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ Migración cancelada")

if __name__ == "__main__":
    main()
