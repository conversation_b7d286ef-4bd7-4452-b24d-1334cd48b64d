<!-- /app/templates/announcements_list.html -->
{% extends "base.html" %}
{# Importar macro de paginación si la tienes en un archivo separado #}
{% from "_pagination.html" import render_pagination %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap"> {# Añadido flex-wrap #}
        <h1><i class="fas fa-bullhorn mr-2"></i>Anuncios</h1>
        {% if current_user.role in ['administrador', 'secretaria', 'pastorado'] %}
            <a href="{{ url_for('routes.manage_announcement') }}" class="btn btn-success mt-2 mt-md-0"> {# Margen para móviles #}
                 <i class="fas fa-plus-circle mr-1"></i> Crear Nuevo Anuncio
            </a>
        {% endif %}
    </div>

    <!-- Filtro/Búsqueda Form -->
    <div class="card card-body bg-light mb-4 shadow-sm">
        <form method="GET" action="{{ url_for('routes.list_announcements') }}" class="form">
            <div class="input-group">
                <input type="search" class="form-control" id="search" name="search" placeholder="Buscar en título, contenido o autor..." value="{{ filters.search or '' }}">
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary">Buscar</button>
                    <a href="{{ url_for('routes.list_announcements') }}" class="btn btn-secondary" title="Limpiar búsqueda">Limpiar</a>
                </div>
            </div>
            {# Aquí podrías añadir más campos de filtro si los implementas en la ruta (ej. dropdown de autor, rango de fechas) #}
        </form>
    </div>
    <!-- Fin Filtro/Búsqueda Form -->

    {% if announcements %}
        <div class="list-group shadow-sm">
            {% for announcement in announcements %}
                <div class="list-group-item list-group-item-action flex-column align-items-start mb-2 border rounded">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">{{ announcement.title }}</h5>
                        <small class="text-muted">{{ announcement.created_at | to_local }}</small>
                    </div>
                    <p class="mb-1 small">{{ announcement.content | nl2br | safe }}</p>
                    <small class="text-muted">
                        Por: {{ announcement.author.full_name if announcement.author else 'Sistema' }} |
                        Visibilidad: {{ announcement.visibility|title }}
                        {% if announcement.target_church %}
                            ({{ announcement.target_church.name }})
                        {% endif %}
                    </small>
                     {# Botón Editar para roles autorizados #}
                     {% if current_user.role in ['administrador', 'secretaria'] or (announcement.author_id == current_user.id) %}
                         <a href="{{ url_for('routes.manage_announcement', announcement_id=announcement.id) }}" class="btn btn-sm btn-outline-primary py-0 float-right">Editar</a>
                     {% endif %}
                </div>
            {% endfor %}
        </div>

        <!-- Render Pagination -->
        {# Llama a tu macro de paginación. Asegúrate que maneje los filtros #}
        {{ render_pagination(pagination, 'routes.list_announcements', filters=filters) }}

    {% else %}
        <div class="alert alert-warning" role="alert">
            {% if filters.search %}
                No se encontraron anuncios que coincidan con tu búsqueda "{{ filters.search }}".
            {% else %}
                No hay anuncios para mostrar.
            {% endif %}
             <a href="{{ url_for('routes.list_announcements') }}" class="alert-link ml-2">Ver todos</a>
        </div>
    {% endif %}

</div>
{% endblock %}