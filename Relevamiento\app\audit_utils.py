# app/audit_utils.py
"""
Utilidades para el sistema de auditoría.
"""

from functools import wraps
from flask import request, current_app
from flask_login import current_user
from app.models_audit import AuditLog
import inspect

def audit_action(action, table_name, module=None, description=None):
    """
    Decorador para registrar automáticamente acciones en el log de auditoría.
    
    Args:
        action: Tipo de acción (CREATE, UPDATE, DELETE, VIEW, etc.)
        table_name: Nombre de la tabla afectada
        module: Módulo de la aplicación
        description: Descripción personalizada
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Ejecutar la función original
            result = f(*args, **kwargs)
            
            try:
                # Obtener IP del usuario
                user_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
                
                # Registrar la acción
                AuditLog.log_action(
                    user=current_user if current_user.is_authenticated else None,
                    action=action,
                    table_name=table_name,
                    description=description,
                    module=module,
                    user_ip=user_ip
                )
                
            except Exception as e:
                current_app.logger.error(f"Error en auditoría: {e}")
            
            return result
        return decorated_function
    return decorator

def log_model_change(model_instance, action, old_values=None, description=None):
    """
    Función para registrar cambios en modelos de SQLAlchemy.
    
    Args:
        model_instance: Instancia del modelo
        action: Tipo de acción (CREATE, UPDATE, DELETE)
        old_values: Valores anteriores (para UPDATE)
        description: Descripción personalizada
    """
    try:
        # Obtener información del modelo
        table_name = model_instance.__tablename__
        record_id = getattr(model_instance, 'id', None)
        
        # Obtener valores actuales
        new_values = {}
        if action in ['CREATE', 'UPDATE']:
            for column in model_instance.__table__.columns:
                value = getattr(model_instance, column.name, None)
                # Convertir a string para serialización JSON
                if value is not None:
                    new_values[column.name] = str(value)
        
        # Obtener IP del usuario
        user_ip = None
        if request:
            user_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        
        # Determinar módulo basado en la tabla
        module_mapping = {
            'user': 'Usuarios',
            'point': 'Puntos',
            'camera': 'Cámaras',
            'image': 'Imágenes'
        }
        module = module_mapping.get(table_name, 'Sistema')
        
        # Registrar en auditoría
        AuditLog.log_action(
            user=current_user if current_user.is_authenticated else None,
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=old_values,
            new_values=new_values,
            description=description,
            module=module,
            user_ip=user_ip
        )
        
    except Exception as e:
        current_app.logger.error(f"Error registrando cambio de modelo: {e}")

def get_model_values(model_instance):
    """
    Obtiene todos los valores de un modelo como diccionario.
    """
    values = {}
    for column in model_instance.__table__.columns:
        value = getattr(model_instance, column.name, None)
        if value is not None:
            values[column.name] = str(value)
    return values

class AuditMixin:
    """
    Mixin para agregar funcionalidad de auditoría a modelos.
    """
    
    def audit_create(self, description=None):
        """Registra la creación del modelo."""
        log_model_change(self, 'CREATE', description=description)
    
    def audit_update(self, old_values, description=None):
        """Registra la actualización del modelo."""
        log_model_change(self, 'UPDATE', old_values=old_values, description=description)
    
    def audit_delete(self, description=None):
        """Registra la eliminación del modelo."""
        old_values = get_model_values(self)
        log_model_change(self, 'DELETE', old_values=old_values, description=description)

def audit_login(user, ip_address=None):
    """Registra un inicio de sesión."""
    try:
        AuditLog.log_action(
            user=user,
            action='LOGIN',
            table_name='user',
            record_id=user.id,
            description=f"Inicio de sesión exitoso",
            module='Autenticación',
            user_ip=ip_address
        )
    except Exception as e:
        current_app.logger.error(f"Error registrando login: {e}")

def audit_logout(user, ip_address=None):
    """Registra un cierre de sesión."""
    try:
        AuditLog.log_action(
            user=user,
            action='LOGOUT',
            table_name='user',
            record_id=user.id,
            description=f"Cierre de sesión",
            module='Autenticación',
            user_ip=ip_address
        )
    except Exception as e:
        current_app.logger.error(f"Error registrando logout: {e}")

def audit_view_access(table_name, record_id=None, description=None):
    """Registra el acceso a una vista."""
    try:
        module_mapping = {
            'user': 'Usuarios',
            'point': 'Puntos',
            'camera': 'Cámaras',
            'image': 'Imágenes',
            'report': 'Reportes'
        }
        
        user_ip = None
        if request:
            user_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        
        AuditLog.log_action(
            user=current_user if current_user.is_authenticated else None,
            action='VIEW',
            table_name=table_name,
            record_id=record_id,
            description=description,
            module=module_mapping.get(table_name, 'Sistema'),
            user_ip=user_ip
        )
    except Exception as e:
        current_app.logger.error(f"Error registrando acceso a vista: {e}")

def audit_export(table_name, description=None, record_count=None):
    """Registra una exportación de datos."""
    try:
        user_ip = None
        if request:
            user_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        
        export_description = description
        if record_count:
            export_description = f"{description} ({record_count} registros)" if description else f"Exportación de {record_count} registros"
        
        AuditLog.log_action(
            user=current_user if current_user.is_authenticated else None,
            action='EXPORT',
            table_name=table_name,
            description=export_description,
            module='Reportes',
            user_ip=user_ip
        )
    except Exception as e:
        current_app.logger.error(f"Error registrando exportación: {e}")

def get_audit_stats():
    """Obtiene estadísticas del sistema de auditoría."""
    try:
        from sqlalchemy import func
        from datetime import datetime, timedelta
        
        # Estadísticas generales
        total_actions = AuditLog.query.count()
        
        # Acciones por tipo
        actions_by_type = db.session.query(
            AuditLog.action,
            func.count(AuditLog.id).label('count')
        ).group_by(AuditLog.action).all()
        
        # Actividad reciente (últimas 24 horas)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_activity = AuditLog.query.filter(
            AuditLog.timestamp >= yesterday
        ).count()
        
        # Usuarios más activos
        active_users = db.session.query(
            AuditLog.username,
            func.count(AuditLog.id).label('count')
        ).filter(
            AuditLog.timestamp >= yesterday
        ).group_by(AuditLog.username).order_by(
            func.count(AuditLog.id).desc()
        ).limit(5).all()
        
        return {
            'total_actions': total_actions,
            'actions_by_type': dict(actions_by_type),
            'recent_activity': recent_activity,
            'active_users': active_users
        }
        
    except Exception as e:
        current_app.logger.error(f"Error obteniendo estadísticas de auditoría: {e}")
        return {}
