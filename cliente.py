#!/usr/bin/env python3
# client.py

from flask import Flask, render_template, request, jsonify, redirect, url_for, current_app
from flask_socketio import <PERSON><PERSON><PERSON>
from flask_cors import CORS
from ptt_control import set_ptt
import json
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'

CORS(app, resources={r"/*": {"origins": "*"}})

socketio = SocketIO(app, cors_allowed_origins="*")

serial_connection = None

def load_config():
    if not os.path.exists('config.json'):
        return None
    try:
        with open('config.json', 'r', encoding='utf-8') as file:
            config = json.load(file)
            return config
    except FileNotFoundError:
        print("Error: FileNotFoundError en load_config().")
        return None

@app.route('/')
def index():
    config = load_config()
    if config:
        return render_template('index.html', node_id=config['node_id'], username=config['username'])
    else:
        return redirect(url_for('config_form'))

@app.route('/config_form')
def config_form():
    return render_template('config_form.html')

@app.route('/save_config', methods=['POST'])
def save_user_config():
    config = {
        "username": request.form['username'],
        "password": request.form['password'],
        "node_id": request.form['node_id'],
        "input_device_index": int(request.form['input_device_index']),
        "volume_level": int(request.form['volume_level']),
        "port_number": request.form['port_number']
    }
    save_config(config)
    return redirect(url_for('index'))

def save_config(config):
    # Asegurar que node_url esté presente
    if 'node_url' not in config:
        config['node_url'] = "patagoniaservers.com.ar:5000/node"
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)

@app.route('/ptt_event', methods=['POST'])
def handle_ptt_event():
    global serial_connection
    data = request.json
    config = load_config()
    if config and 'ptt_state' in data:
        if data['ptt_state']:
            serial_connection = set_ptt(True, config, serial_connection)
        else:
            serial_connection = set_ptt(False, config, serial_connection)
        return jsonify({"status": "success", "ptt_state": data['ptt_state']})
    else:
        return jsonify({"status": "error", "message": "Invalid request"}), 400

@socketio.on('receive_audio')
def handle_receive_audio(data):
    global serial_connection
    config = load_config()
    if config and 'audio' in data:
        try:
            print(f"Recibiendo audio de {data.get('user', 'desconocido')}")
            # Activar PTT
            serial_connection = set_ptt(True, config, serial_connection)
            # Emitir el audio a todos los clientes conectados
            socketio.emit('audio_received', data)
            # Nota: El PTT se desactivará cuando el cliente JavaScript
            # termine de reproducir el audio y envíe un evento 'ptt_event' con state=False
        except Exception as e:
            print(f"Error al procesar el audio recibido: {e}")
            # Asegurar que el PTT se desactive en caso de error
            serial_connection = set_ptt(False, config, serial_connection)

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
