// --- Archivo: app/static/js/point-editor.js ---
// Responsabilidades: Edición de ubicación de puntos mediante arrastrar marcadores

let pointEditMap = null;
let pointEditMarker = null;
let isEditingPoint = false;

/**
 * Alterna el modo de edición del punto
 */
function togglePointEditing() {
    const mapCard = document.getElementById('point-edit-map-card');
    const editBtn = document.getElementById('edit-point-btn');
    
    if (!isEditingPoint) {
        // Activar modo edición
        mapCard.style.display = 'block';
        editBtn.textContent = '❌ Cancelar Edición';
        editBtn.className = 'btn btn-sm btn-outline-danger';
        isEditingPoint = true;
        
        // Inicializar mapa si no existe
        if (!pointEditMap) {
            initializePointEditMap();
        }
    } else {
        // Desactivar modo edición
        mapCard.style.display = 'none';
        editBtn.textContent = '📍 Editar Ubicación';
        editBtn.className = 'btn btn-sm btn-outline-primary';
        isEditingPoint = false;
    }
}

/**
 * Inicializa el mapa de edición del punto
 */
function initializePointEditMap() {
    const mapElement = document.getElementById('point-edit-map');
    if (!mapElement || typeof L === 'undefined') {
        console.error('Mapa o Leaflet no disponible');
        return;
    }
    
    const pointLat = parseFloat(mapElement.dataset.pointLat);
    const pointLon = parseFloat(mapElement.dataset.pointLon);
    const pointId = parseInt(mapElement.dataset.pointId);
    const pointStatus = mapElement.dataset.pointStatus;
    
    console.log('Inicializando mapa de edición del punto:', { pointLat, pointLon, pointId, pointStatus });
    
    // Limpiar contenido de carga
    mapElement.innerHTML = '';
    
    try {
        // Crear mapa centrado en el punto
        pointEditMap = L.map(mapElement).setView([pointLat, pointLon], 16);
        
        // Agregar capa de tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(pointEditMap);
        
        // Crear marcador del punto (editable)
        const pointIcon = L.icon({
            iconUrl: `/static/img/marker_${pointStatus}.png`,
            iconSize: [30, 40],
            iconAnchor: [15, 40],
            popupAnchor: [0, -35]
        });
        
        pointEditMarker = L.marker([pointLat, pointLon], { 
            icon: pointIcon, 
            draggable: true 
        });
        
        // Obtener el nombre del punto desde el DOM
        const pointNameElement = document.querySelector('h1');
        const pointName = pointNameElement ? pointNameElement.textContent.replace('Punto: ', '').trim() : `Punto ${pointId}`;
        
        pointEditMarker.bindPopup(`<b>${pointName}</b><br>Arrastra para cambiar ubicación`);
        pointEditMarker.addTo(pointEditMap);
        
        // Agregar evento de dragend
        pointEditMarker.on('dragend', function(e) {
            const newPos = e.target.getLatLng();
            console.log('Punto arrastrado a nueva posición:', newPos);
            updatePointCoordinates(pointId, newPos.lat, newPos.lng);
        });
        
        // Estilo visual para indicar que es editable
        pointEditMarker.on('add', function() {
            console.log('Marcador del punto agregado al mapa, aplicando estilos de edición');
            const iconElement = pointEditMarker.getElement();
            if (iconElement) {
                iconElement.style.cursor = 'move';
                iconElement.style.filter = 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))'; // Verde para el punto
                console.log('Estilos de edición aplicados al punto');
            }
        });
        
        console.log('Mapa de edición del punto inicializado correctamente');
        
    } catch (error) {
        console.error('Error inicializando mapa de edición del punto:', error);
        mapElement.innerHTML = '<p class="text-danger text-center mt-5">Error al cargar el mapa de edición del punto.</p>';
    }
}

/**
 * Actualiza las coordenadas de un punto en el servidor
 * @param {number} pointId - ID del punto
 * @param {number} latitude - Nueva latitud
 * @param {number} longitude - Nueva longitud
 */
function updatePointCoordinates(pointId, latitude, longitude) {
    console.log('Actualizando coordenadas del punto:', { pointId, latitude, longitude });
    
    const data = {
        latitude: latitude,
        longitude: longitude
    };
    
    fetch(`/points/${pointId}/update_coordinates`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Respuesta del servidor:', response);
        return response.json();
    })
    .then(data => {
        console.log('Datos recibidos del servidor:', data);
        if (data.success) {
            console.log(`Coordenadas de punto ${pointId} actualizadas:`, data.point);
            showNotification(data.message, 'success');
            
            // Actualizar la información mostrada en la página
            updatePointInfoDisplay(data.point);
        } else {
            console.error('Error actualizando coordenadas del punto:', data.error);
            showNotification(`Error: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error en la petición:', error);
        showNotification('Error de conexión al actualizar coordenadas del punto', 'error');
    });
}

/**
 * Actualiza la información del punto mostrada en la página
 * @param {Object} point - Datos actualizados del punto
 */
function updatePointInfoDisplay(point) {
    console.log('Actualizando display de coordenadas del punto:', point);
    
    // Actualizar coordenadas mostradas en la página
    const coordsElement = document.querySelector('.point-coordinates');
    if (coordsElement) {
        const newCoordsText = `(${point.latitude.toFixed(6)}, ${point.longitude.toFixed(6)})`;
        coordsElement.textContent = newCoordsText;
        console.log('Coordenadas actualizadas en elemento .point-coordinates:', newCoordsText);
    } else {
        console.warn('No se encontró elemento .point-coordinates');
        
        // Buscar de forma alternativa
        const coordsElements = document.querySelectorAll('p, span, div');
        let found = false;
        
        coordsElements.forEach(el => {
            if (el.textContent && el.textContent.includes('Coordenadas:')) {
                const text = el.textContent;
                const regex = /\(-?\d+\.\d+,\s*-?\d+\.\d+\)/;
                if (regex.test(text)) {
                    const newText = text.replace(regex, `(${point.latitude.toFixed(6)}, ${point.longitude.toFixed(6)})`);
                    el.textContent = newText;
                    console.log('Coordenadas actualizadas en elemento alternativo:', newText);
                    found = true;
                }
            }
        });
        
        if (!found) {
            console.warn('No se pudo encontrar ningún elemento con coordenadas para actualizar');
        }
    }
    
    console.log('Actualización de display completada');
}

/**
 * Obtiene el token CSRF si está disponible
 * @returns {string|null} - Token CSRF o null
 */
function getCsrfToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : null;
}

/**
 * Muestra una notificación al usuario
 * @param {string} message - Mensaje a mostrar
 * @param {string} type - Tipo de notificación ('success', 'error', 'info')
 */
function showNotification(message, type = 'info') {
    console.log('Mostrando notificación:', { message, type });
    
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remover después de 5 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Hacer funciones disponibles globalmente
if (typeof window !== 'undefined') {
    window.togglePointEditing = togglePointEditing;
    window.updatePointCoordinates = updatePointCoordinates;
    window.updatePointInfoDisplay = updatePointInfoDisplay;
}

console.log('Point Editor JavaScript cargado correctamente');
