# --- Archivo: app/points.py ---

import os
import csv
import json
from io import StringIO
from datetime import datetime

from flask import (
    Blueprint, render_template, flash, redirect, url_for, request, jsonify,
    current_app, abort, Response
)
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.models import Point, Image, Camera
from app.forms import (
    ImageUploadForm, PointStatusForm, PointForm,
    CameraForm, FileImportForm
)
from .pdf_generator import generate_point_report_pdf # <--- Importar la nueva función
from PIL import Image as PILImage, UnidentifiedImageError

bp = Blueprint('points', __name__)

# --- Funciones Auxiliares ---

def get_point_or_404(point_id):
    point = db.session.get(Point, point_id)
    if point is None: abort(404)
    return point

def get_image_or_404(image_id):
    image = db.session.get(Image, image_id)
    if image is None: abort(404)
    return image

# Función auxiliar para verificar si la extensión del archivo está permitida
# Podría estar en un archivo utils.py también
def allowed_file(filename):
    """Verifica si la extensión del archivo está permitida."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

# --- Rutas ---

@bp.route('/<int:point_id>', methods=['GET', 'POST'])
@login_required
def detail(point_id):
    """
    Muestra los detalles de un punto, incluyendo imágenes con sus anotaciones,
    y maneja la actualización de estado/descripción del punto.
    """
    point = get_point_or_404(point_id)
    # Pasar datos actuales al formulario de estado/descripción
    status_form = PointStatusForm(obj=point)
    image_form = ImageUploadForm() # Formulario para subir nuevas imágenes
    camera_form = CameraForm()  # <--- ¡ESTA ES LA LÍNEA CLAVE!

    # Procesar el formulario de actualización de estado/descripción del punto
    if status_form.validate_on_submit() and 'submit_status' in request.form:
        try:
            point.status = status_form.status.data
            # Usar .strip() para quitar espacios extra, o None si queda vacío
            point.description = status_form.description.data.strip() if status_form.description.data else None
            point.updated_at = datetime.utcnow() # Actualizar timestamp
            db.session.commit()
            flash('Estado y descripción del punto actualizados.', 'success')
            current_app.logger.info(f"Punto {point_id} actualizado por {current_user.username}. Nuevo estado: {point.status}")
            # Redirigir para evitar reenvío del formulario al recargar (Patrón PRG)
            return redirect(url_for('points.detail', point_id=point.id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error al actualizar punto {point_id}: {e}")
            flash('Error al actualizar el punto.', 'danger')

    # Obtener imágenes asociadas, ordenadas por fecha descendente
    images = point.images.order_by(Image.upload_timestamp.desc()).all()

    # Renderizar la plantilla pasando los datos necesarios
    return render_template('points/point_detail.html',
                           title=f'Detalle Punto {point.name or point.id}',
                           point=point,
                           image_form=image_form,
                           status_form=status_form,
                           camera_form=camera_form,
                           images=images)


@bp.route('/<int:point_id>/upload_image', methods=['POST'])
@login_required
def upload_image(point_id):
    """Maneja la subida de una nueva imagen para un punto, redimensionándola."""
    point = get_point_or_404(point_id)
    form = ImageUploadForm()

    if form.validate_on_submit():
        f = form.image.data
        base_filename = secure_filename(f.filename)
        timestamp = int(datetime.utcnow().timestamp())
        unique_filename = f"{point_id}_{current_user.id}_{timestamp}_{base_filename}"
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)

        try:
            # --- INICIO: LÓGICA DE REDIMENSIONAMIENTO ---
            with PILImage.open(f.stream) as img:
                # Convertir a RGB para asegurar compatibilidad (especialmente para JPG)
                # y manejar transparencia de PNG/GIF poniéndole fondo blanco por defecto.
                # Si necesitas transparencia, guarda como PNG en lugar de JPG.
                if img.mode in ("RGBA", "P"):
                    img = img.convert("RGB")

                # Redimensionar usando thumbnail (mantiene proporción, no agranda)
                # Usar el tamaño de la configuración
                target_size = current_app.config.get('MAX_IMAGE_SIZE', (1280, 1024))
                img.thumbnail(target_size, PILImage.Resampling.LANCZOS) # LANCZOS es buena calidad para reducir

                # Guardar la imagen redimensionada
                # Guardar como JPEG con calidad razonable (85)
                # Puedes ajustar la calidad (0-100) o guardar como PNG si prefieres.
                img.save(filepath, format='JPEG', quality=85, optimize=True)
                current_app.logger.info(f"Archivo '{base_filename}' abierto, redimensionado a max {target_size} y guardado como '{unique_filename}' en {filepath}")
            # --- FIN: LÓGICA DE REDIMENSIONAMIENTO ---

            # Crear y guardar la nueva entrada en la base de datos (sin cambios aquí)
            new_image = Image(
                filename=unique_filename,
                original_filename=base_filename,
                notes=form.notes.data.strip() if form.notes.data else None,
                point_id=point.id,
                user_id=current_user.id
            )
            db.session.add(new_image)
            point.updated_at = datetime.utcnow() # Marcar punto como actualizado
            db.session.commit()
            flash('Imagen subida y redimensionada correctamente.', 'success') # Mensaje actualizado
            current_app.logger.info(f"Imagen {new_image.id} ({unique_filename}) subida y redim. para punto {point_id} por {current_user.username}")
            return redirect(url_for('points.detail', point_id=point_id))

        except UnidentifiedImageError:
             db.session.rollback()
             current_app.logger.warning(f"Intento de subir archivo no identificado como imagen para punto {point_id}: {base_filename}")
             flash('El archivo subido no parece ser una imagen válida.', 'danger')
        except Exception as e:
             db.session.rollback()
             current_app.logger.exception(f"Error al procesar/guardar imagen redimensionada para punto {point_id}: {e}")
             flash(f'Error al procesar o guardar la imagen: {e}', 'danger')
             # Intentar borrar archivo físico si se guardó pero falló la BD o el procesamiento
             if os.path.exists(filepath):
                 try:
                     os.remove(filepath)
                     current_app.logger.warning(f"Archivo {filepath} eliminado debido a error en procesamiento/BD.")
                 except OSError as remove_err:
                     current_app.logger.error(f"No se pudo eliminar {filepath} tras error: {remove_err}")

    else:
        # Manejo de errores del formulario (sin cambios)
        current_app.logger.warning(f"Intento fallido de subida de imagen para punto {point_id}. Errores: {form.errors}")
        for field, errors in form.errors.items():
            for error in errors:
                flash(f"Error en '{getattr(form, field).label.text}': {error}", 'danger')

    return redirect(url_for('points.detail', point_id=point_id))


# --- RUTA PARA GUARDAR ANOTACIONES ---
@bp.route('/image/<int:image_id>/save_annotations', methods=['POST'])
@login_required
def save_annotations(image_id):
    """Recibe datos JSON de anotaciones Konva y los guarda en la BD."""
    image = get_image_or_404(image_id)
    # Opcional: Verificar permisos
    # if image.user_id != current_user.id and not current_user.is_admin:
    #     return jsonify({'success': False, 'message': 'No autorizado'}), 403

    annotations_json_str = None
    try:
        # Intentar obtener como JSON directamente (si Content-Type es application/json)
        if request.is_json:
            annotations_data = request.get_json()
            # Volver a convertir a string para almacenar en BD Text field
            annotations_json_str = json.dumps(annotations_data)
        else:
            # Intentar obtener como campo de formulario (si se envió con FormData)
            annotations_json_str = request.form.get('annotations_json')
            if annotations_json_str:
                # Validar que sea un JSON parseable antes de guardar
                json.loads(annotations_json_str) # Lanza error si no es válido
            else:
                # Si no viene nada, interpretarlo como borrar anotaciones
                annotations_json_str = None # Guardará NULL en la BD
                current_app.logger.info(f"Limpiando anotaciones para imagen {image_id}")

    except json.JSONDecodeError:
        current_app.logger.warning(f"Se recibió un string inválido como JSON para imagen {image_id}")
        return jsonify({'success': False, 'message': 'Datos JSON inválidos recibidos.'}), 400
    except Exception as e:
        current_app.logger.error(f"Error procesando datos de entrada para save_annotations imagen {image_id}: {e}")
        return jsonify({'success': False, 'message': 'Error al procesar datos de entrada.'}), 400

    # Guardar el string JSON (o None) en la base de datos
    try:
        image.annotations_json = annotations_json_str
        image.point.updated_at = datetime.utcnow() # Marcar punto como actualizado
        db.session.commit()
        message = 'Anotaciones guardadas.' if annotations_json_str else 'Anotaciones limpiadas.'
        current_app.logger.info(f"{message} para imagen {image_id} por {current_user.username}")
        return jsonify({'success': True, 'message': message})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error al guardar anotaciones para imagen {image_id} en BD: {e}")
        return jsonify({'success': False, 'message': f'Error al guardar en base de datos: {e}'}), 500


# --- RUTA PARA BORRAR IMAGEN ---
@bp.route('/image/<int:image_id>/delete', methods=['POST'])
@login_required
def delete_image(image_id):
    """Elimina una imagen, su archivo físico y sus anotaciones."""
    image = get_image_or_404(image_id)
    point_id = image.point_id # Guardar ID del punto para redirigir

    # Opcional: Verificar permisos
    # if image.user_id != current_user.id and not current_user.is_admin:
    #     flash('No tienes permiso para borrar esta imagen.', 'danger')
    #     return redirect(url_for('points.detail', point_id=point_id))

    filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], image.filename)

    try:
        # Borrar registro de la BD primero (cascade debería manejar relaciones)
        db.session.delete(image)
        image.point.updated_at = datetime.utcnow() # Marcar punto como actualizado
        db.session.commit()
        current_app.logger.info(f"Registro de imagen {image_id} ({image.filename}) eliminado de la BD por {current_user.username}.")

        # Borrar archivo físico después de confirmar borrado en BD
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
                flash('Imagen eliminada correctamente.', 'success')
                current_app.logger.info(f"Archivo físico {filepath} eliminado.")
            except OSError as remove_err:
                 flash('Registro de imagen eliminado, pero hubo un error al borrar el archivo físico.', 'warning')
                 current_app.logger.error(f"Error al eliminar archivo físico {filepath}: {remove_err}")
        else:
             flash('Registro de imagen eliminado, pero el archivo físico no se encontró.', 'warning')
             current_app.logger.warning(f"Se eliminó registro de imagen {image_id} pero el archivo {filepath} no existía.")

    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar la imagen: {e}', 'danger')
        current_app.logger.error(f"Error al eliminar imagen {image_id} o archivo {filepath}: {e}")

    return redirect(url_for('points.detail', point_id=point_id))

# --- NUEVAS RUTAS CRUD ---

@bp.route('/list')
@login_required
def list_all():
    from app.forms import FileImportForm, PointsFilterForm
    from app.models import User, Image, Camera
    from datetime import datetime
    from sqlalchemy import func, and_, or_

    # Formularios
    import_form = FileImportForm()
    filter_form = PointsFilterForm()

    # Obtener opciones dinámicas para los selectores
    cities = db.session.query(Point.city).filter(Point.city.isnot(None)).distinct().order_by(Point.city).all()
    filter_form.city.choices = [('', 'Todas las ciudades')] + [(city[0], city[0]) for city in cities if city[0]]

    sources = db.session.query(Point.source).filter(Point.source.isnot(None)).distinct().order_by(Point.source).all()
    filter_form.source.choices = [('', 'Todos los orígenes')] + [(source[0], source[0]) for source in sources if source[0]]

    users = db.session.query(User.username).distinct().order_by(User.username).all()
    filter_form.created_by.choices = [('', 'Todos los usuarios')] + [(user[0], user[0]) for user in users if user[0]]
    filter_form.modified_by.choices = [('', 'Todos los usuarios')] + [(user[0], user[0]) for user in users if user[0]]

    # Parámetros de ordenamiento
    sort_by = request.args.get('sort_by', 'id')
    order = request.args.get('order', 'asc')

    allowed_sort_fields = ['id', 'name', 'city', 'status', 'created_at', 'updated_at']
    if sort_by not in allowed_sort_fields:
        sort_by = 'id'

    if order not in ['asc', 'desc']:
        order = 'asc'

    try:
        sort_column = getattr(Point, sort_by)
    except AttributeError:
        sort_column = Point.id

    order_expression = sort_column.desc() if order == 'desc' else sort_column.asc()

    # Inicializar query base
    query = Point.query
    points_data = []
    total_points = 0

    # Aplicar filtros si hay parámetros en la URL
    if request.method == 'GET' and any(request.args.values()):
        # Obtener valores de filtros desde URL
        point_id = request.args.get('point_id')
        name = request.args.get('name')
        city = request.args.get('city')
        status = request.args.get('status')
        source = request.args.get('source')
        description = request.args.get('description')
        camera_type = request.args.get('camera_type')
        has_images = request.args.get('has_images')
        has_cameras = request.args.get('has_cameras')
        cameras_count = request.args.get('cameras_count')
        has_annotations = request.args.get('has_annotations')
        created_by = request.args.get('created_by')
        modified_by = request.args.get('modified_by')
        lat_min = request.args.get('lat_min')
        lat_max = request.args.get('lat_max')
        lon_min = request.args.get('lon_min')
        lon_max = request.args.get('lon_max')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        modified_from = request.args.get('modified_from')
        modified_to = request.args.get('modified_to')

        # Aplicar filtros básicos
        if point_id:
            try:
                query = query.filter(Point.id == int(point_id))
            except ValueError:
                flash('ID de punto debe ser un número válido.', 'warning')

        if name:
            query = query.filter(Point.name.ilike(f'%{name}%'))

        if city:
            query = query.filter(Point.city == city)

        if status:
            query = query.filter(Point.status == status)

        if source:
            query = query.filter(Point.source == source)

        if description:
            query = query.filter(Point.description.ilike(f'%{description}%'))

        # Filtros de coordenadas
        if lat_min:
            try:
                query = query.filter(Point.latitude >= float(lat_min))
            except ValueError:
                flash('Latitud mínima debe ser un número válido.', 'warning')

        if lat_max:
            try:
                query = query.filter(Point.latitude <= float(lat_max))
            except ValueError:
                flash('Latitud máxima debe ser un número válido.', 'warning')

        if lon_min:
            try:
                query = query.filter(Point.longitude >= float(lon_min))
            except ValueError:
                flash('Longitud mínima debe ser un número válido.', 'warning')

        if lon_max:
            try:
                query = query.filter(Point.longitude <= float(lon_max))
            except ValueError:
                flash('Longitud máxima debe ser un número válido.', 'warning')

        # Filtros de fecha
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(Point.created_at >= date_from_obj)
            except ValueError:
                flash('Formato de fecha inválido.', 'warning')

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                date_to_end = datetime.combine(date_to_obj, datetime.max.time())
                query = query.filter(Point.created_at <= date_to_end)
            except ValueError:
                flash('Formato de fecha inválido.', 'warning')

        if modified_from:
            try:
                modified_from_obj = datetime.strptime(modified_from, '%Y-%m-%d').date()
                query = query.filter(Point.updated_at >= modified_from_obj)
            except ValueError:
                flash('Formato de fecha de modificación inválido.', 'warning')

        if modified_to:
            try:
                modified_to_obj = datetime.strptime(modified_to, '%Y-%m-%d').date()
                modified_to_end = datetime.combine(modified_to_obj, datetime.max.time())
                query = query.filter(Point.updated_at <= modified_to_end)
            except ValueError:
                flash('Formato de fecha de modificación inválido.', 'warning')

        # Filtros complejos que requieren subqueries
        if has_images == 'si':
            query = query.filter(Point.images.any())
        elif has_images == 'no':
            query = query.filter(~Point.images.any())

        if has_cameras == 'si':
            query = query.filter(Point.cameras.any())
        elif has_cameras == 'no':
            query = query.filter(~Point.cameras.any())

        if camera_type:
            query = query.filter(Point.cameras.any(Camera.type == camera_type))

        if cameras_count:
            if cameras_count == '4+':
                query = query.filter(func.count(Camera.id) >= 4).group_by(Point.id)
            else:
                try:
                    count = int(cameras_count)
                    query = query.filter(func.count(Camera.id) == count).group_by(Point.id)
                except ValueError:
                    flash('Cantidad de cámaras debe ser un número válido.', 'warning')

        if has_annotations == 'si':
            query = query.filter(Point.images.any(Image.annotations_json.isnot(None)))
        elif has_annotations == 'no':
            query = query.filter(~Point.images.any(Image.annotations_json.isnot(None)))

        # Ejecutar query y obtener resultados
        try:
            points = query.order_by(order_expression).all()
            total_points = len(points)

            # Preparar datos para mostrar
            for point in points:
                point_data = {
                    'id': point.id,
                    'name': point.name,
                    'city': point.city,
                    'latitude': point.latitude,
                    'longitude': point.longitude,
                    'status': point.status,
                    'source': point.source,
                    'description': point.description,
                    'created_at': point.created_at,
                    'updated_at': point.updated_at,
                    'images_count': point.images.count(),
                    'cameras_count': point.cameras.count(),
                    'has_annotations': any(img.annotations_json for img in point.images)
                }
                points_data.append(point_data)

            current_app.logger.info(f"Filtros de puntos aplicados por {current_user.username}: {total_points} puntos encontrados")

        except Exception as e:
            current_app.logger.error(f"Error en consulta de puntos: {e}")
            flash('Error al ejecutar la consulta. Revise los filtros aplicados.', 'danger')
            points_data = []
            total_points = 0
    else:
        # Sin filtros, mostrar todos los puntos
        try:
            points = Point.query.order_by(order_expression).all()
            total_points = len(points)

            for point in points:
                point_data = {
                    'id': point.id,
                    'name': point.name,
                    'city': point.city,
                    'latitude': point.latitude,
                    'longitude': point.longitude,
                    'status': point.status,
                    'source': point.source,
                    'description': point.description,
                    'created_at': point.created_at,
                    'updated_at': point.updated_at,
                    'images_count': point.images.count(),
                    'cameras_count': point.cameras.count(),
                    'has_annotations': any(img.annotations_json for img in point.images)
                }
                points_data.append(point_data)

        except Exception as e:
            current_app.logger.error(f"Error obteniendo puntos: {e}")
            points_data = []
            total_points = 0

    return render_template('points/list_points.html',
                           title="Gestionar Puntos",
                           points=points_data,  # Cambiar a points_data
                           import_form=import_form,
                           filter_form=filter_form,
                           total_points=total_points,
                           sort_by=sort_by,
                           order=order)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
def create():
    """Muestra el formulario para crear un nuevo punto y procesa el envío."""
    form = PointForm()
    if form.validate_on_submit():
        try:
            new_point = Point(
                name=form.name.data.strip(),
                city=form.city.data.strip() if form.city.data else None, # <-- Añadido
                latitude=form.latitude.data,
                longitude=form.longitude.data,
                status=form.status.data,
                source=form.source.data.strip() if form.source.data else None,  # ✅ ORIGEN
                description=form.description.data.strip() if form.description.data else None
            )
            db.session.add(new_point)
            db.session.commit()
            flash(f'Punto "{new_point.name}" creado exitosamente.', 'success')
            current_app.logger.info(f"Nuevo punto creado (ID: {new_point.id}) por {current_user.username}")
            return redirect(url_for('points.list_all'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error al crear punto: {e}")
            flash('Error al crear el punto.', 'danger')
    return render_template('points/point_form.html',
                           title="Nuevo Punto",
                           form=form,
                           form_action=url_for('points.create'))

@bp.route('/edit/<int:point_id>', methods=['GET', 'POST'])
@login_required
def edit(point_id):
    """Muestra el formulario para editar un punto existente y procesa el envío."""
    point = get_point_or_404(point_id)
    form = PointForm(obj=point) # Precarga en GET

    if form.validate_on_submit():
        try:
            # Actualizar el objeto point
            point.name = form.name.data.strip()
            point.city = form.city.data.strip() if form.city.data else None # <-- Añadido
            point.latitude = form.latitude.data
            point.longitude = form.longitude.data
            point.status = form.status.data
            point.source = form.source.data.strip() if form.source.data else None  # ✅ ORIGEN
            point.description = form.description.data.strip() if form.description.data else None
            point.updated_at = datetime.utcnow()
            db.session.commit()
            flash(f'Punto "{point.name}" actualizado exitosamente.', 'success')
            current_app.logger.info(f"Punto {point_id} actualizado por {current_user.username}")
            return redirect(url_for('points.list_all'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error al editar punto {point_id}: {e}")
            flash('Error al guardar los cambios del punto.', 'danger')

    return render_template('points/point_form.html',
                           title=f"Editar Punto {point.id}",
                           form=form,
                           point_id=point_id,
                           form_action=url_for('points.edit', point_id=point_id)) # Acción para el formulario

@bp.route('/delete/<int:point_id>', methods=['POST']) # Usar POST para eliminar
@login_required
def delete(point_id):
    """Elimina un punto y sus imágenes asociadas."""
    point = get_point_or_404(point_id)
    point_name = point.name or f"ID {point.id}" # Guardar nombre para mensaje flash

    try:
        # Opcional: Borrar archivos físicos de las imágenes antes de borrar el punto
        # (si cascade="all, delete-orphan" no los borra o quieres más control)
        images_to_delete = point.images.all()
        for img in images_to_delete:
             filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], img.filename)
             if os.path.exists(filepath):
                 try:
                     os.remove(filepath)
                     current_app.logger.info(f"Archivo físico eliminado para imagen {img.id} durante borrado de punto {point_id}.")
                 except OSError as e:
                      current_app.logger.error(f"No se pudo eliminar archivo {filepath} para imagen {img.id}: {e}")

        # Eliminar el punto (cascade="all, delete-orphan" debería borrar imágenes de la BD)
        db.session.delete(point)
        db.session.commit()
        flash(f'Punto "{point_name}" y sus imágenes asociadas eliminados.', 'success')
        current_app.logger.info(f"Punto {point_id} eliminado por {current_user.username}")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error al eliminar punto {point_id}: {e}")
        flash('Error al eliminar el punto.', 'danger')

    return redirect(url_for('points.list_all'))

@bp.route('/<int:point_id>/add_camera', methods=['POST'])
@login_required
def add_camera(point_id):
    point = get_point_or_404(point_id)
    form = CameraForm()
    if form.validate_on_submit():
        photo_file = form.photo.data
        filename = None
        if photo_file:
            filename = secure_filename(f"{point_id}_cam_{int(datetime.utcnow().timestamp())}_{photo_file.filename}")
            path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
            photo_file.save(path)

        # Manejar coordenadas
        latitude = None
        longitude = None
        location_source = None
        location_accuracy = None

        # Si se especificó un origen de coordenadas
        if form.location_source.data:
            if form.location_source.data == 'manual' and form.latitude.data is not None and form.longitude.data is not None:
                latitude = form.latitude.data
                longitude = form.longitude.data
                location_source = 'manual'
                location_accuracy = form.location_accuracy.data
            elif form.location_source.data == 'gps' and form.latitude.data is not None and form.longitude.data is not None:
                latitude = form.latitude.data
                longitude = form.longitude.data
                location_source = 'gps'
                location_accuracy = form.location_accuracy.data
            # Si es 'point' o vacío, se dejan las coordenadas como None para usar las del punto

        new_camera = Camera(
            point_id=point_id,
            type=form.type.data,
            direction=form.direction.data.strip(),
            photo_filename=filename,
            latitude=latitude,
            longitude=longitude,
            location_source=location_source,
            location_accuracy=location_accuracy
        )
        db.session.add(new_camera)
        db.session.commit()

        # Mensaje de éxito con información de coordenadas
        if latitude is not None and longitude is not None:
            flash(f'Cámara añadida correctamente con coordenadas propias ({location_source}).', 'success')
        else:
            flash('Cámara añadida correctamente. Usará las coordenadas del punto.', 'success')
    else:
        # Mostrar errores específicos del formulario
        for field, errors in form.errors.items():
            for error in errors:
                flash(f"Error en {getattr(form, field).label.text}: {error}", 'danger')
    return redirect(url_for('points.detail', point_id=point_id))

@bp.route('/camera/<int:camera_id>/delete', methods=['POST'])
@login_required
def delete_camera(camera_id):
    camera = db.session.get(Camera, camera_id)
    if not camera:
        flash("Cámara no encontrada.", "warning")
        return redirect(url_for('points.list_all'))

    point_id = camera.point_id
    try:
        if camera.photo_filename:
            filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], camera.photo_filename)
            if os.path.exists(filepath):
                os.remove(filepath)
        db.session.delete(camera)
        db.session.commit()
        flash("Cámara eliminada correctamente.", "success")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al eliminar la cámara: {e}", "danger")

    return redirect(url_for('points.detail', point_id=point_id))

@bp.route('/cameras/list')
@login_required
def list_cameras():
    from app.models import Camera, Point
    from app.forms import CamerasFilterForm
    from datetime import datetime

    form = CamerasFilterForm()

    # Obtener opciones dinámicas para los selectores
    cities = db.session.query(Point.city).filter(Point.city.isnot(None)).distinct().order_by(Point.city).all()
    form.city.choices = [('', 'Todas las ciudades')] + [(city[0], city[0]) for city in cities if city[0]]

    sources = db.session.query(Point.source).filter(Point.source.isnot(None)).distinct().order_by(Point.source).all()
    form.source.choices = [('', 'Todos los orígenes')] + [(source[0], source[0]) for source in sources if source[0]]

    # Inicializar query base
    query = Camera.query.join(Point)
    cameras_data = []
    total_cameras = 0

    # Aplicar filtros si el formulario fue enviado
    if request.method == 'GET' and any(request.args.values()):
        # Obtener valores de filtros desde URL
        camera_id = request.args.get('camera_id')
        point_id = request.args.get('point_id')
        camera_type = request.args.get('camera_type')
        city = request.args.get('city')
        source = request.args.get('source')
        location_source = request.args.get('location_source')
        has_photo = request.args.get('has_photo')
        has_coordinates = request.args.get('has_coordinates')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Aplicar filtros
        if camera_id:
            try:
                query = query.filter(Camera.id == int(camera_id))
            except ValueError:
                flash('ID de cámara debe ser un número válido.', 'warning')

        if point_id:
            try:
                query = query.filter(Camera.point_id == int(point_id))
            except ValueError:
                flash('ID de punto debe ser un número válido.', 'warning')

        if camera_type:
            query = query.filter(Camera.type == camera_type)

        if city:
            query = query.filter(Point.city == city)

        if source:
            query = query.filter(Point.source == source)

        if location_source:
            if location_source == 'point':
                # Cámaras que usan coordenadas del punto
                query = query.filter(Camera.latitude.is_(None), Camera.longitude.is_(None))
            else:
                # Cámaras con coordenadas propias
                query = query.filter(Camera.location_source == location_source)

        if has_photo == 'si':
            query = query.filter(Camera.photo_filename.isnot(None))
        elif has_photo == 'no':
            query = query.filter(Camera.photo_filename.is_(None))

        if has_coordinates == 'si':
            query = query.filter(Camera.latitude.isnot(None), Camera.longitude.isnot(None))
        elif has_coordinates == 'no':
            query = query.filter(Camera.latitude.is_(None), Camera.longitude.is_(None))

        # Filtros de fecha
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(Camera.created_at >= date_from_obj)
            except ValueError:
                flash('Formato de fecha inválido.', 'warning')

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                date_to_end = datetime.combine(date_to_obj, datetime.max.time())
                query = query.filter(Camera.created_at <= date_to_end)
            except ValueError:
                flash('Formato de fecha inválido.', 'warning')

        # Ejecutar query y obtener resultados
        try:
            cameras = query.order_by(Camera.created_at.desc()).all()
            total_cameras = len(cameras)

            # Preparar datos para mostrar
            for camera in cameras:
                coordinates = camera.get_coordinates()
                camera_data = {
                    'id': camera.id,
                    'point_id': camera.point_id,
                    'point_name': camera.point.name or f"Punto {camera.point_id}",
                    'type': camera.type,
                    'direction': camera.direction,
                    'photo_filename': camera.photo_filename,
                    'created_at': camera.created_at,
                    'coordinates': coordinates,
                    'has_own_coordinates': camera.has_own_coordinates(),
                    'location_source': camera.location_source,
                    'location_accuracy': camera.location_accuracy,
                    'point_city': camera.point.city,
                    'point_source': camera.point.source
                }
                cameras_data.append(camera_data)

            current_app.logger.info(f"Filtros de cámaras aplicados por {current_user.username}: {total_cameras} cámaras encontradas")

        except Exception as e:
            current_app.logger.error(f"Error en consulta de cámaras: {e}")
            flash('Error al ejecutar la consulta. Revise los filtros aplicados.', 'danger')
            cameras_data = []
            total_cameras = 0
    else:
        # Sin filtros, mostrar todas las cámaras
        try:
            cameras = Camera.query.join(Point).order_by(Camera.created_at.desc()).all()
            total_cameras = len(cameras)

            for camera in cameras:
                coordinates = camera.get_coordinates()
                camera_data = {
                    'id': camera.id,
                    'point_id': camera.point_id,
                    'point_name': camera.point.name or f"Punto {camera.point_id}",
                    'type': camera.type,
                    'direction': camera.direction,
                    'photo_filename': camera.photo_filename,
                    'created_at': camera.created_at,
                    'coordinates': coordinates,
                    'has_own_coordinates': camera.has_own_coordinates(),
                    'location_source': camera.location_source,
                    'location_accuracy': camera.location_accuracy,
                    'point_city': camera.point.city,
                    'point_source': camera.point.source
                }
                cameras_data.append(camera_data)

        except Exception as e:
            current_app.logger.error(f"Error obteniendo cámaras: {e}")
            cameras_data = []
            total_cameras = 0

    return render_template('cameras/list_cameras.html',
                         title="Lista de Cámaras",
                         form=form,
                         cameras_data=cameras_data,
                         total_cameras=total_cameras)

# --- RUTA DE EXPORTACIÓN ---
@bp.route('/export/csv')
@login_required
def export_csv():
    """Exporta todos los puntos a un archivo CSV."""
    try:
        points = Point.query.order_by(Point.id).all()
        if not points:
            flash("No hay puntos para exportar.", "warning")
            return redirect(url_for('points.list_all'))

        si = StringIO()
        fieldnames = ['id', 'name', 'city', 'latitude', 'longitude', 'status', 'description',
                      'created_at', 'updated_at', 'image_count', 'source']  # 👈 incluye 'source'
        writer = csv.DictWriter(si, fieldnames=fieldnames, quoting=csv.QUOTE_MINIMAL)

        writer.writeheader()
        for point in points:
            point_dict = point.to_dict()
            point_dict['created_at'] = point.created_at.isoformat() if point.created_at else ''
            point_dict['updated_at'] = point.updated_at.isoformat() if point.updated_at else ''
            row_data = {key: point_dict.get(key, '') for key in fieldnames}
            writer.writerow(row_data)

        output = si.getvalue()
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        filename = f"export_puntos_{timestamp}.csv"
        return Response(
            output,
            mimetype="text/csv",
            headers={"Content-Disposition": f"attachment;filename={filename}"}
        )
    except Exception as e:
        current_app.logger.error(f"Error al exportar puntos a CSV: {e}")
        flash('Error al generar el archivo CSV.', 'danger')
        return redirect(url_for('points.list_all'))


@bp.route('/import_csv', methods=['POST'])
@login_required
def import_csv():
    import unicodedata  # Para normalizar caracteres

    def sanitize_coordinate(raw_str):
        """Corrige coordenadas mal formateadas como '3.898.285.755.792.330' a '-38.98285755792330'."""
        digits = ''.join(c for c in raw_str if c.isdigit())
        if raw_str.strip().startswith('-'):
            digits = '-' + digits
        try:
            if len(digits) > 2:
                return float(digits[:3] + '.' + digits[3:])
            return float(digits)
        except ValueError:
            return None

    def normalize_text(text):
        """Reemplaza caracteres problemáticos y normaliza acentos."""
        if not text:
            return None
        # Reemplazos específicos
        replacements = {
            'Nº': 'Nro.',
            '°': '',
            'ª': 'a',
            'º': 'o',
        }
        for old, new in replacements.items():
            text = text.replace(old, new)

        # Normalizar acentos (ej. árbol → arbol)
        text = unicodedata.normalize('NFKD', text)
        text = ''.join(c for c in text if not unicodedata.combining(c))

        return text.strip()

    form = FileImportForm()
    if form.validate_on_submit():
        csv_file = form.csv_file.data
        if csv_file:
            try:
                content = csv_file.read()
                try:
                    decoded = content.decode('utf-8-sig')
                except UnicodeDecodeError:
                    decoded = content.decode('latin1')  # Fallback

                csv_data = StringIO(decoded)
                reader = csv.DictReader(csv_data, delimiter=';')
                print("ENCABEZADOS CSV:", reader.fieldnames)

                count = 0
                for row in reader:
                    if 'latitude' not in row or 'longitude' not in row or 'name' not in row:
                        continue

                    lat = sanitize_coordinate(row.get('latitude', ''))
                    lon = sanitize_coordinate(row.get('longitude', ''))

                    if lat is None or lon is None:
                        current_app.logger.warning(f"Coordenadas inválidas: {row.get('latitude')} / {row.get('longitude')}")
                        continue

                    new_point = Point(
                        name=normalize_text(row.get('name', '')),
                        city=normalize_text(row.get('city', '')),
                        latitude=lat,
                        longitude=lon,
                        status=row.get('status', 'azul').strip(),
                        description=normalize_text(row.get('description', '')),
                        source=normalize_text(row.get('source', ''))
                    )
                    db.session.add(new_point)
                    count += 1

                db.session.commit()
                flash(f"Se importaron correctamente {count} puntos desde CSV.", "success")
                current_app.logger.info(f"{count} puntos importados desde CSV por {current_user.username}")

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error importando CSV: {e}")
                flash("Error al importar el archivo CSV.", "danger")
    else:
        flash("Formulario inválido o archivo no seleccionado.", "warning")

    return redirect(url_for('points.list_all'))

@bp.route('/import_dbf', methods=['POST'])
@login_required
def import_dbf():
    from dbfread import DBF
    form = FileImportForm()
    if form.validate_on_submit():
        dbf_file = form.dbf_file.data
        if dbf_file:
            try:
                filename = dbf_file.filename.rsplit('.', 1)[0].strip().lower()
                temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], secure_filename(dbf_file.filename))
                dbf_file.save(temp_path)

                table = DBF(temp_path, load=True, encoding='latin1')
                for row in table:
                    new_point = Point(
                        name=row.get("NAME") or row.get("DIRECCION"),
                        city=row.get("CITY") or row.get("CIUDAD"),
                        latitude=float(row.get("LAT") or row.get("LATITUD")),
                        longitude=float(row.get("LON") or row.get("LONGITUD")),
                        status=row.get("STATUS") or "azul",
                        description=row.get("DESCRIP") or "",
                        source=filename  # 👈 importante
                    )
                    db.session.add(new_point)
                db.session.commit()
                os.remove(temp_path)
                flash("Archivo DBF importado correctamente.", "success")
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error importando DBF: {e}")
                flash("Error al importar el archivo DBF.", "danger")
    else:
        flash("Formulario inválido o archivo no seleccionado.", "warning")

    return redirect(url_for('points.list_all'))

@bp.route('/camera/<int:camera_id>/save_annotations', methods=['POST'])
@login_required
def save_camera_annotations(camera_id):
    camera = db.session.get(Camera, camera_id)
    if not camera:
        return jsonify({'success': False, 'message': 'Cámara no encontrada'}), 404

    try:
        annotations_json_str = None
        if request.is_json:
            annotations_json_str = json.dumps(request.get_json())
        else:
            annotations_json_str = request.form.get('annotations_json')
            json.loads(annotations_json_str)  # Valida
        camera.annotations_json = annotations_json_str
        camera.updated_at = datetime.utcnow()
        db.session.commit()
        return jsonify({'success': True, 'message': 'Anotaciones de cámara guardadas correctamente'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@bp.route('/export/pdf/<int:point_id>')
@login_required
def export_point_pdf(point_id):
    """Genera y descarga un reporte PDF para un punto específico."""
    current_app.logger.info(f"Usuario {current_user.username} solicitó reporte PDF para punto {point_id}")
    try:
        # Llama a la función generadora
        pdf_buffer = generate_point_report_pdf(point_id)

        # Obtener nombre del punto para el nombre del archivo
        point = db.session.get(Point, point_id) # Ya sabemos que existe por generate_point_report_pdf
        safe_name = "".join(c if c.isalnum() else "_" for c in (point.name or f"punto_{point_id}"))
        filename = f"reporte_{safe_name}.pdf"

        current_app.logger.info(f"Reporte PDF generado exitosamente para punto {point_id}. Nombre archivo: {filename}")

        # Crear la respuesta HTTP
        return Response(
            pdf_buffer,
            mimetype='application/pdf',
            headers={
                'Content-Disposition': f'attachment; filename={filename}'
            }
        )

    except ValueError as ve: # Error si el punto no existe
        current_app.logger.warning(f"Intento de generar PDF para punto no existente {point_id}: {ve}")
        flash(str(ve), 'warning')
        # Redirigir a la lista o a la página anterior si es posible
        referrer = request.referrer
        if referrer and 'points/list' in referrer:
             return redirect(url_for('points.list_all'))
        elif referrer and f'/points/{point_id}' in referrer:
            return redirect(url_for('points.detail', point_id=point_id))
        else:
            return redirect(url_for('main.index')) # Fallback

    except Exception as e:
        current_app.logger.error(f"Error generando reporte PDF para punto {point_id}: {e}", exc_info=True)
        flash('Ocurrió un error inesperado al generar el reporte PDF.', 'danger')
        # Redirigir de forma segura
        referrer = request.referrer
        if referrer and f'/points/{point_id}' in referrer:
             return redirect(url_for('points.detail', point_id=point_id))
        else:
            return redirect(url_for('points.list_all')) # Fallback a la lista

# --- NUEVA RUTA PARA ACTUALIZAR COORDENADAS DE CÁMARAS ---
@bp.route('/camera/<int:camera_id>/update_coordinates', methods=['POST'])
@login_required
def update_camera_coordinates(camera_id):
    """Actualiza las coordenadas de una cámara via AJAX."""
    try:
        camera = Camera.query.get_or_404(camera_id)

        # Verificar que el usuario tenga permisos (opcional, ajustar según tus reglas)
        # if not current_user.can_edit_point(camera.point):
        #     return jsonify({'success': False, 'error': 'Sin permisos'}), 403

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No se recibieron datos'}), 400

        latitude = data.get('latitude')
        longitude = data.get('longitude')

        # Validar coordenadas
        if latitude is None or longitude is None:
            return jsonify({'success': False, 'error': 'Coordenadas requeridas'}), 400

        try:
            latitude = float(latitude)
            longitude = float(longitude)
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': 'Coordenadas inválidas'}), 400

        # Validar rangos
        if not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
            return jsonify({'success': False, 'error': 'Coordenadas fuera de rango'}), 400

        # Actualizar coordenadas
        camera.latitude = latitude
        camera.longitude = longitude
        camera.location_source = 'manual'  # Marcar como editado manualmente
        camera.location_accuracy = None    # Limpiar precisión GPS
        camera.updated_at = datetime.utcnow()

        db.session.commit()

        current_app.logger.info(f"Coordenadas de cámara {camera_id} actualizadas por {current_user.username}: {latitude}, {longitude}")

        return jsonify({
            'success': True,
            'message': f'Coordenadas de cámara #{camera_id} actualizadas',
            'camera': {
                'id': camera.id,
                'latitude': camera.latitude,
                'longitude': camera.longitude,
                'location_source': camera.location_source
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error actualizando coordenadas de cámara {camera_id}: {e}")
        return jsonify({'success': False, 'error': 'Error interno del servidor'}), 500

# --- NUEVA RUTA PARA ACTUALIZAR COORDENADAS DE PUNTOS ---
@bp.route('/<int:point_id>/update_coordinates', methods=['POST'])
@login_required
def update_point_coordinates(point_id):
    """Actualiza las coordenadas de un punto via AJAX."""
    try:
        point = get_point_or_404(point_id)

        # Verificar que el usuario tenga permisos (opcional, ajustar según tus reglas)
        # if not current_user.can_edit_point(point):
        #     return jsonify({'success': False, 'error': 'Sin permisos'}), 403

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No se recibieron datos'}), 400

        latitude = data.get('latitude')
        longitude = data.get('longitude')

        # Validar coordenadas
        if latitude is None or longitude is None:
            return jsonify({'success': False, 'error': 'Coordenadas requeridas'}), 400

        try:
            latitude = float(latitude)
            longitude = float(longitude)
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': 'Coordenadas inválidas'}), 400

        # Validar rangos
        if not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
            return jsonify({'success': False, 'error': 'Coordenadas fuera de rango'}), 400

        # Actualizar coordenadas
        point.latitude = latitude
        point.longitude = longitude
        point.updated_at = datetime.utcnow()

        db.session.commit()

        current_app.logger.info(f"Coordenadas de punto {point_id} actualizadas por {current_user.username}: {latitude}, {longitude}")

        return jsonify({
            'success': True,
            'message': f'Coordenadas del punto actualizadas',
            'point': {
                'id': point.id,
                'latitude': point.latitude,
                'longitude': point.longitude,
                'name': point.name
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error actualizando coordenadas de punto {point_id}: {e}")
        return jsonify({'success': False, 'error': 'Error interno del servidor'}), 500