// /static/js/index.js

document.addEventListener('DOMContentLoaded', function () {
    // const nodeId = window.nodeId; // Ya están globales
    // const username = window.username; // Ya están globales
    // const nodeName = window.nodeName; // Ya está global

    console.log(`(index.js) Conectando como ${window.username} al nodo ${window.nodeId} en el servidor: ${window.socketIoServerUrl}`);

    // Usa la URL del servidor Socket.IO de la VM y el namespace /node
    const socket = io.connect(window.socketIoServerUrl + '/node', { // <--- MODIFICADO
        transports: ['websocket'], // Forzar websocket es una buena práctica
        query: {
            node_id: String(window.nodeId),
            username: window.username
        }
    });

    // Referencia al botón de transmisión de audio
    const transmitAudioButton = document.getElementById('transmitAudio');

    socket.on('connect', () => {
        console.log(`(index.js) Conexión Socket.IO exitosa a ${window.socketIoServerUrl}/node como ${window.username}`);
        const nodeInfo = document.getElementById('node-info');
        if (nodeInfo && window.nodeName) { // Verifica si nodeName existe
            nodeInfo.textContent = `Conectado al nodo ${window.nodeName} (ID: ${window.nodeId})`;
        } else if (nodeInfo) {
            nodeInfo.textContent = `Conectado al nodo ID: ${window.nodeId}`;
        }
    });

    // Resto del código permanece igual...
    socket.on('update_users', (users) => {
        console.log('Usuarios conectados:', users);
        const uniqueUsers = [...new Set(users)];
        const usersList = document.getElementById('users_list');
        if (usersList) {
            usersList.innerHTML = '';
            uniqueUsers.forEach(user => {
                const userItem = document.createElement('li');
                userItem.textContent = user;
                usersList.appendChild(userItem);
            });
        }
    });

    const volumeMeter = document.getElementById('volume_meter');
    socket.on('audio_level', (level) => {
        if (volumeMeter) {
            volumeMeter.style.width = `${level}%`;
            volumeMeter.style.backgroundColor = level >= 50 ? 'red' : 'green';
        }
        console.log('Nivel de audio:', level);
    });

    socket.on('audio_event', (data) => {
        console.log('Evento de audio recibido:', data);
        const eventsList = document.getElementById('events_list');
        if (eventsList) {
            const newEvent = document.createElement('li');
            newEvent.textContent = `Audio recibido de ${data.user}`;
            eventsList.appendChild(newEvent);
        }
    });

    socket.on('global_audio_start', (data) => {
        if (data.node_id === String(nodeId)) {
            console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio iniciado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/rojo.png';
            }
        }
    });

    socket.on('global_audio_end', (data) => {
        if (data.node_id === String(nodeId)) {
            console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio finalizado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/verde.png';
            }
        }
    });

    socket.emit('get_connected_users', { node_id: String(nodeId) });
});
