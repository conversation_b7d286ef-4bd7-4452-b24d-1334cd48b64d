# 🚀 IMPLEMENTAC<PERSON><PERSON>N SISTEMA ACADÉMICO - GUÍA PASO A PASO

## 📋 PLAN DE IMPLEMENTACIÓN

### **FASE 1: PREPAR<PERSON>IÓN Y BASE DE DATOS** (Semana 1)
### **FASE 2: MODELOS Y SERVICIOS** (Semana 2)
### **FASE 3: RUTAS Y FORMULARIOS** (Semana 3)
### **FASE 4: TEMPLATES Y FRONTEND** (Semana 4)
### **FASE 5: TESTING Y REFINAMIENTO** (Semana 5)

---

## 🔧 **FASE 1: PREPARACIÓN Y BASE DE DATOS**

### **PASO 1.1: Actualizar requirements.txt**
```bash
# Añadir estas dependencias al final de requirements.txt
Flask-WTF==1.1.1
WTForms==3.0.1
```

### **PASO 1.2: Instalar dependencias**
```bash
pip install Flask-WTF WTForms
```

### **PASO 1.3: Actualizar roles en la base de datos**
```sql
-- Ejecutar en MySQL para añadir nuevos roles
ALTER TABLE users MODIFY COLUMN role ENUM(
    'administrador', 
    'secretaria', 
    'pastorado', 
    'miembro',
    'instituto',
    'rector',
    'profesor_corporativo'
) DEFAULT 'miembro';
```

### **PASO 1.4: Crear migración de base de datos**
```bash
# Crear archivo de migración
flask db migrate -m "Add academic system tables"
```

### **PASO 1.5: Aplicar migración**
```bash
flask db upgrade
```

---

## 📊 **FASE 2: MODELOS Y SERVICIOS**

### **PASO 2.1: Actualizar models.py**

Añadir al final de `app/models.py`:

```python
# ============================================================================
# SISTEMA ACADÉMICO - MODELOS
# ============================================================================

# Importar modelos académicos básicos
from datetime import datetime, date
from sqlalchemy import JSON

# ESCUELAS BÍBLICAS (Nivel Básico)
class AcademicSchool(db.Model):
    __tablename__ = 'academic_schools'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=False)
    director_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    max_students = db.Column(db.Integer, default=50)
    auto_enrollment = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    church = db.relationship('Church', backref='academic_schools')
    director = db.relationship('User', backref='directed_schools')

class AcademicCurriculum(db.Model):
    __tablename__ = 'academic_curriculums'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    school_id = db.Column(db.Integer, db.ForeignKey('academic_schools.id'), nullable=False)
    duration_months = db.Column(db.Integer, nullable=False)
    total_credits = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    school = db.relationship('AcademicSchool', backref='curriculums')

class AcademicSubject(db.Model):
    __tablename__ = 'academic_subjects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(20), nullable=False)
    description = db.Column(db.Text)
    curriculum_id = db.Column(db.Integer, db.ForeignKey('academic_curriculums.id'), nullable=False)
    credits = db.Column(db.Integer, default=1)
    level = db.Column(db.Integer, default=1)
    prerequisites = db.Column(db.Text)
    is_mandatory = db.Column(db.Boolean, default=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    curriculum = db.relationship('AcademicCurriculum', backref='subjects')
    teacher = db.relationship('User', backref='taught_subjects')

class AcademicEnrollment(db.Model):
    __tablename__ = 'academic_enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    school_id = db.Column(db.Integer, db.ForeignKey('academic_schools.id'), nullable=False)
    curriculum_id = db.Column(db.Integer, db.ForeignKey('academic_curriculums.id'), nullable=False)
    enrollment_date = db.Column(db.Date, default=date.today)
    status = db.Column(db.Enum('active', 'completed', 'dropped', 'suspended'), default='active')
    completion_date = db.Column(db.Date, nullable=True)
    final_grade = db.Column(db.Float, nullable=True)
    certificate_issued = db.Column(db.Boolean, default=False)
    certificate_date = db.Column(db.Date, nullable=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    student = db.relationship('User', backref='academic_enrollments')
    school = db.relationship('AcademicSchool', backref='enrollments')
    curriculum = db.relationship('AcademicCurriculum', backref='enrollments')

class AcademicGrade(db.Model):
    __tablename__ = 'academic_grades'
    
    id = db.Column(db.Integer, primary_key=True)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('academic_enrollments.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('academic_subjects.id'), nullable=False)
    grade_value = db.Column(db.Float, nullable=False)
    grade_type = db.Column(db.Enum('partial', 'final', 'makeup'), default='partial')
    evaluation_date = db.Column(db.Date, default=date.today)
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    comments = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    enrollment = db.relationship('AcademicEnrollment', backref='grades')
    subject = db.relationship('AcademicSubject', backref='grades')
    teacher = db.relationship('User', backref='assigned_grades')

# INSTITUTO PASTORAL (Nivel Corporativo)
class PastoralInstitute(db.Model):
    __tablename__ = 'pastoral_institutes'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    rector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    academic_coordinator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    rector = db.relationship('User', foreign_keys=[rector_id], backref='rectored_institutes')
    coordinator = db.relationship('User', foreign_keys=[academic_coordinator_id], backref='coordinated_institutes')

class PastoralProgram(db.Model):
    __tablename__ = 'pastoral_programs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    institute_id = db.Column(db.Integer, db.ForeignKey('pastoral_institutes.id'), nullable=False)
    program_type = db.Column(db.Enum('diploma', 'certificate', 'specialization', 'masters'), default='certificate')
    duration_months = db.Column(db.Integer, nullable=False)
    total_credits = db.Column(db.Integer, default=0)
    target_roles = db.Column(db.JSON)
    min_ministry_years = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    max_students = db.Column(db.Integer, default=30)
    requires_approval = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    institute = db.relationship('PastoralInstitute', backref='programs')

class PastoralEnrollment(db.Model):
    __tablename__ = 'pastoral_enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    program_id = db.Column(db.Integer, db.ForeignKey('pastoral_programs.id'), nullable=False)
    enrollment_date = db.Column(db.Date, default=date.today)
    status = db.Column(db.Enum('pending', 'approved', 'active', 'completed', 'dropped'), default='pending')
    approved_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    completion_date = db.Column(db.Date, nullable=True)
    final_gpa = db.Column(db.Float, nullable=True)
    certificate_issued = db.Column(db.Boolean, default=False)
    certificate_number = db.Column(db.String(50), unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    student = db.relationship('User', foreign_keys=[student_id], backref='pastoral_enrollments')
    program = db.relationship('PastoralProgram', backref='enrollments')
    approved_by = db.relationship('User', foreign_keys=[approved_by_id])
```

### **PASO 2.2: Crear servicios académicos**

Crear archivo `app/academic_services.py`:

```python
# app/academic_services.py
from app.models import *
from app import db
from datetime import datetime, date
import secrets

class AcademicService:
    @staticmethod
    def create_school(church_id, name, description, director_id=None, **kwargs):
        school = AcademicSchool(
            name=name,
            description=description,
            church_id=church_id,
            director_id=director_id,
            **kwargs
        )
        db.session.add(school)
        db.session.commit()
        return school
    
    @staticmethod
    def enroll_student(student_id, school_id, curriculum_id):
        existing = AcademicEnrollment.query.filter_by(
            student_id=student_id,
            school_id=school_id,
            status='active'
        ).first()
        
        if existing:
            raise ValueError("El estudiante ya está matriculado")
        
        enrollment = AcademicEnrollment(
            student_id=student_id,
            school_id=school_id,
            curriculum_id=curriculum_id
        )
        db.session.add(enrollment)
        db.session.commit()
        return enrollment

class PastoralAcademicService:
    @staticmethod
    def create_institute(name, description, rector_id=None):
        institute = PastoralInstitute(
            name=name,
            description=description,
            rector_id=rector_id
        )
        db.session.add(institute)
        db.session.commit()
        return institute
    
    @staticmethod
    def enroll_pastor(pastor_id, program_id):
        enrollment = PastoralEnrollment(
            student_id=pastor_id,
            program_id=program_id,
            status='pending'
        )
        db.session.add(enrollment)
        db.session.commit()
        return enrollment
```

---

## 🛣️ **FASE 3: RUTAS Y FORMULARIOS**

### **PASO 3.1: Crear formularios académicos**

Crear archivo `app/academic_forms.py`:

```python
# app/academic_forms.py
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, BooleanField, DateField, FloatField
from wtforms.validators import DataRequired, Optional, NumberRange

class AcademicSchoolForm(FlaskForm):
    name = StringField('Nombre de la Escuela', validators=[DataRequired()])
    description = TextAreaField('Descripción')
    church_id = SelectField('Iglesia', coerce=int, validators=[DataRequired()])
    director_id = SelectField('Director', coerce=int, validators=[Optional()])
    start_date = DateField('Fecha de Inicio', validators=[DataRequired()])
    max_students = IntegerField('Máximo Estudiantes', default=50)
    auto_enrollment = BooleanField('Matrícula Automática')

class PastoralProgramForm(FlaskForm):
    name = StringField('Nombre del Programa', validators=[DataRequired()])
    description = TextAreaField('Descripción')
    program_type = SelectField('Tipo', choices=[
        ('certificate', 'Certificado'),
        ('diploma', 'Diploma'),
        ('specialization', 'Especialización'),
        ('masters', 'Maestría')
    ])
    duration_months = IntegerField('Duración (meses)', validators=[DataRequired()])
    max_students = IntegerField('Máximo Estudiantes', default=30)

class AcademicGradeForm(FlaskForm):
    grade_value = FloatField('Calificación', validators=[DataRequired(), NumberRange(min=1, max=5)])
    grade_type = SelectField('Tipo', choices=[
        ('partial', 'Parcial'),
        ('final', 'Final'),
        ('makeup', 'Recuperación')
    ])
    comments = TextAreaField('Comentarios')
```

### **PASO 3.2: Añadir rutas académicas a routes.py**

Añadir al final de `app/routes.py`:

```python
# ============================================================================
# RUTAS ACADÉMICAS
# ============================================================================

from app.academic_services import AcademicService, PastoralAcademicService
from app.academic_forms import AcademicSchoolForm, PastoralProgramForm, AcademicGradeForm

# Decorador para roles académicos
def academic_role_required(*roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if current_user.role not in roles:
                flash('No tienes permisos para acceder a esta página.', 'danger')
                return redirect(url_for('routes.dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# DASHBOARD ACADÉMICO PRINCIPAL
@routes_bp.route('/academic')
@login_required
def academic_dashboard():
    if current_user.role in ['administrador', 'secretaria', 'instituto']:
        # Vista administrativa
        schools = AcademicSchool.query.all()
        pastoral_programs = PastoralProgram.query.all()
        
        stats = {
            'total_schools': len(schools),
            'total_programs': len(pastoral_programs),
            'active_students': AcademicEnrollment.query.filter_by(status='active').count(),
            'pastoral_students': PastoralEnrollment.query.filter_by(status='active').count()
        }
        
        return render_template('academic/dashboard.html', 
                             schools=schools, 
                             pastoral_programs=pastoral_programs,
                             stats=stats)
    
    elif current_user.role == 'pastorado':
        # Vista de pastor
        my_schools = AcademicSchool.query.filter_by(church_id=current_user.church_id).all()
        my_pastoral_enrollments = PastoralEnrollment.query.filter_by(student_id=current_user.id).all()
        
        return render_template('academic/pastor_dashboard.html',
                             schools=my_schools,
                             enrollments=my_pastoral_enrollments)
    
    else:
        # Vista de miembro/estudiante
        my_enrollments = AcademicEnrollment.query.filter_by(student_id=current_user.id).all()
        return render_template('academic/student_dashboard.html', enrollments=my_enrollments)

# GESTIÓN DE ESCUELAS BÍBLICAS
@routes_bp.route('/academic/schools')
@login_required
@academic_role_required('administrador', 'secretaria', 'instituto')
def academic_schools():
    schools = AcademicSchool.query.all()
    return render_template('academic/schools.html', schools=schools)

@routes_bp.route('/academic/schools/create', methods=['GET', 'POST'])
@login_required
@academic_role_required('administrador', 'secretaria')
def create_academic_school():
    form = AcademicSchoolForm()
    form.church_id.choices = [(c.id, c.name) for c in Church.query.all()]
    form.director_id.choices = [(0, 'Sin Director')] + [
        (u.id, f"{u.first_name} {u.last_name}") 
        for u in User.query.filter_by(role='pastorado').all()
    ]
    
    if form.validate_on_submit():
        school = AcademicService.create_school(
            church_id=form.church_id.data,
            name=form.name.data,
            description=form.description.data,
            director_id=form.director_id.data if form.director_id.data != 0 else None,
            start_date=form.start_date.data,
            max_students=form.max_students.data,
            auto_enrollment=form.auto_enrollment.data
        )
        flash('Escuela creada exitosamente', 'success')
        return redirect(url_for('routes.academic_schools'))
    
    return render_template('academic/create_school.html', form=form)

# GESTIÓN DE PROGRAMAS PASTORALES
@routes_bp.route('/academic/pastoral')
@login_required
@academic_role_required('administrador', 'instituto', 'rector')
def pastoral_programs():
    programs = PastoralProgram.query.all()
    return render_template('academic/pastoral_programs.html', programs=programs)

@routes_bp.route('/academic/pastoral/enroll/<int:program_id>')
@login_required
def enroll_in_pastoral_program(program_id):
    if current_user.role not in ['pastorado', 'superintendente']:
        flash('Solo pastores pueden matricularse en programas pastorales', 'danger')
        return redirect(url_for('routes.academic_dashboard'))
    
    try:
        enrollment = PastoralAcademicService.enroll_pastor(current_user.id, program_id)
        flash('Solicitud de matrícula enviada. Pendiente de aprobación.', 'info')
    except ValueError as e:
        flash(str(e), 'danger')
    
    return redirect(url_for('routes.academic_dashboard'))

# APROBACIÓN DE MATRÍCULAS PASTORALES
@routes_bp.route('/academic/pastoral/approve/<int:enrollment_id>')
@login_required
@academic_role_required('instituto', 'rector')
def approve_pastoral_enrollment(enrollment_id):
    enrollment = PastoralEnrollment.query.get_or_404(enrollment_id)
    enrollment.status = 'approved'
    enrollment.approved_by_id = current_user.id
    db.session.commit()
    
    flash(f'Matrícula de {enrollment.student.first_name} {enrollment.student.last_name} aprobada', 'success')
    return redirect(url_for('routes.pastoral_programs'))
```

---

## 🎨 **FASE 4: TEMPLATES Y FRONTEND**

### **PASO 4.1: Crear estructura de templates**

```bash
mkdir -p app/templates/academic
```

### **PASO 4.2: Template principal del dashboard académico**

Crear `app/templates/academic/dashboard.html`:

```html
{% extends "base.html" %}

{% block title %}Sistema Académico - Intranet IMPA{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-graduation-cap"></i> Sistema Académico
            </h1>
        </div>
    </div>
    
    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                        Escuelas Bíblicas
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {{ stats.total_schools }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        Programas Pastorales
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {{ stats.total_programs }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        Estudiantes Activos
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {{ stats.active_students }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        Pastores en Formación
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {{ stats.pastoral_students }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Acciones rápidas -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Escuelas Bíblicas
                        {% if current_user.role in ['administrador', 'secretaria'] %}
                        <a href="{{ url_for('routes.create_academic_school') }}" class="btn btn-primary btn-sm float-right">
                            <i class="fas fa-plus"></i> Nueva Escuela
                        </a>
                        {% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('routes.academic_schools') }}" class="btn btn-outline-primary">
                        <i class="fas fa-school"></i> Gestionar Escuelas
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Formación Pastoral</h6>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('routes.pastoral_programs') }}" class="btn btn-outline-success">
                        <i class="fas fa-user-graduate"></i> Programas Pastorales
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
```

---

## 🧪 **FASE 5: TESTING Y CONFIGURACIÓN**

### **PASO 5.1: Crear datos de prueba**

Crear archivo `setup_academic_data.py`:

```python
# setup_academic_data.py
from app import create_app, db
from app.models import *
from app.academic_services import AcademicService, PastoralAcademicService
from datetime import date, datetime

def setup_academic_system():
    app = create_app()
    
    with app.app_context():
        print("🎓 Configurando Sistema Académico...")
        
        # 1. Crear Instituto Pastoral
        institute = PastoralAcademicService.create_institute(
            name="Instituto de Formación Pastoral IMPA",
            description="Instituto corporativo para formación de pastores"
        )
        print(f"✅ Instituto creado: {institute.name}")
        
        # 2. Crear escuelas básicas para iglesias existentes
        churches = Church.query.all()
        schools_created = 0
        
        for church in churches:
            # Verificar si ya tiene escuela
            existing_school = AcademicSchool.query.filter_by(church_id=church.id).first()
            
            if not existing_school:
                school = AcademicService.create_school(
                    church_id=church.id,
                    name=f"Escuela Bíblica Básica - {church.name}",
                    description="Fundamentos de la fe cristiana",
                    start_date=date.today(),
                    max_students=50,
                    auto_enrollment=True
                )
                schools_created += 1
                print(f"✅ Escuela creada para: {church.name}")
        
        print(f"\n🎓 Sistema Académico configurado:")
        print(f"   - 1 Instituto Pastoral")
        print(f"   - {schools_created} Escuelas Bíblicas")
        print(f"   - Sistema listo para usar")

if __name__ == '__main__':
    setup_academic_system()
```

### **PASO 5.2: Ejecutar configuración inicial**

```bash
python setup_academic_data.py
```

### **PASO 5.3: Crear usuario con rol Instituto**

```sql
-- Ejecutar en MySQL para crear usuario Instituto
UPDATE users SET role = 'instituto' 
WHERE email = '<EMAIL>';
```

### **PASO 5.4: Actualizar menú de navegación**

Añadir en `app/templates/base.html` en la sección del menú:

```html
<!-- Añadir en el menú principal -->
{% if current_user.role in ['administrador', 'secretaria', 'instituto', 'pastorado'] %}
<li class="nav-item">
    <a class="nav-link" href="{{ url_for('routes.academic_dashboard') }}">
        <i class="fas fa-graduation-cap"></i>
        <span>Sistema Académico</span>
    </a>
</li>
{% endif %}
```

---

## ✅ **CHECKLIST DE IMPLEMENTACIÓN**

### **Semana 1: Base**
- [ ] Actualizar requirements.txt
- [ ] Instalar dependencias
- [ ] Actualizar roles en BD
- [ ] Crear migración
- [ ] Aplicar migración

### **Semana 2: Modelos**
- [ ] Añadir modelos a models.py
- [ ] Crear academic_services.py
- [ ] Crear academic_forms.py
- [ ] Testing de modelos

### **Semana 3: Rutas**
- [ ] Añadir rutas a routes.py
- [ ] Implementar decoradores
- [ ] Testing de rutas
- [ ] Validar formularios

### **Semana 4: Frontend**
- [ ] Crear templates académicos
- [ ] Actualizar menú navegación
- [ ] Styling y responsive
- [ ] Testing UI

### **Semana 5: Finalización**
- [ ] Ejecutar setup_academic_data.py
- [ ] Crear usuarios Instituto
- [ ] Testing completo
- [ ] Documentación

---

## 🚀 **PRÓXIMOS PASOS**

1. **Empezar con Fase 1** - Preparación y BD
2. **Validar cada paso** antes de continuar
3. **Testing incremental** en cada fase
4. **Backup de BD** antes de cambios importantes

¿Estás listo para empezar con la **Fase 1**? ¡Vamos paso a paso! 🎓
