├── README.md
├── cliente.py
├── config.json
├── config.py
├── dig_vhf.py
├── icon_tray.py
├── monitor.py
├── ptt_control.py
├── ptt_test.py
├── requirements.txt
├── static
│   ├── js
│   │   ├── dig-vhf.js
│   │   ├── index.js
│   ├── styles.css
├── templates
│   ├── config_form.html
│   └── index.html




Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\cliente.py
Contenido:
#!/usr/bin/env python3
# client.py

from flask import Flask, render_template, request, jsonify, redirect, url_for, current_app
from flask_socketio import SocketIO
from flask_cors import CORS
from ptt_control import set_ptt
import json
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'

CORS(app, resources={r"/*": {"origins": "*"}})

socketio = SocketIO(app, cors_allowed_origins="*")

serial_connection = None

def load_config():
    if not os.path.exists('config.json'):
        return None
    try:
        with open('config.json', 'r', encoding='utf-8') as file:
            config = json.load(file)
            return config
    except FileNotFoundError:
        print("Error: FileNotFoundError en load_config().")
        return None

@app.route('/')
def index():
    config = load_config()
    if config:
        return render_template('index.html', node_id=config['node_id'], username=config['username'])
    else:
        return redirect(url_for('config_form'))

@app.route('/config_form')
def config_form():
    return render_template('config_form.html')

@app.route('/save_config', methods=['POST'])
def save_user_config():
    config = {
        "username": request.form['username'],
        "password": request.form['password'],
        "node_id": request.form['node_id'],
        "input_device_index": int(request.form['input_device_index']),
        "volume_level": int(request.form['volume_level']),
        "port_number": request.form['port_number']
    }
    save_config(config)
    return redirect(url_for('index'))

def save_config(config):
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)

@app.route('/ptt_event', methods=['POST'])
def handle_ptt_event():
    global serial_connection
    data = request.json
    config = load_config()
    if config and 'ptt_state' in data:
        if data['ptt_state']:
            serial_connection = set_ptt(True, config, serial_connection)
        else:
            serial_connection = set_ptt(False, config, serial_connection)
        return jsonify({"status": "success", "ptt_state": data['ptt_state']})
    else:
        return jsonify({"status": "error", "message": "Invalid request"}), 400

@socketio.on('receive_audio')
def handle_receive_audio(data):
    config = load_config()
    if config:
        pass  # Aquí puedes procesar el audio si lo consideras necesario

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\config.json
Contenido:
{
    "username": "911-viedma",
    "password": "123",
    "node_id": "41",
    "input_device_index": 0,
    "volume_level": 5,
    "port_number": "1"
}

Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\config.py
Contenido:
#!/usr/bin/env python3
# config.py

import json
import os
import pyaudio
from flask import Flask, render_template, request, redirect, url_for

CONFIG_FILE = 'config.json'
app = Flask(__name__)

def load_config():
    if not os.path.exists(CONFIG_FILE):
        return {}
    else:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config

def save_config(config):
    config['node_url'] = "https://************/node"  # Valor predefinido
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)

@app.route('/')
def index():
    config = load_config()
    input_devices = list_audio_devices()
    return render_template('config_form.html', config=config, input_devices=input_devices)

@app.route('/save_config', methods=['POST'])
def save_user_config():
    config = {
        "username": request.form['username'],
        "password": request.form['password'],
        "node_id": request.form['node_id'],
        "input_device_index": int(request.form['input_device_index']),
        "volume_level": int(request.form['volume_level']),
        "port_number": request.form['port_number']
    }
    save_config(config)
    return redirect(url_for('index'))

def list_audio_devices():
    audio = pyaudio.PyAudio()
    input_devices = []
    for i in range(audio.get_device_count()):
        info = audio.get_device_info_by_index(i)
        if info["maxInputChannels"] > 0:
            input_devices.append((i, info['name']))
    audio.terminate()
    if not input_devices:
        input_devices = [(0, "Dispositivo predeterminado")]
    return input_devices

if __name__ == '__main__':
    app.run(debug=True)


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\dig_vhf.py
Contenido:
# dig_vhf.py

import time
import socketio
import json
from ptt_control import set_ptt
import base64
from io import BytesIO
import wave
import simpleaudio as sa

sio = socketio.Client()
serial_connection = None

def load_config():
    try:
        with open('config.json', 'r', encoding='utf-8') as file:
            config = json.load(file)
            if 'port_number' not in config:
                raise ValueError("El archivo de configuración no contiene 'port_number'.")
            return config
    except FileNotFoundError:
        print("Error: config.json no encontrado.")
        return None
    except ValueError as ve:
        print("Error en la configuración:", ve)
        return None

def play_audio(audio_data, config):
    try:
        audio_bytes = base64.b64decode(audio_data)
        audio_stream = BytesIO(audio_bytes)
        activar_ptt(config)
        time.sleep(1)
        with wave.open(audio_stream, 'rb') as wave_obj:
            play_obj = sa.WaveObject(
                wave_obj.readframes(wave_obj.getnframes()),
                wave_obj.getnchannels(),
                wave_obj.getsampwidth(),
                wave_obj.getframerate()
            ).play()
            play_obj.wait_done()
        time.sleep(1)
        desactivar_ptt(config)
    except Exception as e:
        print("Error al reproducir el audio:", e)

def activar_ptt(config):
    global serial_connection
    serial_connection = set_ptt(True, config, serial_connection)

def desactivar_ptt(config):
    global serial_connection
    serial_connection = set_ptt(False, config, serial_connection)

def connect_to_socket(config):
    @sio.event
    def connect():
        pass
    @sio.event
    def disconnect():
        pass
    sio.connect(f"wss://{config['node_url']}",
                headers={'node_id': config['node_id'], 'username': config['username']},
                namespaces=['/node'])

if __name__ == '__main__':
    config = load_config()
    if config:
        try:
            while True:
                time.sleep(0.5)
        except KeyboardInterrupt:
            sio.disconnect()
    else:
        print("Error al cargar la configuración.")


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\icon_tray.py
Contenido:
# /icon_tray.py

import webbrowser
from pystray import Icon, MenuItem as Item, Menu
from PIL import Image
import sys

def open_website(host, port):
    """Abre el navegador web en la dirección del servidor local."""
    webbrowser.open(f"http://{host}:{port}")

def exit_app(icon, item):
    """Cierra la aplicación."""
    icon.stop()
    sys.exit()  # Asegura que la aplicación se cierra correctamente

def create_tray_icon(host, port):
    """Crea el ícono de la bandeja del sistema con opciones."""
    # Cargar una imagen para el icono de la bandeja
    image = Image.open("static/verde.png")

    # Crear el menú con la opción para abrir el sitio web y salir de la aplicación
    menu = Menu(
        Item("Abrir", lambda: open_website(host, port)),
        Item("Salir", exit_app)
    )

    # Crear el icono de la bandeja
    icon = Icon("VHF Node", image, menu=menu)

    # Ejecutar el icono en la bandeja
    icon.run()


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\monitor.py
Contenido:
# monitor.py

import threading
from vhf_dig import capture_audio
from dig_vhf import listen_to_events

def monitor_audio(config):
    # Inicia hilos para monitorizar tanto el micrófono como los eventos
    audio_thread = threading.Thread(target=capture_audio, args=(config,))
    events_thread = threading.Thread(target=listen_to_events, args=(config,))
    
    audio_thread.start()
    events_thread.start()

    audio_thread.join()  # Asegura que los hilos corran indefinidamente
    events_thread.join()


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\ptt_control.py
Contenido:
# ptt_control.py

import serial

def set_ptt(state, config, ser=None):
    com_port = f"COM{config.get('port_number', '1')}"
    try:
        # Si vamos a activar el PTT
        if state:
            if ser is None or not ser.is_open:
                # Abre el puerto serie si no está abierto
                ser = serial.Serial(com_port, baudrate=9600, timeout=1)
                print(f"Puerto {com_port} abierto.", flush=True)
            ser.setDTR(True)  # Activa el DTR para habilitar el PTT
            print(f"PTT activado en {com_port}.", flush=True)
            return ser
        else:
            # Si vamos a desactivar el PTT
            if ser is not None and ser.is_open:
                ser.setDTR(False)  # Desactiva el DTR para deshabilitar el PTT
                print(f"PTT desactivado en {com_port}.", flush=True)
                ser.close()  # Cierra el puerto serie
            return None
    except serial.SerialException as e:
        print(f"Error al abrir el puerto {com_port}: {e}", flush=True)
        return None
    except Exception as e:
        print(f"Error inesperado al manejar el puerto {com_port}: {e}", flush=True)
        return None


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\ptt_test.py
Contenido:
# test_ptt.py

import serial

try:
    ser = serial.Serial('COM1', baudrate=9600, timeout=1)  # Ajusta el puerto y los parámetros según tu configuración
    print(f"Puerto {ser.name} abierto.")
    ser.setDTR(True)  # Activa DTR (simula el PTT)
    input("Presiona Enter para desactivar el PTT...")  # Mantiene el PTT activado hasta que presiones Enter
    ser.setDTR(False)  # Desactiva DTR (desactiva el PTT)
    ser.close()  # Cierra el puerto serie
except serial.SerialException as e:
    print(f"Error al abrir el puerto: {e}")


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\README.md
Contenido:
 # VHF Digital Communicator                                                                                                                                        
                                                                                                                                                                   
 Este proyecto implementa un sistema de comunicación digital para VHF, permitiendo la transmisión y recepción de audio a través de una red. Utiliza WebSockets pa  
 la comunicación en tiempo real y un transceptor VHF para la transmisión de radiofrecuencia.                                                                       
                                                                                                                                                                   
 ## Características                                                                                                                                                
                                                                                                                                                                   
 -   Transmisión de audio en tiempo real.                                                                                                                          
 -   Control de PTT (Push-To-Talk) para la gestión de la transmisión.                                                                                              
 -   Interfaz web para la configuración y el control del sistema.                                                                                                  
 -   Monitoreo y recarga automática de la configuración.                                                                                                           
 -   Icono en la bandeja del sistema para un acceso rápido a las funciones.                                                                                        
                                                                                                                                                                   
 ## Instalación                                                                                                                                                    
                                                                                                                                                                   
 Para instalar las dependencias del proyecto, ejecuta el siguiente comando:                                                                                        
                                                                                                                                                                   
 ```bash                                                                                                                                                           
 pip install -r requirements.txt                                                                                                                                   
                                                                                                                                                                   


                                                                          Configuración

La configuración del sistema se realiza a través del archivo config.json. Este archivo contiene parámetros como el nombre de usuario, la contraseña, la URL del    
nodo, el ID del nodo, el puerto COM para el control del PTT, el índice del dispositivo de entrada de audio y el nivel de volumen para la activación de la 
transmisión.

A continuación, se muestra un ejemplo de la estructura del archivo config.json:

                                                                                                                                                                   
 {                                                                                                                                                                 
     "username": "tu_usuario",                                                                                                                                     
     "password": "tu_contraseña",                                                                                                                                  
     "node_url": "tu_url_de_nodo",                                                                                                                                 
     "node_id": "tu_id_de_nodo",                                                                                                                                   
     "input_device_index": 0,                                                                                                                                      
     "volume_level": 5,                                                                                                                                            
     "port_number": "1"                                                                                                                                            
 }                                                                                                                                                                 
                                                                                                                                                                   


                                                                               Uso

                                                                           Cliente Web

Para iniciar el cliente web, ejecuta el script cliente.py:

                                                                                                                                                                   
 python cliente.py                                                                                                                                                 
                                                                                                                                                                   

Esto iniciará una aplicación Flask que sirve la interfaz de usuario web. Puedes acceder a la interfaz a través de tu navegador web en la dirección 
http://localhost:5000.

                                                                     Captura y Envío de Audio

Para iniciar la captura y el envío de audio, ejecuta el script vhf_dig.py:

                                                                                                                                                                   
 python vhf_dig.py                                                                                                                                                 
                                                                                                                                                                   

Este script se conecta al servidor WebSocket y comienza a monitorear el audio del micrófono. Cuando el volumen del audio supera el umbral configurado, el audio se 
transmite al servidor.

                                                                        Recepción de Audio

Para iniciar la recepción de audio, ejecuta el script dig_vhf.py:

                                                                                                                                                                   
 python dig_vhf.py                                                                                                                                                 
                                                                                                                                                                   

Este script se conecta al servidor WebSocket y escucha los eventos de audio. Cuando se recibe audio, se activa el PTT y se reproduce el audio.

                                                                 Icono de la Bandeja del Sistema

Para iniciar el icono de la bandeja del sistema, ejecuta el script icon_tray.py:

                                                                                                                                                                   
 python icon_tray.py                                                                                                                                               
                                                                                                                                                                   

Esto creará un icono en la bandeja del sistema que te permitirá abrir la interfaz web y salir de la aplicación.

                                                                          Pruebas de PTT

Para probar la funcionalidad del PTT, puedes usar el script ptt_test.py:

                                                                                                                                                                   
 python ptt_test.py                                                                                                                                                
                                                                                                                                                                   

Este script te permitirá activar y desactivar manualmente el PTT.


                                                                          Contribuciones

Las contribuciones a este proyecto son bienvenidas. Si deseas contribuir, por favor, crea un fork del repositorio y envía un pull request con tus cambios.


                                                                             Licencia

Este proyecto está licenciado bajo la Licencia MIT. Consulta el archivo LICENSE para más detalles.

Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\requirements.txt
Contenido:
Flask
pyaudio
numpy
wave
python-socketio[client]
simpleaudio
pyserial

Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\static\js\dig-vhf.js
Contenido:
// /static/js/dig-vhf.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;

    console.log(`Conectado como ${username} al nodo ${nodeId} para Digital a VHF.`);

    const socket = io.connect('https://************:5000/node', {
        transports: ['websocket'],
        query: {
            node_id: String(nodeId),  // Forzar a cadena
            username: username
        }
    });

    let isTransmittingAudio = false;

    socket.on('connect', () => {
        console.log('Conexión exitosa con Socket.IO para Digital a VHF.');
    });

    socket.on('receive_audio', (data) => {
        console.log('Recibiendo audio, activando PTT...', data);
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);
            return;
        }
        if (!data.audio || data.audio.trim() === "") {
            console.error("Error: Audio vacío recibido.");
            return;
        }
        console.log('Enviando evento ptt_control: Activando PTT en 1 segundo...');
        activarPTT();
        setTimeout(() => {
            try {
                const audioBlob = new Blob(
                    [new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))],
                    { type: 'audio/wav' }
                );
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);
                audio.play().then(() => {
                    console.log('Reproduciendo audio de otro usuario...');
                }).catch((error) => {
                    console.error('Error al reproducir el audio:', error);
                });
                audio.addEventListener('ended', () => {
                    console.log('Audio finalizado. Desactivando PTT en 1 segundo...');
                    setTimeout(() => {
                        desactivarPTT();
                    }, 1000);
                });
            } catch (error) {
                console.error('Error al decodificar o reproducir el audio:', error);
            }
        }, 1000);
    });

    socket.on('audio_start', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha comenzado a transmitir audio. Evitando activación de PTT...`);
            isTransmittingAudio = true;
        }
    });

    socket.on('audio_end', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha terminado la transmisión de audio.`);
            isTransmittingAudio = false;
        }
    });

    function activarPTT() {
        if (!isTransmittingAudio) {
            console.log("Activando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ptt_state: true })
            })
            .then(response => {
                if (!response.ok) { throw new Error('Error en la solicitud de activación del PTT'); }
                return response.json();
            })
            .then(data => { console.log('Respuesta del servidor:', data); })
            .catch((error) => { console.error('Error al activar el PTT:', error); });
        } else {
            console.log('PTT no activado ya que el usuario está transmitiendo audio.');
        }
    }

    function desactivarPTT() {
        if (!isTransmittingAudio) {
            console.log("Desactivando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ptt_state: false })
            })
            .then(response => {
                if (!response.ok) { throw new Error('Error en la solicitud de desactivación del PTT'); }
                return response.json();
            })
            .then(data => { console.log('Respuesta del servidor:', data); })
            .catch((error) => { console.error('Error al desactivar el PTT:', error); });
        } else {
            console.log('PTT no desactivado ya que el usuario está transmitiendo audio.');
        }
    }
});


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\static\js\index.js
Contenido:
// /static/js/index.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;
    const nodeName = window.nodeName; // Nueva variable para el nombre del nodo

    console.log(`Conectado como ${username} al nodo ${nodeId}`);  // Log inicial

    const socket = io.connect('https://************:5000/node', {
        query: {
            node_id: String(nodeId),
            username: username
        }
    });

    // Referencia al botón de transmisión de audio
    const transmitAudioButton = document.getElementById('transmitAudio');

    socket.on('connect', () => {
        console.log(`Conexión exitosa como ${username} al nodo ${nodeId}`);
        // Mostrar el nombre del nodo recibido desde el servidor en la interfaz
        const nodeInfo = document.getElementById('node-info');
        if (nodeInfo) {
            nodeInfo.textContent = `Conectado al nodo ${nodeName}`;
        }
    });

    // Resto del código permanece igual...
    socket.on('update_users', (users) => {
        console.log('Usuarios conectados:', users);
        const uniqueUsers = [...new Set(users)];
        const usersList = document.getElementById('users_list');
        if (usersList) {
            usersList.innerHTML = '';
            uniqueUsers.forEach(user => {
                const userItem = document.createElement('li');
                userItem.textContent = user;
                usersList.appendChild(userItem);
            });
        }
    });

    const volumeMeter = document.getElementById('volume_meter');
    socket.on('audio_level', (level) => {
        if (volumeMeter) {
            volumeMeter.style.width = `${level}%`;
            volumeMeter.style.backgroundColor = level >= 50 ? 'red' : 'green';
        }
        console.log('Nivel de audio:', level);
    });

    socket.on('audio_event', (data) => {
        console.log('Evento de audio recibido:', data);
        const eventsList = document.getElementById('events_list');
        if (eventsList) {
            const newEvent = document.createElement('li');
            newEvent.textContent = `Audio recibido de ${data.user}`;
            eventsList.appendChild(newEvent);
        }
    });

    socket.on('global_audio_start', (data) => {
        if (data.node_id === String(nodeId)) {
            console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio iniciado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/rojo.png';
            }
        }
    });

    socket.on('global_audio_end', (data) => {
        if (data.node_id === String(nodeId)) {
            console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio finalizado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/verde.png';
            }
        }
    });

    socket.emit('get_connected_users', { node_id: String(nodeId) });
});


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\static\styles.css
Contenido:
/* Reset de estilos por defecto */
body, h1, p, ul, li, a, form, label, input, button {
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
  text-decoration: none;
  box-sizing: border-box;
}

body {
  background-color: #f0f8ff; /* Fondo celeste claro */
  color: #333;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.body-background::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../static/RNCom.png') no-repeat center center;
  background-size: contain; /* Ajustar el tamaño del logo */
  opacity: 0.3; /* Transparencia del logo */
  z-index: -1;
}

header {
  background-color: #006994; /* Azul oscuro */
  color: #fff;
  padding: 10px 20px;
  text-align: center;
}

header img.logo {
  display: none; /* Ocultar el logo en el header */
}

header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

header p {
  font-size: 14px;
}

main {
  flex: 1;
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  overflow: auto;
}

/* Estilo para la sección de información del nodo */
.node-section {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column; /* Cambiar a columna para que los elementos se dispongan uno debajo del otro */
  justify-content: center;
  align-items: center;
  text-align: center;
}

.node-info-container {
  margin-top: 10px; /* Espacio entre el título y el contenido */
}

.node-section h3 {
  font-size: 18px;
  color: #006994;
  margin-bottom: 0;  /* Asegura que no haya mucho espacio entre el título y el contenido */
}

/* Estilo para el contenido de la información del nodo */
.node-section p {
  font-size: 16px;
  color: #fff;
  background-color: #006994; /* Fondo azul oscuro */
  padding: 10px 20px;  /* Padding para dar más espacio */
  border-radius: 5px;  /* Esquinas redondeadas */
  display: inline-block;
  border: 1px solid #004d73; /* Borde azul intermedio */
  transition: background-color 0.3s, color 0.3s;
}

.node-section p:hover {
  background-color: #004d73; /* Cambiar el color de fondo al pasar el mouse */
  color: #f0f8ff;  /* Cambiar el color del texto al pasar el mouse */
}

/* Contenedor principal que incluye chat y usuarios conectados */
.chat-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 80%;
  max-width: 1200px;
  margin-top: 20px;
  margin-bottom: 20px;
}

/* Sección de eventos de audio (messages) con barra de desplazamiento */
.events-section {
  flex: 2;
  border: 1px solid #726e6e;
  padding: 10px;
  height: 300px; /* Ajuste la altura para que sea más corta por defecto */
  overflow-y: auto; /* Barra de scroll para eventos */
  background-color: #f0f8ff;
  margin-right: 20px;
  border-radius: 10px;
  word-wrap: break-word;
}

/* Sección de usuarios conectados */
.users-section {
  flex: 1;
  border: 1px solid #a89999;
  padding: 10px;
  height: 300px; /* Ajuste la altura para que sea más corta por defecto */
  overflow-y: auto; /* Barra de scroll para usuarios */
  background-color: #f0f8ff;
  border-radius: 10px;
}

/* Estilos para las listas dentro de los contenedores */
.users-section ul,
.events-section ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

/* Estilo para los elementos dentro de la lista de usuarios */
.users-section ul li {
  background-color: #006994;
  color: #fff;
  padding: 10px;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.users-section ul li:hover {
  background-color: #004d73;
}

/* Estilo para los mensajes en eventos de audio */
.events-section ul li {
  background-color: #fff;
  color: #006994;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  border: 1px solid #004d73;
}

/* Sección de botón de transmisión de audio */
.audio-section img {
  cursor: pointer;
  display: block;
  margin: 0 auto;
  width: 100px;
  height: 100px;
}

/* Estilo del pie de página */
footer {
  background-color: #006994;
  color: #fff;
  text-align: center;
  padding: 10px;
  width: 100%;
}

/* Responsividad */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }

  .events-section,
  .users-section {
    margin-bottom: 20px;
    height: 250px; /* Ajuste de altura para pantallas más pequeñas */
  }
}

@media (max-width: 480px) {
  .users-section ul li, .events-section ul li {
    font-size: 12px;
  }

  .audio-section img {
    width: 60px;
    height: 60px;
  }

  .volume-bar-container {
    height: 20px;
  }
}


@media (max-width: 480px) {
  .users-section ul li, .events-section ul li {
    font-size: 12px;
  }

  .audio-section img {
    width: 60px;
    height: 60px;
  }

  .volume-bar-container {
    height: 20px;
  }
}
/* Añadido para los campos de formulario */
.config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: #ffffff; /* Fondo blanco para el formulario */
  border-radius: 15px; /* Bordes redondeados */
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  max-width: 600px; /* Limitar el ancho del formulario */
  margin: 0 auto; /* Centrar el formulario */
}

.form-group {
  display: flex;
  flex-direction: column;
}

label {
  font-size: 18px; /* Tamaño de fuente más grande */
  color: #006994; /* Azul oscuro */
  margin-bottom: 5px; /* Espacio debajo de la etiqueta */
}

.input-field {
  padding: 12px; /* Ajuste del padding para más espacio */
  border: 1px solid #004d73; /* Borde azul intermedio */
  border-radius: 8px; /* Bordes redondeados */
  font-size: 16px;
  transition: border-color 0.3s;
}

.input-field:focus {
  border-color: #006994; /* Azul oscuro */
  outline: none;
}

.submit-btn {
  padding: 12px 20px;
  background-color: #006994; /* Azul oscuro */
  color: #fff;
  border: none;
  border-radius: 8px; /* Bordes redondeados para el botón */
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.submit-btn:hover {
  background-color: #004d73; /* Azul intermedio */
}

/* Aumentar el espacio alrededor del formulario */
.container {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-height: calc(100vh - 120px); /* Espacio para header y footer */
}

.node-section {
  margin-bottom: 20px;
  text-align: center;
}

h3 {
  font-size: 24px;
  color: #004d73; /* Azul intermedio */
}

footer {
  background-color: #006994; /* Azul oscuro */
  color: #fff;
  text-align: center;
  padding: 10px;
  width: 100%;
  position: relative;
  bottom: 0;
}


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\templates\config_form.html
Contenido:
<!-- /static/app-ws/templates/config_form.html -->

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuración de Usuario</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
</head>
<body class="body-background">
    <header>
        <h1>Cliente VHF Digital - Configuración de Usuario</h1>
        <p>Formulario de configuración para la aplicación Cliente VHF Digital.</p>
    </header>

    <main class="container">
        <!-- Sección de formulario de configuración -->
        <div class="node-section">
            <h3>Configuración de Usuario</h3>
        </div>

        <form action="{{ url_for('save_user_config') }}" method="POST" class="config-form">
            <div class="form-group">
                <label for="username">Usuario:</label>
                <input type="text" id="username" name="username" class="input-field" required>
            </div>

            <div class="form-group">
                <label for="password">Contraseña:</label>
                <input type="password" id="password" name="password" class="input-field" required>
            </div>

            <div class="form-group">
                <label for="node_id">ID del Nodo:</label>
                <input type="text" id="node_id" name="node_id" class="input-field" required>
            </div>

            <div class="form-group">
                <label for="input_device_index">Dispositivo de entrada de audio:</label>
                <select id="input_device_index" name="input_device_index" class="input-field">
                    {% if input_devices %}
                        {% for index, name in input_devices %}
                            <option value="{{ index }}">{{ name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="0">Dispositivo predeterminado</option>
                    {% endif %}
                </select>
            </div>

            <div class="form-group">
                <label for="volume_level">Nivel de Volumen:</label>
                <input type="number" id="volume_level" name="volume_level" class="input-field" min="1" max="10" required>
            </div>

            <div class="form-group">
                <label for="port_number">Número de Puerto:</label>
                <input type="text" id="port_number" name="port_number" class="input-field" required>
            </div>

            <div class="form-group">
                <button type="submit" class="submit-btn">Guardar Configuración</button>
            </div>
        </form>
    </main>

    <footer>
        <p>&copy; 2024 Cliente VHF Digital</p>
    </footer>
</body>
</html>


Archivo: C:/Users/<USER>/Documents/GitHub/Comunicaciones_RN/cliente local\templates\index.html
Contenido:
<!-- /static/app-ws/templates/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cliente VHF Digital</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
</head>
<body class="body-background">
    <header>
        <h1>Cliente VHF Digital</h1>
        <p>Aplicación en ejecución automática para captura y recepción de audio.</p>
    </header>

    <main class="container">
        <!-- Sección de Información del Nodo -->
        <div class="node-section">
            <h3>Información del Nodo:</h3>
            <p id="node-info">Conectado al nodo {{ node_name }}</p>
        </div>

        <!-- Sección de Botón de Transmisión de Audio -->
        <div class="audio-section">
            <img id="transmitAudio" src="{{ url_for('static', filename='verde.png') }}" alt="Transmitir Audio" />
        </div>

        <!-- Contenedor Principal: Chat + Usuarios Conectados -->
        <div class="chat-container">
            <!-- Sección de Eventos de Audio -->
            <div class="events-section">
                <h3>Eventos de audio:</h3>
                <ul id="events_list">
                    <li>No hay eventos en este momento</li>
                </ul>
            </div>

            <!-- Sección de Usuarios Conectados -->
            <div class="users-section">
                <h3>Usuarios conectados:</h3>
                <ul id="users_list">
                    <li>joacoabe</li>
                    <li>ur1</li>
                    <li>admin</li>
                </ul>
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; 2024 Cliente VHF Digital</p>
    </footer>

    <script type="text/javascript">
        // Cargar los valores correctos desde Flask
        const nodeId = "{{ node_id }}";
        const username = "{{ username }}";
        const nodeName = "{{ node_name }}";  // Recibido desde el servidor
        window.nodeId = nodeId;
        window.username = username;
        window.nodeName = nodeName;
    </script>
    
    <!-- Agregar librería Socket.IO -->
    <script src="{{ url_for('static', filename='socket.io.min.js') }}"></script>
    <script src="/static/js/socket.io.min.js"></script>

    <!-- Incluir los scripts del cliente -->
    <script src="{{ url_for('static', filename='js/index.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dig-vhf.js') }}"></script>
    <script src="{{ url_for('static', filename='js/vhf-dig.js') }}"></script>
</body>
</html>
