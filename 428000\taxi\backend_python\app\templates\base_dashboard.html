{% extends "base_layout_base.html" %}

{% block title %}Panel de Base - Taxis{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .status-libre {
        background-color: #198754;
    }
    .status-ocupado {
        background-color: #dc3545;
    }
    .status-alerta {
        background-color: #ffc107;
    }
    .status-fuera_de_servicio {
        background-color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Panel de Base</h1>
        <p class="lead">Bienvenido al panel de base del sistema de taxis.</p>
    </div>
</div>

<!-- Estadísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-taxi-front card-icon"></i>
                <div class="stats-value">{{ stats.active_vehicles|default(0) }}</div>
                <div class="stats-label text-white-50">Vehículos Activos</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-person card-icon"></i>
                <div class="stats-value">{{ stats.available_drivers|default(0) }}</div>
                <div class="stats-label text-white-50">Conductores Disponibles</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-geo-alt card-icon"></i>
                <div class="stats-value">{{ stats.trips_today|default(0) }}</div>
                <div class="stats-label text-white-50">Viajes Hoy</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-clock card-icon"></i>
                <div class="stats-value">{{ stats.avg_response_time|default('0m') }}</div>
                <div class="stats-label text-white-50">Tiempo Respuesta</div>
            </div>
        </div>
    </div>
</div>

<!-- Mapa y Estado de Vehículos -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mapa de Vehículos</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Estado de Vehículos</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-4">
                    <div class="text-center">
                        <div class="stats-value text-success">{{ vehicle_stats.available|default(0) }}</div>
                        <div class="stats-label">Disponibles</div>
                    </div>
                    <div class="text-center">
                        <div class="stats-value text-danger">{{ vehicle_stats.busy|default(0) }}</div>
                        <div class="stats-label">Ocupados</div>
                    </div>
                    <div class="text-center">
                        <div class="stats-value text-warning">{{ vehicle_stats.alert|default(0) }}</div>
                        <div class="stats-label">En Alerta</div>
                    </div>
                    <div class="text-center">
                        <div class="stats-value text-secondary">{{ vehicle_stats.offline|default(0) }}</div>
                        <div class="stats-label">Fuera de Servicio</div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>Vehículo</th>
                                <th>Conductor</th>
                                <th>Estado</th>
                                <th>Última Act.</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for vehicle in vehicles|default([]) %}
                            <tr>
                                <td>{{ vehicle.plate_number }}</td>
                                <td>{{ vehicle.driver_name|default('Sin asignar') }}</td>
                                <td>
                                    <span class="status-indicator status-{{ vehicle.status|lower }}"></span>
                                    {{ vehicle.status|upper }}
                                </td>
                                <td>{{ vehicle.last_update|default('Desconocida') }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">No hay vehículos disponibles</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Viajes Recientes y Conductores -->
<div class="row">
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Viajes Recientes</h5>
                <a href="{{ url_for('base_trips_route') }}" class="btn btn-sm btn-outline-primary">Ver Todos</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Conductor</th>
                                <th>Origen</th>
                                <th>Destino</th>
                                <th>Estado</th>
                                <th>Hora</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for trip in recent_trips|default([]) %}
                            <tr>
                                <td>{{ trip.id }}</td>
                                <td>{{ trip.driver_name|default('Sin asignar') }}</td>
                                <td>{{ trip.origin_address|truncate(15) }}</td>
                                <td>{{ trip.destination_address|truncate(15) }}</td>
                                <td>
                                    <span class="badge {% if trip.status == 'COMPLETADO' %}bg-success{% elif trip.status == 'CANCELADO' %}bg-danger{% else %}bg-primary{% endif %}">
                                        {{ trip.status }}
                                    </span>
                                </td>
                                <td>{{ trip.requested_at|default('') }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center">No hay viajes recientes</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Conductores Activos</h5>
                <a href="{{ url_for('base_drivers_route') }}" class="btn btn-sm btn-outline-primary">Ver Todos</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Conductor</th>
                                <th>Vehículo</th>
                                <th>Estado</th>
                                <th>Horas Activo</th>
                                <th>Viajes Hoy</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for driver in active_drivers|default([]) %}
                            <tr>
                                <td>{{ driver.name }}</td>
                                <td>{{ driver.vehicle }}</td>
                                <td>
                                    <span class="status-indicator status-{{ driver.status|lower }}"></span>
                                    {{ driver.status|upper }}
                                </td>
                                <td>{{ driver.hours_active }}</td>
                                <td>{{ driver.trips_today }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">No hay conductores activos</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Acciones Rápidas -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Acciones Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="{{ url_for('base_vehicles_route') }}" class="btn btn-lg btn-outline-primary">
                                <i class="bi bi-taxi-front me-2"></i> Ver Vehículos
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="{{ url_for('base_drivers_route') }}" class="btn btn-lg btn-outline-success">
                                <i class="bi bi-person me-2"></i> Ver Conductores
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="{{ url_for('base_trips_route') }}" class="btn btn-lg btn-outline-info">
                                <i class="bi bi-geo-alt me-2"></i> Ver Viajes
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-lg btn-outline-warning" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise me-2"></i> Actualizar Datos
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 12); // Buenos Aires como ejemplo

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Iconos personalizados para diferentes estados
    const vehicleIcons = {
        libre: L.icon({
            iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            className: 'marker-libre'
        }),
        ocupado: L.icon({
            iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            className: 'marker-ocupado'
        }),
        alerta: L.icon({
            iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            className: 'marker-alerta'
        }),
        fuera_de_servicio: L.icon({
            iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            className: 'marker-fuera-de-servicio'
        })
    };

    // Función para cargar vehículos
    async function loadVehicles() {
        try {
            const response = await fetchWithAuth('/api/v1/vehicles/base');
            if (response.ok) {
                const vehicles = await response.json();
                
                // Limpiar marcadores existentes
                map.eachLayer(layer => {
                    if (layer instanceof L.Marker) {
                        map.removeLayer(layer);
                    }
                });
                
                // Añadir marcadores para cada vehículo
                vehicles.forEach(vehicle => {
                    if (vehicle.last_latitude && vehicle.last_longitude) {
                        const icon = vehicleIcons[vehicle.status] || vehicleIcons.libre;
                        const marker = L.marker([vehicle.last_latitude, vehicle.last_longitude], { icon })
                            .addTo(map)
                            .bindPopup(`
                                <strong>${vehicle.plate_number}</strong><br>
                                Conductor: ${vehicle.driver_name || 'Sin asignar'}<br>
                                Estado: ${vehicle.status.toUpperCase()}<br>
                                Última actualización: ${vehicle.last_update || 'Desconocida'}
                            `);
                    }
                });
            }
        } catch (error) {
            console.error('Error al cargar vehículos:', error);
        }
    }

    // Función para actualizar todos los datos
    function refreshData() {
        loadVehicles();
        window.location.reload();
    }

    // Cargar vehículos al iniciar
    loadVehicles();
    
    // Actualizar cada 30 segundos
    setInterval(loadVehicles, 30000);

    // Añadir estilos para los marcadores
    const style = document.createElement('style');
    style.textContent = `
        .marker-libre { filter: hue-rotate(120deg); }
        .marker-ocupado { filter: hue-rotate(0deg); }
        .marker-alerta { filter: hue-rotate(60deg); }
        .marker-fuera-de-servicio { filter: grayscale(100%); }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}
