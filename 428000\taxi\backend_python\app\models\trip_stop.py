import enum
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base

class TripStopStatusEnum(str, enum.Enum):
    """Estado de una parada intermedia en un viaje."""
    PENDIENTE = "pendiente"
    COMPLETADA = "completada"
    CANCELADA = "cancelada"

class TripStop(Base):
    """
    Modelo para paradas intermedias en un viaje.
    Permite definir múltiples paradas en un viaje.
    """
    __tablename__ = 'trip_stops'
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Relación con el viaje
    trip_id = Column(Integer, ForeignKey('trips.id'), nullable=False)
    trip = relationship("Trip", back_populates="stops")
    
    # Orden de la parada en el viaje (1, 2, 3, etc.)
    order = Column(Integer, nullable=False)
    
    # Ubicación de la parada
    latitude = Column(String, nullable=False)
    longitude = Column(String, nullable=False)
    address = Column(String, nullable=True)
    
    # Estado de la parada
    status = Column(SQLAlchemyEnum(TripStopStatusEnum), default=TripStopStatusEnum.PENDIENTE)
    
    # Tiempos
    estimated_arrival_time = Column(DateTime(timezone=True), nullable=True)
    actual_arrival_time = Column(DateTime(timezone=True), nullable=True)
    
    # Tiempo estimado de espera en la parada (en segundos)
    wait_time_seconds = Column(Integer, default=60)
    
    # Notas o instrucciones para la parada
    notes = Column(String, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
