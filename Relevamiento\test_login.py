#!/usr/bin/env python3
# --- Archivo: test_login.py ---
# Script para probar el login rápidamente

import requests
import time

def test_login():
    """Probar login en la aplicación."""
    
    base_url = "https://patagoniaservers.com.ar:5006"
    
    print("🔑 Probando login...")
    
    try:
        session = requests.Session()
        
        # Probar página principal
        print("📄 Probando página principal...")
        response = session.get(f"{base_url}/", verify=False, timeout=10)
        print(f"  Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Error en página principal: {response.status_code}")
            return False
        
        # Probar página de login
        print("🔐 Probando página de login...")
        response = session.get(f"{base_url}/auth/login", verify=False, timeout=10)
        print(f"  Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Error en página de login: {response.status_code}")
            return False
        
        # Intentar login
        print("👤 Intentando login con admin/isaias52...")
        login_data = {
            'username': 'admin',
            'password': 'isaias52',
            'submit': 'Iniciar Sesión'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data, verify=False, allow_redirects=False)
        print(f"  Status: {response.status_code}")
        
        if response.status_code in [302, 303]:
            print("✅ Login exitoso!")
            
            # Probar acceso a página protegida
            print("🔒 Probando acceso a página protegida...")
            protected_response = session.get(f"{base_url}/points/list", verify=False)
            print(f"  Status: {protected_response.status_code}")
            
            if protected_response.status_code == 200:
                print("✅ Acceso a páginas protegidas funciona")
                return True
            else:
                print(f"⚠️  Páginas protegidas: {protected_response.status_code}")
                return False
        else:
            print(f"❌ Login falló: {response.status_code}")
            if response.text:
                print(f"Respuesta: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Función principal."""
    print("🧪 Test de Login Rápido")
    print("=" * 30)
    
    # Esperar un poco para que la aplicación inicie
    print("⏳ Esperando que la aplicación esté lista...")
    time.sleep(5)
    
    if test_login():
        print("\n🎉 ¡Login funciona correctamente!")
        print("\n🚀 Información de acceso:")
        print("   URL: https://patagoniaservers.com.ar:5006/")
        print("   Usuario: admin")
        print("   Contraseña: isaias52")
        print("   Rol: administrador")
    else:
        print("\n❌ Login no funciona")
        print("\n💡 Verifica:")
        print("   systemctl status relevamiento")
        print("   journalctl -u relevamiento -f")

if __name__ == "__main__":
    main()
