# --- Archivo: app/forms_permissions.py ---
# Formularios para gestión de permisos y roles de usuario

from flask_wtf import FlaskForm
from wtforms import (
    SelectField, SelectMultipleField, BooleanField, StringField, 
    TextAreaField, IntegerField, SubmitField, HiddenField
)
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.widgets import CheckboxInput, ListWidget

class MultiCheckboxField(SelectMultipleField):
    """Campo personalizado para múltiples checkboxes"""
    widget = ListWidget(prefix_label=False)
    option_widget = CheckboxInput()

class UserRoleForm(FlaskForm):
    """Formulario para crear/editar roles de usuario"""
    name = StringField('Nombre del Rol', validators=[
        DataRequired(message='El nombre del rol es obligatorio'),
        Length(min=3, max=50, message='El nombre debe tener entre 3 y 50 caracteres')
    ])
    
    description = TextAreaField('Descripción', validators=[
        Optional(),
        Length(max=500, message='La descripción no puede exceder 500 caracteres')
    ])
    
    level = IntegerField('Nivel de Acceso', validators=[
        DataRequired(message='El nivel de acceso es obligatorio'),
        NumberRange(min=0, max=100, message='El nivel debe estar entre 0 y 100')
    ])
    
    submit = SubmitField('Guardar Rol')

class UserPermissionForm(FlaskForm):
    """Formulario para gestionar permisos de usuario"""
    user_id = HiddenField()
    
    role_id = SelectField('Rol', choices=[], validators=[Optional()], coerce=int)
    
    # Filtros geográficos y de datos
    cities = MultiCheckboxField('Ciudades Permitidas', choices=[], validators=[Optional()])
    sources = MultiCheckboxField('Capas/Orígenes Permitidos', choices=[], validators=[Optional()])
    
    # Permisos para puntos
    can_view_points = BooleanField('Ver Puntos', default=True)
    can_create_points = BooleanField('Crear Puntos')
    can_edit_points = BooleanField('Editar Puntos')
    can_delete_points = BooleanField('Eliminar Puntos')
    
    # Permisos para cámaras
    can_view_cameras = BooleanField('Ver Cámaras', default=True)
    can_create_cameras = BooleanField('Crear Cámaras')
    can_edit_cameras = BooleanField('Editar Cámaras')
    can_delete_cameras = BooleanField('Eliminar Cámaras')
    
    # Permisos para imágenes
    can_view_images = BooleanField('Ver Imágenes', default=True)
    can_upload_images = BooleanField('Subir Imágenes')
    can_annotate_images = BooleanField('Anotar Imágenes')
    can_delete_images = BooleanField('Eliminar Imágenes')
    
    # Permisos para reportes y administración
    can_view_reports = BooleanField('Ver Reportes', default=True)
    can_export_data = BooleanField('Exportar Datos')
    can_manage_users = BooleanField('Gestionar Usuarios')
    
    submit = SubmitField('Guardar Permisos')
    
    def __init__(self, *args, **kwargs):
        super(UserPermissionForm, self).__init__(*args, **kwargs)
        
        # Cargar opciones dinámicamente
        self.load_role_choices()
        self.load_city_choices()
        self.load_source_choices()
    
    def load_role_choices(self):
        """Carga opciones de roles"""
        from app.models_permissions import UserRole
        roles = UserRole.query.order_by(UserRole.level.desc()).all()
        self.role_id.choices = [('', 'Sin rol específico')] + [
            (role.id, f"{role.name} (Nivel {role.level})") for role in roles
        ]
    
    def load_city_choices(self):
        """Carga opciones de ciudades"""
        from app.models import Point
        from app import db
        
        cities = db.session.query(Point.city).filter(
            Point.city.isnot(None)
        ).distinct().order_by(Point.city).all()
        
        self.cities.choices = [(city[0], city[0]) for city in cities if city[0]]
    
    def load_source_choices(self):
        """Carga opciones de capas/orígenes"""
        from app.models import Point
        from app import db
        
        sources = db.session.query(Point.source).filter(
            Point.source.isnot(None)
        ).distinct().order_by(Point.source).all()
        
        self.sources.choices = [(source[0], source[0]) for source in sources if source[0]]

class UserManagementForm(FlaskForm):
    """Formulario para gestión general de usuarios"""
    user_id = SelectField('Usuario', choices=[], validators=[DataRequired()], coerce=int)
    action = SelectField('Acción', choices=[
        ('view', 'Ver Permisos'),
        ('edit', 'Editar Permisos'),
        ('reset', 'Restablecer Permisos'),
        ('delete', 'Eliminar Permisos')
    ], validators=[DataRequired()])
    
    submit = SubmitField('Ejecutar')
    
    def __init__(self, *args, **kwargs):
        super(UserManagementForm, self).__init__(*args, **kwargs)
        self.load_user_choices()
    
    def load_user_choices(self):
        """Carga opciones de usuarios"""
        from app.models import User
        users = User.query.order_by(User.username).all()
        self.user_id.choices = [
            (user.id, f"{user.username} ({user.email})") for user in users
        ]

class BulkPermissionForm(FlaskForm):
    """Formulario para asignación masiva de permisos"""
    users = MultiCheckboxField('Usuarios', choices=[], validators=[DataRequired()])
    role_id = SelectField('Rol a Asignar', choices=[], validators=[DataRequired()], coerce=int)
    
    # Opciones de aplicación
    override_existing = BooleanField('Sobrescribir permisos existentes', default=False)
    apply_city_restrictions = BooleanField('Aplicar restricciones de ciudad', default=False)
    apply_source_restrictions = BooleanField('Aplicar restricciones de capa', default=False)
    
    # Filtros para aplicar
    cities = MultiCheckboxField('Ciudades a Restringir', choices=[], validators=[Optional()])
    sources = MultiCheckboxField('Capas a Restringir', choices=[], validators=[Optional()])
    
    submit = SubmitField('Aplicar Permisos Masivos')
    
    def __init__(self, *args, **kwargs):
        super(BulkPermissionForm, self).__init__(*args, **kwargs)
        self.load_choices()
    
    def load_choices(self):
        """Carga todas las opciones necesarias"""
        from app.models import User
        from app.models_permissions import UserRole
        from app.models import Point
        from app import db
        
        # Usuarios
        users = User.query.order_by(User.username).all()
        self.users.choices = [
            (user.id, f"{user.username} ({user.email})") for user in users
        ]
        
        # Roles
        roles = UserRole.query.order_by(UserRole.level.desc()).all()
        self.role_id.choices = [
            (role.id, f"{role.name} (Nivel {role.level})") for role in roles
        ]
        
        # Ciudades
        cities = db.session.query(Point.city).filter(
            Point.city.isnot(None)
        ).distinct().order_by(Point.city).all()
        self.cities.choices = [(city[0], city[0]) for city in cities if city[0]]
        
        # Capas/Orígenes
        sources = db.session.query(Point.source).filter(
            Point.source.isnot(None)
        ).distinct().order_by(Point.source).all()
        self.sources.choices = [(source[0], source[0]) for source in sources if source[0]]

class PermissionTemplateForm(FlaskForm):
    """Formulario para crear plantillas de permisos"""
    name = StringField('Nombre de la Plantilla', validators=[
        DataRequired(message='El nombre de la plantilla es obligatorio'),
        Length(min=3, max=100, message='El nombre debe tener entre 3 y 100 caracteres')
    ])
    
    description = TextAreaField('Descripción', validators=[
        Optional(),
        Length(max=500, message='La descripción no puede exceder 500 caracteres')
    ])
    
    # Permisos base de la plantilla
    base_role_id = SelectField('Rol Base', choices=[], validators=[DataRequired()], coerce=int)
    
    # Restricciones predeterminadas
    default_cities = MultiCheckboxField('Ciudades por Defecto', choices=[], validators=[Optional()])
    default_sources = MultiCheckboxField('Capas por Defecto', choices=[], validators=[Optional()])
    
    # Configuración de la plantilla
    is_active = BooleanField('Plantilla Activa', default=True)
    auto_apply_new_users = BooleanField('Aplicar automáticamente a nuevos usuarios')
    
    submit = SubmitField('Guardar Plantilla')
    
    def __init__(self, *args, **kwargs):
        super(PermissionTemplateForm, self).__init__(*args, **kwargs)
        self.load_choices()
    
    def load_choices(self):
        """Carga opciones para la plantilla"""
        from app.models_permissions import UserRole
        from app.models import Point
        from app import db
        
        # Roles
        roles = UserRole.query.order_by(UserRole.level.desc()).all()
        self.base_role_id.choices = [
            (role.id, f"{role.name} (Nivel {role.level})") for role in roles
        ]
        
        # Ciudades
        cities = db.session.query(Point.city).filter(
            Point.city.isnot(None)
        ).distinct().order_by(Point.city).all()
        self.default_cities.choices = [(city[0], city[0]) for city in cities if city[0]]
        
        # Capas/Orígenes
        sources = db.session.query(Point.source).filter(
            Point.source.isnot(None)
        ).distinct().order_by(Point.source).all()
        self.default_sources.choices = [(source[0], source[0]) for source in sources if source[0]]
