#!/usr/bin/env python3
# --- Archivo: fix_db.py ---
# Script para arreglar la inconsistencia en la base de datos

import sqlite3
import os
import sys
from datetime import datetime

def backup_database():
    """Crear backup de la base de datos."""
    if not os.path.exists('app.db'):
        print("❌ No existe app.db")
        return False
    
    import shutil
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f'app_backup_fix_{timestamp}.db'
    
    try:
        shutil.copy2('app.db', backup_name)
        print(f"✅ Backup creado: {backup_name}")
        return True
    except Exception as e:
        print(f"❌ Error creando backup: {e}")
        return False

def check_table_structure():
    """Verificar la estructura actual de la tabla user."""
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        print("🔍 Verificando estructura actual de tabla user...")
        
        # Verificar estructura
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        
        print("📋 Columnas actuales:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # Verificar datos
        cursor.execute("SELECT COUNT(*) FROM user;")
        count = cursor.fetchone()[0]
        print(f"📊 Total usuarios: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM user LIMIT 1;")
            sample = cursor.fetchone()
            print(f"📝 Muestra de datos: {sample}")
        
        conn.close()
        return columns
        
    except Exception as e:
        print(f"❌ Error verificando estructura: {e}")
        return None

def recreate_user_table():
    """Recrear la tabla user con la estructura correcta."""
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        print("🔄 Recreando tabla user...")
        
        # Obtener datos existentes
        cursor.execute("SELECT id, username, email, password_hash FROM user;")
        existing_users = cursor.fetchall()
        print(f"💾 Guardando {len(existing_users)} usuarios existentes")
        
        # Eliminar tabla actual
        cursor.execute("DROP TABLE user;")
        print("🗑️  Tabla user eliminada")
        
        # Crear nueva tabla con estructura correcta
        cursor.execute("""
            CREATE TABLE user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(64) NOT NULL UNIQUE,
                email VARCHAR(120) UNIQUE,
                password_hash VARCHAR(256) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'operador',
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES user (id)
            );
        """)
        print("✅ Nueva tabla user creada")
        
        # Restaurar datos existentes
        for user_data in existing_users:
            cursor.execute("""
                INSERT INTO user (id, username, email, password_hash, role, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, 'administrador', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, user_data)
        
        print(f"✅ {len(existing_users)} usuarios restaurados como administradores")
        
        # Actualizar secuencia
        if existing_users:
            max_id = max(user[0] for user in existing_users)
            cursor.execute(f"UPDATE sqlite_sequence SET seq = {max_id} WHERE name = 'user';")
        
        conn.commit()
        conn.close()
        
        print("✅ Tabla user recreada exitosamente")
        return True
        
    except Exception as e:
        print(f"❌ Error recreando tabla user: {e}")
        return False

def verify_fix():
    """Verificar que la corrección funcionó."""
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        print("🔍 Verificando corrección...")
        
        # Verificar estructura
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        
        expected_columns = ['id', 'username', 'email', 'password_hash', 'role', 'is_active', 'created_by', 'created_at', 'updated_at']
        actual_columns = [col[1] for col in columns]
        
        missing_columns = set(expected_columns) - set(actual_columns)
        if missing_columns:
            print(f"❌ Columnas faltantes: {missing_columns}")
            return False
        
        print("✅ Todas las columnas esperadas están presentes")
        
        # Verificar datos
        cursor.execute("SELECT username, role, is_active FROM user;")
        users = cursor.fetchall()
        
        print("👥 Usuarios verificados:")
        for user in users:
            print(f"  - {user[0]} ({user[1]}) - Activo: {user[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verificando corrección: {e}")
        return False

def test_sqlalchemy():
    """Probar que SQLAlchemy funcione con la nueva estructura."""
    try:
        # Importar después de la corrección
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            print("🧪 Probando SQLAlchemy...")
            
            # Probar consulta básica
            users = User.query.all()
            print(f"✅ SQLAlchemy funciona: {len(users)} usuarios encontrados")
            
            for user in users:
                print(f"  👤 {user.username} ({user.role}) - Activo: {user.is_active}")
                print(f"    🔐 Es admin: {user.is_admin()}")
                print(f"    🔐 Puede gestionar usuarios: {user.can_manage_users()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error probando SQLAlchemy: {e}")
        return False

def main():
    """Función principal."""
    print("🔧 Reparador de Base de Datos")
    print("=" * 40)
    
    # Crear backup
    if not backup_database():
        response = input("❓ ¿Continuar sin backup? (s/n): ").strip().lower()
        if response not in ['s', 'si', 'y', 'yes']:
            print("❌ Operación cancelada")
            sys.exit(0)
    
    # Verificar estructura actual
    columns = check_table_structure()
    if not columns:
        sys.exit(1)
    
    # Verificar si necesita corrección
    column_names = [col[1] for col in columns]
    if 'role' not in column_names:
        print("\n⚠️  La columna 'role' no existe, recreando tabla...")
        
        if not recreate_user_table():
            sys.exit(1)
    else:
        print("\n✅ La estructura parece correcta")
    
    # Verificar corrección
    if not verify_fix():
        sys.exit(1)
    
    # Probar SQLAlchemy
    if not test_sqlalchemy():
        print("\n❌ SQLAlchemy aún tiene problemas")
        sys.exit(1)
    
    print("\n🎉 ¡Base de datos reparada exitosamente!")
    print("\n🚀 Ahora puedes:")
    print("   1. Reiniciar la aplicación: python run.py &")
    print("   2. Probar login: admin / isaias52")

if __name__ == "__main__":
    main()
