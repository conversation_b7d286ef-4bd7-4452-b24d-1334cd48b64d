# run.py

from app import create_app, db
from app.models import User, Point, Image
# Asegúrate de importar Migrate si lo estás usando
from flask_migrate import Migrate # <--- NECESARIO SI USAS Migrate

# 1. Crea la aplicación PRIMERO
app = create_app()

# 2. INICIALIZA las extensiones DESPUÉS de crear la app
migrate = Migrate(app, db) # <--- Ahora 'app' existe

# 3. DEFINE funciones/decoradores que usen 'app' DESPUÉS de crearla
@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'Point': Point, 'Image': Image, 'migrate': migrate} # Añade migrate al contexto (opcional)

# 4. Bloque para ejecutar la app (si se corre con 'python run.py')
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5006, debug=True, ssl_context=('cert.pem', 'key.pem'))