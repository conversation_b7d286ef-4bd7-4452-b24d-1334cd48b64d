<!-- /app/templates/admin/list_churches.html -->
{% extends "base.html" %}

{% block title %}Lista de Iglesias{% endblock %}

{% block content %}
    <h1>Lista de Iglesias</h1>
    {% include '_search_form.html' %}
    <table class="table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Dirección</th>
                <th>Ciudad</th>       
                <th>Provincia</th>    
                <th>Distrito</th>
                <th>Pastor</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tbody>
            {% for church in churches %}
                <tr>
                    <td>{{ church.id }}</td>
                    <td>{{ church.name }}</td>
                    <td>{{ church.address }}</td>
                    <td>{{ church.city or '-' }}</td>  
                    <td>{{ church.province or '-' }}</td> 
                    <td>{{ church.district }}</td>
                    <td>{{ church.pastor.full_name if church.pastor else '<PERSON><PERSON><PERSON>' }}</td>
                    <td>
                        <a href="{{ url_for('routes.edit_church', church_id=church.id) }}" class="btn btn-warning btn-sm">Editar</a>
                        <!-- Botón para eliminar (implementar con cuidado) -->
                        <form action="{{ url_for('routes.delete_church', church_id=church.id) }}" method="POST" style="display: inline;">
                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de eliminar esta iglesia?');">Eliminar</button>
                        </form>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}