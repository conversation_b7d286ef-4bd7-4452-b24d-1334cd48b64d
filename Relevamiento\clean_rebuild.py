#!/usr/bin/env python3
# --- Archivo: clean_rebuild.py ---
# Script para reconstruir completamente la base de datos

import os
import sys
import sqlite3
from datetime import datetime

def backup_and_extract_data():
    """Hacer backup y extraer datos importantes."""
    if not os.path.exists('app.db'):
        print("❌ No existe app.db")
        return None
    
    try:
        # Backup
        import shutil
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'app_backup_rebuild_{timestamp}.db'
        shutil.copy2('app.db', backup_name)
        print(f"✅ Backup creado: {backup_name}")
        
        # Extraer datos
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        data = {
            'users': [],
            'points': [],
            'images': [],
            'cameras': []
        }
        
        # Extraer usuarios
        try:
            cursor.execute("SELECT username, email, password_hash FROM user;")
            data['users'] = cursor.fetchall()
            print(f"💾 Extraídos {len(data['users'])} usuarios")
        except:
            print("⚠️  No se pudieron extraer usuarios")
        
        # Extraer puntos
        try:
            cursor.execute("SELECT * FROM point;")
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            data['points'] = {'columns': columns, 'rows': rows}
            print(f"💾 Extraídos {len(rows)} puntos")
        except:
            print("⚠️  No se pudieron extraer puntos")
        
        # Extraer imágenes
        try:
            cursor.execute("SELECT * FROM image;")
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            data['images'] = {'columns': columns, 'rows': rows}
            print(f"💾 Extraídas {len(rows)} imágenes")
        except:
            print("⚠️  No se pudieron extraer imágenes")
        
        # Extraer cámaras
        try:
            cursor.execute("SELECT * FROM camera;")
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            data['cameras'] = {'columns': columns, 'rows': rows}
            print(f"💾 Extraídas {len(rows)} cámaras")
        except:
            print("⚠️  No se pudieron extraer cámaras")
        
        conn.close()
        return data
        
    except Exception as e:
        print(f"❌ Error extrayendo datos: {e}")
        return None

def rebuild_database(data):
    """Reconstruir la base de datos desde cero."""
    try:
        # Eliminar base de datos actual
        if os.path.exists('app.db'):
            os.remove('app.db')
            print("🗑️  Base de datos anterior eliminada")
        
        # Importar Flask app
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from app.models import User, Point, Image, Camera
        
        print("🚀 Creando nueva base de datos...")
        
        app = create_app()
        
        with app.app_context():
            # Crear todas las tablas
            db.create_all()
            print("✅ Tablas creadas")
            
            # Restaurar usuarios
            if data and data['users']:
                print("👥 Restaurando usuarios...")
                for user_data in data['users']:
                    user = User(
                        username=user_data[0],
                        email=user_data[1],
                        password_hash=user_data[2],
                        role='administrador',  # Todos los usuarios existentes serán admin
                        is_active=True
                    )
                    db.session.add(user)
                
                db.session.commit()
                print(f"✅ {len(data['users'])} usuarios restaurados")
            else:
                # Crear usuario admin por defecto
                print("👤 Creando usuario administrador por defecto...")
                from werkzeug.security import generate_password_hash
                
                admin = User(
                    username='admin',
                    password_hash=generate_password_hash('isaias52'),
                    role='administrador',
                    is_active=True
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ Usuario admin creado")
            
            # Restaurar puntos
            if data and data['points'] and data['points']['rows']:
                print("📍 Restaurando puntos...")
                try:
                    for row in data['points']['rows']:
                        # Crear punto con los datos disponibles
                        point_data = dict(zip(data['points']['columns'], row))
                        
                        point = Point(
                            name=point_data.get('name'),
                            latitude=point_data.get('latitude'),
                            longitude=point_data.get('longitude'),
                            status=point_data.get('status', 'azul'),
                            city=point_data.get('city'),
                            source=point_data.get('source'),
                            description=point_data.get('description')
                        )
                        db.session.add(point)
                    
                    db.session.commit()
                    print(f"✅ {len(data['points']['rows'])} puntos restaurados")
                except Exception as e:
                    print(f"⚠️  Error restaurando puntos: {e}")
            
            # Restaurar imágenes
            if data and data['images'] and data['images']['rows']:
                print("🖼️  Restaurando imágenes...")
                try:
                    for row in data['images']['rows']:
                        image_data = dict(zip(data['images']['columns'], row))
                        
                        image = Image(
                            filename=image_data.get('filename'),
                            point_id=image_data.get('point_id'),
                            user_id=image_data.get('user_id', 1),  # Asignar al primer usuario
                            annotations_json=image_data.get('annotations_json')
                        )
                        db.session.add(image)
                    
                    db.session.commit()
                    print(f"✅ {len(data['images']['rows'])} imágenes restauradas")
                except Exception as e:
                    print(f"⚠️  Error restaurando imágenes: {e}")
            
            # Restaurar cámaras
            if data and data['cameras'] and data['cameras']['rows']:
                print("📷 Restaurando cámaras...")
                try:
                    for row in data['cameras']['rows']:
                        camera_data = dict(zip(data['cameras']['columns'], row))
                        
                        camera = Camera(
                            point_id=camera_data.get('point_id'),
                            type=camera_data.get('type', 'otra'),
                            direction=camera_data.get('direction'),
                            photo_filename=camera_data.get('photo_filename'),
                            latitude=camera_data.get('latitude'),
                            longitude=camera_data.get('longitude'),
                            location_source=camera_data.get('location_source'),
                            location_accuracy=camera_data.get('location_accuracy')
                        )
                        db.session.add(camera)
                    
                    db.session.commit()
                    print(f"✅ {len(data['cameras']['rows'])} cámaras restauradas")
                except Exception as e:
                    print(f"⚠️  Error restaurando cámaras: {e}")
            
            # Mostrar estadísticas finales
            print("\n📊 Estadísticas finales:")
            print(f"  👥 Usuarios: {User.query.count()}")
            print(f"  📍 Puntos: {Point.query.count()}")
            print(f"  🖼️  Imágenes: {Image.query.count()}")
            print(f"  📷 Cámaras: {Camera.query.count()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error reconstruyendo base de datos: {e}")
        return False

def main():
    """Función principal."""
    print("🔄 Reconstructor Completo de Base de Datos")
    print("=" * 50)
    
    print("⚠️  ADVERTENCIA: Esto eliminará la base de datos actual y la recreará")
    response = input("¿Continuar? (s/n): ").strip().lower()
    if response not in ['s', 'si', 'y', 'yes']:
        print("❌ Operación cancelada")
        sys.exit(0)
    
    # Extraer datos existentes
    data = backup_and_extract_data()
    
    # Reconstruir base de datos
    if rebuild_database(data):
        print("\n🎉 ¡Base de datos reconstruida exitosamente!")
        print("\n🚀 Credenciales de acceso:")
        print("   Usuario: admin")
        print("   Contraseña: isaias52")
        print("   Rol: administrador")
        print("\n💡 Reinicia la aplicación:")
        print("   pkill -f 'python.*run.py'")
        print("   python run.py &")
    else:
        print("\n❌ Error reconstruyendo la base de datos")
        sys.exit(1)

if __name__ == "__main__":
    main()
