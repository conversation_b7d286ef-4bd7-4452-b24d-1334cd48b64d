<!-- app/templates/audit/dashboard.html -->
{% extends "base.html" %}

{% block title %}Dashboard de Auditoría{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .activity-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .log-item {
        border-left: 4px solid #e9ecef;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 0 8px 8px 0;
        transition: all 0.3s ease;
    }

    .log-item:hover {
        background: #e9ecef;
        border-left-color: #007bff;
    }

    .log-item.CREATE { border-left-color: #28a745; }
    .log-item.UPDATE { border-left-color: #ffc107; }
    .log-item.DELETE { border-left-color: #dc3545; }
    .log-item.LOGIN { border-left-color: #17a2b8; }
    .log-item.LOGOUT { border-left-color: #6c757d; }
    .log-item.VIEW { border-left-color: #007bff; }
    .log-item.EXPORT { border-left-color: #6f42c1; }

    .action-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }

    .module-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .module-item:last-child {
        border-bottom: none;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
        background: linear-gradient(90deg, #007bff, #0056b3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-chart-line"></i> Dashboard de Auditoría</h2>
            <p class="text-muted">Monitoreo y análisis de actividad del sistema</p>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> {{ error }}
    </div>
    {% else %}

    <!-- Estadísticas Principales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ stats.total_actions or 0 }}</div>
                <div class="stats-label">
                    <i class="fas fa-list"></i> Total de Acciones
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ stats.recent_activity or 0 }}</div>
                <div class="stats-label">
                    <i class="fas fa-clock"></i> Últimas 24 Horas
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ stats.active_users|length or 0 }}</div>
                <div class="stats-label">
                    <i class="fas fa-users"></i> Usuarios Activos
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ stats.actions_by_type|length or 0 }}</div>
                <div class="stats-label">
                    <i class="fas fa-cogs"></i> Tipos de Acción
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos y Actividad -->
    <div class="row">
        <!-- Actividad Reciente -->
        <div class="col-md-8">
            <div class="activity-card">
                <h5><i class="fas fa-history"></i> Actividad Reciente</h5>
                <div class="d-flex justify-content-between mb-3">
                    <small class="text-muted">Últimas 50 acciones</small>
                    <a href="{{ url_for('audit.logs') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list"></i> Ver Todos
                    </a>
                </div>

                {% if recent_logs %}
                <div style="max-height: 400px; overflow-y: auto;">
                    {% for log in recent_logs %}
                    <div class="log-item {{ log.action }}" onclick="showLogDetail({{ log.id }})">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <i class="{{ log.get_action_icon() }} me-2"></i>
                                    <strong>{{ log.username }}</strong>
                                    <span class="action-badge bg-{{ log.get_action_color() }} text-white ms-2">
                                        {{ log.action }}
                                    </span>
                                </div>
                                <div class="text-muted small">
                                    {{ log.get_readable_description() }}
                                    {% if log.module %}
                                    <span class="badge bg-light text-dark ms-1">{{ log.module }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">
                                    {{ log.timestamp.strftime('%d/%m %H:%M') }}
                                </small>
                                {% if log.user_ip %}
                                <br><small class="text-muted">{{ log.user_ip }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No hay actividad reciente</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Panel Lateral -->
        <div class="col-md-4">
            <!-- Acciones por Tipo -->
            <div class="activity-card mb-3">
                <h6><i class="fas fa-chart-pie"></i> Acciones por Tipo</h6>
                {% if stats.actions_by_type %}
                {% for action, count in stats.actions_by_type.items() %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="action-badge bg-{{
                        'success' if action == 'CREATE' else
                        'warning' if action == 'UPDATE' else
                        'danger' if action == 'DELETE' else
                        'info' if action in ['LOGIN', 'VIEW'] else
                        'secondary'
                    }} text-white">{{ action }}</span>
                    <strong>{{ count }}</strong>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted small">No hay datos disponibles</p>
                {% endif %}
            </div>

            <!-- Usuarios Más Activos -->
            <div class="activity-card mb-3">
                <h6><i class="fas fa-user-friends"></i> Usuarios Más Activos (24h)</h6>
                {% if stats.active_users %}
                {% for username, count in stats.active_users %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><i class="fas fa-user"></i> {{ username }}</span>
                    <span class="badge bg-primary">{{ count }}</span>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted small">No hay actividad reciente</p>
                {% endif %}
            </div>

            <!-- Módulos Más Utilizados -->
            <div class="activity-card">
                <h6><i class="fas fa-cubes"></i> Módulos Más Utilizados</h6>
                {% if module_activity %}
                {% for module, count in module_activity %}
                <div class="module-item">
                    <span>{{ module or 'Sin especificar' }}</span>
                    <span class="badge bg-info">{{ count }}</span>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted small">No hay datos de módulos</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Actividad por Día -->
    {% if daily_activity %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="activity-card">
                <h5><i class="fas fa-calendar-alt"></i> Actividad por Día (Últimos 7 días)</h5>
                <div class="chart-container">
                    <canvas id="dailyActivityChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Acciones Rápidas -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="activity-card">
                <h5><i class="fas fa-tools"></i> Acciones Rápidas</h5>
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('audit.logs') }}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-list"></i> Ver Todos los Logs
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('audit.sessions') }}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-users"></i> Sesiones de Usuario
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('audit.export_logs') }}" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-download"></i> Exportar Logs
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button onclick="refreshStats()" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="fas fa-sync"></i> Actualizar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% endif %}
</div>

<!-- Modal para Detalle de Log -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detalle de Acción</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="logDetailContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Cargando...
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Gráfico de actividad diaria
{% if daily_activity %}
const dailyData = {
    labels: [
        {% for date, count in daily_activity %}
        '{{ date.strftime("%d/%m") }}',
        {% endfor %}
    ],
    datasets: [{
        label: 'Acciones',
        data: [
            {% for date, count in daily_activity %}
            {{ count }},
            {% endfor %}
        ],
        borderColor: '#007bff',
        backgroundColor: 'rgba(0, 123, 255, 0.1)',
        tension: 0.4,
        fill: true
    }]
};

const dailyConfig = {
    type: 'line',
    data: dailyData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
};

new Chart(document.getElementById('dailyActivityChart'), dailyConfig);
{% endif %}

// Función para mostrar detalle de log
function showLogDetail(logId) {
    $('#logDetailModal').modal('show');

    fetch(`/audit/log/${logId}`)
        .then(response => response.text())
        .then(html => {
            // Extraer solo el contenido del modal
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const content = doc.querySelector('.modal-body');
            document.getElementById('logDetailContent').innerHTML = content.innerHTML;
        })
        .catch(error => {
            document.getElementById('logDetailContent').innerHTML =
                '<div class="alert alert-danger">Error cargando detalle</div>';
        });
}

// Función para actualizar estadísticas
function refreshStats() {
    location.reload();
}

// Auto-refresh cada 5 minutos
setInterval(function() {
    fetch('/audit/api/stats')
        .then(response => response.json())
        .then(data => {
            // Actualizar contadores sin recargar la página
            console.log('Estadísticas actualizadas', data);
        })
        .catch(error => console.error('Error actualizando estadísticas:', error));
}, 300000); // 5 minutos
</script>
{% endblock %}
