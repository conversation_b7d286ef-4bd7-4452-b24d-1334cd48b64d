#!/usr/bin/env python3
"""
Script para aplicar la corrección al archivo auth_router.py
"""

import os
import shutil

def apply_fix():
    print("Aplicando corrección al archivo auth_router.py...")
    
    # Rutas de los archivos
    original_file = "app/api/v1/auth_router.py"
    fixed_file = "app/api/v1/auth_router_fix.py"
    backup_file = "app/api/v1/auth_router.py.bak"
    
    # Verificar que los archivos existen
    if not os.path.exists(fixed_file):
        print(f"Error: El archivo {fixed_file} no existe.")
        return
    
    if not os.path.exists(original_file):
        print(f"Error: El archivo {original_file} no existe.")
        return
    
    # Hacer una copia de seguridad del archivo original
    try:
        shutil.copy2(original_file, backup_file)
        print(f"Copia de seguridad creada: {backup_file}")
    except Exception as e:
        print(f"Error al crear la copia de seguridad: {e}")
        return
    
    # Reemplazar el archivo original con la versión corregida
    try:
        shutil.copy2(fixed_file, original_file)
        print(f"Archivo {original_file} reemplazado con éxito.")
    except Exception as e:
        print(f"Error al reemplazar el archivo: {e}")
        # Intentar restaurar la copia de seguridad
        try:
            shutil.copy2(backup_file, original_file)
            print("Se ha restaurado la copia de seguridad.")
        except:
            print("No se pudo restaurar la copia de seguridad.")
    
    print("Corrección aplicada. Reinicie el servicio para que los cambios surtan efecto.")

if __name__ == "__main__":
    apply_fix()
