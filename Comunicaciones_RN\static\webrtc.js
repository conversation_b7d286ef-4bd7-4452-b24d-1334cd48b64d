// /static/webrtc.js 

document.addEventListener('DOMContentLoaded', function () {
    const socket = io.connect('https://' + document.domain + ':' + location.port + '/node', {
        query: {
            node_id: nodeId,
            username: username
        }
    });
    
    socket.on('connect_error', (error) => {
        console.error('Connection failed, attempting reconnection:', error);
        socket.io.opts.query = {
            node_id: nodeId,
            username: username
        };
        socket.connect();
    });
    
    socket.on('reconnect_attempt', (attemptNumber) => {
        console.log('Reconnection attempt:', attemptNumber);
    });
    
    socket.on('reconnect_failed', () => {
        console.error('Reconnection failed.');
    });
    
    socket.on('reconnect', (attemptNumber) => {
        console.log('Reconnected successfully after', attemptNumber, 'attempts');
    });

    // Selección de elementos del DOM
    const messageInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendButton');
    const messagesDiv = document.getElementById('messages');
    const startAudioButton = document.getElementById('startAudio');
    const stopAudioButton = document.getElementById('stopAudio');
    const transmitAudioButton = document.getElementById('transmitAudio');
    const userListDiv = document.getElementById('userList'); // Asegúrate de que este elemento exista
    const currentTransmitter = document.getElementById('currentTransmitter'); // Elemento para mostrar el usuario actual que está transmitiendo

    let mediaRecorder;
    let audioChunks = [];

    console.log('DOM fully loaded and parsed.');

    // Función para enviar mensajes
    function sendMessage() {
        const message = messageInput.value.trim();
        if (message !== '') {
            socket.emit('send_message', {
                data: message,
                user: "{{ current_user.username }}",
                node_id: "{{ node.id }}"
            });
            messageInput.value = ''; // Limpia el campo de entrada de mensaje
        }
    }

    // Verificar la existencia de los elementos antes de agregar eventos
    if (sendButton && messageInput) {
        // Evento para enviar mensajes de texto con el botón "Enviar"
        sendButton.addEventListener('click', sendMessage);

        // Evento para enviar mensajes de texto con la tecla "Enter"
        messageInput.addEventListener('keypress', function (event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
    } else {
        console.error('chatInput or sendButton not found.');
    }

    // Recibe y muestra mensajes de otros usuarios
    socket.on('broadcast_message', function (data) {
        const newMessage = document.createElement('p');
        newMessage.className = 'message';
        newMessage.innerHTML = `<strong>${data.user}:</strong> ${data.data} <span class="timestamp">${data.timestamp}</span>`;
        messagesDiv.appendChild(newMessage);

        // Mostrar notificación para el otro usuario
        if (data.user !== "{{ current_user.username }}") {
            alert(`Nuevo mensaje de ${data.user}`);
        }
    });

    // Evento para dejar la sala al cerrar la ventana o pestaña
    window.addEventListener('beforeunload', function () {
        socket.emit('leave', { username: "{{ current_user.username }}", node_id: "{{ node.id }}" });
    });

    if (startAudioButton) {
        // Inicia la recepción de audio del canal
        startAudioButton.addEventListener('click', function () {
            console.log('Start Audio button clicked.');
            socket.on('receive_audio', function (data) {
                const audioBlob = new Blob([new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))], { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);
                audio.play();
            });
        });
    } else {
        console.error('startAudioButton not found.');
    }

    if (stopAudioButton) {
        // Detiene la recepción de audio del canal
        stopAudioButton.addEventListener('click', function () {
            console.log('Stop Audio button clicked.');
            socket.off('receive_audio');
        });
    } else {
        console.error('stopAudioButton not found.');
    }

    // Función para iniciar la grabación de audio
    async function startRecording() {
        console.log('Transmit Audio button pressed.');
        socket.emit('audio_start', { user: "{{ current_user.username }}", node_id: "{{ node.id }}" });
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('getUserMedia is not supported in this browser.');
            alert('getUserMedia is not supported in this browser.');
            return;
        }
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);
            mediaRecorder.ondataavailable = function (event) {
                audioChunks.push(event.data);
            };
            mediaRecorder.onstop = function () {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                audioChunks = [];
                const reader = new FileReader();
                reader.onload = function () {
                    const base64AudioMessage = reader.result.split(',')[1];
                    console.log(`Sending audio from ${username} to node ${nodeId}`);
                    socket.emit('transmit_audio', {
                        audio: base64AudioMessage,
                        user: "{{ current_user.username }}",
                        node_id: "{{ node.id }}"
                    });
                    console.log('Audio transmitted.');
                };
                reader.readAsDataURL(audioBlob);
            };
            mediaRecorder.start();
            console.log('Audio recording started.');
        } catch (error) {
            console.error('Error accessing media devices.', error);
            alert('Error accessing media devices: ' + error.message);
        }
    }

    // Función para detener la grabación de audio
    function stopRecording() {
        console.log('Transmit Audio button released.');
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
            mediaRecorder.stop();
            console.log('Audio recording stopped.');
            socket.emit('audio_end', { user: "{{ current_user.username }}", node_id: "{{ node.id }}" });
        }
    }   

    if (transmitAudioButton) {
        // Transmite el audio grabado al servidor mientras se mantiene presionado
        transmitAudioButton.addEventListener('mousedown', startRecording);
        transmitAudioButton.addEventListener('mouseup', stopRecording);
        transmitAudioButton.addEventListener('touchstart', startRecording);
        transmitAudioButton.addEventListener('touchend', stopRecording);
    } else {
        console.error('transmitAudioButton not found.');
    }

    // Función para actualizar la lista de usuarios conectados
    function updateConnectedUsers(users) {
        if (userListDiv) {
            userListDiv.innerHTML = '<h3>Usuarios Conectados</h3>';
            users.forEach(user => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.innerHTML = `<a href="/private_chat/${user}">${user}</a> <img src="/static/audio-icon.png" class="audio-icon" id="audio-${user}" style="display: none;">`;
                userListDiv.appendChild(userItem);
            });
        } else {
            console.error('userListDiv not found.');
        }
    }

    // Escucha el evento 'update_users' para actualizar la lista de usuarios
    socket.on('update_users', function (users) {
        console.log('Received update_users event with users:', users);
        updateConnectedUsers(users);
    });

    // Escucha el evento 'audio_start' para mostrar el nombre del usuario que está transmitiendo
    socket.on('audio_start', function (data) {
        if (currentTransmitter) {
            currentTransmitter.textContent = `Transmitting: ${data.user}`;
        }
        if (transmitAudioButton) {
            transmitAudioButton.disabled = true; // Deshabilitar el botón de transmisión
        }

        // Mostrar el icono de audio junto al usuario que está transmitiendo
        const audioIcon = document.getElementById(`audio-${data.user}`);
        if (audioIcon) {
            audioIcon.style.display = 'inline'; // Mostrar el icono de audio
        }
    });

    // Escucha el evento 'audio_end' para limpiar el nombre del usuario que estaba transmitiendo
    socket.on('audio_end', function (data) {
        if (currentTransmitter) {
            currentTransmitter.textContent = '';
        }
        if (transmitAudioButton) {
            transmitAudioButton.disabled = false; // Habilitar el botón de transmisión
        }

        // Ocultar el icono de audio junto al usuario que dejó de transmitir
        const audioIcon = document.getElementById(`audio-${data.user}`);
        if (audioIcon) {
            audioIcon.style.display = 'none'; // Ocultar el icono de audio
        }
    });

    // Escucha el evento 'audio_busy' para notificar al usuario que el canal está ocupado
    socket.on('audio_busy', function (data) {
        alert(`Audio transmission is currently busy by ${data.user}. Please wait.`);
    });

    // Solicita la lista de usuarios conectados al iniciar
    socket.emit('get_connected_users', { node_id: "{{ node.id }}" });
});

socket.on('global_audio_start', function(data) {
    if (data.node_id === nodeId) {
        console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
        // Aquí puedes iniciar la reproducción de audio o mostrar una notificación
    }
});

socket.on('global_audio_end', function(data) {
    if (data.node_id === nodeId) {
        console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
        // Aquí puedes detener la reproducción de audio o mostrar una notificación
    }
});
