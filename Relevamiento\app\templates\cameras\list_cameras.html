<!-- app/templates/cameras/list_cameras.html -->
{% extends "base.html" %}

{% block title %}Gestionar Cámaras - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Gestionar Cámaras</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">{{ total_cameras }} cámara{{ 's' if total_cameras != 1 else '' }} encontrada{{ 's' if total_cameras != 1 else '' }}</span>
    </div>
</div>

<!-- Formulario de Filtros -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">🔍 Filtros de Búsqueda</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('points.list_cameras') }}">
            <div class="row g-3">
                <!-- Primera fila -->
                <div class="col-md-2">
                    {{ form.camera_id.label(class="form-label") }}
                    {{ form.camera_id(class="form-control form-control-sm", placeholder="123") }}
                </div>
                <div class="col-md-2">
                    {{ form.point_id.label(class="form-label") }}
                    {{ form.point_id(class="form-control form-control-sm", placeholder="456") }}
                </div>
                <div class="col-md-2">
                    {{ form.camera_type.label(class="form-label") }}
                    {{ form.camera_type(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-3">
                    {{ form.city.label(class="form-label") }}
                    {{ form.city(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-3">
                    {{ form.source.label(class="form-label") }}
                    {{ form.source(class="form-select form-select-sm") }}
                </div>

                <!-- Segunda fila -->
                <div class="col-md-2">
                    {{ form.location_source.label(class="form-label") }}
                    {{ form.location_source(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-2">
                    {{ form.has_photo.label(class="form-label") }}
                    {{ form.has_photo(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-2">
                    {{ form.has_coordinates.label(class="form-label") }}
                    {{ form.has_coordinates(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-3">
                    {{ form.date_from.label(class="form-label") }}
                    {{ form.date_from(class="form-control form-control-sm") }}
                </div>
                <div class="col-md-3">
                    {{ form.date_to.label(class="form-label") }}
                    {{ form.date_to(class="form-control form-control-sm") }}
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="bi bi-search"></i> Aplicar Filtros
                    </button>
                    <a href="{{ url_for('points.list_cameras') }}" class="btn btn-outline-secondary btn-sm ms-2">
                        <i class="bi bi-x-circle"></i> Limpiar Filtros
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabla de Resultados -->
{% if cameras_data %}
<div class="table-responsive">
    <table class="table table-striped table-hover table-sm">
        <thead class="table-dark">
            <tr>
                <th>ID</th>
                <th>Punto</th>
                <th>Tipo</th>
                <th>Dirección</th>
                <th>📍 Coordenadas</th>
                <th>Fuente</th>
                <th>Foto</th>
                <th>Creado</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tbody>
            {% for camera in cameras_data %}
            <tr>
                <td><strong>{{ camera.id }}</strong></td>
                <td>
                    <a href="{{ url_for('points.detail', point_id=camera.point_id) }}" class="text-decoration-none">
                        <strong>{{ camera.point_name }}</strong>
                    </a>
                    <br>
                    <small class="text-muted">ID: {{ camera.point_id }}</small>
                    {% if camera.point_city %}
                        <br><small class="text-muted">📍 {{ camera.point_city }}</small>
                    {% endif %}
                </td>
                <td>
                    <span class="badge bg-secondary">{{ camera.type.capitalize() }}</span>
                </td>
                <td>{{ camera.direction }}</td>
                <td>
                    {% if camera.coordinates %}
                        <small class="font-monospace">
                            {{ "%.6f"|format(camera.coordinates.lat) }},<br>
                            {{ "%.6f"|format(camera.coordinates.lon) }}
                        </small>
                        {% if camera.coordinates.accuracy %}
                            <br><small class="text-muted">±{{ "%.1f"|format(camera.coordinates.accuracy) }}m</small>
                        {% endif %}
                    {% else %}
                        <span class="text-muted">Sin coordenadas</span>
                    {% endif %}
                </td>
                <td>
                    {% if camera.has_own_coordinates %}
                        <span class="badge bg-success">{{ camera.location_source|upper }}</span>
                    {% else %}
                        <span class="badge bg-info">PUNTO</span>
                    {% endif %}
                </td>
                <td>
                    {% if camera.photo_filename %}
                        <img src="{{ url_for('static', filename='uploads/' ~ camera.photo_filename) }}"
                             alt="Foto Cámara" width="50" class="rounded">
                    {% else %}
                        <span class="text-muted">Sin foto</span>
                    {% endif %}
                </td>
                <td>
                    <small>{{ camera.created_at.strftime('%d/%m/%Y') }}</small>
                    <br>
                    <small class="text-muted">{{ camera.created_at.strftime('%H:%M') }}</small>
                </td>
                <td>
                    <form action="{{ url_for('points.delete_camera', camera_id=camera.id) }}" method="POST" class="d-inline"
                          onsubmit="return confirm('¿Estás seguro de que quieres eliminar esta cámara?');">
                        <button type="submit" class="btn btn-danger btn-sm" title="Eliminar Cámara">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="alert alert-info mt-3">
    <i class="bi bi-info-circle"></i>
    {% if request.args %}
        No se encontraron cámaras que coincidan con los filtros aplicados.
    {% else %}
        No hay cámaras registradas en el sistema.
    {% endif %}
</div>
{% endif %}

<!-- Información adicional -->
{% if cameras_data %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">📊 Estadísticas</h6>
                <p class="card-text">
                    <strong>Total de cámaras:</strong> {{ total_cameras }}<br>
                    <strong>Con coordenadas propias:</strong> {{ cameras_data|selectattr('has_own_coordinates')|list|length }}<br>
                    <strong>Con foto:</strong> {{ cameras_data|selectattr('photo_filename')|list|length }}
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">🎯 Tipos de Cámara</h6>
                <p class="card-text">
                    {% set domo_count = cameras_data|selectattr('type', 'equalto', 'domo')|list|length %}
                    {% set fija_count = cameras_data|selectattr('type', 'equalto', 'fija')|list|length %}
                    {% set otra_count = cameras_data|selectattr('type', 'equalto', 'otra')|list|length %}
                    <span class="badge bg-success">Domo: {{ domo_count }}</span>
                    <span class="badge bg-warning">Fija: {{ fija_count }}</span>
                    <span class="badge bg-secondary">Otra: {{ otra_count }}</span>
                </p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
{% endblock %}