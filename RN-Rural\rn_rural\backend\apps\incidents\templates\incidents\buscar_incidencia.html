{# File: backend/apps/incidents/templates/incidents/buscar_incidencia.html #}
{% extends "core/base.html" %}

{% block title %}Buscar Incidencia - RN-Rural{% endblock %}

{% block extra_head %}
<style>
    .card-dashboard {
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    .card-dashboard:hover {
        transform: translateY(-5px);
    }
    .card-header-custom {
        border-radius: 10px 10px 0 0;
        padding: 15px;
        font-weight: bold;
    }
    .bg-nueva { background-color: #0dcaf0; color: white; }
    .bg-proceso { background-color: #ffc107; color: black; }
    .bg-derivada { background-color: #fd7e14; color: white; }
    .bg-resuelta { background-color: #198754; color: white; }
    .bg-cerrada { background-color: #6c757d; color: white; }
    .estado-badge {
        font-size: 0.85rem;
        padding: 6px 10px;
        border-radius: 20px;
    }
    .table-custom {
        border-collapse: separate;
        border-spacing: 0 8px;
    }
    .table-custom thead th {
        border-bottom: none;
        background-color: #f8f9fa;
        padding: 12px 15px;
        font-weight: 600;
    }
    .table-custom tbody tr {
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border-radius: 8px;
        background-color: white;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    .table-custom tbody tr:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .table-custom td {
        padding: 15px;
        vertical-align: middle;
        border-top: none;
    }
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    .search-box {
        position: relative;
        margin-bottom: 20px;
    }
    .search-box input {
        padding-left: 40px;
        border-radius: 20px;
    }
    .search-box i {
        position: absolute;
        left: 15px;
        top: 10px;
        color: #6c757d;
    }
    .search-options {
        margin-bottom: 20px;
    }
    .description-cell {
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-3">Buscar Incidencia</h2>
    <hr>

    <div class="card card-dashboard">
        <div class="card-header card-header-custom bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-search"></i> Criterios de Búsqueda</h4>
            </div>
        </div>
        <div class="card-body">
            <form method="get" action="{% url 'incidents:buscar_incidencia' %}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_incidencia" class="form-label"><i class="fas fa-hashtag"></i> ID de Incidencia</label>
                            <input type="text" class="form-control" id="id_incidencia" name="id" value="{{ request.GET.id|default:'' }}" placeholder="Ej: 123">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_usuario" class="form-label"><i class="fas fa-user"></i> Usuario que Reportó</label>
                            <input type="text" class="form-control" id="id_usuario" name="usuario" value="{{ request.GET.usuario|default:'' }}" placeholder="Nombre de usuario">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_estado" class="form-label"><i class="fas fa-tasks"></i> Estado</label>
                            <select class="form-select" id="id_estado" name="estado">
                                <option value="" {% if not request.GET.estado %}selected{% endif %}>Todos los estados</option>
                                <option value="NUEVA" {% if request.GET.estado == 'NUEVA' %}selected{% endif %}>Nueva</option>
                                <option value="ASIGNADA_OPERADOR" {% if request.GET.estado == 'ASIGNADA_OPERADOR' %}selected{% endif %}>Asignada a Operador</option>
                                <option value="EN_PROCESO_OPERADOR" {% if request.GET.estado == 'EN_PROCESO_OPERADOR' %}selected{% endif %}>En Proceso por Operador</option>
                                <option value="DERIVADA_BRIGADA" {% if request.GET.estado == 'DERIVADA_BRIGADA' %}selected{% endif %}>Derivada a Brigada</option>
                                <option value="CERRADA_RESUELTA" {% if request.GET.estado == 'CERRADA_RESUELTA' %}selected{% endif %}>Cerrada - Resuelta</option>
                                <option value="CERRADA_NO_RESUELTA" {% if request.GET.estado == 'CERRADA_NO_RESUELTA' %}selected{% endif %}>Cerrada - No Resuelta</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_texto" class="form-label"><i class="fas fa-comment-alt"></i> Texto en Descripción</label>
                            <input type="text" class="form-control" id="id_texto" name="texto" value="{{ request.GET.texto|default:'' }}" placeholder="Palabras clave">
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{% url 'incidents:buscar_incidencia' %}" class="btn btn-secondary me-md-2">
                        <i class="fas fa-redo"></i> Limpiar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Buscar
                    </button>
                </div>
            </form>
        </div>
    </div>

    {% if incidencias %}
        <div class="card card-dashboard">
            <div class="card-header card-header-custom bg-success text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fas fa-list"></i> Resultados de la Búsqueda</h4>
                    <span class="badge bg-light text-dark">{{ incidencias.count }} incidencia(s) encontrada(s)</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-custom">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Usuario</th>
                                <th>Fecha</th>
                                <th>Estado</th>
                                <th>Descripción</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for incidencia in incidencias %}
                            <tr data-estado="{{ incidencia.estado }}">
                                <td><strong>#{{ incidencia.id }}</strong></td>
                                <td>
                                    <i class="fas fa-user"></i> {{ incidencia.usuario_reporta.username }}
                                </td>
                                <td>
                                    <i class="fas fa-calendar-alt"></i> {{ incidencia.fecha_creacion|date:"d/m/Y H:i" }}
                                </td>
                                <td>
                                    {% if incidencia.estado == 'NUEVA' %}
                                        <span class="badge bg-nueva estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% elif incidencia.estado == 'ASIGNADA_OPERADOR' or incidencia.estado == 'EN_PROCESO_OPERADOR' %}
                                        <span class="badge bg-proceso estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% elif incidencia.estado == 'DERIVADA_BRIGADA' %}
                                        <span class="badge bg-derivada estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% elif incidencia.estado == 'CERRADA_RESUELTA' %}
                                        <span class="badge bg-resuelta estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% else %}
                                        <span class="badge bg-cerrada estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% endif %}
                                </td>
                                <td class="description-cell" title="{{ incidencia.descripcion_texto }}">
                                    {{ incidencia.descripcion_texto|truncatewords:10 }}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{% url 'incidents:detalle_incidencia_operador' pk=incidencia.pk %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> Ver
                                        </a>
                                    </div>
                                </td>                
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% elif search_performed %}
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle"></i> No se encontraron incidencias que coincidan con los criterios de búsqueda.
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
{% endblock %}
