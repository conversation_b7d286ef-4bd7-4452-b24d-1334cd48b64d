<!-- /app/templates/pastorado/members_list.html -->
{% extends "base.html" %}
{# Importar macros si las usas, ej. paginación #}
{% from "_pagination.html" import render_pagination %}

{% block title %}Miembros de tu Iglesia{% endblock %}

{% block content %}
<div class="container my-4">
    {# Ajusta el título para que sea más general o muestre el nombre de la iglesia si está disponible #}
    <h1>Miembros de la Iglesia: {{ current_user.church.name if current_user.church else 'Iglesia No Asignada' }}</h1>

    {# Incluir formulario de búsqueda si se implementa la búsqueda en la ruta `pastor_members` #}
    <div class="card card-body bg-light mb-4 shadow-sm">
        <form method="GET" action="{{ url_for('routes.pastor_members') }}" class="form-inline">
            <div class="input-group flex-grow-1">
                 <input class="form-control" type="search" name="search" placeholder="Buscar miembro por nombre, usuario, email..." value="{{ search_term or '' }}">
                 <div class="input-group-append">
                     <button class="btn btn-primary" type="submit"><i class="fas fa-search"></i> Buscar</button>
                      <a href="{{ url_for('routes.pastor_members') }}" class="btn btn-secondary" title="Limpiar búsqueda">Limpiar</a>
                 </div>
            </div>
            {# Botón para registrar nuevo miembro #}
            <a href="{{ url_for('routes.register_member') }}" class="btn btn-success ml-md-3 mt-2 mt-md-0"><i class="fas fa-user-plus"></i> Registrar Nuevo</a>
        </form>
    </div>

    {# Mensaje si no hay miembros #}
    {% if not members %}
        <div class="alert alert-info">
            {% if search_term %}
                No se encontraron miembros que coincidan con tu búsqueda "{{ search_term }}".
                 <a href="{{ url_for('routes.pastor_members') }}" class="alert-link">Ver todos</a>
            {% else %}
                Aún no hay miembros registrados en tu iglesia.
                <a href="{{ url_for('routes.register_member') }}" class="alert-link">¡Registra el primero!</a>
            {% endif %}
        </div>
    {% else %}
        <div class="table-responsive"> {# Para tablas anchas en móviles #}
            <table class="table table-striped table-hover">
                <thead class="thead-light"> {# Cabecera con fondo claro #}
                <tr>
                    <th>ID</th>
                    <th>Nombre Completo</th>
                    {# <th>Rol</th> #} {# No necesario si solo muestras miembros #}
                    <th>Función/Cargo</th>
                    <th>Dirección</th>
                    <th>Edad</th>
                    <th>Acciones</th>
                </tr>
                </thead>
                <tbody>
                {% for user in members %}
                    {# Solo mostrar usuarios con rol 'miembro' en esta lista específica #}
                    {% if user.role == 'miembro' %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.first_name }} {{ user.last_name }}</td>
                            {# <td>{{ user.role|title }}</td> #} {# Comentado/Eliminado #}
                            <td>
                                {# Acceder a las funciones a través de la relación user.member #}
                                {% if user.member and user.member.functions %}
                                    {# Unir los nombres de las funciones con una coma #}
                                    {{ user.member.functions | map(attribute='name') | join(', ') }}
                                {% else %}
                                    <span class="text-muted">Sin Función</span> {# Mensaje si no tiene #}
                                {% endif %}
                            </td>
                            <td>{{ user.address or 'Sin dirección' }}</td>
                            <td>
                                {% if user.date_of_birth %}
                                {{ user.date_of_birth | calculate_age }}
                                {% else %}
                                <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                {# Botón Editar Perfil: Llama a edit_profile, la ruta debe validar permisos #}
                                <a href="{{ url_for('routes.edit_profile', user_id=user.id) }}" class="btn btn-warning btn-sm" title="Editar Perfil"><i class="fas fa-edit"></i></a>
                                {# Botón Ver Detalle: Llama a user_detail, la vista debe mostrar info relevante #}
                                <a href="{{ url_for('routes.user_detail', user_id=user.id) }}" class="btn btn-info btn-sm" title="Ver Detalles"><i class="fas fa-eye"></i></a>
                                {# Botón Iniciar Transferencia #}
                                <a href="{{ url_for('routes.request_member_transfer', user_id=user.id) }}" class="btn btn-primary btn-sm" title="Solicitar Transferencia">
                                    <i class="fas fa-exchange-alt"></i>
                                </a>
                                {# Botón Agregar Histórico (Reseña) #}
                                <a href="{{ url_for('routes.add_review', user_id=user.id) }}" class="btn btn-secondary btn-sm" title="Agregar Histórico/Reseña">
                                    <i class="fas fa-history"></i>
                                </a>
                            </td>
                        </tr>
                     {% endif %} {# Fin del if user.role == 'miembro' #}
                {% endfor %}
                </tbody>
            </table>
        </div> {# Fin table-responsive #}
    {% endif %} {# Fin del if not members #}

    {# Incluir Paginación si se implementa en la ruta #}
    {# {{ render_pagination(pagination, 'routes.pastor_members', search=search_term) }} #}

    <a href="{{ url_for('routes.dashboard') }}" class="btn btn-outline-secondary mt-3"><i class="fas fa-arrow-left"></i> Volver al Escritorio</a>
</div> {# Fin container #}
{% endblock %}