import json
import logging
import asyncio
from typing import Dict, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.models.user import User
from app.models.vehicle import Vehicle
from app.services import user_service, vehicle_service
from app.api import deps
from app.websockets.connection import connection_manager, get_redis_connection

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def location_websocket_endpoint(
    websocket: WebSocket,
    vehicle_id: int = None,
    trip_id: int = None,
    db: Session = Depends(get_db)
):
    """
    Endpoint WebSocket para actualizaciones de ubicación.
    
    Puede ser usado para:
    - Vehículos que envían su ubicación actual
    - Clientes que quieren recibir actualizaciones de ubicación de un vehículo específico
    - Clientes que quieren recibir actualizaciones de ubicación relacionadas con un viaje específico
    """
    # Determinar el grupo al que se conectará
    if vehicle_id:
        group = f"vehicle_{vehicle_id}"
    elif trip_id:
        group = f"trip_{trip_id}"
    else:
        # Si no se especifica ni vehículo ni viaje, rechazar la conexión
        await websocket.close(code=1008, reason="Se requiere vehicle_id o trip_id")
        return
    
    # Aceptar la conexión y añadirla al grupo correspondiente
    try:
        await connection_manager.connect(websocket, group)
        
        # Enviar mensaje de bienvenida
        await connection_manager.send_personal_message(
            websocket,
            {
                "type": "connection_established",
                "message": f"Conectado al grupo {group}"
            }
        )
        
        # Bucle principal para recibir mensajes
        while True:
            try:
                # Esperar a recibir un mensaje
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Procesar el mensaje según su tipo
                message_type = message.get("type", "")
                
                if message_type == "location_update":
                    # Actualizar la ubicación en la base de datos
                    if vehicle_id:
                        await process_vehicle_location_update(db, vehicle_id, message, group)
                    
                elif message_type == "ping":
                    # Responder a los pings para mantener la conexión viva
                    await connection_manager.send_personal_message(
                        websocket,
                        {
                            "type": "pong",
                            "message": "Pong"
                        }
                    )
                
                else:
                    logger.warning(f"Tipo de mensaje desconocido: {message_type}")
                    
            except WebSocketDisconnect:
                connection_manager.disconnect(websocket, group)
                logger.info(f"Cliente desconectado del grupo {group}")
                break
                
            except Exception as e:
                logger.error(f"Error en el bucle de WebSocket: {e}")
                await connection_manager.send_personal_message(
                    websocket,
                    {
                        "type": "error",
                        "message": f"Error: {str(e)}"
                    }
                )
    
    except Exception as e:
        logger.error(f"Error al establecer conexión WebSocket: {e}")
        try:
            await websocket.close(code=1011, reason=f"Error interno: {str(e)}")
        except:
            pass

async def process_vehicle_location_update(db: Session, vehicle_id: int, message: Dict[str, Any], group: str):
    """Procesar una actualización de ubicación de un vehículo."""
    try:
        # Extraer datos de ubicación del mensaje
        latitude = message.get("latitude")
        longitude = message.get("longitude")
        
        if not latitude or not longitude:
            logger.error("Faltan datos de ubicación en el mensaje")
            return
        
        # Actualizar la ubicación del vehículo en la base de datos
        vehicle_service.update_vehicle_location(db, vehicle_id, latitude, longitude)
        
        # Preparar mensaje para broadcast
        broadcast_message = {
            "type": "location_update",
            "vehicle_id": vehicle_id,
            "latitude": latitude,
            "longitude": longitude,
            "timestamp": message.get("timestamp")
        }
        
        # Enviar actualización a todos los clientes en el grupo
        await connection_manager.broadcast_to_group(group, broadcast_message)
        
        # Si el vehículo está asignado a un viaje, también enviar la actualización al grupo del viaje
        vehicle = vehicle_service.get_vehicle(db, vehicle_id)
        if vehicle:
            # Aquí necesitaríamos una forma de obtener el viaje activo del vehículo
            # Por ahora, lo dejamos como un TODO
            # TODO: Implementar la obtención del viaje activo del vehículo
            pass
            
    except Exception as e:
        logger.error(f"Error al procesar actualización de ubicación: {e}")
