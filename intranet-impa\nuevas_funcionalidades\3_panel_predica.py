# 3_panel_predica.py
# Panel de Prédica Semanal para Intranet IMPA

"""
PANEL DE PRÉDICA SEMANAL
========================

Funcionalidades:
- Distribución de temas y pasajes bíblicos semanales
- Planificación de series de prédicas
- Asignación de temas por iglesia/pastor
- Recursos y materiales de apoyo
- Seguimiento de enseñanzas impartidas
- Reportes de cumplimiento
- Calendario de prédicas

Inspirado en Software Redil pero adaptado para corporaciones de iglesias.
"""

from datetime import datetime, date, timedelta
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Date, Enum
from sqlalchemy.orm import relationship
from app import db

# ============================================================================
# 1. MODELOS DE BASE DE DATOS
# ============================================================================

class PreachingSeries(db.Model):
    """Series de prédicas temáticas"""
    __tablename__ = 'preaching_series'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    theme = db.Column(db.String(255))  # Tema general
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    total_weeks = db.Column(db.Integer, default=1)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    target_audience = db.Column(db.Enum('all', 'adults', 'youth', 'children'), default='all')
    difficulty_level = db.Column(db.Enum('basic', 'intermediate', 'advanced'), default='basic')
    is_active = db.Column(db.Boolean, default=True)
    is_corporate = db.Column(db.Boolean, default=False)  # Para toda la corporación
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    created_by = db.relationship('User', backref='created_series')
    weekly_topics = db.relationship('WeeklyPreachingTopic', backref='series', cascade='all, delete-orphan')
    assignments = db.relationship('PreachingAssignment', backref='series', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<PreachingSeries(title='{self.title}')>"

class WeeklyPreachingTopic(db.Model):
    """Temas semanales de prédica"""
    __tablename__ = 'weekly_preaching_topics'
    
    id = db.Column(db.Integer, primary_key=True)
    series_id = db.Column(db.Integer, db.ForeignKey('preaching_series.id'), nullable=False)
    week_number = db.Column(db.Integer, nullable=False)
    title = db.Column(db.String(255), nullable=False)
    main_scripture = db.Column(db.String(255), nullable=False)  # Ej: "Juan 3:16-21"
    secondary_scriptures = db.Column(db.Text)  # JSON con pasajes adicionales
    key_points = db.Column(db.Text)  # JSON con puntos principales
    objective = db.Column(db.Text)  # Objetivo de la enseñanza
    practical_application = db.Column(db.Text)  # Aplicación práctica
    discussion_questions = db.Column(db.Text)  # JSON con preguntas para grupos
    resources = db.Column(db.Text)  # JSON con recursos adicionales
    estimated_duration = db.Column(db.Integer, default=30)  # Minutos
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    assignments = db.relationship('PreachingAssignment', backref='topic')
    implementations = db.relationship('PreachingImplementation', backref='topic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<WeeklyPreachingTopic(title='{self.title}', week={self.week_number})>"

class PreachingAssignment(db.Model):
    """Asignaciones de prédica a iglesias/pastores"""
    __tablename__ = 'preaching_assignments'
    
    id = db.Column(db.Integer, primary_key=True)
    series_id = db.Column(db.Integer, db.ForeignKey('preaching_series.id'), nullable=False)
    topic_id = db.Column(db.Integer, db.ForeignKey('weekly_preaching_topics.id'), nullable=False)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=False)
    assigned_pastor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    scheduled_date = db.Column(db.Date, nullable=False)
    service_type = db.Column(db.Enum('sunday_morning', 'sunday_evening', 'wednesday', 'special'), default='sunday_morning')
    status = db.Column(db.Enum('assigned', 'prepared', 'delivered', 'cancelled'), default='assigned')
    assigned_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    
    # Relaciones
    church = db.relationship('Church', backref='preaching_assignments')
    assigned_pastor = db.relationship('User', foreign_keys=[assigned_pastor_id], backref='assigned_preachings')
    assigned_by = db.relationship('User', foreign_keys=[assigned_by_id], backref='preaching_assignments_made')
    implementation = db.relationship('PreachingImplementation', backref='assignment', uselist=False)
    
    def __repr__(self):
        return f"<PreachingAssignment(church='{self.church.name}', date='{self.scheduled_date}')>"

class PreachingImplementation(db.Model):
    """Implementación/ejecución de prédicas"""
    __tablename__ = 'preaching_implementations'
    
    id = db.Column(db.Integer, primary_key=True)
    assignment_id = db.Column(db.Integer, db.ForeignKey('preaching_assignments.id'), nullable=False)
    topic_id = db.Column(db.Integer, db.ForeignKey('weekly_preaching_topics.id'), nullable=False)
    actual_date = db.Column(db.Date, nullable=False)
    actual_pastor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    attendance = db.Column(db.Integer)
    duration_minutes = db.Column(db.Integer)
    scripture_used = db.Column(db.String(255))  # Pasaje realmente usado
    key_points_covered = db.Column(db.Text)  # JSON con puntos cubiertos
    adaptations_made = db.Column(db.Text)  # Adaptaciones realizadas
    congregation_response = db.Column(db.Enum('excellent', 'good', 'fair', 'poor'), default='good')
    pastor_notes = db.Column(db.Text)
    follow_up_needed = db.Column(db.Boolean, default=False)
    follow_up_notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    actual_pastor = db.relationship('User', backref='delivered_preachings')
    
    def __repr__(self):
        return f"<PreachingImplementation(date='{self.actual_date}', pastor='{self.actual_pastor.first_name}')>"

class PreachingResource(db.Model):
    """Recursos para prédicas"""
    __tablename__ = 'preaching_resources'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    resource_type = db.Column(db.Enum('document', 'video', 'audio', 'image', 'link'), nullable=False)
    file_path = db.Column(db.String(500))  # Para archivos locales
    external_url = db.Column(db.String(500))  # Para recursos externos
    topic_id = db.Column(db.Integer, db.ForeignKey('weekly_preaching_topics.id'), nullable=True)
    series_id = db.Column(db.Integer, db.ForeignKey('preaching_series.id'), nullable=True)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_public = db.Column(db.Boolean, default=True)
    download_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    topic = db.relationship('WeeklyPreachingTopic', backref='resources')
    series = db.relationship('PreachingSeries', backref='resources')
    uploaded_by = db.relationship('User', backref='uploaded_resources')

# ============================================================================
# 2. SERVICIOS DE PRÉDICA
# ============================================================================

class PreachingService:
    """Servicio para gestión de prédicas"""
    
    @staticmethod
    def create_series(title, description, start_date, created_by_id, **kwargs):
        """Crear nueva serie de prédicas"""
        series = PreachingSeries(
            title=title,
            description=description,
            start_date=start_date,
            created_by_id=created_by_id,
            **kwargs
        )
        db.session.add(series)
        db.session.commit()
        return series
    
    @staticmethod
    def add_weekly_topic(series_id, week_number, title, main_scripture, **kwargs):
        """Añadir tema semanal a una serie"""
        topic = WeeklyPreachingTopic(
            series_id=series_id,
            week_number=week_number,
            title=title,
            main_scripture=main_scripture,
            **kwargs
        )
        db.session.add(topic)
        db.session.commit()
        return topic
    
    @staticmethod
    def assign_to_churches(series_id, church_ids, assigned_by_id, start_date=None):
        """Asignar serie a múltiples iglesias"""
        series = PreachingSeries.query.get(series_id)
        if not series:
            return []
        
        if not start_date:
            start_date = series.start_date
        
        assignments = []
        
        for church_id in church_ids:
            # Obtener pastor principal de la iglesia
            from app.models import Church, User
            church = Church.query.get(church_id)
            pastor = User.query.filter_by(church_id=church_id, role='pastorado').first()
            
            if not pastor:
                continue
            
            # Crear asignaciones para cada tema de la serie
            current_date = start_date
            for topic in series.weekly_topics:
                assignment = PreachingAssignment(
                    series_id=series_id,
                    topic_id=topic.id,
                    church_id=church_id,
                    assigned_pastor_id=pastor.id,
                    scheduled_date=current_date,
                    assigned_by_id=assigned_by_id
                )
                db.session.add(assignment)
                assignments.append(assignment)
                
                # Avanzar una semana
                current_date += timedelta(weeks=1)
        
        db.session.commit()
        return assignments
    
    @staticmethod
    def get_weekly_schedule(church_id=None, week_start=None):
        """Obtener programación semanal de prédicas"""
        if not week_start:
            week_start = date.today() - timedelta(days=date.today().weekday())
        
        week_end = week_start + timedelta(days=6)
        
        query = PreachingAssignment.query.filter(
            PreachingAssignment.scheduled_date >= week_start,
            PreachingAssignment.scheduled_date <= week_end
        )
        
        if church_id:
            query = query.filter(PreachingAssignment.church_id == church_id)
        
        return query.order_by(PreachingAssignment.scheduled_date).all()
    
    @staticmethod
    def mark_as_delivered(assignment_id, actual_pastor_id, attendance=None, **kwargs):
        """Marcar prédica como entregada"""
        assignment = PreachingAssignment.query.get(assignment_id)
        if not assignment:
            return None
        
        # Actualizar estado de asignación
        assignment.status = 'delivered'
        
        # Crear registro de implementación
        implementation = PreachingImplementation(
            assignment_id=assignment_id,
            topic_id=assignment.topic_id,
            actual_date=kwargs.get('actual_date', assignment.scheduled_date),
            actual_pastor_id=actual_pastor_id,
            attendance=attendance,
            **{k: v for k, v in kwargs.items() if k != 'actual_date'}
        )
        
        db.session.add(implementation)
        db.session.commit()
        
        return implementation
    
    @staticmethod
    def generate_preaching_plan(church_id, weeks_ahead=12):
        """Generar plan de prédicas para las próximas semanas"""
        from app.models import Church
        
        church = Church.query.get(church_id)
        if not church:
            return None
        
        # Obtener series activas
        active_series = PreachingSeries.query.filter_by(is_active=True).all()
        
        # Obtener asignaciones existentes
        start_date = date.today()
        end_date = start_date + timedelta(weeks=weeks_ahead)
        
        existing_assignments = PreachingAssignment.query.filter(
            PreachingAssignment.church_id == church_id,
            PreachingAssignment.scheduled_date >= start_date,
            PreachingAssignment.scheduled_date <= end_date
        ).all()
        
        existing_dates = {a.scheduled_date for a in existing_assignments}
        
        # Generar plan para fechas sin asignación
        plan = []
        current_date = start_date
        
        while current_date <= end_date:
            # Solo domingos
            if current_date.weekday() == 6 and current_date not in existing_dates:
                plan.append({
                    'date': current_date,
                    'suggested_series': active_series[0] if active_series else None,
                    'church': church
                })
            
            current_date += timedelta(days=1)
        
        return plan

# ============================================================================
# 3. FORMULARIOS DE PRÉDICA
# ============================================================================

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, BooleanField, DateField
from wtforms.validators import DataRequired, Optional, NumberRange

class PreachingSeriesForm(FlaskForm):
    """Formulario para crear/editar series de prédicas"""
    title = StringField('Título de la Serie', validators=[DataRequired()])
    description = TextAreaField('Descripción', validators=[Optional()])
    theme = StringField('Tema General', validators=[Optional()])
    start_date = DateField('Fecha de Inicio', validators=[DataRequired()])
    end_date = DateField('Fecha de Fin', validators=[Optional()])
    total_weeks = IntegerField('Total de Semanas', validators=[NumberRange(min=1, max=52)], default=1)
    target_audience = SelectField('Audiencia', choices=[
        ('all', 'Todos'),
        ('adults', 'Adultos'),
        ('youth', 'Jóvenes'),
        ('children', 'Niños')
    ], default='all')
    difficulty_level = SelectField('Nivel', choices=[
        ('basic', 'Básico'),
        ('intermediate', 'Intermedio'),
        ('advanced', 'Avanzado')
    ], default='basic')
    is_corporate = BooleanField('Serie Corporativa')
    submit = SubmitField('Guardar Serie')

class WeeklyTopicForm(FlaskForm):
    """Formulario para temas semanales"""
    title = StringField('Título del Tema', validators=[DataRequired()])
    main_scripture = StringField('Pasaje Principal', validators=[DataRequired()])
    secondary_scriptures = TextAreaField('Pasajes Adicionales', validators=[Optional()])
    objective = TextAreaField('Objetivo de la Enseñanza', validators=[Optional()])
    key_points = TextAreaField('Puntos Principales', validators=[Optional()])
    practical_application = TextAreaField('Aplicación Práctica', validators=[Optional()])
    discussion_questions = TextAreaField('Preguntas para Discusión', validators=[Optional()])
    estimated_duration = IntegerField('Duración Estimada (min)', validators=[NumberRange(min=5, max=120)], default=30)
    notes = TextAreaField('Notas Adicionales', validators=[Optional()])
    submit = SubmitField('Guardar Tema')

class PreachingAssignmentForm(FlaskForm):
    """Formulario para asignar prédicas"""
    series_id = SelectField('Serie', coerce=int, validators=[DataRequired()])
    church_ids = SelectField('Iglesias', coerce=int, validators=[DataRequired()])  # Múltiple en template
    start_date = DateField('Fecha de Inicio', validators=[DataRequired()])
    service_type = SelectField('Tipo de Servicio', choices=[
        ('sunday_morning', 'Domingo Mañana'),
        ('sunday_evening', 'Domingo Noche'),
        ('wednesday', 'Miércoles'),
        ('special', 'Especial')
    ], default='sunday_morning')
    notes = TextAreaField('Notas', validators=[Optional()])
    submit = SubmitField('Asignar Prédicas')

class PreachingImplementationForm(FlaskForm):
    """Formulario para registrar implementación"""
    actual_date = DateField('Fecha Real', validators=[DataRequired()])
    attendance = IntegerField('Asistencia', validators=[Optional(), NumberRange(min=0)])
    duration_minutes = IntegerField('Duración (min)', validators=[Optional(), NumberRange(min=5, max=180)])
    scripture_used = StringField('Pasaje Usado', validators=[Optional()])
    congregation_response = SelectField('Respuesta de la Congregación', choices=[
        ('excellent', 'Excelente'),
        ('good', 'Buena'),
        ('fair', 'Regular'),
        ('poor', 'Pobre')
    ], default='good')
    pastor_notes = TextAreaField('Notas del Pastor', validators=[Optional()])
    follow_up_needed = BooleanField('Requiere Seguimiento')
    follow_up_notes = TextAreaField('Notas de Seguimiento', validators=[Optional()])
    submit = SubmitField('Registrar Implementación')

# ============================================================================
# 4. REPORTES DE PRÉDICA
# ============================================================================

class PreachingReports:
    """Generador de reportes de prédicas"""
    
    @staticmethod
    def compliance_report(church_id=None, start_date=None, end_date=None):
        """Reporte de cumplimiento de prédicas"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        query = PreachingAssignment.query.filter(
            PreachingAssignment.scheduled_date >= start_date,
            PreachingAssignment.scheduled_date <= end_date
        )
        
        if church_id:
            query = query.filter(PreachingAssignment.church_id == church_id)
        
        assignments = query.all()
        
        total = len(assignments)
        delivered = len([a for a in assignments if a.status == 'delivered'])
        cancelled = len([a for a in assignments if a.status == 'cancelled'])
        pending = total - delivered - cancelled
        
        return {
            'period': {'start': start_date, 'end': end_date},
            'total_assigned': total,
            'delivered': delivered,
            'cancelled': cancelled,
            'pending': pending,
            'compliance_rate': (delivered / total * 100) if total > 0 else 0,
            'assignments': assignments
        }
    
    @staticmethod
    def series_progress_report(series_id):
        """Reporte de progreso de una serie"""
        series = PreachingSeries.query.get(series_id)
        if not series:
            return None
        
        assignments = PreachingAssignment.query.filter_by(series_id=series_id).all()
        implementations = PreachingImplementation.query.join(PreachingAssignment).filter(
            PreachingAssignment.series_id == series_id
        ).all()
        
        churches_assigned = len(set(a.church_id for a in assignments))
        total_topics = len(series.weekly_topics)
        completed_implementations = len(implementations)
        
        return {
            'series': series,
            'churches_assigned': churches_assigned,
            'total_topics': total_topics,
            'total_assignments': len(assignments),
            'completed_implementations': completed_implementations,
            'completion_rate': (completed_implementations / len(assignments) * 100) if assignments else 0,
            'avg_attendance': sum(i.attendance for i in implementations if i.attendance) / len(implementations) if implementations else 0
        }

print("Panel de Prédica Semanal - Listo para implementar")
print("Funcionalidades incluidas:")
print("- Series temáticas de prédicas")
print("- Asignación automática a iglesias")
print("- Seguimiento de implementación")
print("- Recursos y materiales")
print("- Reportes de cumplimiento")
