-- Crear el tipo enum para la categoría de vehículos si no existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'vehiclecategory') THEN
        CREATE TYPE vehiclecategory AS ENUM ('activo', 'en_mantenimiento', 'inactivo');
    END IF;
END
$$;

-- Agregar la columna category a la tabla vehicles si no existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vehicles' AND column_name = 'category') THEN
        ALTER TABLE vehicles ADD COLUMN category vehiclecategory DEFAULT 'activo';
    END IF;
END
$$;

-- Actualizar los vehículos existentes para asignarles una categoría basada en su estado
UPDATE vehicles SET category = 'activo' WHERE status = 'libre' OR status = 'ocupado';
UPDATE vehicles SET category = 'en_mantenimiento' WHERE status = 'fuera_de_servicio';
UPDATE vehicles SET category = 'inactivo' WHERE status = 'alerta' OR status = 'emergencia';
