{% extends "base_layout.html" %}

{% block title %}Gestión de Vehículos - Panel de Taxis{% endblock %}

{% block head_extra %}
<style>
    .vehicle-card {
        transition: transform 0.3s;
        margin-bottom: 20px;
    }
    .vehicle-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .vehicle-img {
        height: 150px;
        object-fit: cover;
    }
    .status-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .search-container {
        margin-bottom: 20px;
    }
    .filter-buttons {
        margin-bottom: 15px;
    }
    .filter-buttons .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }
    .vehicle-details {
        font-size: 0.9rem;
    }
    .vehicle-details i {
        width: 20px;
        text-align: center;
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Gestión de Vehículos</h2>
                <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addVehicleModal">
                    <i class="bi bi-plus-circle"></i> Nuevo Vehículo
                </button>
            </div>
            <div class="card-body">
                <!-- Estadísticas rápidas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5>Total Vehículos</h5>
                                <h2>{{ total_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>Activos</h5>
                                <h2>{{ active_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h5>En Mantenimiento</h5>
                                <h2>{{ maintenance_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5>Inactivos</h5>
                                <h2>{{ inactive_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Búsqueda y filtros -->
                <div class="search-container">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchVehicle" placeholder="Buscar por placa, modelo o conductor...">
                                <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                    <i class="bi bi-search"></i> Buscar
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filter-buttons d-flex flex-wrap justify-content-end">
                                <h6 class="me-2 mt-2">Estado:</h6>
                                <button class="btn btn-outline-primary filter-btn active" data-filter="all" data-filter-type="status">Todos</button>
                                <button class="btn btn-outline-success filter-btn" data-filter="libre" data-filter-type="status">Libres</button>
                                <button class="btn btn-outline-primary filter-btn" data-filter="ocupado" data-filter-type="status">Ocupados</button>
                                <button class="btn btn-outline-warning filter-btn" data-filter="alerta" data-filter-type="status">En Alerta</button>
                                <button class="btn btn-outline-danger filter-btn" data-filter="emergencia" data-filter-type="status">En Emergencia</button>
                                <button class="btn btn-outline-secondary filter-btn" data-filter="fuera_de_servicio" data-filter-type="status">Fuera de servicio</button>
                            </div>
                            <div class="filter-buttons d-flex flex-wrap justify-content-end mt-2">
                                <h6 class="me-2 mt-2">Categoría:</h6>
                                <button class="btn btn-outline-primary filter-btn" data-filter="all" data-filter-type="category">Todos</button>
                                <button class="btn btn-outline-success filter-btn" data-filter="activo" data-filter-type="category">Activos</button>
                                <button class="btn btn-outline-warning filter-btn" data-filter="en_mantenimiento" data-filter-type="category">En Mantenimiento</button>
                                <button class="btn btn-outline-danger filter-btn" data-filter="inactivo" data-filter-type="category">Inactivos</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vehículos en tarjetas -->
                <div class="row" id="vehiclesContainer">
                    {% for vehicle in vehicles %}
                    <div class="col-md-4 col-lg-3 vehicle-item" data-status="{{ vehicle.status }}" data-category="{{ vehicle.category }}">
                        <div class="card vehicle-card">
                            {% if vehicle.status == 'libre' %}
                            <span class="badge bg-success status-badge">Libre</span>
                            {% elif vehicle.status == 'ocupado' %}
                            <span class="badge bg-primary status-badge">Ocupado</span>
                            {% elif vehicle.status == 'alerta' %}
                            <span class="badge bg-warning text-dark status-badge">Alerta</span>
                            {% elif vehicle.status == 'emergencia' %}
                            <span class="badge bg-danger status-badge">Emergencia</span>
                            {% elif vehicle.status == 'fuera_de_servicio' %}
                            <span class="badge bg-secondary status-badge">Fuera de servicio</span>
                            {% else %}
                            <span class="badge bg-info status-badge">{{ vehicle.status }}</span>
                            {% endif %}

                            <img src="{{ vehicle.image_url|default('/static/img/default-car.jpg') }}" class="card-img-top vehicle-img" alt="{{ vehicle.model }}">
                            <div class="card-body">
                                <h5 class="card-title">{{ vehicle.brand }} {{ vehicle.model }}</h5>
                                <h6 class="card-subtitle mb-2 text-muted">Placa: {{ vehicle.license_plate }}</h6>

                                <div class="vehicle-details mt-3">
                                    <p class="mb-1"><i class="bi bi-person-fill"></i> {{ vehicle.driver_name|default('Sin conductor') }}</p>
                                    <p class="mb-1"><i class="bi bi-calendar-event"></i> Año: {{ vehicle.year }}</p>
                                    <p class="mb-1"><i class="bi bi-palette-fill"></i> Color: {{ vehicle.color }}</p>
                                    <p class="mb-1"><i class="bi bi-speedometer2"></i> {{ vehicle.mileage }} km</p>
                                    <p class="mb-1"><i class="bi bi-tag-fill"></i> Categoría:
                                        {% if vehicle.category == 'activo' %}
                                        <span class="badge bg-success">Activo</span>
                                        {% elif vehicle.category == 'en_mantenimiento' %}
                                        <span class="badge bg-warning text-dark">En Mantenimiento</span>
                                        {% elif vehicle.category == 'inactivo' %}
                                        <span class="badge bg-danger">Inactivo</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ vehicle.category }}</span>
                                        {% endif %}
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between mt-3">
                                    <button class="btn btn-sm btn-primary" onclick="viewVehicle({{ vehicle.id }})">
                                        <i class="bi bi-eye"></i> Ver
                                    </button>
                                    <button class="btn btn-sm btn-warning" onclick="editVehicle({{ vehicle.id }})">
                                        <i class="bi bi-pencil"></i> Editar
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteVehicle({{ vehicle.id }})">
                                        <i class="bi bi-trash"></i> Eliminar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Paginación -->
                <nav aria-label="Paginación de vehículos">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Siguiente</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal para añadir/editar vehículo -->
<div class="modal fade" id="addVehicleModal" tabindex="-1" aria-labelledby="addVehicleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addVehicleModalLabel">Nuevo Vehículo</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="vehicleForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="licensePlate" class="form-label">Placa</label>
                            <input type="text" class="form-control" id="licensePlate" name="license_plate" required>
                        </div>
                        <div class="col-md-6">
                            <label for="vehicleStatus" class="form-label">Estado</label>
                            <select class="form-select" id="vehicleStatus" name="status" required>
                                <option value="libre">Libre</option>
                                <option value="ocupado">Ocupado</option>
                                <option value="alerta">Alerta</option>
                                <option value="emergencia">Emergencia</option>
                                <option value="fuera_de_servicio">Fuera de servicio</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="vehicleCategory" class="form-label">Categoría</label>
                            <select class="form-select" id="vehicleCategory" name="category" required>
                                <option value="activo">Activo</option>
                                <option value="en_mantenimiento">En Mantenimiento</option>
                                <option value="inactivo">Inactivo</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="driver" class="form-label">Conductor</label>
                            <select class="form-select" id="driver" name="driver_id">
                                <option value="">Seleccionar conductor...</option>
                                {% for driver in drivers %}
                                <option value="{{ driver.id }}">{{ driver.full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="brand" class="form-label">Marca</label>
                            <input type="text" class="form-control" id="brand" name="brand" required>
                        </div>
                        <div class="col-md-6">
                            <label for="model" class="form-label">Modelo</label>
                            <input type="text" class="form-control" id="model" name="model" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="year" class="form-label">Año</label>
                            <input type="number" class="form-control" id="year" name="year" required>
                        </div>
                        <div class="col-md-4">
                            <label for="color" class="form-label">Color</label>
                            <input type="text" class="form-control" id="color" name="color" required>
                        </div>
                        <div class="col-md-4">
                            <label for="mileage" class="form-label">Kilometraje</label>
                            <input type="number" class="form-control" id="mileage" name="mileage" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="vehicleImage" class="form-label">Imagen</label>
                            <input type="file" class="form-control" id="vehicleImage" name="image">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notas</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="saveVehicleBtn">Guardar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
    // Variables globales
    let currentVehicleId = null;
    let isEditing = false;

    // Función para obtener el token de autenticación
    function getAuthToken() {
        const cookieValue = document.cookie.split('; ')
            .find(row => row.startsWith('admin_access_token='))
            ?.split('=')[1];

        if (cookieValue) {
            // Decodificar el valor de la cookie si está codificado
            try {
                return decodeURIComponent(cookieValue);
            } catch (e) {
                return cookieValue;
            }
        }
        return null;
    }

    // Función para realizar peticiones a la API con autenticación
    async function fetchWithAuth(url, options = {}) {
        const token = getAuthToken();
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
            console.log('Token enviado:', token);
        } else {
            console.error('No se encontró token de autenticación');
        }

        return fetch(url, {
            ...options,
            headers,
            credentials: 'include' // Incluir cookies en la solicitud
        });
    }

    // Cargar vehículos desde la API (simulado por ahora)
    async function loadVehicles() {
        try {
            // Simulación de datos - en una implementación real, esto vendría de la API
            const vehicles = [
                {id: 1, brand: "Toyota", model: "Corolla", license_plate: "ABC123", year: 2020, color: "Blanco", status: "libre", category: "activo", driver_name: "Juan Pérez", mileage: 15000, image_url: "/static/img/default-car.jpg"},
                {id: 2, brand: "Honda", model: "Civic", license_plate: "DEF456", year: 2019, color: "Negro", status: "fuera_de_servicio", category: "en_mantenimiento", driver_name: "María López", mileage: 25000, image_url: "/static/img/default-car.jpg"},
                {id: 3, brand: "Ford", model: "Focus", license_plate: "GHI789", year: 2021, color: "Azul", status: "ocupado", category: "activo", driver_name: "Carlos Gómez", mileage: 5000, image_url: "/static/img/default-car.jpg"},
                {id: 4, brand: "Chevrolet", model: "Cruze", license_plate: "JKL012", year: 2018, color: "Rojo", status: "alerta", category: "inactivo", driver_name: "Ana Martínez", mileage: 35000, image_url: "/static/img/default-car.jpg"},
                {id: 5, brand: "Volkswagen", model: "Gol", license_plate: "MNO345", year: 2022, color: "Gris", status: "emergencia", category: "inactivo", driver_name: "Roberto Sánchez", mileage: 8000, image_url: "/static/img/default-car.jpg"}
            ];

            renderVehicles(vehicles);
        } catch (error) {
            console.error('Error:', error);
            alert('Error al cargar vehículos: ' + error.message);
        }
    }

    // Renderizar vehículos en la interfaz
    function renderVehicles(vehicles) {
        const container = document.getElementById('vehiclesContainer');
        container.innerHTML = '';

        vehicles.forEach(vehicle => {
            const vehicleCard = document.createElement('div');
            vehicleCard.className = 'col-md-4 col-lg-3 vehicle-item';
            vehicleCard.setAttribute('data-status', vehicle.status);
            vehicleCard.setAttribute('data-category', vehicle.category);

            vehicleCard.innerHTML = `
                <div class="card vehicle-card">
                    ${getStatusBadge(vehicle.status)}

                    <img src="${vehicle.image_url || '/static/img/default-car.jpg'}" class="card-img-top vehicle-img" alt="${vehicle.model}">
                    <div class="card-body">
                        <h5 class="card-title">${vehicle.brand} ${vehicle.model}</h5>
                        <h6 class="card-subtitle mb-2 text-muted">Placa: ${vehicle.license_plate}</h6>

                        <div class="vehicle-details mt-3">
                            <p class="mb-1"><i class="bi bi-person-fill"></i> ${vehicle.driver_name || 'Sin conductor'}</p>
                            <p class="mb-1"><i class="bi bi-calendar-event"></i> Año: ${vehicle.year}</p>
                            <p class="mb-1"><i class="bi bi-palette-fill"></i> Color: ${vehicle.color}</p>
                            <p class="mb-1"><i class="bi bi-speedometer2"></i> ${vehicle.mileage} km</p>
                        </div>

                        <div class="d-flex justify-content-between mt-3">
                            <button class="btn btn-sm btn-primary" onclick="viewVehicle(${vehicle.id})">
                                <i class="bi bi-eye"></i> Ver
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="editVehicle(${vehicle.id})">
                                <i class="bi bi-pencil"></i> Editar
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteVehicle(${vehicle.id})">
                                <i class="bi bi-trash"></i> Eliminar
                            </button>
                        </div>
                    </div>
                </div>
            `;

            container.appendChild(vehicleCard);
        });

        // Actualizar contadores
        updateCounters(vehicles);
    }

    // Obtener badge HTML para el estado
    function getStatusBadge(status) {
        switch(status) {
            case 'libre':
                return '<span class="badge bg-success status-badge">Libre</span>';
            case 'ocupado':
                return '<span class="badge bg-primary status-badge">Ocupado</span>';
            case 'alerta':
                return '<span class="badge bg-warning text-dark status-badge">Alerta</span>';
            case 'emergencia':
                return '<span class="badge bg-danger status-badge">Emergencia</span>';
            case 'fuera_de_servicio':
                return '<span class="badge bg-secondary status-badge">Fuera de servicio</span>';
            default:
                return `<span class="badge bg-info status-badge">${status}</span>`;
        }
    }

    // Actualizar contadores de vehículos
    function updateCounters(vehicles) {
        const totalVehicles = vehicles.length;
        const activeVehicles = vehicles.filter(v => v.category === 'activo').length;
        const maintenanceVehicles = vehicles.filter(v => v.category === 'en_mantenimiento').length;
        const inactiveVehicles = vehicles.filter(v => v.category === 'inactivo').length;

        document.querySelector('.card.bg-info h2').textContent = totalVehicles;
        document.querySelector('.card.bg-success h2').textContent = activeVehicles;
        document.querySelector('.card.bg-warning h2').textContent = maintenanceVehicles;
        document.querySelector('.card.bg-danger h2').textContent = inactiveVehicles;
    }

    // Filtrar vehículos por estado o categoría
    document.querySelectorAll('.filter-btn').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            const filterType = this.getAttribute('data-filter-type');
            const vehicles = document.querySelectorAll('.vehicle-item');

            // Resaltar botón activo solo en su grupo de filtros
            document.querySelectorAll(`.filter-btn[data-filter-type="${filterType}"]`).forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');

            // Obtener filtros activos
            const activeStatusFilter = document.querySelector('.filter-btn[data-filter-type="status"].active').getAttribute('data-filter');
            const activeCategoryFilter = document.querySelector('.filter-btn[data-filter-type="category"].active').getAttribute('data-filter');

            // Filtrar vehículos
            vehicles.forEach(vehicle => {
                const vehicleStatus = vehicle.getAttribute('data-status');
                const vehicleCategory = vehicle.getAttribute('data-category');

                const matchesStatus = activeStatusFilter === 'all' || vehicleStatus === activeStatusFilter;
                const matchesCategory = activeCategoryFilter === 'all' || vehicleCategory === activeCategoryFilter;

                if (matchesStatus && matchesCategory) {
                    vehicle.style.display = '';
                } else {
                    vehicle.style.display = 'none';
                }
            });
        });
    });

    // Búsqueda de vehículos
    document.getElementById('searchButton').addEventListener('click', function() {
        const searchTerm = document.getElementById('searchVehicle').value.toLowerCase();
        const vehicles = document.querySelectorAll('.vehicle-item');

        vehicles.forEach(vehicle => {
            const text = vehicle.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                vehicle.style.display = '';
            } else {
                vehicle.style.display = 'none';
            }
        });
    });

    // También buscar al presionar Enter
    document.getElementById('searchVehicle').addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            document.getElementById('searchButton').click();
        }
    });

    // Ver detalles del vehículo
    function viewVehicle(vehicleId) {
        // Simulación - en una implementación real, esto vendría de la API
        const vehicle = {
            id: vehicleId,
            brand: "Toyota",
            model: "Corolla",
            license_plate: "ABC123",
            year: 2020,
            color: "Blanco",
            status: "active",
            driver_name: "Juan Pérez",
            mileage: 15000,
            last_service: "2023-04-15",
            notes: "Vehículo en excelentes condiciones"
        };

        // Mostrar detalles en un modal o alerta
        alert(`
            ID: ${vehicle.id}
            Vehículo: ${vehicle.brand} ${vehicle.model}
            Placa: ${vehicle.license_plate}
            Año: ${vehicle.year}
            Color: ${vehicle.color}
            Conductor: ${vehicle.driver_name}
            Kilometraje: ${vehicle.mileage} km
            Último servicio: ${vehicle.last_service}
            Estado: ${vehicle.status}
            Notas: ${vehicle.notes}
        `);
    }

    // Editar vehículo
    function editVehicle(vehicleId) {
        // Simulación - en una implementación real, esto vendría de la API
        const vehicle = {
            id: vehicleId,
            brand: "Toyota",
            model: "Corolla",
            license_plate: "ABC123",
            year: 2020,
            color: "Blanco",
            status: "active",
            driver_id: 1,
            mileage: 15000,
            notes: "Vehículo en excelentes condiciones"
        };

        // Establecer modo de edición
        isEditing = true;
        currentVehicleId = vehicleId;

        // Llenar el formulario con los datos del vehículo
        document.getElementById('licensePlate').value = vehicle.license_plate;
        document.getElementById('vehicleStatus').value = vehicle.status;
        document.getElementById('vehicleCategory').value = vehicle.category;
        document.getElementById('brand').value = vehicle.brand;
        document.getElementById('model').value = vehicle.model;
        document.getElementById('year').value = vehicle.year;
        document.getElementById('color').value = vehicle.color;
        document.getElementById('mileage').value = vehicle.mileage;
        document.getElementById('driver').value = vehicle.driver_id;
        document.getElementById('notes').value = vehicle.notes || '';

        // Cambiar título del modal
        document.getElementById('addVehicleModalLabel').textContent = 'Editar Vehículo';

        // Mostrar modal
        new bootstrap.Modal(document.getElementById('addVehicleModal')).show();
    }

    // Eliminar vehículo
    function deleteVehicle(vehicleId) {
        if (!confirm('¿Está seguro de que desea eliminar este vehículo?')) {
            return;
        }

        // Simulación - en una implementación real, esto sería una llamada a la API
        alert('Vehículo eliminado correctamente');

        // Recargar vehículos
        loadVehicles();
    }

    // Preparar modal para nuevo vehículo
    document.querySelector('button[data-bs-target="#addVehicleModal"]').addEventListener('click', function() {
        // Establecer modo de creación
        isEditing = false;
        currentVehicleId = null;

        // Limpiar formulario
        document.getElementById('vehicleForm').reset();

        // Cambiar título del modal
        document.getElementById('addVehicleModalLabel').textContent = 'Nuevo Vehículo';
    });

    // Guardar vehículo (nuevo o editado)
    document.getElementById('saveVehicleBtn').addEventListener('click', function() {
        // Validar formulario
        const form = document.getElementById('vehicleForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Recopilar datos del formulario
        const vehicleData = {
            license_plate: document.getElementById('licensePlate').value,
            status: document.getElementById('vehicleStatus').value,
            category: document.getElementById('vehicleCategory').value,
            brand: document.getElementById('brand').value,
            model: document.getElementById('model').value,
            year: parseInt(document.getElementById('year').value),
            color: document.getElementById('color').value,
            mileage: parseInt(document.getElementById('mileage').value),
            driver_id: document.getElementById('driver').value || null,
            notes: document.getElementById('notes').value
        };

        // Simulación - en una implementación real, esto sería una llamada a la API
        alert(isEditing ? 'Vehículo actualizado correctamente' : 'Vehículo creado correctamente');
        bootstrap.Modal.getInstance(document.getElementById('addVehicleModal')).hide();

        // Recargar vehículos
        loadVehicles();
    });

    // Cargar vehículos al iniciar la página
    document.addEventListener('DOMContentLoaded', function() {
        loadVehicles();
    });
</script>
{% endblock %}
