{% extends "base_layout_owner.html" %}

{% block title %}Panel de Titular - Taxis{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    .vehicle-card {
        border-left: 4px solid #fd7e14;
        margin-bottom: 10px;
        transition: all 0.3s;
    }
    .vehicle-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .vehicle-card.active {
        border-left-color: #198754;
    }
    .vehicle-card.maintenance {
        border-left-color: #ffc107;
    }
    .vehicle-card.inactive {
        border-left-color: #6c757d;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .status-libre {
        background-color: #198754;
    }
    .status-ocupado {
        background-color: #dc3545;
    }
    .status-alerta {
        background-color: #ffc107;
    }
    .status-fuera_de_servicio {
        background-color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Panel de Titular</h1>
        <p class="lead">Bienvenido al panel de titular del sistema de taxis.</p>
    </div>
</div>

<!-- Estadísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-taxi-front card-icon"></i>
                <div class="stats-value">{{ stats.total_vehicles|default(0) }}</div>
                <div class="stats-label text-white-50">Vehículos Totales</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-person card-icon"></i>
                <div class="stats-value">{{ stats.total_drivers|default(0) }}</div>
                <div class="stats-label text-white-50">Conductores</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-geo-alt card-icon"></i>
                <div class="stats-value">{{ stats.total_trips|default(0) }}</div>
                <div class="stats-label text-white-50">Viajes Totales</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-cash-coin card-icon"></i>
                <div class="stats-value">${{ stats.total_earnings|default(0) }}</div>
                <div class="stats-label text-white-50">Ganancias Totales</div>
            </div>
        </div>
    </div>
</div>

<!-- Mapa y Vehículos Activos -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mapa de Vehículos</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Vehículos Activos</h5>
                <span class="badge bg-success">{{ active_vehicles|default([])|length }}</span>
            </div>
            <div class="card-body">
                <div class="vehicle-list">
                    {% for vehicle in active_vehicles|default([]) %}
                    <div class="card vehicle-card active">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="card-title mb-0">{{ vehicle.plate_number }}</h6>
                                <span class="badge bg-primary">{{ vehicle.brand }} {{ vehicle.model }}</span>
                            </div>
                            <p class="card-text">
                                <small>
                                    <span class="status-indicator status-{{ vehicle.status|lower }}"></span>
                                    <strong>Estado:</strong> {{ vehicle.status|upper }}<br>
                                    <strong>Conductor:</strong> {{ vehicle.driver_name|default('Sin asignar') }}<br>
                                    <strong>Última actualización:</strong> {{ vehicle.last_update|default('Desconocida') }}
                                </small>
                            </p>
                            <div class="d-flex justify-content-end">
                                <a href="{{ url_for('vehicle_details_route', vehicle_id=vehicle.id) }}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-eye"></i> Ver
                                </a>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <p class="text-center text-muted">No hay vehículos activos</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ganancias y Conductores -->
<div class="row">
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Ganancias Recientes</h5>
            </div>
            <div class="card-body">
                <canvas id="earningsChart" height="250"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Conductores Destacados</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Conductor</th>
                                <th>Vehículo</th>
                                <th>Viajes</th>
                                <th>Calificación</th>
                                <th>Ganancias</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for driver in top_drivers|default([]) %}
                            <tr>
                                <td>{{ driver.name }}</td>
                                <td>{{ driver.vehicle }}</td>
                                <td>{{ driver.trips }}</td>
                                <td>
                                    <span class="text-warning">
                                        {% for i in range(driver.rating|int) %}
                                        <i class="bi bi-star-fill"></i>
                                        {% endfor %}
                                        {% if driver.rating % 1 >= 0.5 %}
                                        <i class="bi bi-star-half"></i>
                                        {% endif %}
                                    </span>
                                </td>
                                <td>${{ driver.earnings }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">No hay datos disponibles</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Acciones Rápidas -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Acciones Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="{{ url_for('owner_vehicles_route') }}" class="btn btn-lg btn-outline-primary">
                                <i class="bi bi-taxi-front me-2"></i> Gestionar Vehículos
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="{{ url_for('owner_drivers_route') }}" class="btn btn-lg btn-outline-success">
                                <i class="bi bi-person me-2"></i> Gestionar Conductores
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="{{ url_for('owner_earnings_route') }}" class="btn btn-lg btn-outline-warning">
                                <i class="bi bi-cash-coin me-2"></i> Ver Ganancias
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-lg btn-outline-info" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise me-2"></i> Actualizar Datos
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 12); // Buenos Aires como ejemplo

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Función para cargar vehículos
    async function loadVehicles() {
        try {
            const response = await fetchWithAuth('/api/v1/vehicles/owner');
            if (response.ok) {
                const vehicles = await response.json();
                
                // Limpiar marcadores existentes
                map.eachLayer(layer => {
                    if (layer instanceof L.Marker) {
                        map.removeLayer(layer);
                    }
                });
                
                // Añadir marcadores para cada vehículo
                vehicles.forEach(vehicle => {
                    if (vehicle.last_latitude && vehicle.last_longitude) {
                        const marker = L.marker([vehicle.last_latitude, vehicle.last_longitude])
                            .addTo(map)
                            .bindPopup(`
                                <strong>${vehicle.plate_number}</strong><br>
                                Conductor: ${vehicle.driver_name || 'Sin asignar'}<br>
                                Estado: ${vehicle.status}
                            `);
                    }
                });
            }
        } catch (error) {
            console.error('Error al cargar vehículos:', error);
        }
    }

    // Función para actualizar todos los datos
    function refreshData() {
        loadVehicles();
        window.location.reload();
    }

    // Cargar vehículos al iniciar
    loadVehicles();
    
    // Actualizar cada 30 segundos
    setInterval(loadVehicles, 30000);

    // Inicializar gráfico de ganancias
    const ctx = document.getElementById('earningsChart').getContext('2d');
    const earningsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: {{ earnings_data.labels|default(['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'])|tojson }},
            datasets: [{
                label: 'Ganancias ($)',
                data: {{ earnings_data.values|default([0, 0, 0, 0, 0, 0, 0])|tojson }},
                backgroundColor: 'rgba(253, 126, 20, 0.7)',
                borderColor: 'rgba(253, 126, 20, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>
{% endblock %}
