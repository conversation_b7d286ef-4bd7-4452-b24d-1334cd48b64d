# --- Archivo: app/users.py ---
# Rutas para gestión de usuarios y permisos

from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app import db
from app.models import User, UserCityPermission, UserSourcePermission, UserPermission, Point, PERMISSION_TYPES, DEFAULT_PERMISSIONS, AuditLog
from app.forms import UserCreateForm, UserEditForm, UsersFilterForm
from app.audit_utils import log_model_change, get_model_values, audit_view_access
from sqlalchemy import distinct, func
from datetime import datetime
from functools import wraps

bp = Blueprint('users', __name__, url_prefix='/users')

# --- DECORADOR PARA VERIFICAR PERMISOS DE ADMIN ---
def admin_required(f):
    """Decorador para verificar que el usuario sea administrador."""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.can_manage_users():
            flash('No tienes permisos para acceder a esta página.', 'danger')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

# --- RUTAS PRINCIPALES ---
@bp.route('/list')
@login_required
@admin_required
def list_users():
    """Lista todos los usuarios con filtros."""
    form = UsersFilterForm()

    # Obtener opciones dinámicas para los selectores
    cities = db.session.query(distinct(Point.city)).filter(Point.city.isnot(None)).order_by(Point.city).all()
    form.city.choices = [('', 'Todas las ciudades')] + [(city[0], city[0]) for city in cities if city[0]]

    sources = db.session.query(distinct(Point.source)).filter(Point.source.isnot(None)).order_by(Point.source).all()
    form.source.choices = [('', 'Todas las capas')] + [(source[0], source[0]) for source in sources if source[0]]

    # Inicializar query base
    query = User.query
    users_data = []
    total_users = 0

    # Aplicar filtros si hay parámetros en la URL
    if request.method == 'GET' and any(request.args.values()):
        username = request.args.get('username')
        role = request.args.get('role')
        is_active = request.args.get('is_active')
        city = request.args.get('city')
        source = request.args.get('source')

        # Aplicar filtros
        if username:
            query = query.filter(User.username.ilike(f'%{username}%'))

        if role:
            query = query.filter(User.role == role)

        if is_active == 'true':
            query = query.filter(User.is_active == True)
        elif is_active == 'false':
            query = query.filter(User.is_active == False)

        if city:
            query = query.filter(User.city_permissions.any(UserCityPermission.city == city))

        if source:
            query = query.filter(User.source_permissions.any(UserSourcePermission.source == source))

    # Ejecutar query y obtener resultados
    try:
        users = query.order_by(User.created_at.desc()).all()
        total_users = len(users)

        # Preparar datos para mostrar
        for user in users:
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'is_active': user.is_active,
                'created_at': user.created_at,
                'updated_at': user.updated_at,
                'cities_count': user.city_permissions.count(),
                'sources_count': user.source_permissions.count(),
                'allowed_cities': [perm.city for perm in user.city_permissions.all()],
                'allowed_sources': [perm.source for perm in user.source_permissions.all()],
                'creator': user.creator.username if user.creator else None
            }
            users_data.append(user_data)

        current_app.logger.info(f"Lista de usuarios consultada por {current_user.username}: {total_users} usuarios encontrados")

    except Exception as e:
        current_app.logger.error(f"Error obteniendo usuarios: {e}")
        flash('Error al cargar la lista de usuarios.', 'danger')
        users_data = []
        total_users = 0

    return render_template('users/list_users.html',
                         title="Gestión de Usuarios",
                         form=form,
                         users_data=users_data,
                         total_users=total_users)

@bp.route('/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_user():
    """Crear nuevo usuario."""
    form = UserCreateForm()

    # Obtener opciones dinámicas para ciudades y fuentes
    cities = db.session.query(distinct(Point.city)).filter(Point.city.isnot(None)).order_by(Point.city).all()
    form.cities.choices = [(city[0], city[0]) for city in cities if city[0]]

    sources = db.session.query(distinct(Point.source)).filter(Point.source.isnot(None)).order_by(Point.source).all()
    form.sources.choices = [(source[0], source[0]) for source in sources if source[0]]

    if form.validate_on_submit():
        try:
            # Verificar que el username no exista
            existing_user = User.query.filter_by(username=form.username.data).first()
            if existing_user:
                flash('El nombre de usuario ya existe.', 'danger')
                return render_template('users/create_user.html', form=form, title="Crear Usuario")

            # Crear nuevo usuario
            user = User(
                username=form.username.data,
                email=form.email.data if form.email.data else None,
                role=form.role.data,
                is_active=form.is_active.data,
                created_by=current_user.id
            )
            user.set_password(form.password.data)

            db.session.add(user)
            db.session.flush()  # Para obtener el ID del usuario

            # Agregar permisos de ciudades
            for city in form.cities.data:
                city_perm = UserCityPermission(user_id=user.id, city=city)
                db.session.add(city_perm)

            # Agregar permisos de fuentes
            for source in form.sources.data:
                source_perm = UserSourcePermission(user_id=user.id, source=source)
                db.session.add(source_perm)

            # Agregar permisos específicos
            permissions_data = {
                'points_view': form.points_view.data,
                'points_edit': form.points_edit.data,
                'points_create': form.points_create.data,
                'points_delete': form.points_delete.data,
                'cameras_view': form.cameras_view.data,
                'cameras_edit': form.cameras_edit.data,
                'cameras_create': form.cameras_create.data,
                'cameras_delete': form.cameras_delete.data,
                'reports_view': form.reports_view.data,
                'reports_export': form.reports_export.data
            }

            for perm_type, perm_value in permissions_data.items():
                permission = UserPermission(
                    user_id=user.id,
                    permission_type=perm_type,
                    permission_value=perm_value
                )
                db.session.add(permission)

            db.session.commit()

            current_app.logger.info(f"Usuario {user.username} creado por {current_user.username}")
            flash(f'Usuario {user.username} creado exitosamente.', 'success')
            return redirect(url_for('users.list_users'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creando usuario: {e}")
            flash('Error al crear el usuario. Intente nuevamente.', 'danger')

    return render_template('users/create_user.html', form=form, title="Crear Usuario")

@bp.route('/edit/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """Editar usuario existente."""
    user = User.query.get_or_404(user_id)

    # No permitir que un admin se edite a sí mismo para evitar problemas
    if user.id == current_user.id:
        flash('No puedes editar tu propio usuario. Pide a otro administrador que lo haga.', 'warning')
        return redirect(url_for('users.list_users'))

    form = UserEditForm(obj=user)

    # Obtener opciones dinámicas para ciudades y fuentes
    cities = db.session.query(distinct(Point.city)).filter(Point.city.isnot(None)).order_by(Point.city).all()
    form.cities.choices = [(city[0], city[0]) for city in cities if city[0]]

    sources = db.session.query(distinct(Point.source)).filter(Point.source.isnot(None)).order_by(Point.source).all()
    form.sources.choices = [(source[0], source[0]) for source in sources if source[0]]

    if request.method == 'GET':
        # Precargar datos del usuario
        form.cities.data = [perm.city for perm in user.city_permissions.all()]
        form.sources.data = [perm.source for perm in user.source_permissions.all()]

        # Precargar permisos específicos
        for perm in user.permissions.all():
            if hasattr(form, perm.permission_type):
                getattr(form, perm.permission_type).data = perm.permission_value

    if form.validate_on_submit():
        try:
            # Verificar que el username no exista (excepto el actual)
            existing_user = User.query.filter(User.username == form.username.data, User.id != user.id).first()
            if existing_user:
                flash('El nombre de usuario ya existe.', 'danger')
                return render_template('users/edit_user.html', form=form, user=user, title="Editar Usuario")

            # Actualizar datos básicos
            user.username = form.username.data
            user.email = form.email.data if form.email.data else None
            user.role = form.role.data
            user.is_active = form.is_active.data
            user.updated_at = datetime.utcnow()

            # Actualizar contraseña si se proporcionó
            if form.password.data:
                user.set_password(form.password.data)

            # Actualizar permisos de ciudades
            user.city_permissions.delete()
            for city in form.cities.data:
                city_perm = UserCityPermission(user_id=user.id, city=city)
                db.session.add(city_perm)

            # Actualizar permisos de fuentes
            user.source_permissions.delete()
            for source in form.sources.data:
                source_perm = UserSourcePermission(user_id=user.id, source=source)
                db.session.add(source_perm)

            # Actualizar permisos específicos
            permissions_data = {
                'points_view': form.points_view.data,
                'points_edit': form.points_edit.data,
                'points_create': form.points_create.data,
                'points_delete': form.points_delete.data,
                'cameras_view': form.cameras_view.data,
                'cameras_edit': form.cameras_edit.data,
                'cameras_create': form.cameras_create.data,
                'cameras_delete': form.cameras_delete.data,
                'reports_view': form.reports_view.data,
                'reports_export': form.reports_export.data
            }

            # Eliminar permisos existentes y crear nuevos
            user.permissions.delete()
            for perm_type, perm_value in permissions_data.items():
                permission = UserPermission(
                    user_id=user.id,
                    permission_type=perm_type,
                    permission_value=perm_value
                )
                db.session.add(permission)

            db.session.commit()

            current_app.logger.info(f"Usuario {user.username} editado por {current_user.username}")
            flash(f'Usuario {user.username} actualizado exitosamente.', 'success')
            return redirect(url_for('users.list_users'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error editando usuario {user_id}: {e}")
            flash('Error al actualizar el usuario. Intente nuevamente.', 'danger')

    return render_template('users/edit_user.html', form=form, user=user, title="Editar Usuario")

@bp.route('/delete/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """Eliminar usuario."""
    user = User.query.get_or_404(user_id)

    # No permitir que un admin se elimine a sí mismo
    if user.id == current_user.id:
        flash('No puedes eliminar tu propio usuario.', 'danger')
        return redirect(url_for('users.list_users'))

    # No permitir eliminar el último administrador
    admin_count = User.query.filter_by(role='administrador', is_active=True).count()
    if user.role == 'administrador' and admin_count <= 1:
        flash('No se puede eliminar el último administrador del sistema.', 'danger')
        return redirect(url_for('users.list_users'))

    try:
        username = user.username
        db.session.delete(user)
        db.session.commit()

        current_app.logger.info(f"Usuario {username} eliminado por {current_user.username}")
        flash(f'Usuario {username} eliminado exitosamente.', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error eliminando usuario {user_id}: {e}")
        flash('Error al eliminar el usuario. Intente nuevamente.', 'danger')

    return redirect(url_for('users.list_users'))

@bp.route('/toggle_status/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """Activar/desactivar usuario."""
    user = User.query.get_or_404(user_id)

    # No permitir desactivar el propio usuario
    if user.id == current_user.id:
        flash('No puedes desactivar tu propio usuario.', 'danger')
        return redirect(url_for('users.list_users'))

    # No permitir desactivar el último administrador
    if user.role == 'administrador' and user.is_active:
        admin_count = User.query.filter_by(role='administrador', is_active=True).count()
        if admin_count <= 1:
            flash('No se puede desactivar el último administrador del sistema.', 'danger')
            return redirect(url_for('users.list_users'))

    try:
        user.is_active = not user.is_active
        user.updated_at = datetime.utcnow()
        db.session.commit()

        status = 'activado' if user.is_active else 'desactivado'
        current_app.logger.info(f"Usuario {user.username} {status} por {current_user.username}")
        flash(f'Usuario {user.username} {status} exitosamente.', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error cambiando estado de usuario {user_id}: {e}")
        flash('Error al cambiar el estado del usuario. Intente nuevamente.', 'danger')

    return redirect(url_for('users.list_users'))

@bp.route('/apply_role_defaults/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def apply_role_defaults(user_id):
    """Aplicar permisos por defecto según el rol del usuario."""
    user = User.query.get_or_404(user_id)

    try:
        # Obtener permisos por defecto para el rol
        default_perms = DEFAULT_PERMISSIONS.get(user.role, {})

        # Eliminar permisos existentes
        user.permissions.delete()

        # Aplicar permisos por defecto
        for perm_type, perm_value in default_perms.items():
            permission = UserPermission(
                user_id=user.id,
                permission_type=perm_type,
                permission_value=perm_value
            )
            db.session.add(permission)

        db.session.commit()

        current_app.logger.info(f"Permisos por defecto aplicados a {user.username} por {current_user.username}")
        flash(f'Permisos por defecto aplicados a {user.username} según su rol {user.role}.', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error aplicando permisos por defecto a usuario {user_id}: {e}")
        flash('Error al aplicar permisos por defecto. Intente nuevamente.', 'danger')

    return redirect(url_for('users.edit_user', user_id=user_id))

@bp.route('/permissions/<int:user_id>')
@login_required
@admin_required
def user_permissions(user_id):
    """Ver permisos detallados de un usuario."""
    user = User.query.get_or_404(user_id)

    try:
        # Calcular estadísticas de acceso
        accessible_points_count = None
        accessible_cameras_count = None

        if user.city_permissions.count() > 0:
            # Si tiene restricciones de ciudad, contar puntos accesibles
            allowed_cities = [perm.city for perm in user.city_permissions.all()]
            accessible_points_count = Point.query.filter(Point.city.in_(allowed_cities)).count()

        if user.source_permissions.count() > 0:
            # Si tiene restricciones de fuente, contar cámaras accesibles
            allowed_sources = [perm.source for perm in user.source_permissions.all()]
            accessible_cameras_count = Point.query.filter(Point.source.in_(allowed_sources)).count()

        current_app.logger.info(f"Permisos de usuario {user.username} consultados por {current_user.username}")

        return render_template('users/user_permissions.html',
                             title=f"Permisos de {user.username}",
                             user=user,
                             accessible_points_count=accessible_points_count,
                             accessible_cameras_count=accessible_cameras_count)

    except Exception as e:
        current_app.logger.error(f"Error obteniendo permisos de usuario {user_id}: {e}")
        flash('Error al cargar los permisos del usuario.', 'danger')
        return redirect(url_for('users.list_users'))

# --- RUTAS API PARA AJAX ---
@bp.route('/api/role_permissions/<role>')
@login_required
@admin_required
def api_get_role_permissions(role):
    """API para obtener permisos por defecto de un rol."""
    try:
        permissions = DEFAULT_PERMISSIONS.get(role, {})
        return jsonify({'success': True, 'permissions': permissions})
    except Exception as e:
        current_app.logger.error(f"Error obteniendo permisos de rol {role}: {e}")
        return jsonify({'success': False, 'error': str(e)})