-- Eliminar la base de datos existente (¡SOLO SI ESTÁS SEGURO!)
DROP DATABASE IF EXISTS intranet;

-- Crear la base de datos
CREATE DATABASE IF NOT EXISTS intranet CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE intranet;

CREATE DATABASE IF NOT EXISTS intranet CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE intranet;

-- Se crea la tabla 'churches' sin la restricción de clave foránea hacia 'users'
CREATE TABLE churches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address VARCHAR(255),
    pastor_id INT,  -- Puede ser NULL
    district VARCHAR(255),
    latitude FLOAT,
    longitude FLOAT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Se crea la tabla 'users' sin la restricción de clave foránea hacia 'churches'
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role ENUM('administrador', 'secretaria', 'pastorado', 'miembro') NOT NULL,
    full_name VARCHAR(255),
    phone_number VARCHAR(255),
    address VARCHAR(255),
    date_of_birth DATE,
    is_active BOOLEAN DEFAULT TRUE,
    church_id INT  -- Puede ser NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Una vez creadas ambas tablas, se agregan las claves foráneas para resolver la dependencia circular.
-- Agregar la clave foránea en 'churches' que referencia a 'users'
ALTER TABLE churches
ADD CONSTRAINT fk_churches_users
FOREIGN KEY (pastor_id) REFERENCES users(id) ON DELETE SET NULL;

-- Agregar la clave foránea en 'users' que referencia a 'churches'
ALTER TABLE users
ADD CONSTRAINT fk_users_churches
FOREIGN KEY (church_id) REFERENCES churches(id) ON DELETE SET NULL;

-- Tabla de miembros
CREATE TABLE members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    church_id INT NOT NULL,  --  NOT NULL (un miembro *debe* pertenecer a una iglesia)
    alergies VARCHAR(255),
    emergency_contact VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (church_id) REFERENCES churches(id) ON DELETE CASCADE -- Importante para la integridad referencial
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de pastores
CREATE TABLE pastores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    latitude FLOAT,
    longitude FLOAT,
    address VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de roles de pastor
CREATE TABLE pastor_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(255) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla intermedia para usuarios y roles de pastor (muchos a muchos)
CREATE TABLE user_pastor_roles (
    user_id INT,
    pastor_role_id INT,
    PRIMARY KEY (user_id, pastor_role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pastor_role_id) REFERENCES pastor_roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de documentos (la dejamos como estaba)
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_path VARCHAR(255) NOT NULL,
    upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT,
    category ENUM('acta', 'informe_financiero', 'circular', 'informe_miembros', 'diploma') NOT NULL,
    topic VARCHAR(255),
    version INT DEFAULT 1,
    previous_version_id INT,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (previous_version_id) REFERENCES documents(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de mensajes (la dejamos como estaba)
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT,
    receiver_id INT,
    message_text TEXT NOT NULL,
    sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de mapas (la dejamos como estaba)
CREATE TABLE maps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    geodata JSON,
    description TEXT,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla para la jerarquía de pastores
CREATE TABLE jerarquia_pastores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pastor_id INT,
    supervisor_id INT,
    FOREIGN KEY (pastor_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (supervisor_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de categorías de usuario (simplificada)
CREATE TABLE categorias (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla intermedia para la relación muchos a muchos usuarios-categorías
CREATE TABLE usuario_categoria (
    usuario_id INT,
    categoria_id INT,
    PRIMARY KEY (usuario_id, categoria_id),
    FOREIGN KEY (usuario_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (categoria_id) REFERENCES categorias(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserts (roles de pastor y categorías - los mismos que ya tenías)
INSERT INTO pastor_roles (role_name) VALUES
('Pastor Gobernante'),
('Pastor de Jóvenes'),
('Pastor de Música'),
('Superintendente'),
('Jefe de Sector'),
('Director de Jóvenes'),
('Obispo Presidente'),
('Miembro del Presbiterio Mayor'),
('Miembro del Tribunal de Disciplina');

INSERT INTO categorias (nombre) VALUES ('Pastor'), ('Miembro'), ('Presbitero'), ('Diacono'),
('Superintendente'), ('Probando'), ('Plena Comunion'), ('Obispo Presidente'),
('Administracion'), ('Tribunal Eclesiastico'), ('Oficial'), ('Presbisterio Mayor');