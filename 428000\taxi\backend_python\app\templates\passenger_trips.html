{% extends "base_layout_passenger.html" %}

{% block title %}Mis Viajes - Panel de Pasajero{% endblock %}

{% block head_extra %}
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    .trip-card {
        border-left: 4px solid #6f42c1;
        margin-bottom: 10px;
        transition: all 0.3s;
    }
    .trip-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .trip-card.active {
        border-left-color: #0d6efd;
    }
    .trip-card.completed {
        border-left-color: #198754;
    }
    .trip-card.cancelled {
        border-left-color: #dc3545;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Mis Viajes</h1>
        <p class="lead">Historial de todos tus viajes realizados.</p>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Filtros</h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-4">
                        <label for="status-filter" class="form-label">Estado</label>
                        <select id="status-filter" class="form-select">
                            <option value="">Todos</option>
                            <option value="solicitado">Solicitado</option>
                            <option value="aceptado">Aceptado</option>
                            <option value="en_viaje">En viaje</option>
                            <option value="completado">Completado</option>
                            <option value="cancelado">Cancelado</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="date-filter" class="form-label">Fecha</label>
                        <input type="date" id="date-filter" class="form-control">
                    </div>
                    <div class="col-md-4">
                        <label for="search-filter" class="form-label">Buscar</label>
                        <input type="text" id="search-filter" class="form-control" placeholder="Dirección, conductor...">
                    </div>
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">Limpiar</button>
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">Aplicar Filtros</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Viajes -->
<div class="row">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Historial de Viajes</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshTrips()">
                        <i class="bi bi-arrow-clockwise"></i> Actualizar
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Fecha</th>
                                <th>Origen</th>
                                <th>Destino</th>
                                <th>Conductor</th>
                                <th>Estado</th>
                                <th>Tarifa</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="trips-table-body">
                            {% for trip in trips|default([]) %}
                            <tr>
                                <td>{{ trip.id }}</td>
                                <td>{{ trip.requested_at|default('') }}</td>
                                <td>{{ trip.origin_address|truncate(15) }}</td>
                                <td>{{ trip.destination_address|truncate(15) }}</td>
                                <td>{{ trip.driver_name|default('Sin asignar') }}</td>
                                <td>
                                    <span class="badge {% if trip.status == 'completado' %}bg-success{% elif trip.status == 'cancelado' %}bg-danger{% elif trip.status == 'en_viaje' %}bg-primary{% else %}bg-warning{% endif %}">
                                        {{ trip.status|upper }}
                                    </span>
                                </td>
                                <td>${{ trip.actual_fare|default(trip.estimated_fare) }}</td>
                                <td>
                                    <a href="{{ url_for('passenger_trip_details_route', trip_id=trip.id) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center">No has realizado ningún viaje aún</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span class="text-muted">Mostrando {{ trips|default([])|length }} viajes</span>
                    </div>
                    <nav aria-label="Paginación de viajes">
                        <ul class="pagination pagination-sm">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                            </li>
                            <li class="page-item active" aria-current="page">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Siguiente</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Solicitar Nuevo Viaje -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <h5 class="card-title">¿Necesitas un taxi?</h5>
                <p class="card-text">Solicita un nuevo viaje ahora mismo.</p>
                <a href="{{ url_for('passenger_request_trip_route') }}" class="btn btn-lg btn-primary">
                    <i class="bi bi-plus-circle me-2"></i> Solicitar Nuevo Viaje
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
    // Función para aplicar filtros
    function applyFilters() {
        const statusFilter = document.getElementById('status-filter').value;
        const dateFilter = document.getElementById('date-filter').value;
        const searchFilter = document.getElementById('search-filter').value;
        
        // Construir URL con parámetros de filtro
        let url = '/api/v1/trips/passenger?';
        if (statusFilter) url += `status=${statusFilter}&`;
        if (dateFilter) url += `date=${dateFilter}&`;
        if (searchFilter) url += `search=${searchFilter}&`;
        
        // Eliminar el último '&' si existe
        url = url.endsWith('&') ? url.slice(0, -1) : url;
        
        // Cargar viajes filtrados
        loadFilteredTrips(url);
    }

    // Función para cargar viajes filtrados
    async function loadFilteredTrips(url) {
        try {
            const response = await fetchWithAuth(url);
            if (response.ok) {
                const trips = await response.json();
                updateTripsTable(trips);
            }
        } catch (error) {
            console.error('Error al cargar viajes filtrados:', error);
        }
    }

    // Función para actualizar la tabla de viajes
    function updateTripsTable(trips) {
        const tableBody = document.getElementById('trips-table-body');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        if (trips.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">No has realizado ningún viaje que coincida con los filtros</td>
                </tr>
            `;
            return;
        }
        
        trips.forEach(trip => {
            const statusClass = trip.status === 'completado' ? 'bg-success' : 
                               trip.status === 'cancelado' ? 'bg-danger' :
                               trip.status === 'en_viaje' ? 'bg-primary' : 'bg-warning';
            
            tableBody.innerHTML += `
                <tr>
                    <td>${trip.id}</td>
                    <td>${trip.requested_at || ''}</td>
                    <td>${trip.origin_address ? trip.origin_address.substring(0, 15) + '...' : ''}</td>
                    <td>${trip.destination_address ? trip.destination_address.substring(0, 15) + '...' : ''}</td>
                    <td>${trip.driver_name || 'Sin asignar'}</td>
                    <td>
                        <span class="badge ${statusClass}">
                            ${trip.status.toUpperCase()}
                        </span>
                    </td>
                    <td>$${trip.actual_fare || trip.estimated_fare}</td>
                    <td>
                        <a href="/web/passenger/trip/${trip.id}" class="btn btn-sm btn-primary">
                            <i class="bi bi-eye"></i>
                        </a>
                    </td>
                </tr>
            `;
        });
    }

    // Función para resetear filtros
    function resetFilters() {
        document.getElementById('status-filter').value = '';
        document.getElementById('date-filter').value = '';
        document.getElementById('search-filter').value = '';
        
        // Recargar todos los viajes
        loadTrips();
    }

    // Función para actualizar viajes
    function refreshTrips() {
        loadTrips();
    }

    // Función para cargar viajes
    async function loadTrips() {
        try {
            const response = await fetchWithAuth('/api/v1/trips/passenger');
            if (response.ok) {
                const trips = await response.json();
                updateTripsTable(trips);
            }
        } catch (error) {
            console.error('Error al cargar viajes:', error);
        }
    }

    // Cargar viajes al iniciar
    document.addEventListener('DOMContentLoaded', function() {
        loadTrips();
    });
</script>
{% endblock %}
