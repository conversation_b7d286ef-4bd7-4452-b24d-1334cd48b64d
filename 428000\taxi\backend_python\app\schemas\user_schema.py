from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional, Union
from datetime import datetime
from app.models.user import RoleEnum # Importar el Enum de Python
from app.models.user import UserRoleModel # Importar el modelo de rol

class RoleBase(BaseModel):
    name: Union[RoleEnum, str] # Permitir tanto el Enum como una cadena
    description: Optional[str] = None

class RoleCreate(RoleBase):
    pass

class Role(RoleBase): # Este es el schema de Pydantic para el rol
    id: int
    class Config:
        from_attributes = True

class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    is_active: Optional[bool] = True

class UserCreate(UserBase):
    password: str = Field(min_length=8)
    roles: Optional[List[Union[RoleEnum, str]]] = None # Hacer el campo opcional

class UserUpdate(UserBase):
    password: Optional[str] = Field(None, min_length=8)
    is_superuser: Optional[bool] = None
    roles: Optional[List[Union[RoleEnum, str]]] = None

class UserInDBBase(UserBase):
    id: int
    is_superuser: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    roles: Optional[List[Role]] = [] # Usar el schema Role para mostrar, pero hacerlo opcional

    class Config:
        from_attributes = True

class User(UserInDBBase): # Schema para devolver al cliente
    pass

class UserInDB(UserInDBBase): # Schema interno con contrasena hash
    hashed_password: str
