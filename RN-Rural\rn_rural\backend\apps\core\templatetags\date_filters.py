from django import template
from django.utils import timezone
from datetime import datetime

register = template.Library()

@register.filter(name='format_date')
def format_date(value):
    """
    Formatea una fecha en formato dd-mm-yyyy
    """
    if not value:
        return ""
    
    if isinstance(value, str):
        try:
            value = datetime.fromisoformat(value.replace('Z', '+00:00'))
        except (ValueError, TypeError):
            return value
    
    try:
        return value.strftime('%d-%m-%Y')
    except (AttributeError, ValueError):
        return value

@register.filter(name='format_datetime')
def format_datetime(value):
    """
    Formatea una fecha y hora en formato dd-mm-yyyy HH:MM
    """
    if not value:
        return ""
    
    if isinstance(value, str):
        try:
            value = datetime.fromisoformat(value.replace('Z', '+00:00'))
        except (ValueError, TypeError):
            return value
    
    try:
        # Convertir a la zona horaria local si es necesario
        if timezone.is_aware(value):
            value = timezone.localtime(value)
        
        return value.strftime('%d-%m-%Y %H:%M')
    except (AttributeError, ValueError):
        return value
