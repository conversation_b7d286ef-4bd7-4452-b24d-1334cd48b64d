<!-- /app/templates/admin/list_users.html -->
{% extends "base.html" %}
{% from "_pagination.html" import render_pagination %} {# Asegúrate de importar tu macro #}

{% block title %}Lista de Usuarios{% endblock %}

{% block content %}
    <h1>Lista de Usuarios</h1>

    <!-- Formulario de Búsqueda y Filtro de Estado -->
    <div class="card card-body bg-light mb-4 shadow-sm">
        <form method="GET" action="{{ url_for('routes.list_users') }}" class="form-row align-items-end">
            <div class="col-md-6 mb-2 mb-md-0">
                <label for="search" class="sr-only">Buscar</label>
                <div class="input-group">
                    <input class="form-control" type="search" id="search" name="search" placeholder="Buscar por nombre, usuario, email..." value="{{ filters.search or '' }}">
                     <div class="input-group-append">
                         <button class="btn btn-primary" type="submit"><i class="fas fa-search"></i></button>
                     </div>
                </div>
            </div>
            <div class="col-md-4 mb-2 mb-md-0">
                <label for="status" class="sr-only">Estado</label>
                 <select name="status" id="status" class="form-control">
                    <option value="all" {% if filters.status == 'all' %}selected{% endif %}>Todos los Estados</option>
                    <option value="active" {% if filters.status == 'active' %}selected{% endif %}>Activos</option>
                    <option value="inactive" {% if filters.status == 'inactive' %}selected{% endif %}>Inactivos</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-secondary btn-block">Filtrar</button>
                 <a href="{{ url_for('routes.list_users') }}" class="btn btn-link btn-sm btn-block text-muted">Limpiar</a>
            </div>
        </form>
    </div>
    <!-- Fin Formulario -->


    <table class="table table-striped table-hover"> {# Añadido table-hover #}
        <thead>
            <tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Nombre Completo</th>
                <th>Email</th>
                <th>Rol</th>
                <th>Iglesia</th>
                <th>Estado</th> {# NUEVA COLUMNA #}
                <th>Acciones</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
                <tr class="{{ 'table-secondary text-muted' if not user.is_active else '' }}"> {# Estilo para inactivos #}
                    <td>{{ user.id }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.first_name }} {{ user.last_name }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.role|title }}</td>
                    <td>{{ user.church.name if user.church else 'Ninguna' }}</td>
                    <td>
                        {# NUEVO: Mostrar estado con un badge #}
                        {% if user.is_active %}
                            <span class="badge badge-success">Activo</span>
                        {% else %}
                            <span class="badge badge-danger">Inactivo</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{{ url_for('routes.user_detail', user_id=user.id) }}" class="btn btn-info btn-sm" title="Ver Detalles"><i class="fas fa-eye"></i></a>
                        <a href="{{ url_for('routes.edit_profile', user_id=user.id) }}" class="btn btn-warning btn-sm" title="Editar Perfil"><i class="fas fa-edit"></i></a>

                        {# Lógica botones Activar/Desactivar/Eliminar #}
                        {% if current_user.role in ('administrador', 'secretaria') and user.role not in ('administrador', 'secretaria') and user.id != current_user.id %}
                            {# Botón Activar/Desactivar (Usa un formulario POST) #}
                            <form method="POST" action="{{ url_for('routes.toggle_user_activation', user_id=user.id, search=filters.search, page=pagination.page, status=filters.status) }}" style="display:inline;">
                                {# Pasar filtros/página para volver al mismo lugar #}
                                {% if user.is_active %}
                                    <button type="submit" class="btn btn-secondary btn-sm" title="Desactivar Usuario" onclick="return confirm('¿Estás seguro de DESACTIVAR a {{ user.username }}? No podrá iniciar sesión.');"><i class="fas fa-user-slash"></i></button>
                                {% else %}
                                     <button type="submit" class="btn btn-success btn-sm" title="Reactivar Usuario" onclick="return confirm('¿Estás seguro de REACTIVAR a {{ user.username }}?');"><i class="fas fa-user-check"></i></button>
                                {% endif %}
                            </form>

                            {# Botón Ver Como (si aplica) #}
                            <a href="{{ url_for('routes.dashboard', view_as=user.id) }}" class="btn btn-outline-secondary btn-sm" title="Ver Como {{ user.username }}"><i class="fas fa-binoculars"></i></a>

                            {# Botón Eliminar Permanente (con advertencia extra) #}
                            {# Considera ocultar este botón si prefieres solo desactivar #}
                            <form method="POST" action="{{ url_for('routes.delete_user', user_id=user.id) }}" style="display:inline;">
                                <button type="submit" class="btn btn-danger btn-sm" title="ELIMINAR PERMANENTEMENTE" onclick="return confirm('¡ATENCIÓN! ¿Estás SEGURO de ELIMINAR PERMANENTEMENTE a {{ user.username }}? Esta acción NO se puede deshacer y borrará datos asociados si hay cascadas.');">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                {# Confirmación extra opcional: <input type="text" name="confirm_delete" placeholder="Escribe CONFIRMAR"> #}
                            </form>
                        {% endif %}
                    </td>
                </tr>
            {% else %}
             <tr>
                 <td colspan="8" class="text-center text-muted">
                    {% if filters.search or filters.status != 'all' %}
                         No se encontraron usuarios que coincidan con los filtros aplicados.
                    {% else %}
                         No hay usuarios registrados (aparte de ti).
                    {% endif %}
                    <a href="{{ url_for('routes.list_users') }}" class="ml-2">Limpiar filtros</a>
                </td>
             </tr>
            {% endfor %}
        </tbody>
    </table>

     {# Renderizar paginación pasando los filtros #}
     {{ render_pagination(pagination, 'routes.list_users', filters=filters) }}

{% endblock %}