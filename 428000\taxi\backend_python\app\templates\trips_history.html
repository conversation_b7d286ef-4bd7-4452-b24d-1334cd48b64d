{% extends "base_layout.html" %}

{% block title %}Historial de Viajes - Panel de Taxis{% endblock %}

{% block head_extra %}
<style>
    .trip-card {
        transition: transform 0.3s;
        margin-bottom: 20px;
    }
    .trip-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .search-container {
        margin-bottom: 20px;
    }
    .filter-buttons {
        margin-bottom: 15px;
    }
    .filter-buttons .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }
    .date-filters {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }
    .trip-details {
        font-size: 0.9rem;
    }
    .trip-details i {
        width: 20px;
        text-align: center;
        margin-right: 5px;
    }
    .map-container {
        height: 200px;
        margin-bottom: 15px;
        background-color: #f0f0f0;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .table-responsive {
        overflow-x: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Historial de Viajes</h2>
                <button type="button" class="btn btn-light" id="exportTripsBtn">
                    <i class="bi bi-download"></i> Exportar
                </button>
            </div>
            <div class="card-body">
                <!-- Estadísticas rápidas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5>Total Viajes</h5>
                                <h2>{{ total_trips }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>Completados</h5>
                                <h2>{{ completed_trips }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h5>En Progreso</h5>
                                <h2>{{ in_progress_trips }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5>Cancelados</h5>
                                <h2>{{ cancelled_trips }}</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Búsqueda y filtros -->
                <div class="search-container">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchTrip" placeholder="Buscar por ID, pasajero o conductor...">
                                <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                    <i class="bi bi-search"></i> Buscar
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filter-buttons d-flex flex-wrap justify-content-end">
                                <button class="btn btn-outline-primary filter-btn active" data-filter="all">Todos</button>
                                <button class="btn btn-outline-success filter-btn" data-filter="completed">Completados</button>
                                <button class="btn btn-outline-warning filter-btn" data-filter="in_progress">En Progreso</button>
                                <button class="btn btn-outline-danger filter-btn" data-filter="cancelled">Cancelados</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="date-filters mt-3">
                        <div class="input-group">
                            <span class="input-group-text">Desde</span>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="input-group">
                            <span class="input-group-text">Hasta</span>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <button class="btn btn-outline-primary" id="filterDateBtn">Filtrar</button>
                        <button class="btn btn-outline-secondary" id="resetFilterBtn">Resetear</button>
                    </div>
                </div>

                <!-- Tabla de viajes -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Fecha</th>
                                <th>Pasajero</th>
                                <th>Conductor</th>
                                <th>Origen</th>
                                <th>Destino</th>
                                <th>Distancia</th>
                                <th>Precio</th>
                                <th>Estado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="tripsTableBody">
                            {% for trip in trips %}
                            <tr data-status="{{ trip.status }}">
                                <td>{{ trip.id }}</td>
                                <td>{{ trip.date }}</td>
                                <td>{{ trip.passenger_name }}</td>
                                <td>{{ trip.driver_name }}</td>
                                <td>{{ trip.origin }}</td>
                                <td>{{ trip.destination }}</td>
                                <td>{{ trip.distance }} km</td>
                                <td>${{ trip.price }}</td>
                                <td>
                                    {% if trip.status == 'completed' %}
                                    <span class="badge bg-success">Completado</span>
                                    {% elif trip.status == 'in_progress' %}
                                    <span class="badge bg-warning text-dark">En Progreso</span>
                                    {% elif trip.status == 'cancelled' %}
                                    <span class="badge bg-danger">Cancelado</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ trip.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="viewTrip({{ trip.id }})">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginación -->
                <nav aria-label="Paginación de viajes">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Siguiente</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal para ver detalles del viaje -->
<div class="modal fade" id="tripDetailsModal" tabindex="-1" aria-labelledby="tripDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="tripDetailsModalLabel">Detalles del Viaje</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="map-container">
                    <p class="text-muted">Mapa del recorrido (simulado)</p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>Información del Viaje</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>ID:</span>
                                <span id="tripId">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Fecha:</span>
                                <span id="tripDate">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Estado:</span>
                                <span id="tripStatus">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Origen:</span>
                                <span id="tripOrigin">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Destino:</span>
                                <span id="tripDestination">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Distancia:</span>
                                <span id="tripDistance">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Duración:</span>
                                <span id="tripDuration">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Precio:</span>
                                <span id="tripPrice">-</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Personas</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Pasajero:</span>
                                <span id="tripPassenger">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Conductor:</span>
                                <span id="tripDriver">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Vehículo:</span>
                                <span id="tripVehicle">-</span>
                            </li>
                        </ul>
                        
                        <h5 class="mt-4">Calificación</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Calificación del pasajero:</span>
                                <span id="passengerRating">-</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Calificación del conductor:</span>
                                <span id="driverRating">-</span>
                            </li>
                            <li class="list-group-item">
                                <span>Comentarios:</span>
                                <p id="tripComments" class="mt-2">-</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                <button type="button" class="btn btn-primary" id="printTripBtn">Imprimir</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
    // Filtrar viajes por estado
    document.querySelectorAll('.filter-btn').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            const rows = document.querySelectorAll('#tripsTableBody tr');
            
            // Resaltar botón activo
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
            
            // Filtrar filas
            rows.forEach(row => {
                if (filter === 'all' || row.getAttribute('data-status') === filter) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
    
    // Búsqueda de viajes
    document.getElementById('searchButton').addEventListener('click', function() {
        const searchTerm = document.getElementById('searchTrip').value.toLowerCase();
        const rows = document.querySelectorAll('#tripsTableBody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // También buscar al presionar Enter
    document.getElementById('searchTrip').addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            document.getElementById('searchButton').click();
        }
    });
    
    // Filtrar por fecha
    document.getElementById('filterDateBtn').addEventListener('click', function() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        
        if (!startDate || !endDate) {
            alert('Por favor seleccione fechas de inicio y fin');
            return;
        }
        
        // Aquí implementar la lógica para filtrar por fecha
        alert(`Filtrando viajes desde ${startDate} hasta ${endDate}`);
    });
    
    // Resetear filtros
    document.getElementById('resetFilterBtn').addEventListener('click', function() {
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';
        document.getElementById('searchTrip').value = '';
        
        // Mostrar todos los viajes
        document.querySelectorAll('#tripsTableBody tr').forEach(row => {
            row.style.display = '';
        });
        
        // Resetear botones de filtro
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector('.filter-btn[data-filter="all"]').classList.add('active');
    });
    
    // Ver detalles del viaje
    function viewTrip(tripId) {
        // Aquí implementar la lógica para cargar los detalles del viaje
        // Simulación de datos
        document.getElementById('tripId').textContent = tripId;
        document.getElementById('tripDate').textContent = '2023-05-22 14:30';
        document.getElementById('tripStatus').innerHTML = '<span class="badge bg-success">Completado</span>';
        document.getElementById('tripOrigin').textContent = 'Av. Corrientes 1234, CABA';
        document.getElementById('tripDestination').textContent = 'Av. Santa Fe 4321, CABA';
        document.getElementById('tripDistance').textContent = '5.2 km';
        document.getElementById('tripDuration').textContent = '18 minutos';
        document.getElementById('tripPrice').textContent = '$1,250.00';
        document.getElementById('tripPassenger').textContent = 'Juan Pérez';
        document.getElementById('tripDriver').textContent = 'Carlos Gómez';
        document.getElementById('tripVehicle').textContent = 'Toyota Corolla (ABC123)';
        document.getElementById('passengerRating').textContent = '⭐⭐⭐⭐⭐ (5.0)';
        document.getElementById('driverRating').textContent = '⭐⭐⭐⭐ (4.0)';
        document.getElementById('tripComments').textContent = 'Excelente servicio, muy puntual y amable.';
        
        // Mostrar modal
        var myModal = new bootstrap.Modal(document.getElementById('tripDetailsModal'));
        myModal.show();
    }
    
    // Exportar viajes
    document.getElementById('exportTripsBtn').addEventListener('click', function() {
        alert('Exportando viajes a CSV...');
        // Implementar lógica de exportación
    });
    
    // Imprimir detalles del viaje
    document.getElementById('printTripBtn').addEventListener('click', function() {
        alert('Imprimiendo detalles del viaje...');
        // Implementar lógica de impresión
    });
</script>
{% endblock %}
