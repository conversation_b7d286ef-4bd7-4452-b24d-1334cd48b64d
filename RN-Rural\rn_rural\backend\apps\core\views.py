# File: apps/core/views.py
# -----------------------------------------------

from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate, logout # Corregido: 'logout' a<PERSON><PERSON><PERSON>
from django.contrib import messages
from .forms import CustomLoginForm, CustomRegisterForm
from django.contrib.auth.decorators import login_required
from apps.incidents.models import Incidencia   # opcional, para estadísticas
from apps.locations.models import UbicacionUsuario # Para la ubicación del usuario

# from apps.users.models import User # No es estrictamente necesario aquí si solo usas request.user.role

def custom_login_view(request): # <--- ASEGÚRATE DE QUE ESTA FUNCIÓN EXISTA Y SE LLAME ASÍ
    if request.user.is_authenticated:
        # Si el usuario ya está autenticado, redirigir según rol
        if request.user.role == 'ADMIN':
            return redirect('admin:index')
        elif request.user.role == 'OPERADOR':
            return redirect('dashboard_operador')
        elif request.user.role == 'CIUDADANO':
            return redirect('dashboard_ciudadano')
        elif request.user.role == 'BRIGADA':
            return redirect('dashboard_brigada')
        else:
            return redirect('/')

    if request.method == 'POST':
        form = CustomLoginForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f"Bienvenido de nuevo, {user.username}!")
                if user.role == 'ADMIN':
                    return redirect('admin:index')
                elif user.role == 'OPERADOR':
                    return redirect('dashboard_operador')
                elif user.role == 'CIUDADANO':
                    return redirect('dashboard_ciudadano')
                elif user.role == 'BRIGADA':
                    return redirect('dashboard_brigada')
                else:
                    return redirect('/')
            else:
                messages.error(request, "Usuario o contraseña incorrectos.")
        else:
            # Iterar sobre los errores del formulario para más detalle
            error_list = []
            for field, errors in form.errors.items():
                for error in errors:
                    error_list.append(f"{form.fields[field].label if field != '__all__' else ''}: {error}")
            messages.error(request, "Formulario inválido. " + " ".join(error_list))
    else:
        form = CustomLoginForm()

    return render(request, 'core/login.html', {'form': form})

def custom_logout_view(request): # <--- ASEGÚRATE DE QUE ESTA FUNCIÓN EXISTA
    logout(request)
    messages.info(request, "Has cerrado sesión exitosamente.")
    return redirect('login')

def dashboard_operador_view(request): # <--- ASEGÚRATE DE QUE ESTA FUNCIÓN EXISTA
    if not request.user.is_authenticated or request.user.role != 'OPERADOR':
        # messages.error(request, "Acceso no autorizado.") # Podrías usar el decorador @login_required
        return redirect('login')

    # Obtener contadores de incidencias por estado
    from apps.incidents.models import Incidencia

    count_nuevas = Incidencia.objects.filter(estado='NUEVA').count()
    count_proceso = Incidencia.objects.filter(
        estado__in=[
            'ASIGNADA_OPERADOR',
            'EN_PROCESO_OPERADOR'
        ]
    ).count()
    count_derivadas = Incidencia.objects.filter(estado='DERIVADA_BRIGADA').count()
    count_resueltas = Incidencia.objects.filter(
        estado__in=[
            'CERRADA_RESUELTA',
            'CERRADA_NO_RESUELTA'
        ]
    ).count()

    context = {
        'user': request.user,
        'count_nuevas': count_nuevas,
        'count_proceso': count_proceso,
        'count_derivadas': count_derivadas,
        'count_resueltas': count_resueltas
    }

    return render(request, 'core/dashboard_operador.html', context)

@login_required
def dashboard_ciudadano_view(request):
    if not request.user.is_authenticated or request.user.role != 'CIUDADANO':
        # Redirigir si no es ciudadano o no está autenticado
        # (La lógica de redirección a otros dashboards si está autenticado pero no es ciudadano
        # ya está en custom_login_view. Aquí, si llega y no es ciudadano, lo mandamos al login).
        messages.error(request, "Acceso no autorizado.")
        return redirect('login')

    # Obtener incidencias reportadas por este usuario
    incidencias_reportadas = Incidencia.objects.filter(usuario_reporta=request.user).order_by('-fecha_creacion')

    # Obtener la última ubicación conocida del usuario (si la tienes)
    ubicacion_actual_usuario = None
    try:
        # Asumiendo que tienes un modelo UbicacionUsuario como el que creaste antes
        # y que lo actualizas de alguna manera (por ahora, puede ser None)
        ubicacion_obj = UbicacionUsuario.objects.get(usuario=request.user)
        if ubicacion_obj.posicion_actual:
            ubicacion_actual_usuario = {
                "lat": ubicacion_obj.posicion_actual.y,
                "lng": ubicacion_obj.posicion_actual.x
            }
    except UbicacionUsuario.DoesNotExist:
        pass # No hay ubicación guardada para este usuario

    context = {
        'user': request.user,
        'incidencias_reportadas': incidencias_reportadas,
        'ubicacion_actual_usuario': ubicacion_actual_usuario, # Pasamos la ubicación a la plantilla
        'titulo_pagina': "Mi Portal Ciudadano"
    }
    return render(request, 'core/dashboard_ciudadano.html', context)

def register_view(request):
    if request.method == "POST":
        form = CustomRegisterForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Cuenta creada. Ya puedes iniciar sesión.")
            return redirect("login")
    else:
        form = CustomRegisterForm()
    return render(request, "core/register.html", {"form": form})

@login_required
def dashboard_brigada_view(request):
    if request.user.role != 'BRIGADA':
        return redirect('login')

    # Obtener incidencias asignadas a esta brigada
    incidencias_asignadas = Incidencia.objects.filter(
        brigada_asignada=request.user,
        estado__in=['DERIVADA_BRIGADA', 'EN_PROCESO_BRIGADA']
    ).order_by('-fecha_creacion')

    # Contar incidencias pendientes
    pendientes = incidencias_asignadas.filter(estado='DERIVADA_BRIGADA').count()

    # Obtener la ubicación actual de la brigada
    from apps.locations.models import UbicacionUsuario
    ubicacion_brigada = None
    try:
        ubicacion_obj = UbicacionUsuario.objects.get(usuario=request.user)
        if ubicacion_obj.posicion_actual:
            ubicacion_brigada = {
                "lat": ubicacion_obj.posicion_actual.y,
                "lng": ubicacion_obj.posicion_actual.x,
                "ultima_actualizacion": ubicacion_obj.fecha_actualizacion.strftime("%d/%m/%Y %H:%M")
            }
    except UbicacionUsuario.DoesNotExist:
        pass

    # Preparar datos de ubicación para cada incidencia
    for incidencia in incidencias_asignadas:
        if incidencia.ubicacion_incidencia:
            incidencia.ubicacion_data = {
                "lat": incidencia.ubicacion_incidencia.y,
                "lng": incidencia.ubicacion_incidencia.x
            }

            # Si tenemos la ubicación de la brigada, calcular la distancia
            if ubicacion_brigada and incidencia.ubicacion_incidencia:
                from apps.core.route_service import calculate_route_with_openrouteservice
                import math
                import logging
                logger = logging.getLogger(__name__)

                # Coordenadas en formato (longitud, latitud) para OpenRouteService
                start_coords = (ubicacion_obj.posicion_actual.x, ubicacion_obj.posicion_actual.y)
                end_coords = (incidencia.ubicacion_incidencia.x, incidencia.ubicacion_incidencia.y)

                try:
                    # Intentar calcular la ruta usando OpenRouteService
                    route_data = calculate_route_with_openrouteservice(start_coords, end_coords)

                    if route_data:
                        incidencia.ruta = {
                            'coordenadas': route_data['coordinates'],
                            'distancia': route_data['distance'],
                            'duracion': route_data['duration']
                        }
                    else:
                        # Calcular distancia en línea recta como fallback
                        # Convertir coordenadas a radianes
                        lat1 = math.radians(ubicacion_obj.posicion_actual.y)
                        lon1 = math.radians(ubicacion_obj.posicion_actual.x)
                        lat2 = math.radians(incidencia.ubicacion_incidencia.y)
                        lon2 = math.radians(incidencia.ubicacion_incidencia.x)

                        # Fórmula de Haversine
                        dlon = lon2 - lon1
                        dlat = lat2 - lat1
                        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
                        c = 2 * math.asin(math.sqrt(a))
                        r = 6371000  # Radio de la Tierra en metros

                        # Calcular distancia en metros
                        distancia = c * r

                        # Estimar duración (asumiendo velocidad promedio de 40 km/h)
                        duracion = (distancia / 1000) / 40 * 3600  # Convertir a segundos

                        incidencia.ruta = {
                            'distancia': distancia,
                            'duracion': duracion,
                            'es_linea_recta': True
                        }
                except Exception as e:
                    logger.error(f"Error al calcular la ruta: {str(e)}")
        else:
            incidencia.ubicacion_data = None

    return render(request, 'core/dashboard_brigada.html', {
        'user': request.user,
        'pendientes': pendientes,
        'incidencias_asignadas': incidencias_asignadas,
        'ubicacion_brigada': ubicacion_brigada,
        'titulo_pagina': "Panel de Brigada"
    })

@login_required
def gestionar_brigadas_view(request):
    """
    Vista para gestionar brigadas desde el panel del operador.
    """
    if not request.user.is_authenticated or request.user.role != 'OPERADOR':
        messages.error(request, "Acceso no autorizado.")
        return redirect('login')

    # Obtener todas las brigadas
    from apps.users.models import User
    from apps.locations.models import UbicacionUsuario
    from django.db.models import Count, Q

    # Obtener brigadas con conteo de incidencias asignadas
    brigadas = User.objects.filter(role='BRIGADA').annotate(
        incidencias_count=Count('incidencias_asignadas_brigada', filter=Q(incidencias_asignadas_brigada__estado__in=['DERIVADA_BRIGADA', 'EN_PROCESO_BRIGADA']))
    ).order_by('id')

    # Obtener información de ubicación para cada brigada
    for brigada in brigadas:
        try:
            brigada.ubicacion = UbicacionUsuario.objects.get(usuario=brigada)
        except UbicacionUsuario.DoesNotExist:
            brigada.ubicacion = None

    context = {
        'brigadas': brigadas,
        'titulo_pagina': "Gestionar Brigadas"
    }

    return render(request, 'core/gestionar_brigadas.html', context)

@login_required
def estadisticas_view(request):
    """
    Vista para mostrar estadísticas del sistema.
    """
    if not request.user.is_authenticated or request.user.role != 'OPERADOR':
        messages.error(request, "Acceso no autorizado.")
        return redirect('login')

    from apps.incidents.models import Incidencia
    from apps.users.models import User
    from django.db.models import Count, Q
    from django.utils import timezone
    from datetime import datetime, timedelta

    # Obtener fechas de filtro
    fecha_fin = request.GET.get('fecha_fin', None)
    fecha_inicio = request.GET.get('fecha_inicio', None)

    if fecha_fin:
        try:
            fecha_fin = datetime.strptime(fecha_fin, '%Y-%m-%d').date()
        except ValueError:
            fecha_fin = timezone.now().date()
    else:
        fecha_fin = timezone.now().date()

    if fecha_inicio:
        try:
            fecha_inicio = datetime.strptime(fecha_inicio, '%Y-%m-%d').date()
        except ValueError:
            fecha_inicio = fecha_fin - timedelta(days=30)
    else:
        fecha_inicio = fecha_fin - timedelta(days=30)

    # Asegurarse de que fecha_inicio sea anterior a fecha_fin
    if fecha_inicio > fecha_fin:
        fecha_inicio, fecha_fin = fecha_fin, fecha_inicio

    # Convertir a datetime para incluir todo el día en fecha_fin
    fecha_fin_datetime = datetime.combine(fecha_fin, datetime.max.time())
    fecha_inicio_datetime = datetime.combine(fecha_inicio, datetime.min.time())

    # Filtrar incidencias por fecha
    incidencias_filtradas = Incidencia.objects.filter(
        fecha_creacion__gte=fecha_inicio_datetime,
        fecha_creacion__lte=fecha_fin_datetime
    )

    # Obtener contadores de incidencias por estado
    count_nuevas = incidencias_filtradas.filter(estado='NUEVA').count()
    count_proceso = incidencias_filtradas.filter(
        estado__in=['ASIGNADA_OPERADOR', 'EN_PROCESO_OPERADOR']
    ).count()
    count_derivadas = incidencias_filtradas.filter(estado='DERIVADA_BRIGADA').count()
    count_resueltas = incidencias_filtradas.filter(
        estado__in=['CERRADA_RESUELTA', 'CERRADA_NO_RESUELTA']
    ).count()

    # Obtener incidencias por día
    incidencias_por_dia = []
    current_date = fecha_inicio
    while current_date <= fecha_fin:
        next_date = current_date + timedelta(days=1)
        count = incidencias_filtradas.filter(
            fecha_creacion__gte=datetime.combine(current_date, datetime.min.time()),
            fecha_creacion__lt=datetime.combine(next_date, datetime.min.time())
        ).count()
        incidencias_por_dia.append((current_date, count))
        current_date = next_date

    # Obtener rendimiento de brigadas
    brigadas = User.objects.filter(role='BRIGADA')
    rendimiento_brigadas = []

    for brigada in brigadas:
        # Incidencias resueltas por esta brigada
        resueltas = incidencias_filtradas.filter(
            brigada_asignada=brigada,
            estado__in=['CERRADA_RESUELTA', 'CERRADA_NO_RESUELTA']
        ).count()

        # Incidencias pendientes para esta brigada
        pendientes = incidencias_filtradas.filter(
            brigada_asignada=brigada,
            estado='DERIVADA_BRIGADA'
        ).count()

        if resueltas > 0 or pendientes > 0:
            rendimiento_brigadas.append({
                'username': brigada.username,
                'resueltas': resueltas,
                'pendientes': pendientes
            })

    context = {
        'count_nuevas': count_nuevas,
        'count_proceso': count_proceso,
        'count_derivadas': count_derivadas,
        'count_resueltas': count_resueltas,
        'incidencias_por_dia': incidencias_por_dia,
        'rendimiento_brigadas': rendimiento_brigadas,
        'fecha_inicio': fecha_inicio,
        'fecha_fin': fecha_fin,
        'titulo_pagina': "Estadísticas del Sistema"
    }

    return render(request, 'core/estadisticas.html', context)