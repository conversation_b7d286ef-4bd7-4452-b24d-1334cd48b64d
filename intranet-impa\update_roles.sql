-- update_roles.sql
-- Script para actualizar roles y añadir nuevos roles académicos

-- 1. Actualizar la columna role para incluir nuevos roles académicos
ALTER TABLE users MODIFY COLUMN role ENUM(
    'administrador', 
    'secretaria', 
    'pastorado', 
    'miembro',
    'instituto',
    'rector',
    'profesor_corporativo'
) DEFAULT 'miembro';

-- 2. Verificar que la actualización fue exitosa
SELECT DISTINCT role FROM users;

-- 3. Mostrar información de la columna actualizada
DESCRIBE users;
