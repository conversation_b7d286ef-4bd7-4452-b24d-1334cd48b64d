#!/usr/bin/env python3
"""
Script para modificar el archivo app/models/user.py y cambiar los valores del enum a MAYÚSCULAS.
"""

import os
import re

def fix_enum_values():
    print("Modificando los valores del enum RoleEnum a MAYÚSCULAS...")
    
    # Ruta al archivo
    file_path = "app/models/user.py"
    
    # Hacer una copia de seguridad
    os.system(f"cp {file_path} {file_path}.bak")
    print(f"Copia de seguridad creada: {file_path}.bak")
    
    # Leer el archivo
    with open(file_path, "r") as file:
        content = file.read()
    
    # Buscar y reemplazar los valores del enum
    pattern = r'(USUARIO\s*=\s*["\'])usuario(["\'])'
    replacement = r'\1USUARIO\2'
    content = re.sub(pattern, replacement, content)
    
    pattern = r'(TAXI\s*=\s*["\'])taxi(["\'])'
    replacement = r'\1TAXI\2'
    content = re.sub(pattern, replacement, content)
    
    pattern = r'(OPERADOR\s*=\s*["\'])operador(["\'])'
    replacement = r'\1OPERADOR\2'
    content = re.sub(pattern, replacement, content)
    
    pattern = r'(TITULAR\s*=\s*["\'])titular(["\'])'
    replacement = r'\1TITULAR\2'
    content = re.sub(pattern, replacement, content)
    
    pattern = r'(BASE\s*=\s*["\'])base(["\'])'
    replacement = r'\1BASE\2'
    content = re.sub(pattern, replacement, content)
    
    pattern = r'(ADMINISTRADOR\s*=\s*["\'])administrador(["\'])'
    replacement = r'\1ADMINISTRADOR\2'
    content = re.sub(pattern, replacement, content)
    
    # Guardar el archivo
    with open(file_path, "w") as file:
        file.write(content)
    
    print(f"Archivo {file_path} modificado correctamente.")
    print("Los valores del enum ahora son:")
    print("  - USUARIO = \"USUARIO\"")
    print("  - TAXI = \"TAXI\"")
    print("  - OPERADOR = \"OPERADOR\"")
    print("  - TITULAR = \"TITULAR\"")
    print("  - BASE = \"BASE\"")
    print("  - ADMINISTRADOR = \"ADMINISTRADOR\"")

if __name__ == "__main__":
    fix_enum_values()
