// /static/js/dig-vhf.js

document.addEventListener('DOMContentLoaded', function () {
    // const nodeId = window.nodeId; // Ya están globales
    // const username = window.username; // Ya están globales
    // const nodeName = window.nodeName; // Ya está global

    console.log(`(index.js) Conectando como ${window.username} al nodo ${window.nodeId} en el servidor: ${window.socketIoServerUrl}`);

    // Usa la URL del servidor Socket.IO de la VM y el namespace /node
    const socket = io.connect(window.socketIoServerUrl + '/node', { // <--- MODIFICADO
        transports: ['websocket'], // Forzar websocket es una buena práctica
        query: {
            node_id: String(window.nodeId),
            username: window.username
        }
    });

    let isTransmittingAudio = false;

    socket.on('connect', () => {
        console.log('Conexión exitosa con Socket.IO para Digital a VHF.');
    });

    socket.on('receive_audio', (data) => {
        console.log('Recibiendo audio, activando PTT...', data);
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);
            return;
        }
        if (!data.audio || data.audio.trim() === "") {
            console.error("Error: Audio vacío recibido.");
            return;
        }
        console.log('Enviando evento ptt_control: Activando PTT en 1 segundo...');
        activarPTT();
        setTimeout(() => {
            try {
                const audioBlob = new Blob(
                    [new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))],
                    { type: 'audio/wav' }
                );
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);
                audio.play().then(() => {
                    console.log('Reproduciendo audio de otro usuario...');
                }).catch((error) => {
                    console.error('Error al reproducir el audio:', error);
                });
                audio.addEventListener('ended', () => {
                    console.log('Audio finalizado. Desactivando PTT en 1 segundo...');
                    setTimeout(() => {
                        desactivarPTT();
                    }, 1000);
                });
            } catch (error) {
                console.error('Error al decodificar o reproducir el audio:', error);
            }
        }, 1000);
    });

    socket.on('audio_start', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha comenzado a transmitir audio. Evitando activación de PTT...`);
            isTransmittingAudio = true;
        }
    });

    socket.on('audio_end', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha terminado la transmisión de audio.`);
            isTransmittingAudio = false;
        }
    });

    function activarPTT() {
        if (!isTransmittingAudio) {
            console.log("Activando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ptt_state: true })
            })
            .then(response => {
                if (!response.ok) { throw new Error('Error en la solicitud de activación del PTT'); }
                return response.json();
            })
            .then(data => { console.log('Respuesta del servidor:', data); })
            .catch((error) => { console.error('Error al activar el PTT:', error); });
        } else {
            console.log('PTT no activado ya que el usuario está transmitiendo audio.');
        }
    }

    function desactivarPTT() {
        if (!isTransmittingAudio) {
            console.log("Desactivando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ptt_state: false })
            })
            .then(response => {
                if (!response.ok) { throw new Error('Error en la solicitud de desactivación del PTT'); }
                return response.json();
            })
            .then(data => { console.log('Respuesta del servidor:', data); })
            .catch((error) => { console.error('Error al desactivar el PTT:', error); });
        } else {
            console.log('PTT no desactivado ya que el usuario está transmitiendo audio.');
        }
    }
});
