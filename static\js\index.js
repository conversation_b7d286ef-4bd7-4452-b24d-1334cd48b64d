// /static/js/index.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;
    const nodeName = window.nodeName; // Nueva variable para el nombre del nodo

    console.log(`Conectado como ${username} al nodo ${nodeId}`);  // Log inicial

    const socket = io.connect('https://patagoniaservers.com.ar:5000/node', {
        query: {
            node_id: String(nodeId),
            username: username
        }
    });

    // Referencia al botón de transmisión de audio
    const transmitAudioButton = document.getElementById('transmitAudio');

    socket.on('connect', () => {
        console.log(`Conexión exitosa como ${username} al nodo ${nodeId}`);
        // Mostrar el nombre del nodo recibido desde el servidor en la interfaz
        const nodeInfo = document.getElementById('node-info');
        if (nodeInfo) {
            nodeInfo.textContent = `Conectado al nodo ${nodeName}`;
        }
    });

    // Resto del código permanece igual...
    socket.on('update_users', (users) => {
        console.log('Usuarios conectados:', users);
        const uniqueUsers = [...new Set(users)];
        const usersList = document.getElementById('users_list');
        if (usersList) {
            usersList.innerHTML = '';
            uniqueUsers.forEach(user => {
                const userItem = document.createElement('li');
                userItem.textContent = user;
                usersList.appendChild(userItem);
            });
        }
    });

    const volumeMeter = document.getElementById('volume_meter');
    socket.on('audio_level', (level) => {
        if (volumeMeter) {
            volumeMeter.style.width = `${level}%`;
            volumeMeter.style.backgroundColor = level >= 50 ? 'red' : 'green';
        }
        console.log('Nivel de audio:', level);
    });

    socket.on('audio_event', (data) => {
        console.log('Evento de audio recibido:', data);
        const eventsList = document.getElementById('events_list');
        if (eventsList) {
            const newEvent = document.createElement('li');
            newEvent.textContent = `Audio recibido de ${data.user}`;
            eventsList.appendChild(newEvent);
        }
    });

    socket.on('global_audio_start', (data) => {
        if (data.node_id === String(nodeId)) {
            console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio iniciado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/rojo.png';
            }
        }
    });

    socket.on('global_audio_end', (data) => {
        if (data.node_id === String(nodeId)) {
            console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio finalizado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/verde.png';
            }
        }
    });

    socket.emit('get_connected_users', { node_id: String(nodeId) });
});
