import os
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk

class FileTreeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Generador de Árbol de Directorios y Contenido de Archivos")
        
        self.tree = ttk.Treeview(root, columns=('select'), show='tree')
        self.tree.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)

        self.tree.tag_configure('unchecked', background='white')
        self.tree.tag_configure('checked', background='lightgray')

        self.select_button = tk.Button(root, text="Seleccionar directorio", command=self.select_directory)
        self.select_button.pack(pady=10)

        self.generate_button = tk.Button(root, text="Generar y Guardar", command=self.generate_and_save)
        self.generate_button.pack(pady=10)

        self.tree.bind("<Button-1>", self.toggle_check)

        self.directory_path = filedialog.askdirectory(title="Seleccionar directorio inicial")
        if self.directory_path:
            self.populate_tree(self.directory_path)

    def select_directory(self):
        self.directory_path = filedialog.askdirectory(title="Seleccionar directorio")
        if self.directory_path:
            for item in self.tree.get_children():
                self.tree.delete(item)
            self.populate_tree(self.directory_path)

    def populate_tree(self, path, parent=""):
        for item in os.listdir(path):
            abspath = os.path.join(path, item)
            isdir = os.path.isdir(abspath)
            oid = self.tree.insert(parent, 'end', text=item, values=(False,), tags=('unchecked',))
            if isdir:
                self.populate_tree(abspath, oid)

    def toggle_check(self, event):
        item = self.tree.identify_row(event.y)
        if item:
            tags = list(self.tree.item(item, "tags"))
            if 'checked' in tags:
                tags.remove('checked')
                tags.append('unchecked')
                self.tree.item(item, tags=tags, values=(False,))
            else:
                tags.remove('unchecked')
                tags.append('checked')
                self.tree.item(item, tags=tags, values=(True,))

    def generate_and_save(self):
        if not self.directory_path:
            messagebox.showerror("Error", "Primero selecciona un directorio.")
            return

        selected_files = self.get_selected_files(self.directory_path)
        if not selected_files:
            messagebox.showerror("Error", "No se han seleccionado archivos o carpetas.")
            return

        contenido_archivos = {}
        for archivo in selected_files:
            if os.path.isfile(archivo):
                try:
                    with open(archivo, 'r', encoding='utf-8') as file:
                        contenido_archivos[archivo] = file.read()
                except UnicodeDecodeError:
                    messagebox.showwarning("Advertencia", f"No se pudo leer el archivo {archivo} debido a problemas de codificación.")
                    contenido_archivos[archivo] = "[No se pudo leer el archivo debido a problemas de codificación]"

        arbol_directorios = self.generar_arbol_directorios(self.directory_path, selected_files)
        arbol_contenido = self.generar_arbol_archivos(contenido_archivos)
        arbol_completo = arbol_directorios + "\n\n" + arbol_contenido
        ruta_salida = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Archivos de texto", "*.txt")], title="Guardar como")
        if ruta_salida:
            self.guardar_arbol_contenido(arbol_completo, ruta_salida)

    def get_selected_files(self, path, parent=""):
        selected_files = []
        for item in self.tree.get_children(parent):
            item_text = self.tree.item(item, 'text')
            item_path = os.path.join(path, item_text)
            if self.tree.item(item, 'values')[0] == 'True':
                selected_files.append(item_path)
            if os.path.isdir(item_path):
                selected_files.extend(self.get_selected_files(item_path, item))
        return selected_files

    def generar_arbol_directorios(self, path, selected_files, indent=""):
        arbol = ""
        items = sorted(os.listdir(path))
        for i, item in enumerate(items):
            abspath = os.path.join(path, item)
            if abspath in selected_files or any(sf.startswith(abspath) for sf in selected_files):
                if i == len(items) - 1:
                    arbol += indent + "└── " + item + "\n"
                    if os.path.isdir(abspath):
                        arbol += self.generar_arbol_directorios(abspath, selected_files, indent + "    ")
                else:
                    arbol += indent + "├── " + item + "\n"
                    if os.path.isdir(abspath):
                        arbol += self.generar_arbol_directorios(abspath, selected_files, indent + "│   ")
        return arbol

    def generar_arbol_archivos(self, contenido_archivos):
        arbol = ""
        for nombre_archivo, contenido in contenido_archivos.items():
            arbol += f"\n\nArchivo: {nombre_archivo}\nContenido:\n{contenido}"
        return arbol

    def guardar_arbol_contenido(self, arbol, ruta_salida):
        try:
            with open(ruta_salida, 'w', encoding='utf-8') as archivo:
                archivo.write(arbol)
            messagebox.showinfo("Éxito", f"Árbol de directorio y contenido guardado en {ruta_salida}")
        except OSError:
            messagebox.showerror("Error", "No se pudo guardar el archivo.")

if __name__ == "__main__":
    root = tk.Tk()
    app = FileTreeApp(root)
    root.mainloop()
