<!-- /app/templates/register.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}
{% block title %}Registrar Usuario{% endblock %}

{% block content %}
<div class="container my-4">
  <h1>Registrar Usuario</h1>
  <form method="POST" action="">
    {{ form.hidden_tag() }}
    <div class="form-group">
      {{ render_field(form.username, class="form-control") }}
    </div>
    <!-- Se separan los nombres -->
    <div class="form-group">
      {{ render_field(form.first_name, class="form-control", placeholder="Nombre") }}
    </div>
    <div class="form-group">
      {{ render_field(form.last_name, class="form-control", placeholder="Apellido") }}
    </div>
    <!-- Nuevos campos -->
    <div class="form-group">
      {{ render_field(form.dni, class="form-control", placeholder="DNI") }}
    </div>
    <div class="form-group">
      {{ render_field(form.ciudad, class="form-control", placeholder="Ciudad") }}
    </div>
    <div class="form-group">
      {{ render_field(form.estado_civil, class="form-control", placeholder="Estado Civil") }}
    </div>
    <div class="form-group">
      {{ render_field(form.phone_number, class="form-control", placeholder="Teléfono") }}
    </div>
    <div class="form-group">
      {{ render_field(form.email, class="form-control", placeholder="Correo Electrónico") }}
    </div>
    <div class="form-group">
      {{ render_field(form.password, class="form-control", placeholder="Contraseña") }}
    </div>
    <div class="form-group">
      {{ render_field(form.confirm_password, class="form-control", placeholder="Confirmar Contraseña") }}
    </div>
    <div class="form-group">
      {{ render_field(form.role, class="form-control", id="role") }}
    </div>
    <div class="form-group">
      {{ render_field(form.church, class="form-control") }}
    </div>

    <!-- Campos específicos para pastores -->
    <div id="pastor-fields" style="display: none;">
      <h3>Información del Pastor</h3>
      <div class="form-group">
        {{ render_field(form.grado, class="form-control", placeholder="Grado") }}
      </div>
      <div class="form-group">
        {{ render_field(form.address, class="form-control", placeholder="Dirección (Casa Pastoral)") }}
      </div>
      <div class="form-group">
        {{ render_field(form.latitude, class="form-control", placeholder="Latitud") }}
      </div>
      <div class="form-group">
        {{ render_field(form.longitude, class="form-control", placeholder="Longitud") }}
      </div>
      <h3>Roles de Pastor</h3>
      <div class="form-group">
        <label for="pastor_roles">Seleccione Roles</label>
        {% for subfield in form.pastor_roles %}
          <div class="form-check">
            {{ subfield(class="form-check-input") }}
            {{ subfield.label(class="form-check-label") }}
          </div>
        {% endfor %}
      </div>
    </div>

    {{ form.submit(class="btn btn-primary") }}
  </form>
</div>

{% block scripts %}
<script>
    // Mostrar/ocultar campos para pastores según el rol seleccionado
    const roleSelect = document.getElementById('role');
    const pastorFields = document.getElementById('pastor-fields');
    function togglePastorFields() {
      if (roleSelect.value === 'pastorado') {
        pastorFields.style.display = 'block';
      } else {
        pastorFields.style.display = 'none';
      }
    }
    roleSelect.addEventListener('change', togglePastorFields);
    togglePastorFields();
</script>
{% endblock %}
{% endblock %}
