#!/usr/bin/env python3
# --- Archivo: recover_database.py ---
# Script para recuperar la base de datos perdida

import os
import subprocess
import sqlite3
import glob
from datetime import datetime

def check_system_info():
    """Verificar información del sistema."""
    print("🔍 Verificando información del sistema...")
    
    # Verificar sistema de archivos
    result = subprocess.run(['df', '-T', '/home'], capture_output=True, text=True)
    print("📊 Sistema de archivos:")
    print(result.stdout)
    
    # Verificar particiones
    result = subprocess.run(['lsblk'], capture_output=True, text=True)
    print("💾 Particiones:")
    print(result.stdout)
    
    # Verificar espacio libre
    result = subprocess.run(['df', '-h', '/home'], capture_output=True, text=True)
    print("📊 Espacio disponible:")
    print(result.stdout)

def search_existing_backups():
    """Buscar backups existentes en el sistema."""
    print("\n🔍 Buscando backups existentes...")
    
    # Buscar en directorios comunes
    search_paths = [
        '/home/<USER>/',
        '/home/<USER>/instance/',
        '/tmp/',
        '/var/tmp/',
        '/home/<USER>/Relevamiento/',
        '/home/<USER>/backup/',
        '/backup/',
        '/var/backup/'
    ]
    
    found_files = []
    
    for path_pattern in search_paths:
        try:
            # Buscar archivos .db
            db_files = glob.glob(f"{path_pattern}**/*.db", recursive=True)
            db_files.extend(glob.glob(f"{path_pattern}**/app.db*", recursive=True))
            
            for db_file in db_files:
                if os.path.exists(db_file):
                    size = os.path.getsize(db_file)
                    mtime = datetime.fromtimestamp(os.path.getmtime(db_file))
                    found_files.append((db_file, size, mtime))
        except:
            continue
    
    if found_files:
        print("📁 Archivos .db encontrados:")
        for db_file, size, mtime in sorted(found_files, key=lambda x: x[2], reverse=True):
            print(f"  📄 {db_file}")
            print(f"      Tamaño: {size:,} bytes")
            print(f"      Modificado: {mtime}")
            
            # Verificar si es una base de datos SQLite válida
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [table[0] for table in cursor.fetchall()]
                
                if 'point' in tables:
                    cursor.execute("SELECT COUNT(*) FROM point;")
                    point_count = cursor.fetchone()[0]
                    print(f"      ✅ Base de datos válida con {point_count} puntos")
                else:
                    print(f"      ⚠️  Base de datos sin tabla 'point'")
                
                conn.close()
            except:
                print(f"      ❌ No es una base de datos SQLite válida")
            
            print()
    else:
        print("❌ No se encontraron archivos .db")
    
    return found_files

def check_journal_files():
    """Verificar archivos de journal de SQLite."""
    print("🔍 Buscando archivos de journal SQLite...")
    
    journal_patterns = [
        '/home/<USER>/instance/app.db-journal',
        '/home/<USER>/instance/app.db-wal',
        '/home/<USER>/instance/app.db-shm',
        '/home/<USER>/app.db-journal',
        '/home/<USER>/app.db-wal',
        '/home/<USER>/app.db-shm'
    ]
    
    found_journals = []
    
    for pattern in journal_patterns:
        if os.path.exists(pattern):
            size = os.path.getsize(pattern)
            mtime = datetime.fromtimestamp(os.path.getmtime(pattern))
            found_journals.append((pattern, size, mtime))
            print(f"  📄 {pattern} ({size:,} bytes, {mtime})")
    
    if not found_journals:
        print("❌ No se encontraron archivos de journal")
    
    return found_journals

def install_recovery_tools():
    """Instalar herramientas de recuperación."""
    print("🔧 Instalando herramientas de recuperación...")
    
    tools = [
        ('testdisk', 'TestDisk y PhotoRec'),
        ('extundelete', 'Extundelete'),
        ('foremost', 'Foremost'),
        ('scalpel', 'Scalpel')
    ]
    
    for tool, description in tools:
        print(f"\n📦 Instalando {description}...")
        try:
            result = subprocess.run(['sudo', 'apt', 'install', '-y', tool], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ {description} instalado")
            else:
                print(f"  ❌ Error instalando {description}")
                print(f"      {result.stderr}")
        except Exception as e:
            print(f"  ❌ Error: {e}")

def create_recovery_script():
    """Crear script de recuperación con PhotoRec."""
    print("\n📝 Creando script de recuperación...")
    
    script_content = '''#!/bin/bash
# Script de recuperación de base de datos

echo "🔍 Recuperación de Base de Datos SQLite"
echo "======================================"

# Crear directorio de recuperación
mkdir -p ~/recovery_db
cd ~/recovery_db

echo "📁 Directorio de recuperación: $(pwd)"

# Obtener partición de /home
PARTITION=$(df /home | tail -1 | awk '{print $1}')
echo "💾 Partición detectada: $PARTITION"

echo ""
echo "⚠️  IMPORTANTE:"
echo "   1. Este proceso puede tomar tiempo"
echo "   2. No uses el disco durante la recuperación"
echo "   3. Los archivos se guardarán en ~/recovery_db"
echo ""

read -p "¿Continuar con PhotoRec? (s/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Ss]$ ]]; then
    echo "🚀 Iniciando PhotoRec..."
    sudo photorec /log /d ~/recovery_db $PARTITION
    
    echo ""
    echo "🔍 Buscando archivos SQLite recuperados..."
    find ~/recovery_db -name "*.db" -o -name "*.sqlite*" | while read file; do
        echo "📄 Encontrado: $file"
        
        # Verificar si es una base de datos válida
        if sqlite3 "$file" "SELECT name FROM sqlite_master WHERE type='table';" 2>/dev/null | grep -q "point"; then
            echo "  ✅ Base de datos con tabla 'point' encontrada!"
            
            # Contar puntos
            POINTS=$(sqlite3 "$file" "SELECT COUNT(*) FROM point;" 2>/dev/null)
            echo "  📊 Puntos encontrados: $POINTS"
            
            # Copiar a ubicación segura
            cp "$file" ~/recovery_db/recovered_app_$(date +%Y%m%d_%H%M%S).db
            echo "  💾 Copiado como: recovered_app_$(date +%Y%m%d_%H%M%S).db"
        fi
    done
    
    echo ""
    echo "✅ Recuperación completada"
    echo "📁 Revisa los archivos en: ~/recovery_db"
else
    echo "❌ Recuperación cancelada"
fi
'''
    
    with open('recovery_script.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('recovery_script.sh', 0o755)
    print("✅ Script creado: recovery_script.sh")

def create_extundelete_script():
    """Crear script para extundelete."""
    print("\n📝 Creando script de extundelete...")
    
    script_content = '''#!/bin/bash
# Script de recuperación con extundelete

echo "🔍 Recuperación con Extundelete"
echo "==============================="

# Obtener partición de /home
PARTITION=$(df /home | tail -1 | awk '{print $1}')
echo "💾 Partición detectada: $PARTITION"

# Crear directorio de recuperación
mkdir -p ~/recovery_extundelete
cd ~/recovery_extundelete

echo "📁 Directorio de recuperación: $(pwd)"

echo ""
echo "⚠️  IMPORTANTE:"
echo "   1. Desmonta la partición si es posible"
echo "   2. Este proceso puede tomar tiempo"
echo "   3. Funciona mejor en ext3/ext4"
echo ""

read -p "¿Continuar con extundelete? (s/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Ss]$ ]]; then
    echo "🚀 Buscando archivos eliminados..."
    
    # Buscar archivos app.db eliminados
    sudo extundelete $PARTITION --restore-file home/Relevamiento/instance/app.db
    
    # Buscar todos los archivos .db eliminados recientemente
    sudo extundelete $PARTITION --restore-all --after $(date -d "yesterday" +%s)
    
    echo ""
    echo "🔍 Buscando archivos recuperados..."
    find . -name "*.db" | while read file; do
        echo "📄 Encontrado: $file"
        
        if sqlite3 "$file" "SELECT name FROM sqlite_master WHERE type='table';" 2>/dev/null | grep -q "point"; then
            echo "  ✅ Base de datos válida encontrada!"
            POINTS=$(sqlite3 "$file" "SELECT COUNT(*) FROM point;" 2>/dev/null)
            echo "  📊 Puntos: $POINTS"
        fi
    done
    
    echo "✅ Recuperación con extundelete completada"
else
    echo "❌ Recuperación cancelada"
fi
'''
    
    with open('extundelete_script.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('extundelete_script.sh', 0o755)
    print("✅ Script creado: extundelete_script.sh")

def main():
    """Función principal."""
    print("🔍 Recuperador de Base de Datos")
    print("=" * 40)
    
    print("⚠️  IMPORTANTE: Detén cualquier escritura en el disco para maximizar las posibilidades de recuperación")
    print()
    
    # Verificar información del sistema
    check_system_info()
    
    # Buscar backups existentes
    found_files = search_existing_backups()
    
    # Verificar archivos de journal
    journal_files = check_journal_files()
    
    if found_files:
        print("✅ Se encontraron archivos de base de datos existentes")
        print("💡 Revisa los archivos listados arriba antes de proceder con recuperación")
    
    print("\n🔧 Opciones de recuperación:")
    print("1. 📦 Instalar herramientas de recuperación")
    print("2. 📝 Crear script de PhotoRec (recomendado)")
    print("3. 📝 Crear script de Extundelete")
    print("4. 🔍 Solo buscar archivos existentes")
    print("5. ❌ Salir")
    
    choice = input("\n¿Qué opción prefieres? (1-5): ").strip()
    
    if choice == '1':
        install_recovery_tools()
        create_recovery_script()
        create_extundelete_script()
        print("\n🚀 Herramientas instaladas. Ejecuta:")
        print("   ./recovery_script.sh")
        print("   ./extundelete_script.sh")
    
    elif choice == '2':
        create_recovery_script()
        print("\n🚀 Ejecuta: ./recovery_script.sh")
    
    elif choice == '3':
        create_extundelete_script()
        print("\n🚀 Ejecuta: ./extundelete_script.sh")
    
    elif choice == '4':
        print("✅ Búsqueda completada")
    
    else:
        print("❌ Saliendo")

if __name__ == "__main__":
    main()
