# admin.py
from flask import Blueprint, render_template, redirect, url_for, request, flash, jsonify, current_app
from flask_login import login_required, current_user
from extensions import db, socketio  # Importar socketio
from models import User, Node
from werkzeug.security import generate_password_hash
import serial  # Not used in the provided code, but kept as it's in your original
# from flask_socketio import emit  <- Ya no se necesita aquí, se usa en app.py
from globals import connected_users

admin_bp = Blueprint('admin', __name__)

# Opciones predefinidas
ROLES = ['admin', 'user_manager', 'password_manager', 'node_manager', 'standard_user']
ORGANISMOS = ['Policia', 'Agencia', 'Salud', 'Educacion', 'Ipross']

@admin_bp.route('/users')
@login_required
def list_users():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    users = User.query.all()
    print(users)  # Agrega esta línea para verificar los datos de los usuarios
    return render_template('admin/users.html', users=users)

@admin_bp.route('/users/create', methods=['GET', 'POST'])
@login_required
def create_user():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))

    nodes = Node.query.all()
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        badge_number = request.form.get('badge_number')
        organization = request.form.get('organization')
        role = request.form.get('role')
        node_access = request.form.getlist('node_access')
        regional_units = request.form.getlist('regional_units')
        has_police_access = 'police_access' in request.form

        hashed_password = generate_password_hash(password, method='pbkdf2:sha256')
        new_user = User(
            username=username,
            password=hashed_password,
            first_name=first_name,
            last_name=last_name,
            badge_number=badge_number,
            organization=organization,
            role=role,
            node_access=",".join(node_access),
            regional_units=",".join(regional_units),
            has_police_access=has_police_access
        )
        db.session.add(new_user)
        db.session.commit()
        flash('User created successfully.')
        return redirect(url_for('admin.list_users'))
    return render_template('admin/create_user.html', roles=ROLES, organismos=ORGANISMOS, nodes=nodes)

@admin_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))

    user = User.query.get_or_404(user_id)
    nodes = Node.query.all()
    if request.method == 'POST':
        user.username = request.form.get('username')
        user.first_name = request.form.get('first_name')
        user.last_name = request.form.get('last_name')
        user.badge_number = request.form.get('badge_number')
        user.organization = request.form.get('organization')
        user.role = request.form.get('role')
        user.node_access = ",".join(request.form.getlist('node_access'))
        user.regional_units = ",".join(request.form.getlist('regional_units'))
        user.has_police_access = 'police_access' in request.form

        password = request.form.get('password')
        if password:
            user.password = generate_password_hash(password, method='pbkdf2:sha256')

        db.session.commit()
        flash('User updated successfully.')
        return redirect(url_for('admin.list_users'))
    return render_template('admin/edit_user.html', user=user, roles=ROLES, organismos=ORGANISMOS, nodes=nodes)

# editar y crear nodos
@admin_bp.route('/nodes')
@login_required
def list_nodes():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    nodes = Node.query.all()  # Get all nodes
    return render_template('admin/nodes.html', nodes=nodes)

@admin_bp.route('/nodes/create', methods=['GET', 'POST'])
@login_required
def create_node():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))

    if request.method == 'POST':
        name = request.form.get('name')
        parent_id = request.form.get('parent_id')
        new_node = Node(name=name, parent_id=parent_id)
        db.session.add(new_node)
        db.session.commit()
        flash('Node created successfully.')
        return redirect(url_for('admin.list_nodes'))

    parent_nodes = Node.query.all()
    return render_template('admin/create_node.html', parent_nodes=parent_nodes)

@admin_bp.route('/nodes/<int:node_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_node(node_id):
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))

    node = Node.query.get_or_404(node_id)
    if request.method == 'POST':
        node.name = request.form.get('name')
        node.parent_id = request.form.get('parent_id')
        db.session.commit()
        flash('Node updated successfully.')
        return redirect(url_for('admin.list_nodes'))

    parent_nodes = Node.query.all()
    return render_template('admin/edit_node.html', node=node, parent_nodes=parent_nodes)

@admin_bp.route('/connected_users')
@login_required
def view_connected_users():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))

    # Simplemente renderiza la plantilla. La lógica de obtener los usuarios
    # se maneja ahora completamente a través de SocketIO en connected_users.html
    return render_template('admin/connected_users.html')

@admin_bp.route('/disconnect_user/<node_id>/<username>', methods=['POST'])
@login_required
def disconnect_user(node_id, username):
    try:
        if current_user.role != 'admin':
            return jsonify({'error': 'Access denied.'}), 403

        # Recopilar todas las session IDs que correspondan al usuario en ese nodo
        sids_to_disconnect = [sid for sid, info in connected_users.items()
                               if info['username'] == username and info['node_id'] == str(node_id)]
        
        if sids_to_disconnect:
            for sid in sids_to_disconnect:
                try:
                    socketio.disconnect(sid, namespace='/node')
                except Exception as e:
                    current_app.logger.error("Error disconnecting user from /node (sid %s): %s", sid, e)
                try:
                    socketio.disconnect(sid, namespace='/private')
                except Exception as e:
                    current_app.logger.error("Error disconnecting user from /private (sid %s): %s", sid, e)
                # Emitir un evento para forzar el logout en el cliente
                socketio.emit('force_logout', {'message': 'Has sido expulsado y deslogueado del sistema.'}, room=sid)
                # Eliminar la sesión del usuario del diccionario global
                if sid in connected_users:
                    del connected_users[sid]
            # Actualizar la lista de usuarios conectados en el nodo
            users_in_node = [user['username'] for user in connected_users.values() if user['node_id'] == str(node_id)]
            socketio.emit('update_users', users_in_node, room=node_id)
            return jsonify({'message': f'Usuario {username} ha sido expulsado del nodo {node_id}.'})
        else:
            return jsonify({'error': 'Usuario no encontrado en el nodo.'}), 404
    except Exception as ex:
        current_app.logger.error("Error in disconnect_user: %s", ex)
        return jsonify({'error': 'Internal server error: ' + str(ex)}), 500
