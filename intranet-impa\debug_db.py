#debug_db.py
from app import create_app, db
from app.models import <PERSON><PERSON>, <PERSON>, Pastor, Member, MemberFunction

app = create_app()

with app.app_context():
    print("==== Usuarios ====")
    users = User.query.all()
    for u in users:
        print(u)

    print("\n==== Iglesias ====")
    churches = Church.query.all()
    for c in churches:
        print(c)

    print("\n==== Pastores ====")
    pastores = Pastor.query.all()
    for p in pastores:
        print(p)

    print("\n==== Miembros ====")
    members = Member.query.all()
    for m in members:
        print(m)
        if m.functions:
            funcs = ", ".join([f.name for f in m.functions])
            print("  Funciones:", funcs)
