#!/usr/bin/env python3
# --- Archivo: migrate_permissions.py ---
# Script para migrar la base de datos existente al sistema de permisos

import os
import sys
import sqlite3

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def backup_database():
    """Crear backup de la base de datos antes de migrar."""
    
    if not os.path.exists('app.db'):
        print("❌ No existe app.db para hacer backup")
        return False
    
    import shutil
    from datetime import datetime
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f'app_backup_{timestamp}.db'
    
    try:
        shutil.copy2('app.db', backup_name)
        print(f"✅ Backup creado: {backup_name}")
        return True
    except Exception as e:
        print(f"❌ Error creando backup: {e}")
        return False

def check_table_exists(cursor, table_name):
    """Verificar si una tabla existe."""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?;", (table_name,))
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """Verificar si una columna existe en una tabla."""
    cursor.execute(f"PRAGMA table_info({table_name});")
    columns = cursor.fetchall()
    return any(col[1] == column_name for col in columns)

def migrate_user_table(cursor):
    """Migrar la tabla user para agregar campos de permisos."""
    
    print("👤 Migrando tabla user...")
    
    if not check_table_exists(cursor, 'user'):
        print("❌ Tabla user no existe. Usa init_db.py para crear la estructura inicial.")
        return False
    
    # Verificar qué columnas faltan
    columns_to_add = [
        ('role', "VARCHAR(20) DEFAULT 'operador'"),
        ('is_active', "BOOLEAN DEFAULT TRUE"),
        ('created_by', "INTEGER"),
        ('created_at', "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
        ('updated_at', "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    ]
    
    for column_name, column_def in columns_to_add:
        if not check_column_exists(cursor, 'user', column_name):
            try:
                cursor.execute(f"ALTER TABLE user ADD COLUMN {column_name} {column_def};")
                print(f"  ✅ Agregada columna: {column_name}")
            except Exception as e:
                print(f"  ❌ Error agregando columna {column_name}: {e}")
                return False
        else:
            print(f"  ℹ️  Columna {column_name} ya existe")
    
    # Actualizar usuarios existentes para que sean administradores
    try:
        cursor.execute("UPDATE user SET role = 'administrador' WHERE role IS NULL OR role = '';")
        cursor.execute("UPDATE user SET is_active = TRUE WHERE is_active IS NULL;")
        cursor.execute("UPDATE user SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL;")
        cursor.execute("UPDATE user SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;")
        print("  ✅ Usuarios existentes actualizados como administradores")
    except Exception as e:
        print(f"  ❌ Error actualizando usuarios existentes: {e}")
        return False
    
    return True

def create_permission_tables(cursor):
    """Crear las tablas de permisos."""
    
    print("🔐 Creando tablas de permisos...")
    
    # Tabla de permisos de ciudades
    if not check_table_exists(cursor, 'user_city_permissions'):
        try:
            cursor.execute("""
                CREATE TABLE user_city_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    city VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, city)
                );
            """)
            print("  ✅ Tabla user_city_permissions creada")
        except Exception as e:
            print(f"  ❌ Error creando user_city_permissions: {e}")
            return False
    else:
        print("  ℹ️  Tabla user_city_permissions ya existe")
    
    # Tabla de permisos de fuentes
    if not check_table_exists(cursor, 'user_source_permissions'):
        try:
            cursor.execute("""
                CREATE TABLE user_source_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    source VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, source)
                );
            """)
            print("  ✅ Tabla user_source_permissions creada")
        except Exception as e:
            print(f"  ❌ Error creando user_source_permissions: {e}")
            return False
    else:
        print("  ℹ️  Tabla user_source_permissions ya existe")
    
    # Tabla de permisos específicos
    if not check_table_exists(cursor, 'user_permissions'):
        try:
            cursor.execute("""
                CREATE TABLE user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    permission_type VARCHAR(50) NOT NULL,
                    permission_value BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, permission_type)
                );
            """)
            print("  ✅ Tabla user_permissions creada")
        except Exception as e:
            print(f"  ❌ Error creando user_permissions: {e}")
            return False
    else:
        print("  ℹ️  Tabla user_permissions ya existe")
    
    return True

def create_indexes(cursor):
    """Crear índices para optimizar consultas."""
    
    print("📊 Creando índices...")
    
    indexes = [
        ("idx_user_city_permissions_user_id", "user_city_permissions", "user_id"),
        ("idx_user_city_permissions_city", "user_city_permissions", "city"),
        ("idx_user_source_permissions_user_id", "user_source_permissions", "user_id"),
        ("idx_user_source_permissions_source", "user_source_permissions", "source"),
        ("idx_user_permissions_user_id", "user_permissions", "user_id"),
        ("idx_user_permissions_type", "user_permissions", "permission_type")
    ]
    
    for index_name, table_name, column_name in indexes:
        try:
            cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name});")
            print(f"  ✅ Índice {index_name} creado")
        except Exception as e:
            print(f"  ❌ Error creando índice {index_name}: {e}")
    
    return True

def migrate_database():
    """Ejecutar la migración completa."""
    
    if not os.path.exists('app.db'):
        print("❌ No existe app.db. Usa init_db.py para crear la estructura inicial.")
        return False
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        print("🔄 Iniciando migración...")
        
        # Migrar tabla user
        if not migrate_user_table(cursor):
            return False
        
        # Crear tablas de permisos
        if not create_permission_tables(cursor):
            return False
        
        # Crear índices
        if not create_indexes(cursor):
            return False
        
        # Confirmar cambios
        conn.commit()
        conn.close()
        
        print("✅ Migración completada exitosamente")
        return True
        
    except Exception as e:
        print(f"❌ Error durante la migración: {e}")
        return False

def main():
    """Función principal."""
    print("🔄 Migrador de Base de Datos - Sistema de Permisos")
    print("=" * 50)
    
    # Crear backup
    if not backup_database():
        response = input("❓ ¿Continuar sin backup? (s/n): ").strip().lower()
        if response not in ['s', 'si', 'y', 'yes']:
            print("❌ Migración cancelada")
            sys.exit(0)
    
    # Ejecutar migración
    if migrate_database():
        print("\n✅ Migración completada correctamente")
        print("\n🔍 Verifica el resultado con:")
        print("   python3 check_db.py")
    else:
        print("\n❌ Error en la migración")
        sys.exit(1)

if __name__ == "__main__":
    main()
