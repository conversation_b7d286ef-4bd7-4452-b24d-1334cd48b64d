<!-- /app/templates/edit_church.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}
{% include '_search_form.html' %}
{% block title %}Editar Iglesia{% endblock %}

{% block content %}
    <h1>Editar Iglesia: {{ church.name }}</h1>
    <form method="POST" action="">
        {{ form.hidden_tag() }}
        <div class="form-group">
            {{ render_field(form.name, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.address, class="form-control") }}
        </div>
         {# --- AÑADIDOS CAMPOS CIUDAD Y PROVINCIA --- #}
        <div class="form-group">
            {{ render_field(form.city, class="form-control", placeholder="Ciudad") }}
        </div>
        <div class="form-group">
            {{ render_field(form.province, class="form-control", placeholder="Provincia") }}
        </div>
        {# --- FIN AÑADIDOS --- #}
        <div class="form-group">
            {{ render_field(form.district, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.pastor, class="form-control") }} {# Pastor Gobernante #}
        </div>

         {# --- Campos Latitud/Longitud y Mapa --- #}
        <div class="form-group" style="display: none;"> {# Ocultar labels #}
            {{ form.latitude.label }}
            {{ form.latitude(class="form-control", type="hidden") }}
            {{ form.longitude.label }}
            {{ form.longitude(class="form-control", type="hidden") }}
        </div>
        <!-- Botón para mostrar/ocultar el mapa -->
        <button type="button" class="btn btn-info mb-3" id="toggle-map">Mostrar/Ocultar Mapa para Ubicación</button>
        <!-- Contenedor del mapa (inicialmente oculto) -->
        <div id="map" style="height: 400px; display:none;"></div>
         {# --- Fin Campos Mapa --- #}

        <div class="d-flex justify-content-between mt-3">
            {{ form.submit(class="btn btn-primary") }}
            <!-- Botón Inventario -->
            <a href="{{ url_for('routes.inventory', church_id=church.id) }}" class="btn btn-warning">Ver Inventario</a>
            <a href="{{ url_for('routes.list_churches') }}" class="btn btn-secondary">Cancelar</a> {# Añadido botón Cancelar #}
        </div>
    </form>
{% endblock %}

{% block scripts %}
    {{ super() }} {# Importante heredar scripts de base.html #}
    <script>
    // Inicialización del mapa
    var map = L.map('map');
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Capa para los elementos dibujados
    var drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);

    // Configuración de Leaflet.draw
    var drawControl = new L.Control.Draw({
        draw: {
            polygon: false, polyline: false, rectangle: false, circle: false, circlemarker: false, marker: true
        },
        edit: { featureGroup: drawnItems, remove: false } // Habilitar edición si hay marcador
    });
    map.addControl(drawControl);

    // Evento para agregar marcador manualmente
    map.on(L.Draw.Event.CREATED, function (e) {
        var layer = e.layer;
        var lat = layer.getLatLng().lat;
        var lng = layer.getLatLng().lng;
        document.getElementById('latitude').value = lat.toFixed(6);
        document.getElementById('longitude').value = lng.toFixed(6);
        drawnItems.clearLayers();
        drawnItems.addLayer(layer);
    });

     // Evento para editar marcador existente
    map.on(L.Draw.Event.EDITED, function (e) {
        e.layers.eachLayer(function (layer) {
            if (layer instanceof L.Marker) {
                var lat = layer.getLatLng().lat;
                var lng = layer.getLatLng().lng;
                document.getElementById('latitude').value = lat.toFixed(6);
                document.getElementById('longitude').value = lng.toFixed(6);
            }
        });
    });

    // Mostrar marcador existente al cargar la página
    var initialLat = {{ church.latitude or 'null' }}; // Usar 'null' si no existe
    var initialLng = {{ church.longitude or 'null' }};
    var existingMarker = null;

    if (initialLat !== null && initialLng !== null) {
        existingMarker = L.marker([initialLat, initialLng], {
            // Puedes añadir el icono personalizado aquí si quieres
            icon: L.icon({
                 iconUrl: "{{ url_for('static', filename='img/edif.png') }}", // Asegúrate que esta imagen exista
                 iconSize: [38, 38], iconAnchor: [19, 37], popupAnchor: [0, -37]
            }),
            draggable: true // Permitir arrastrar el marcador existente
        }).addTo(map);
        drawnItems.addLayer(existingMarker);

         // Actualizar campos si se arrastra el marcador
        existingMarker.on('dragend', function(event){
            var marker = event.target;
            var position = marker.getLatLng();
            document.getElementById('latitude').value = position.lat.toFixed(6);
            document.getElementById('longitude').value = position.lng.toFixed(6);
        });
    }

    // Botón para mostrar/ocultar el mapa
    document.getElementById('toggle-map').addEventListener('click', function() {
        var mapDiv = document.getElementById('map');
        if (mapDiv.style.display === 'none') {
            mapDiv.style.display = 'block';
            map.invalidateSize();
            // Centrar en marcador existente o en vista por defecto
            if (existingMarker) {
                map.setView(existingMarker.getLatLng(), 15);
            } else {
                map.setView([-40.8110, -62.9972], 13); // Viedma/Patagones
            }
        } else {
            mapDiv.style.display = 'none';
        }
    });

    // --- Buscar dirección ---
    var addressInput = document.getElementById('address');
    var cityInput = document.getElementById('city');
    var provinceInput = document.getElementById('province');

    function searchAddress() {
        var addressQuery = addressInput.value;
        var cityQuery = cityInput ? cityInput.value : '';
        var provinceQuery = provinceInput ? provinceInput.value : '';
        var fullQuery = addressQuery;
        if (cityQuery) fullQuery += ', ' + cityQuery;
        if (provinceQuery) fullQuery += ', ' + provinceQuery;

        if(addressQuery) {
             console.log("Buscando:", fullQuery);
            fetch("https://nominatim.openstreetmap.org/search?format=json&limit=1&countrycodes=ar&q=" + encodeURIComponent(fullQuery))
            .then(response => response.json())
            .then(data => {
                if(data.length > 0) {
                    var result = data[0];
                    var lat = parseFloat(result.lat);
                    var lon = parseFloat(result.lon);
                     console.log("Encontrado:", result.display_name, lat, lon);

                    drawnItems.clearLayers(); // Limpiar marcadores anteriores
                    existingMarker = L.marker([lat, lon], {draggable: true}).addTo(map); // Crear nuevo marcador arrastrable
                    drawnItems.addLayer(existingMarker);
                    document.getElementById('latitude').value = lat.toFixed(6);
                    document.getElementById('longitude').value = lon.toFixed(6);

                    // Actualizar si se arrastra
                    existingMarker.on('dragend', function(event){
                        var marker = event.target;
                        var position = marker.getLatLng();
                        document.getElementById('latitude').value = position.lat.toFixed(6);
                        document.getElementById('longitude').value = position.lng.toFixed(6);
                    });

                    var mapDiv = document.getElementById('map');
                    if(mapDiv.style.display === 'none') {
                        mapDiv.style.display = 'block';
                        map.invalidateSize();
                    }
                    map.setView([lat, lon], 15);
                } else {
                    console.log("No se encontró la dirección:", fullQuery);
                }
            })
            .catch(error => {
                console.error("Error al buscar la dirección:", error);
            });
        }
    }
    if(addressInput) addressInput.addEventListener('blur', searchAddress);
    if(cityInput) cityInput.addEventListener('blur', searchAddress);
    if(provinceInput) provinceInput.addEventListener('blur', searchAddress);
    </script>
{% endblock %}