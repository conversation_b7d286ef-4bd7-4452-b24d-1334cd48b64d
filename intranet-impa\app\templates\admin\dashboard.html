<!-- /app/templates/admin/dashboard.html -->
{% extends "base.html" %}
{% block title %}Dashboard Administrador{% endblock %}

{% block content %}
<div class="container my-4">
    <h1><i class="fas fa-tachometer-alt mr-2"></i>Escritorio Administrador</h1>
    <p>Bienvenido, {{ view_user.first_name }}!</p>

    <!-- ############################################## -->
    <!-- ## SECCIÓN DE WIDGETS (NUEVO)                 ## -->
    <!-- ############################################## -->
    <div class="row mb-4">
        <!-- Widget Nuevos Usuarios (Admin) -->
        <div class="col-md-3">
            <div class="card text-white bg-success mb-3 shadow">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-user-plus mr-2"></i>Nuevos Usuarios</h5>
                    <p class="card-text display-4">{{ new_users_count if new_users_count is defined else 'N/A' }}</p>
                    <small>Últimos 7 días</small>
                </div>
                 <a href="{{ url_for('routes.list_users', sort_by='created_at', sort_dir='desc') }}" class="card-footer text-white">Ver detalles <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Docs Pendientes (Admin/Sec) -->
        <div class="col-md-3">
            <div class="card text-white bg-warning mb-3 shadow">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-file-alt mr-2"></i>Docs Pendientes</h5>
                    <p class="card-text display-4">{{ pending_docs_count if pending_docs_count is defined else 'N/A' }}</p>
                    <small>Esperando aprobación</small>
                </div>
                 {# Ajustar el enlace si tienes filtro por status #}
                 <a href="{{ url_for('routes.list_documents', status='pendiente') }}" class="card-footer text-white">Gestionar <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Mensajes Nuevos (Todos) -->
        <div class="col-md-3">
            <div class="card text-white bg-info mb-3 shadow">
                 <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-envelope-open-text mr-2"></i>Mensajes Nuevos</h5>
                    <p class="card-text display-4">{{ unread_count if unread_count is defined else 'N/A' }}</p>
                    <small>Sin leer</small>
                </div>
                <a href="{{ url_for('routes.inbox') }}" class="card-footer text-white">Ir al Buzón <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Cumpleaños (Todos) -->
        <div class="col-md-3">
            <div class="card bg-light mb-3 shadow">
                <div class="card-header"><i class="fas fa-birthday-cake mr-2"></i>Próximos Cumpleaños</div>
                <div class="card-body" style="max-height: 150px; overflow-y: auto;"> {# Scroll si hay muchos #}
                    {% if upcoming_birthdays %}
                        <ul class="list-unstyled mb-0"> {# Quitar margen inferior #}
                            {% for user_bday in upcoming_birthdays %}
                                <li class="mb-1"> {# Margen entre items #}
                                    <a href="{{ url_for('routes.user_detail', user_id=user_bday.id) }}">{{ user_bday.full_name }}</a>
                                    <span class="badge badge-pill badge-secondary float-right">{{ user_bday.date_of_birth.strftime('%d/%m') }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted small">No hay cumpleaños próximos.</p>
                    {% endif %}
                </div>
                 <a href="{{ url_for('routes.calendar_view') }}" class="card-footer text-muted">Ver Calendario <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>
    <!-- ############################################## -->
    <!-- ## FIN SECCIÓN DE WIDGETS                     ## -->
    <!-- ############################################## -->

    <hr> {# Separador opcional #}

    <!-- ############################################## -->
    <!-- ## SECCIÓN DE ACCIONES/ENLACES PRINCIPALES   ## -->
    <!-- ############################################## -->
    <div class="row mb-4">
        <!-- Columna Acciones Rápidas (o enlaces principales) -->
        <div class="col-lg-4">
             <div class="card shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <i class="fas fa-cogs mr-2"></i>Acciones Principales
                </div>
                <div class="list-group list-group-flush">
                    {# Aquí puedes poner las acciones más importantes como botones/links #}
                    <a href="{{ url_for('routes.register') }}" class="list-group-item list-group-item-action"><i class="fas fa-user-plus fa-fw mr-2 text-success"></i>Registrar Usuario</a>
                    <a href="{{ url_for('routes.create_church') }}" class="list-group-item list-group-item-action"><i class="fas fa-church fa-fw mr-2 text-primary"></i>Crear Iglesia</a>
                    <a href="{{ url_for('routes.upload_document') }}" class="list-group-item list-group-item-action"><i class="fas fa-file-upload fa-fw mr-2 text-info"></i>Subir Documento</a>
                    <a href="{{ url_for('routes.manage_announcement') }}" class="list-group-item list-group-item-action"><i class="fas fa-bullhorn fa-fw mr-2 text-warning"></i>Crear Anuncio</a>
                    <a href="{{ url_for('routes.asignar_jerarquias') }}" class="list-group-item list-group-item-action"><i class="fas fa-sitemap fa-fw mr-2 text-secondary"></i>Asignar Jerarquías</a>
                     {# Puedes añadir más o mover algunos de las cards originales aquí #}
                </div>
            </div>
             {# Mover aquí las cards originales si prefieres esta disposición #}
             {# Ejemplo:
             <div class="card mb-3">
                <div class="card-header bg-info text-white">Miembros</div>
                 ...
             </div>
             #}
        </div>

        <!-- Columna Anuncios Recientes -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                 <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-bullhorn mr-2"></i>Anuncios Recientes</span>
                     {# Mover aquí el botón de Crear Anuncio si se prefiere #}
                     {#
                     {% if current_user.role in ['administrador', 'secretaria', 'pastorado'] %}
                        <a href="{{ url_for('routes.manage_announcement') }}" class="btn btn-sm btn-success">
                             <i class="fas fa-plus-circle mr-1"></i> Nuevo
                        </a>
                     {% endif %}
                     #}
                 </div>
                 <div class="card-body" style="max-height: 400px; overflow-y: auto;"> {# Scroll si hay muchos anuncios #}
                    {% if announcements %}
                        {% for announcement in announcements %}
                            <div class="alert alert-{{ 'secondary' if loop.index % 2 == 0 else 'light' }} border mb-2" role="alert"> {# Estilo alterno #}
                                <h5 class="alert-heading">{{ announcement.title }}</h5>
                                <p class="mb-1 small">{{ announcement.content | nl2br | safe }}</p> {# Texto más pequeño #}
                                <hr class="my-1"> {# Separador más fino #}
                                <p class="mb-0 small text-muted d-flex justify-content-between">
                                    <span>
                                        <i class="fas fa-user fa-fw"></i> {{ announcement.author.full_name if announcement.author else 'Sistema' }}
                                        <i class="far fa-clock fa-fw ml-2"></i> {{ announcement.created_at | to_local }}
                                    </span>
                                    {% if current_user.role in ['administrador', 'secretaria'] or (announcement.author_id == current_user.id) %}
                                     <span>
                                         <a href="{{ url_for('routes.manage_announcement', announcement_id=announcement.id) }}" class="badge badge-primary"><i class="fas fa-edit"></i> Editar</a>
                                         {# Añadir botón eliminar con confirmación si se desea #}
                                     </span>
                                    {% endif %}
                                </p>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No hay anuncios para mostrar.</p>
                    {% endif %}
                 </div>
             </div>
        </div>
    </div>
    <!-- ############################################## -->
    <!-- ## FIN SECCIÓN ACCIONES/ANUNCIOS            ## -->
    <!-- ############################################## -->


    <hr> {# Separador opcional #}

    <!-- ############################################## -->
    <!-- ## SECCIÓN DE ENLACES DETALLADOS (OPCIONAL) ## -->
    <!-- ############################################## -->
    <h2>Otras Secciones</h2>
    <!-- Mantener las filas originales de cards si se prefiere tener enlaces más detallados -->
    <div class="row">
        <!-- Grupo: Miembros (Usuarios) -->
        <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Miembros</div>
            <div class="card-body">
              {# <a href="{{ url_for('routes.register') }}" class="btn btn-outline-success btn-block">Registrar Usuario</a> #} {# Ya está en Acciones Rápidas #}
              <a href="{{ url_for('routes.list_users') }}" class="btn btn-outline-primary btn-block">Listar/Buscar Usuarios</a>
              <a href="{{ url_for('routes.church_members_count') }}" class="btn btn-outline-secondary btn-block">Miembros por Iglesia</a>
            </div>
          </div>
        </div>
        <!-- Grupo: Pastores -->
        <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Pastores</div>
            <div class="card-body">
              <a href="{{ url_for('routes.list_pastores') }}" class="btn btn-outline-primary btn-block">Listado</a>
              <a href="{{ url_for('routes.seleccionar_pastor_credencial') }}" class="btn btn-outline-secondary btn-block">Generar Credencial</a>
            </div>
          </div>
        </div>
        <!-- Grupo: Iglesia -->
        <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Iglesia</div>
            <div class="card-body">
               {# <a href="{{ url_for('routes.create_church') }}" class="btn btn-outline-success btn-block">Crear Iglesias</a> #} {# Ya está en Acciones Rápidas #}
              <a href="{{ url_for('routes.list_churches') }}" class="btn btn-outline-primary btn-block">Ver Iglesias</a>
              <a href="{{ url_for('routes.list_inventories') }}" class="btn btn-outline-info btn-block">Inventario</a>
            </div>
          </div>
        </div>
        <!-- Grupo: Mapa -->
        <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Mapa</div>
            <div class="card-body">
              <a href="{{ url_for('routes.map_view') }}" class="btn btn-outline-dark btn-block">Ver Mapa</a>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4">
      <!-- Grupo: Correo -->
      <div class="col-md-3">
        <div class="card mb-3">
          <div class="card-header bg-light">Correo</div>
          <div class="card-body">
            <a href="{{ url_for('routes.send_message') }}" class="btn btn-outline-primary btn-block">Enviar Mensaje</a>
            <a href="{{ url_for('routes.inbox') }}" class="btn btn-outline-secondary btn-block">Consultar Mensajes</a>
          </div>
        </div>
      </div>
        <!-- Grupo: Documentación -->
        <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Documentación</div>
            <div class="card-body">
              {# <a href="{{ url_for('routes.upload_document') }}" class="btn btn-outline-success btn-block">Subir Documento</a> #} {# Ya está en Acciones Rápidas #}
              <a href="{{ url_for('routes.list_documents') }}" class="btn btn-outline-primary btn-block">Ver Documentación</a>
              <a href="{{ url_for('routes.diploma_preview') }}" class="btn btn-outline-secondary btn-block">Diplomas</a>
              <a href="{{ url_for('routes.acta_preview') }}" class="btn btn-outline-secondary btn-block">Actas</a>
            </div>
          </div>
        </div>
        <!-- Grupo: Histórico -->
        <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Histórico</div>
            <div class="card-body">
              <a href="{{ url_for('routes.choose_review_user') }}" class="btn btn-outline-primary btn-block">Agregar Histórico</a>
              <a href="{{ url_for('routes.list_reviews') }}" class="btn btn-outline-secondary btn-block">Ver Histórico</a>
            </div>
          </div>
        </div>
        <!-- Grupo: Calendario -->
        <div class="col-md-3">
          <div class="card mb-3">
            <div class="card-header bg-light">Calendario</div>
            <div class="card-body">
              <a href="{{ url_for('routes.calendar_view') }}" class="btn btn-outline-dark btn-block">Ver Calendario</a>
            </div>
          </div>
        </div>
      </div>
     <!-- ############################################## -->
     <!-- ## FIN SECCIÓN ENLACES DETALLADOS           ## -->
     <!-- ############################################## -->

</div> {# Cierre del container #}
{% endblock %}