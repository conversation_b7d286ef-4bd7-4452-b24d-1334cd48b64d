// socketConnection.js

// --- Conexión a Socket.IO (namespace /node, forzar WebSockets) ---
// Usamos 'document' directamente, ya que este módulo se carga en el contexto del navegador.
// Asumimos que nodeId y username están disponibles en el dataset del body.
const socket = io.connect('https://' + document.domain + ':' + location.port + '/node', {
    query: {
        node_id: parseInt(document.body.dataset.nodeId), // Asegúrate de que sea un entero.
        username: document.body.dataset.username
    },
    transports: ['websocket'] // Forzar WebSockets
});

// --- Función auxiliar para emitir eventos de forma segura ---
function safeEmit(event, data) {
    if (socket && socket.connected) {
        try {
            socket.emit(event, data);
        } catch (err) {
            console.error(`Error al emitir ${event}:`, err);
        }
    } else {
        console.warn(`No se pudo emitir el evento "${event}": conexión no abierta.`);
    }
}

// --- Función para loguear (consola + archivo) ---
//  (La movemos aquí desde webrtc.js, como solicitaste)
function logToFile(message) {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] ${message}\n`;
    console.log(formattedMessage); // Mostrar en consola
    // Enviar al servidor para guardar en archivo
    fetch('/log', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: formattedMessage }),
    }).catch(error => console.error('Error logging to file:', error));
}

// --- Manejo de eventos de conexión/reconexión ---
socket.on('connect_error', (error) => {
    logToFile('Connection failed: ' + error);
});

socket.on('reconnect_attempt', (attemptNumber) => {
    logToFile('Reconnection attempt: ' + attemptNumber);
});

socket.on('reconnect_failed', () => {
    logToFile('Reconnection failed.');
});

socket.on('reconnect', (attemptNumber) => {
    logToFile('Reconnected successfully after ' + attemptNumber + ' attempts');
});

// --- Listener para forzar logout (podría ir en un módulo de autenticación/autorización) ---
socket.on('force_logout', function(data) {
  // Desactivar reconexión
  socket.io.opts.reconnection = false;
  alert(data.message);
  window.location.href = '/logout';
});

// --- Exportar la instancia del socket y la función safeEmit ---
export { socket, safeEmit, logToFile };