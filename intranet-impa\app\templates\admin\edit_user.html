<!-- /app/templates/admin/edit_user.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}Editar Usuario{% endblock %}

{% block content %}
    <!-- Mostrar nombres concatenados -->
    <h1>Editar Usuario: {{ user.first_name }} {{ user.last_name }}</h1>
    <form method="POST" action="">
        {{ form.hidden_tag() }}
        <div class="form-group">
            {{ render_field(form.username, class="form-control") }}
        </div>
        <!-- Separar full_name en dos campos -->
        <div class="form-group">
            {{ render_field(form.first_name, class="form-control", placeholder="Nombre") }}
        </div>
        <div class="form-group">
            {{ render_field(form.last_name, class="form-control", placeholder="Apellido") }}
        </div>
        <div class="form-group">
            {{ render_field(form.email, class="form-control", placeholder="Correo Electrónico") }}
        </div>
        <div class="form-group">
            {{ render_field(form.password, class="form-control", placeholder="Contraseña") }}
        </div>
        <div class="form-group">
            {{ render_field(form.confirm_password, class="form-control", placeholder="Confirmar Contraseña") }}
        </div>
        <div class="form-group">
            {{ render_field(form.role, class="form-control", id="role") }}
        </div>
        <div class="form-group">
            {{ render_field(form.church, class="form-control") }}
        </div>
        <!-- Campos adicionales -->
        <div class="form-group">
            {{ render_field(form.phone_number, class="form-control", placeholder="Teléfono") }}
        </div>
         <div class="form-group">
            {{ render_field(form.address, class="form-control", placeholder="Dirección") }}
        </div>
        <div class="form-group">
            {{ render_field(form.date_of_birth, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.alergies, class="form-control", placeholder="Alergias") }}
        </div>
        <div class="form-group">
            {{ render_field(form.emergency_contact, class="form-control", placeholder="Contacto de Emergencia") }}
        </div>
        <!-- Si el rol es pastor, mostrar roles específicos -->
        <div class="form-group" id="pastor-roles-container" style="display: none;">
            {{ render_field(form.pastor_roles, class="form-control") }}
        </div>

        {{ form.submit(class="btn btn-primary") }}
    </form>

    <script>
        const roleSelect = document.getElementById('role');
        const pastorRolesContainer = document.getElementById('pastor-roles-container');
        roleSelect.addEventListener('change', function() {
            if (this.value === 'pastorado') {
                pastorRolesContainer.style.display = 'block';
            } else {
                pastorRolesContainer.style.display = 'none';
            }
        });
        if (roleSelect.value === 'pastorado') {
            pastorRolesContainer.style.display = 'block';
        }
    </script>
{% endblock %}
