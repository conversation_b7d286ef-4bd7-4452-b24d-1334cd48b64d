// static/js/filter.js
document.addEventListener('DOMContentLoaded', function () {
    const normalize = str =>
        (str || '')
            .toLowerCase()
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, '') // Elimina acentos
            .trim();

    const filterInputs = document.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
        input.addEventListener('input', applyFilters);
    });

    function applyFilters() {
        const filters = {
            ciudad: normalize(document.getElementById('filter-ciudad').value),
            origen: normalize(document.getElementById('filter-origen').value),
            estado: normalize(document.getElementById('filter-estado').value)
        };

        const rows = document.querySelectorAll('table tbody tr');
        rows.forEach(row => {
            const ciudad = normalize(row.children[2]?.textContent);
            const origen = normalize(row.children[3]?.textContent);
            const estado = normalize(row.children[6]?.textContent);

            const matchesCiudad = ciudad.includes(filters.ciudad);
            const matchesOrigen = origen.includes(filters.origen);
            const matchesEstado = estado.includes(filters.estado);

            row.style.display = (matchesCiudad && matchesOrigen && matchesEstado) ? '' : 'none';
        });
    }
});
