<!-- /app/templates/admin/member_relationships.html -->
{% extends "base.html" %}
{% block title %}Relaciones de {{ user.first_name }} {{ user.last_name }}{% endblock %}

{% block content %}
  <h1>Relaciones Familiares de {{ user.first_name }} {{ user.last_name }}</h1>
  <table class="table table-striped">
    <thead>
      <tr>
        <th>Relación</th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% for rel in relationships %}
      <tr>
        <td>{{ describe_relationship(rel, user.id) }}</td>
        <td>
          {% if current_user.role in ['administrador', 'secretaria'] %}
            <a href="{{ url_for('routes.edit_relationship', relationship_id=rel.id) }}" class="btn btn-warning btn-sm">Editar</a>
            <form method="POST" action="{{ url_for('routes.delete_relationship', relationship_id=rel.id) }}" style="display:inline;">
              <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de eliminar esta relación?');">Eliminar</button>
            </form>
          {% elif current_user.role == 'pastorado' %}
            <a href="{{ url_for('routes.edit_pastor_relationship', relationship_id=rel.id) }}" class="btn btn-warning btn-sm">Editar</a>
            <form method="POST" action="{{ url_for('routes.delete_pastor_relationship', relationship_id=rel.id) }}" style="display:inline;">
              <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de eliminar esta relación?');">Eliminar</button>
            </form>
          {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  <a href="{{ url_for('routes.add_relationship') }}" class="btn btn-primary">Agregar Relación</a>
  <a href="{{ url_for('routes.dashboard') }}" class="btn btn-secondary">Volver al Dashboard</a>
{% endblock %}
