from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import logging

from app.models.vehicle import Vehicle, VehicleCategory
from app.schemas.vehicle_schema import VehicleCreate, VehicleUpdate

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_vehicle_by_plate(db: Session, plate_number: str) -> Optional[Vehicle]:
    return db.query(Vehicle).filter(Vehicle.plate_number == plate_number).first()

def get_vehicle(db: Session, vehicle_id: int) -> Optional[Vehicle]:
    return db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()

def get_vehicles(db: Session, skip: int = 0, limit: int = 100) -> List[Vehicle]:
    return db.query(Vehicle).offset(skip).limit(limit).all()

def create_vehicle(db: Session, vehicle_in: VehicleCreate, owner_id: Optional[int] = None) -> Vehicle:
    db_vehicle = Vehicle(
        plate_number=vehicle_in.plate_number,
        brand=vehicle_in.brand,
        model=vehicle_in.model,
        year=vehicle_in.year,
        color=vehicle_in.color,
        status=vehicle_in.status,
        category=vehicle_in.category or VehicleCategory.ACTIVO,
        last_latitude=vehicle_in.last_latitude,
        last_longitude=vehicle_in.last_longitude,
        owner_id=owner_id,
        driver_id=vehicle_in.driver_id
    )
    db.add(db_vehicle)
    db.commit()
    db.refresh(db_vehicle)
    return db_vehicle

def update_vehicle(db: Session, db_vehicle: Vehicle, vehicle_in: VehicleUpdate) -> Vehicle:
    # Actualizar solo los campos que se proporcionan
    update_data = vehicle_in.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(db_vehicle, field, value)

    db.commit()
    db.refresh(db_vehicle)
    return db_vehicle

def delete_vehicle(db: Session, vehicle_id: int) -> Optional[Vehicle]:
    vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
    if vehicle:
        db.delete(vehicle)
        db.commit()
        return vehicle
    return None

def assign_driver(db: Session, vehicle_id: int, driver_id: int) -> Optional[Vehicle]:
    vehicle = get_vehicle(db, vehicle_id)
    if not vehicle:
        return None

    vehicle.driver_id = driver_id
    db.commit()
    db.refresh(vehicle)
    return vehicle

def get_vehicles_by_status(db: Session, status: str, skip: int = 0, limit: int = 100) -> List[Vehicle]:
    return db.query(Vehicle).filter(Vehicle.status == status).offset(skip).limit(limit).all()

def get_vehicles_by_category(db: Session, category: str, skip: int = 0, limit: int = 100) -> List[Vehicle]:
    return db.query(Vehicle).filter(Vehicle.category == category).offset(skip).limit(limit).all()

def get_vehicles_by_driver(db: Session, driver_id: int) -> List[Vehicle]:
    return db.query(Vehicle).filter(Vehicle.driver_id == driver_id).all()

def get_vehicles_by_owner(db: Session, owner_id: int) -> List[Vehicle]:
    return db.query(Vehicle).filter(Vehicle.owner_id == owner_id).all()

def update_vehicle_location(db: Session, vehicle_id: int, latitude: str, longitude: str) -> Optional[Vehicle]:
    """
    Actualiza la ubicación de un vehículo.

    Args:
        db: Sesión de base de datos
        vehicle_id: ID del vehículo
        latitude: Latitud en formato string
        longitude: Longitud en formato string

    Returns:
        Vehicle: El vehículo actualizado o None si no se encuentra
    """
    try:
        vehicle = get_vehicle(db, vehicle_id)
        if not vehicle:
            logger.warning(f"Vehículo con ID {vehicle_id} no encontrado al actualizar ubicación")
            return None

        vehicle.last_latitude = latitude
        vehicle.last_longitude = longitude
        vehicle.last_location_update = datetime.utcnow()

        db.commit()
        db.refresh(vehicle)
        logger.info(f"Ubicación del vehículo {vehicle_id} actualizada: {latitude}, {longitude}")
        return vehicle
    except Exception as e:
        db.rollback()
        logger.error(f"Error al actualizar ubicación del vehículo {vehicle_id}: {e}")
        return None

def get_nearby_vehicles(db: Session, latitude: str, longitude: str, radius_meters: int = 1000, status: Optional[VehicleCategory] = None) -> List[Vehicle]:
    """
    Obtiene vehículos cercanos a una ubicación.

    Nota: Esta es una implementación simplificada. En una implementación real,
    se usaría PostGIS o una consulta espacial más eficiente.

    Args:
        db: Sesión de base de datos
        latitude: Latitud en formato string
        longitude: Longitud en formato string
        radius_meters: Radio de búsqueda en metros
        status: Estado opcional para filtrar vehículos

    Returns:
        List[Vehicle]: Lista de vehículos cercanos
    """
    # En una implementación real, usaríamos PostGIS y una consulta como:
    # SELECT * FROM vehicles
    # WHERE ST_DWithin(
    #     ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)::geography,
    #     ST_SetSRID(ST_MakePoint(vehicles.last_longitude, vehicles.last_latitude), 4326)::geography,
    #     radius_meters
    # )

    # Implementación simplificada: devolver todos los vehículos activos
    # En una app real, esto debería filtrar por distancia
    query = db.query(Vehicle).filter(
        Vehicle.last_latitude.isnot(None),
        Vehicle.last_longitude.isnot(None)
    )

    if status:
        query = query.filter(Vehicle.status == status)

    return query.all()
