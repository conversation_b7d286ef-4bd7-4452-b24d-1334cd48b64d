# app/academic_services.py
# Servicios para el Sistema Académico

from app import db
from datetime import datetime, date, timedelta
import secrets
import json

# ============================================================================
# SERVICIOS PARA ESCUELAS BÍBLICAS (Nivel Básico)
# ============================================================================

class AcademicService:
    """Servicio para gestión de escuelas bíblicas"""
    
    @staticmethod
    def create_school(church_id, name, description, director_id=None, **kwargs):
        """Crear nueva escuela bíblica"""
        from app.models import AcademicSchool
        
        school = AcademicSchool(
            name=name,
            description=description,
            church_id=church_id,
            director_id=director_id,
            **kwargs
        )
        db.session.add(school)
        db.session.commit()
        return school
    
    @staticmethod
    def create_curriculum(school_id, name, description, duration_months, subjects_data):
        """Crear pensum académico con materias"""
        from app.models import AcademicCurriculum, AcademicSubject
        
        curriculum = AcademicCurriculum(
            name=name,
            description=description,
            school_id=school_id,
            duration_months=duration_months
        )
        db.session.add(curriculum)
        db.session.flush()  # Para obtener el ID
        
        total_credits = 0
        for subject_data in subjects_data:
            subject = AcademicSubject(
                curriculum_id=curriculum.id,
                **subject_data
            )
            total_credits += subject_data.get('credits', 1)
            db.session.add(subject)
        
        curriculum.total_credits = total_credits
        db.session.commit()
        return curriculum
    
    @staticmethod
    def enroll_student(student_id, school_id, curriculum_id):
        """Matricular estudiante"""
        from app.models import AcademicEnrollment
        
        # Verificar si ya está matriculado
        existing = AcademicEnrollment.query.filter_by(
            student_id=student_id,
            school_id=school_id,
            status='active'
        ).first()
        
        if existing:
            raise ValueError("El estudiante ya está matriculado en esta escuela")
        
        enrollment = AcademicEnrollment(
            student_id=student_id,
            school_id=school_id,
            curriculum_id=curriculum_id
        )
        db.session.add(enrollment)
        db.session.commit()
        return enrollment
    
    @staticmethod
    def auto_enroll_eligible_students(school_id):
        """Auto-matrícula de estudiantes elegibles"""
        from app.models import AcademicSchool, Member, User, AcademicEnrollment
        
        school = AcademicSchool.query.get(school_id)
        if not school or not school.auto_enrollment:
            return []
        
        # Obtener miembros activos de la iglesia que no estén matriculados
        eligible_students = db.session.query(User).join(Member).filter(
            Member.church_id == school.church_id,
            Member.is_active == True,
            ~User.id.in_(
                db.session.query(AcademicEnrollment.student_id).filter(
                    AcademicEnrollment.school_id == school_id,
                    AcademicEnrollment.status == 'active'
                )
            )
        ).limit(school.max_students).all()
        
        enrolled = []
        curriculum = school.curriculums[0] if school.curriculums else None
        
        if curriculum:
            for student in eligible_students:
                try:
                    enrollment = AcademicService.enroll_student(
                        student.id, school_id, curriculum.id
                    )
                    enrolled.append(enrollment)
                except ValueError:
                    continue
        
        return enrolled
    
    @staticmethod
    def assign_grade(enrollment_id, subject_id, grade_value, teacher_id, grade_type='partial', comments=None):
        """Asignar calificación"""
        from app.models import AcademicGrade
        
        grade = AcademicGrade(
            enrollment_id=enrollment_id,
            subject_id=subject_id,
            grade_value=grade_value,
            grade_type=grade_type,
            teacher_id=teacher_id,
            comments=comments
        )
        db.session.add(grade)
        
        # Verificar si el estudiante completó todas las materias
        if grade_type == 'final':
            AcademicService.check_completion(enrollment_id)
        
        db.session.commit()
        return grade
    
    @staticmethod
    def check_completion(enrollment_id):
        """Verificar si el estudiante completó el pensum"""
        from app.models import AcademicEnrollment, AcademicSubject, AcademicGrade
        
        enrollment = AcademicEnrollment.query.get(enrollment_id)
        curriculum = enrollment.curriculum
        
        # Obtener materias obligatorias
        mandatory_subjects = AcademicSubject.query.filter_by(
            curriculum_id=curriculum.id,
            is_mandatory=True
        ).all()
        
        # Verificar calificaciones finales
        final_grades = AcademicGrade.query.filter_by(
            enrollment_id=enrollment_id,
            grade_type='final'
        ).all()
        
        completed_subjects = {grade.subject_id for grade in final_grades if grade.grade_value >= 3.0}
        required_subjects = {subject.id for subject in mandatory_subjects}
        
        if required_subjects.issubset(completed_subjects):
            # Calcular promedio final
            total_grade = sum(grade.grade_value for grade in final_grades)
            enrollment.final_grade = total_grade / len(final_grades)
            enrollment.status = 'completed'
            enrollment.completion_date = date.today()
            
            # Generar certificado automáticamente
            AcademicService.generate_certificate(enrollment_id)
    
    @staticmethod
    def generate_certificate(enrollment_id):
        """Generar certificado automático"""
        from app.models import AcademicEnrollment
        
        enrollment = AcademicEnrollment.query.get(enrollment_id)
        
        if enrollment.status == 'completed' and not enrollment.certificate_issued:
            enrollment.certificate_issued = True
            enrollment.certificate_date = date.today()
            
            # Aquí se podría integrar con el sistema de documentos existente
            # para generar el PDF del certificado
            
            db.session.commit()
            return True
        return False

# ============================================================================
# SERVICIOS PARA INSTITUTO PASTORAL (Nivel Corporativo)
# ============================================================================

class PastoralAcademicService:
    """Servicio para gestión académica pastoral"""
    
    @staticmethod
    def create_institute(name, description, rector_id=None, coordinator_id=None):
        """Crear instituto pastoral"""
        from app.models import PastoralInstitute
        
        institute = PastoralInstitute(
            name=name,
            description=description,
            rector_id=rector_id,
            academic_coordinator_id=coordinator_id
        )
        db.session.add(institute)
        db.session.commit()
        return institute
    
    @staticmethod
    def create_pastoral_program(institute_id, name, program_type, duration_months, 
                              target_roles=None, min_ministry_years=0, created_by_id=None, **kwargs):
        """Crear programa pastoral"""
        from app.models import PastoralProgram
        
        program = PastoralProgram(
            institute_id=institute_id,
            name=name,
            program_type=program_type,
            duration_months=duration_months,
            target_roles=target_roles or ['pastorado'],
            min_ministry_years=min_ministry_years,
            created_by_id=created_by_id,
            **kwargs
        )
        db.session.add(program)
        db.session.commit()
        return program
    
    @staticmethod
    def enroll_pastor(pastor_id, program_id):
        """Matricular pastor en programa (requiere aprobación)"""
        from app.models import User, PastoralProgram, PastoralEnrollment
        
        # Verificar elegibilidad
        pastor = User.query.get(pastor_id)
        program = PastoralProgram.query.get(program_id)
        
        if not pastor or not program:
            raise ValueError("Pastor o programa no encontrado")
        
        # Verificar si el rol del pastor está en los roles objetivo
        if pastor.role not in program.target_roles:
            raise ValueError("Pastor no elegible para este programa")
        
        # Verificar si ya está matriculado
        existing = PastoralEnrollment.query.filter_by(
            student_id=pastor_id,
            program_id=program_id
        ).filter(PastoralEnrollment.status.in_(['pending', 'approved', 'active'])).first()
        
        if existing:
            raise ValueError("Pastor ya está matriculado en este programa")
        
        # Crear matrícula pendiente
        enrollment = PastoralEnrollment(
            student_id=pastor_id,
            program_id=program_id,
            status='pending' if program.requires_approval else 'approved'
        )
        
        db.session.add(enrollment)
        db.session.commit()
        
        return enrollment
    
    @staticmethod
    def approve_enrollment(enrollment_id, approved_by_id, notes=None):
        """Aprobar matrícula pastoral"""
        from app.models import PastoralEnrollment
        
        enrollment = PastoralEnrollment.query.get(enrollment_id)
        if not enrollment or enrollment.status != 'pending':
            return False
        
        enrollment.status = 'approved'
        enrollment.approved_by_id = approved_by_id
        enrollment.approval_date = date.today()
        enrollment.approval_notes = notes
        
        db.session.commit()
        return True
    
    @staticmethod
    def assign_pastoral_grade(enrollment_id, subject_id, grade_value, professor_id, 
                            grade_type='assignment', **kwargs):
        """Asignar calificación pastoral"""
        from app.models import PastoralGrade
        
        grade = PastoralGrade(
            enrollment_id=enrollment_id,
            subject_id=subject_id,
            grade_value=grade_value,
            professor_id=professor_id,
            grade_type=grade_type,
            **kwargs
        )
        
        db.session.add(grade)
        
        # Actualizar GPA si es calificación final
        if grade_type == 'final':
            PastoralAcademicService.update_student_gpa(enrollment_id)
        
        db.session.commit()
        return grade
    
    @staticmethod
    def update_student_gpa(enrollment_id):
        """Actualizar GPA del estudiante"""
        from app.models import PastoralEnrollment, PastoralGrade
        
        enrollment = PastoralEnrollment.query.get(enrollment_id)
        
        # Obtener calificaciones finales
        final_grades = PastoralGrade.query.filter_by(
            enrollment_id=enrollment_id,
            grade_type='final'
        ).all()
        
        if final_grades:
            # Calcular GPA ponderado por créditos
            total_points = 0
            total_credits = 0
            
            for grade in final_grades:
                credits = grade.subject.credits
                total_points += grade.grade_value * credits
                total_credits += credits
            
            enrollment.cumulative_gpa = total_points / total_credits if total_credits > 0 else 0
            enrollment.credits_completed = total_credits
    
    @staticmethod
    def generate_certificate_number():
        """Generar número único de certificado"""
        year = date.today().year
        random_part = secrets.token_hex(4).upper()
        return f"IMPA-{year}-{random_part}"
    
    @staticmethod
    def get_dashboard_stats():
        """Obtener estadísticas para dashboard"""
        from app.models import (AcademicSchool, AcademicEnrollment, 
                               PastoralProgram, PastoralEnrollment)
        
        stats = {
            'total_schools': AcademicSchool.query.filter_by(is_active=True).count(),
            'total_programs': PastoralProgram.query.filter_by(is_active=True).count(),
            'active_students': AcademicEnrollment.query.filter_by(status='active').count(),
            'pastoral_students': PastoralEnrollment.query.filter_by(status='active').count(),
            'pending_approvals': PastoralEnrollment.query.filter_by(status='pending').count(),
            'completed_this_month': AcademicEnrollment.query.filter(
                AcademicEnrollment.status == 'completed',
                AcademicEnrollment.completion_date >= date.today().replace(day=1)
            ).count()
        }
        
        return stats

# ============================================================================
# SERVICIOS DE REPORTES
# ============================================================================

class AcademicReports:
    """Generador de reportes académicos"""
    
    @staticmethod
    def school_statistics(school_id):
        """Estadísticas de una escuela"""
        from app.models import AcademicSchool, AcademicEnrollment
        
        school = AcademicSchool.query.get(school_id)
        
        total_enrolled = AcademicEnrollment.query.filter_by(
            school_id=school_id, status='active'
        ).count()
        
        completed = AcademicEnrollment.query.filter_by(
            school_id=school_id, status='completed'
        ).count()
        
        dropped = AcademicEnrollment.query.filter_by(
            school_id=school_id, status='dropped'
        ).count()
        
        return {
            'school': school,
            'total_enrolled': total_enrolled,
            'completed': completed,
            'dropped': dropped,
            'completion_rate': (completed / (completed + dropped)) * 100 if (completed + dropped) > 0 else 0
        }
    
    @staticmethod
    def pastoral_program_stats(program_id):
        """Estadísticas de programa pastoral"""
        from app.models import PastoralProgram, PastoralEnrollment
        
        program = PastoralProgram.query.get(program_id)
        
        total_enrolled = PastoralEnrollment.query.filter_by(program_id=program_id).count()
        active = PastoralEnrollment.query.filter_by(program_id=program_id, status='active').count()
        completed = PastoralEnrollment.query.filter_by(program_id=program_id, status='completed').count()
        pending = PastoralEnrollment.query.filter_by(program_id=program_id, status='pending').count()
        
        return {
            'program': program,
            'total_enrolled': total_enrolled,
            'active': active,
            'completed': completed,
            'pending': pending,
            'capacity_usage': (active / program.max_students) * 100 if program.max_students > 0 else 0
        }
