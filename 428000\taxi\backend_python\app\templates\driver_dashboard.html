{% extends "base_layout_driver.html" %}

{% block title %}Panel de Conductor - Taxis{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .status-indicator {
        width: 15px;
        height: 15px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .status-libre {
        background-color: #198754;
    }
    .status-ocupado {
        background-color: #dc3545;
    }
    .status-alerta {
        background-color: #ffc107;
    }
    .status-emergencia {
        background-color: #dc3545;
        animation: blink 1s infinite;
    }
    .status-fuera_de_servicio {
        background-color: #6c757d;
    }
    @keyframes blink {
        0% { opacity: 1; }
        50% { opacity: 0.3; }
        100% { opacity: 1; }
    }
    .trip-card {
        border-left: 4px solid #198754;
        margin-bottom: 10px;
        transition: all 0.3s;
    }
    .trip-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Panel de Conductor</h1>
        <p class="lead">Bienvenido al panel de conductor del sistema de taxis.</p>
    </div>
</div>

<!-- Estado del Conductor -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Estado Actual</h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="status-indicator status-{{ vehicle.status|default('libre')|lower }}"></span>
                            <h3 class="mb-0">{{ vehicle.status|default('LIBRE')|upper }}</h3>
                        </div>
                        <p>
                            <strong>Vehículo:</strong> {{ vehicle.plate_number|default('No asignado') }}<br>
                            <strong>Modelo:</strong> {{ vehicle.brand|default('') }} {{ vehicle.model|default('') }} ({{ vehicle.year|default('') }})<br>
                            <strong>Color:</strong> {{ vehicle.color|default('') }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-lg" onclick="updateStatus('libre')" {% if vehicle.status == 'libre' %}disabled{% endif %}>
                                <i class="bi bi-check-circle me-2"></i> Disponible
                            </button>
                            <button class="btn btn-danger btn-lg" onclick="updateStatus('ocupado')" {% if vehicle.status == 'ocupado' %}disabled{% endif %}>
                                <i class="bi bi-x-circle me-2"></i> Ocupado
                            </button>
                            <button class="btn btn-warning btn-lg" onclick="updateStatus('alerta')">
                                <i class="bi bi-exclamation-triangle me-2"></i> Alerta
                            </button>
                            <button class="btn btn-secondary btn-lg" onclick="updateStatus('fuera_de_servicio')" {% if vehicle.status == 'fuera_de_servicio' %}disabled{% endif %}>
                                <i class="bi bi-power me-2"></i> Fuera de Servicio
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mapa y Viaje Actual -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mi Ubicación</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Viaje Actual</h5>
            </div>
            <div class="card-body">
                {% if current_trip %}
                <div class="trip-card">
                    <div class="card-body">
                        <h5 class="card-title">Viaje #{{ current_trip.id }}</h5>
                        <p>
                            <strong>Estado:</strong> <span class="badge bg-primary">{{ current_trip.status }}</span><br>
                            <strong>Pasajero:</strong> {{ current_trip.passenger_name }}<br>
                            <strong>Origen:</strong> {{ current_trip.origin_address }}<br>
                            <strong>Destino:</strong> {{ current_trip.destination_address }}<br>
                            <strong>Tarifa Estimada:</strong> ${{ current_trip.estimated_fare }}
                        </p>
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('trip_details_route', trip_id=current_trip.id) }}" class="btn btn-primary">
                                <i class="bi bi-eye me-2"></i> Ver Detalles
                            </a>
                            <button class="btn btn-success" onclick="updateTripStatus('{{ current_trip.id }}', 'en_viaje')">
                                <i class="bi bi-play-circle me-2"></i> Iniciar Viaje
                            </button>
                            <button class="btn btn-info" onclick="updateTripStatus('{{ current_trip.id }}', 'completado')">
                                <i class="bi bi-check-circle me-2"></i> Completar Viaje
                            </button>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-car-front display-1 text-muted"></i>
                    <p class="mt-3">No tienes un viaje asignado actualmente.</p>
                    <p class="text-muted">Cuando recibas una asignación, aparecerá aquí.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Estadísticas y Últimos Viajes -->
<div class="row">
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Estadísticas de Hoy</h5>
            </div>
            <div class="card-body text-center">
                <div class="row">
                    <div class="col-6">
                        <i class="bi bi-geo-alt card-icon text-primary"></i>
                        <div class="stats-value">{{ stats.trips_today|default(0) }}</div>
                        <div class="stats-label">Viajes</div>
                    </div>
                    <div class="col-6">
                        <i class="bi bi-cash-coin card-icon text-success"></i>
                        <div class="stats-value">${{ stats.earnings_today|default(0) }}</div>
                        <div class="stats-label">Ganancias</div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-6">
                        <i class="bi bi-clock card-icon text-info"></i>
                        <div class="stats-value">{{ stats.hours_online|default(0) }}</div>
                        <div class="stats-label">Horas Online</div>
                    </div>
                    <div class="col-6">
                        <i class="bi bi-star card-icon text-warning"></i>
                        <div class="stats-value">{{ stats.rating|default(0) }}</div>
                        <div class="stats-label">Calificación</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Últimos Viajes</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Fecha</th>
                                <th>Origen</th>
                                <th>Destino</th>
                                <th>Tarifa</th>
                                <th>Calificación</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for trip in recent_trips|default([]) %}
                            <tr>
                                <td>{{ trip.id }}</td>
                                <td>{{ trip.completed_at|default('') }}</td>
                                <td>{{ trip.origin_address|truncate(15) }}</td>
                                <td>{{ trip.destination_address|truncate(15) }}</td>
                                <td>${{ trip.fare|default(trip.estimated_fare) }}</td>
                                <td>
                                    {% if trip.passenger_rating_for_driver %}
                                    <span class="text-warning">
                                        {% for i in range(trip.passenger_rating_for_driver) %}
                                        <i class="bi bi-star-fill"></i>
                                        {% endfor %}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">Sin calificar</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center">No hay viajes recientes</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 15); // Buenos Aires como ejemplo
    let currentPositionMarker = null;
    let watchId = null;

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Función para actualizar el estado del conductor
    async function updateStatus(status) {
        try {
            const response = await fetchWithAuth('/api/v1/vehicles/{{ vehicle.id|default(0) }}/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status: status })
            });

            if (response.ok) {
                // Recargar la página para mostrar el nuevo estado
                window.location.reload();
            } else {
                alert('Error al actualizar el estado');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error de red al actualizar el estado');
        }
    }

    // Función para actualizar el estado del viaje
    async function updateTripStatus(tripId, status) {
        try {
            const response = await fetchWithAuth(`/api/v1/trips/${tripId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status: status })
            });

            if (response.ok) {
                // Recargar la página para mostrar el nuevo estado
                window.location.reload();
            } else {
                alert('Error al actualizar el estado del viaje');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error de red al actualizar el estado del viaje');
        }
    }

    // Función para actualizar la ubicación
    async function updateLocation(latitude, longitude) {
        try {
            const response = await fetchWithAuth('/api/v1/vehicles/{{ vehicle.id|default(0) }}/location', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    latitude: latitude.toString(), 
                    longitude: longitude.toString() 
                })
            });

            if (!response.ok) {
                console.error('Error al actualizar ubicación:', await response.text());
            }
        } catch (error) {
            console.error('Error de red al actualizar ubicación:', error);
        }
    }

    // Función para obtener y mostrar la ubicación actual
    function showCurrentPosition(position) {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;
        
        // Actualizar el mapa
        map.setView([latitude, longitude], 15);
        
        // Actualizar o crear el marcador
        if (currentPositionMarker) {
            currentPositionMarker.setLatLng([latitude, longitude]);
        } else {
            currentPositionMarker = L.marker([latitude, longitude]).addTo(map);
            currentPositionMarker.bindPopup("Tu ubicación actual").openPopup();
        }
        
        // Enviar la ubicación al servidor
        updateLocation(latitude, longitude);
    }

    // Función para manejar errores de geolocalización
    function handleLocationError(error) {
        let errorMessage;
        switch(error.code) {
            case error.PERMISSION_DENIED:
                errorMessage = "Usuario denegó la solicitud de geolocalización.";
                break;
            case error.POSITION_UNAVAILABLE:
                errorMessage = "Información de ubicación no disponible.";
                break;
            case error.TIMEOUT:
                errorMessage = "Tiempo de espera agotado para obtener la ubicación.";
                break;
            case error.UNKNOWN_ERROR:
                errorMessage = "Error desconocido al obtener la ubicación.";
                break;
        }
        console.error('Error de geolocalización:', errorMessage);
        alert('Error: ' + errorMessage);
    }

    // Iniciar seguimiento de ubicación
    if (navigator.geolocation) {
        // Obtener ubicación inicial
        navigator.geolocation.getCurrentPosition(showCurrentPosition, handleLocationError);
        
        // Iniciar seguimiento continuo
        watchId = navigator.geolocation.watchPosition(showCurrentPosition, handleLocationError, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
        });
    } else {
        alert("La geolocalización no es soportada por este navegador.");
    }

    // Limpiar el seguimiento al cerrar la página
    window.addEventListener('beforeunload', function() {
        if (watchId !== null) {
            navigator.geolocation.clearWatch(watchId);
        }
    });
</script>
{% endblock %}
