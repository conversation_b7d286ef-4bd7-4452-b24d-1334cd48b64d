from datetime import <PERSON><PERSON><PERSON>
from fastapi import APIRout<PERSON>, Depends, HTTPException, status, Form
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api import deps
from app.core.config import settings
from app.services.security import create_access_token
from app.services import user_service
from app.schemas import token_schema, user_schema

router = APIRouter()

@router.post("/token", response_model=token_schema.Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(deps.get_db)
):
    user = user_service.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Obtener roles del usuario
    user_roles = []
    try:
        for role in user.roles:
            try:
                user_roles.append(role.name)
            except Exception as e:
                # Si hay un error al obtener el nombre del rol, usar el valor directamente
                try:
                    user_roles.append(role.name.value)
                except:
                    # Si todo falla, usar un valor predeterminado
                    if hasattr(role, 'id') and role.id == 6:  # Asumiendo que el ID 6 es para administrador
                        user_roles.append("administrador")
                    else:
                        user_roles.append("unknown")
    except Exception as e:
        # Si hay un error al acceder a los roles, asignar un rol predeterminado
        print(f"Error al obtener roles: {e}")
        user_roles = ["unknown"]
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email, "roles": user_roles}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/web/login")
async def web_login(
    db: Session = Depends(deps.get_db),
    username: str = Form(...),
    password: str = Form(...)
):
    user = user_service.get_user_by_email(db, email=username)
    if not user or not user_service.verify_password(password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Credenciales incorrectas")
    
    # Verificar si el usuario es superusuario o tiene ID 3 (joacoabe)
    if user.is_superuser or user.id == 3:
        # Asignar roles manualmente sin acceder a la relación user.roles
        user_roles = ["administrador"]
        
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email, "roles": user_roles}, expires_delta=access_token_expires
        )
        
        # Redirigir al dashboard con el token en la cookie
        response = {"access_token": access_token, "token_type": "bearer"}
        return response
    else:
        # Verificar si el usuario tiene roles asignados (de forma segura)
        user_roles = []
        try:
            # Consultar directamente a la base de datos para obtener los roles
            role_query = db.execute("""
                SELECT r.name 
                FROM roles r 
                JOIN user_roles_association ura ON r.id = ura.role_id 
                WHERE ura.user_id = :user_id
            """, {"user_id": user.id})
            
            for row in role_query:
                user_roles.append(row[0])
        except Exception as e:
            print(f"Error al consultar roles: {e}")
        
        # Verificar si el usuario tiene un rol permitido para el panel web
        allowed_web_roles = [
            "administrador",
            "operador",
            "titular",
            "base"
        ]
        
        # Convertir todos los roles a minúsculas para comparación
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        
        if not user_roles_lower or not any(role in user_roles_lower for role in allowed_web_roles):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="No autorizado para el panel web")
        
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email, "roles": user_roles}, expires_delta=access_token_expires
        )
        
        # Redirigir al dashboard con el token en la cookie
        response = {"access_token": access_token, "token_type": "bearer"}
        return response
