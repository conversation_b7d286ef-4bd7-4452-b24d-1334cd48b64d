#!/usr/bin/env python3
"""
Script para modificar el archivo app/models/user.py y cambiar los valores del enum a MAYÚSCULAS.
"""

import os
import shutil
import re
from pathlib import Path

def fix_enum_values():
    print("Modificando los valores del enum RoleEnum a MAYÚSCULAS...")
    
    # Ruta al archivo
    # Es mejor definir la ruta base del proyecto o asumir que el script se ejecuta desde la raíz.
    # Para este ejemplo, asumiremos que la ruta relativa es correcta desde donde se ejecuta.
    file_path = Path("app/models/user.py")
    backup_file_path = file_path.with_suffix(file_path.suffix + ".bak")

    if not file_path.exists():
        print(f"Error: El archivo {file_path} no existe.")
        return

    # Hacer una copia de seguridad
    try:
        shutil.copy2(file_path, backup_file_path)
        print(f"Copia de seguridad creada: {backup_file_path}")
    except Exception as e:
        print(f"Error al crear la copia de seguridad: {e}")
        return
    
    # Leer el archivo
    with open(file_path, "r", encoding="utf-8") as file:
        content = file.read()
    
    # Patrón general para encontrar miembros del enum y convertir su valor a mayúsculas
    # Asume que el nombre del miembro del enum es el mismo que su valor, pero en mayúsculas.
    # Ejemplo: MIEMBRO = "miembro" -> MIEMBRO = "MIEMBRO"
    # (NOMBRE_ENUM\s*=\s*["'])(valor_actual)(["'])
    def replace_enum_value(match):
        return f"{match.group(1)}{match.group(1).split('=')[0].strip()}{match.group(3)}"

    content = re.sub(r'([A-Z_]+\s*=\s*["\'])([a-z_]+)(["\'])', lambda m: f"{m.group(1)}{m.group(1).split('=')[0].strip()}{m.group(3)}", content)
    
    # Guardar el archivo
    with open(file_path, "w", encoding="utf-8") as file:
        file.write(content)
    
    print(f"Archivo {file_path} modificado correctamente.")

if __name__ == "__main__":
    fix_enum_values()