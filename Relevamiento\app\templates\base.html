<!-- app/templates/base.html -->
<!doctype html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{{ title | default('GeoApp') }}{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Leaflet CSS (solo si se necesita en la página) -->
    {% block head_css %}
    {% if request.endpoint.startswith('points.') or request.endpoint == 'main.index' or request.endpoint == 'main.reportes' %}
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    {% endif %}
    {% endblock %}

    <!-- Tu CSS personalizado -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    {% block head_extra %}{% endblock %} {# Para CSS o meta tags adicionales #}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top"> {# Navbar oscura y fija arriba #}
      <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('main.index') }}">
             <!-- Icono Geo -->
             <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-geo-alt-fill me-2" viewBox="0 0 16 16">
               <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
             </svg>
            GeoApp
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
              <a class="nav-link {% if request.endpoint == 'main.index' %}active{% endif %}" aria-current="page" href="{{ url_for('main.index') }}">Mapa</a>
            </li>
            {# --- AÑADIR ESTE ENLACE --- #}
            <li class="nav-item">
              <a class="nav-link {% if request.endpoint.startswith('points.') and request.endpoint != 'points.detail' %}active{% endif %}" href="{{ url_for('points.list_all') }}">Gestionar Puntos</a>
            </li>
            {# --- FIN ENLACE AÑADIDO --- #}
            {# --- ENLACE A GESTIÓN DE CÁMARAS --- #}
            <li class="nav-item">
              <a class="nav-link {% if request.endpoint.startswith('points.list_cameras') %}active{% endif %}"
                 href="{{ url_for('points.list_cameras') }}">
                 Gestionar Cámaras
              </a>
            </li>
            {# --- FIN ENLACE --- #}
            {# --- ENLACE A GESTIÓN DE USUARIOS (Solo para administradores) --- #}
            {% if current_user.is_authenticated and current_user.role == 'administrador' %}
            <li class="nav-item">
              <a class="nav-link {% if request.endpoint.startswith('users.') %}active{% endif %}"
                 href="{{ url_for('users.list_users') }}">
                 <i class="fas fa-users"></i> Gestionar Usuarios
              </a>
            </li>
            {% endif %}
            {# --- FIN ENLACE USUARIOS --- #}
            {# --- ENLACE A AUDITORÍA (Solo para administradores) --- #}
            {% if current_user.is_authenticated and current_user.role == 'administrador' %}
            <li class="nav-item">
              <a class="nav-link {% if request.endpoint.startswith('audit.') %}active{% endif %}"
                 href="{{ url_for('audit.dashboard') }}">
                 <i class="fas fa-chart-line"></i> Auditoría
              </a>
            </li>
            {% endif %}
            {# --- FIN ENLACE AUDITORÍA --- #}
            {# --- ENLACE A REPORTES --- #}
            <li class="nav-item">
              <a class="nav-link {% if request.endpoint == 'main.reportes' %}active{% endif %}"
                 href="{{ url_for('main.reportes') }}">
                 Reportes
              </a>
            </li>
            {# --- FIN ENLACE REPORTES --- #}
            {# --- ENLACE A UBICACION DE USUARIOS --- #}
            <li class="nav-item">
              <button id="btn-mi-ubicacion" class="btn btn-outline-light btn-sm ms-2">
                <i class="bi bi-crosshair2"></i> Mi ubicación
              </button>
            </li>
            {# --- FIN ENLACE AÑADIDO --- #}
                    </ul>
          <ul class="navbar-nav">
            {% if current_user.is_authenticated %}
              <li class="nav-item dropdown">
                  <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-circle me-1" viewBox="0 0 16 16">
                      <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                      <path fill-rule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                    </svg>
                    {{ current_user.username }}
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">Cerrar Sesión</a></li>
                  </ul>
              </li>
            {% else %}
              <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'auth.login' %}active{% endif %}" href="{{ url_for('auth.login') }}">Iniciar Sesión</a>
              </li>
               <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'auth.register' %}active{% endif %}" href="{{ url_for('auth.register') }}">Registrarse</a>
              </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <main class="container mt-5 pt-3"> {# Añade margen y padding superior para compensar navbar fija #}
        {# Mostrar mensajes flash (alertas) #}
        <div id="flash-message-container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category | default('info') }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>

        {# El contenido específico de cada página irá aquí #}
        {% block content %}{% endblock %}
    </main>

    <footer class="container mt-5 py-3 text-center text-muted border-top">
         GeoApp © {% if now %}{{ now().year }}{% else %}{{ '' }}{% endif %}
         <p><small>Una aplicación Flask de ejemplo.</small></p>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Vendor JS: Leaflet, Konva -->
    {% block scripts_vendor %}
        {# Leaflet (solo si se necesita) #}
        {% if request.endpoint == 'main.index' or request.endpoint == 'points.detail' or request.endpoint == 'main.reportes' %}
            <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
        {% endif %}
        {# Konva.js (solo en la página de detalles del punto) #}
        {% if request.endpoint == 'points.detail' %}
            <script src="https://unpkg.com/konva@9.3.6/konva.min.js"></script> {# <-- KONVA.JS INCLUIDO #}
        {% endif %}
    {% endblock %}


    <!-- Tu JS personalizado -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {# Cargar el nuevo script SÓLO en la página de detalles #}
    {% if request.endpoint == 'points.detail' %}
        <script src="{{ url_for('static', filename='js/image_annotator.js') }}"></script>
        <script src="{{ url_for('static', filename='js/filter.js') }}"></script>
    {% endif %}

    {# Para JS adicional específico de cada página #}
    {% block scripts_extra %}{% endblock %}

</body>
</html>