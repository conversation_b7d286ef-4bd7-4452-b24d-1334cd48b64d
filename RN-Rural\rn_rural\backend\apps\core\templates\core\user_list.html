{# /backend/apps/core/templates/core/user_list.html #}
{% extends "core/base.html" %}

{% block title %}Gestión de Usuarios - RN-Rural{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-3">Gestión de Usuarios</h2>
    <hr>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card card-dashboard">
        <div class="card-header card-header-custom bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-users"></i> Lista de Usuarios</h4>
                <a href="{% url 'user_add' %}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus"></i> Nuevo Usuario
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col"><i class="fas fa-user"></i> Usuario</th>
                            <th scope="col"><i class="fas fa-user-tag"></i> Rol</th>
                            <th scope="col"><i class="fas fa-toggle-on"></i> Activo</th>
                            <th scope="col"><i class="fas fa-cogs"></i> Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for u in users %}
                        <tr>
                            <td>{{ u.id }}</td>
                            <td>{{ u.username }}</td>
                            <td>
                                <span class="badge
                                    {% if u.role == 'ADMIN' %}bg-danger
                                    {% elif u.role == 'OPERADOR' %}bg-primary
                                    {% elif u.role == 'BRIGADA' %}bg-warning
                                    {% else %}bg-info{% endif %}">
                                    {{ u.get_role_display|default:u.role }}
                                </span>
                            </td>
                            <td>
                                {% if u.is_active %}
                                    <span class="badge bg-success">Activo</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactivo</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'user_edit' u.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="{% url 'user_delete' u.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i> Eliminar
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center py-3">
                                <i class="fas fa-info-circle"></i> No hay usuarios registrados.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
