{% extends "base_layout.html" %}

{% block title %}Perfil de Usuario - Sistema de Taxis{% endblock %}

{% block head_extra %}
<style>
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
    }
    .profile-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
    }
    .profile-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        margin-right: 20px;
    }
    .profile-info {
        flex: 1;
    }
    .profile-section {
        margin-bottom: 30px;
    }
    .profile-section h3 {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container mt-4">
    <div class="profile-header d-flex align-items-center">
        <div class="profile-avatar">
            <i class="bi bi-person"></i>
        </div>
        <div class="profile-info">
            <h2>{{ user.full_name }}</h2>
            <p class="text-muted">{{ user.email }}</p>
            <div class="badge bg-primary">{{ user.roles[0].name.value if user.roles else 'Sin rol' }}</div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card profile-section">
                <div class="card-header bg-light">
                    <h3 class="mb-0">Información Personal</h3>
                </div>
                <div class="card-body">
                    <form id="profile-form">
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Nombre Completo</label>
                            <input type="text" class="form-control" id="full_name" value="{{ user.full_name }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Correo Electrónico</label>
                            <input type="email" class="form-control" id="email" value="{{ user.email }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="phone_number" class="form-label">Teléfono</label>
                            <input type="tel" class="form-control" id="phone_number" value="{{ user.phone_number }}">
                        </div>
                        <button type="submit" class="btn btn-primary">Guardar Cambios</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card profile-section">
                <div class="card-header bg-light">
                    <h3 class="mb-0">Cambiar Contraseña</h3>
                </div>
                <div class="card-body">
                    <form id="password-form">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Contraseña Actual</label>
                            <input type="password" class="form-control" id="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">Nueva Contraseña</label>
                            <input type="password" class="form-control" id="new_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmar Contraseña</label>
                            <input type="password" class="form-control" id="confirm_password" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Cambiar Contraseña</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
    document.getElementById('profile-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const userData = {
            full_name: document.getElementById('full_name').value,
            phone_number: document.getElementById('phone_number').value
        };
        
        try {
            const response = await fetchWithAuth('/api/v1/users/me', {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
            
            if (response.ok) {
                alert('Perfil actualizado correctamente');
            } else {
                const error = await response.json();
                alert('Error: ' + (error.detail || 'No se pudo actualizar el perfil'));
            }
        } catch (error) {
            alert('Error de red: ' + error.message);
        }
    });
    
    document.getElementById('password-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (newPassword !== confirmPassword) {
            alert('Las contraseñas no coinciden');
            return;
        }
        
        const passwordData = {
            current_password: document.getElementById('current_password').value,
            new_password: newPassword
        };
        
        try {
            const response = await fetchWithAuth('/api/v1/users/me/password', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(passwordData)
            });
            
            if (response.ok) {
                alert('Contraseña actualizada correctamente');
                document.getElementById('password-form').reset();
            } else {
                const error = await response.json();
                alert('Error: ' + (error.detail || 'No se pudo actualizar la contraseña'));
            }
        } catch (error) {
            alert('Error de red: ' + error.message);
        }
    });
</script>
{% endblock %}
