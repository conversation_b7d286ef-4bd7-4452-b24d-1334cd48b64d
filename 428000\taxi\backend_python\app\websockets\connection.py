import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
import redis.asyncio as redis
from fastapi import WebSocket, WebSocketDisconnect

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Obtener configuración de Redis desde variables de entorno o usar valores predeterminados
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))

# Conexión a Redis
redis_url = f"redis://{REDIS_HOST}:{REDIS_PORT}"
redis_connection_pool = None

# Diccionario para almacenar conexiones WebSocket activas
active_connections: Dict[str, List[WebSocket]] = {}

async def get_redis_connection():
    """Obtener una conexión a Redis."""
    global redis_connection_pool
    if redis_connection_pool is None:
        try:
            redis_connection_pool = await redis.from_url(redis_url, decode_responses=True)
            logger.info(f"Conexión a Redis establecida en {redis_url}")
        except Exception as e:
            logger.error(f"Error al conectar a Redis: {e}")
            # Usar un diccionario en memoria como fallback
            redis_connection_pool = None
            return None
    return redis_connection_pool

async def close_redis_connection():
    """Cerrar la conexión a Redis."""
    global redis_connection_pool
    if redis_connection_pool is not None:
        await redis_connection_pool.close()
        redis_connection_pool = None
        logger.info("Conexión a Redis cerrada")

class ConnectionManager:
    """Gestor de conexiones WebSocket."""

    def __init__(self):
        # Conexiones activas por grupo (por ejemplo, por vehículo o por usuario)
        self.active_connections: Dict[str, List[WebSocket]] = {}
        # Set para mantener un registro de todos los grupos
        self.groups: Set[str] = set()

    async def connect(self, websocket: WebSocket, group: str):
        """Conectar un cliente WebSocket a un grupo específico."""
        await websocket.accept()
        if group not in self.active_connections:
            self.active_connections[group] = []
            self.groups.add(group)
        self.active_connections[group].append(websocket)
        logger.info(f"Cliente conectado al grupo {group}. Total en grupo: {len(self.active_connections[group])}")

    def disconnect(self, websocket: WebSocket, group: str):
        """Desconectar un cliente WebSocket de un grupo específico."""
        if group in self.active_connections:
            if websocket in self.active_connections[group]:
                self.active_connections[group].remove(websocket)
                logger.info(f"Cliente desconectado del grupo {group}. Restantes en grupo: {len(self.active_connections[group])}")

            # Si no quedan conexiones en el grupo, eliminar el grupo
            if not self.active_connections[group]:
                del self.active_connections[group]
                self.groups.remove(group)
                logger.info(f"Grupo {group} eliminado por falta de conexiones")

    async def broadcast_to_group(self, group: str, message: Dict[str, Any]):
        """Enviar un mensaje a todos los clientes en un grupo específico."""
        if group in self.active_connections:
            disconnected_websockets = []
            for websocket in self.active_connections[group]:
                try:
                    await websocket.send_json(message)
                except Exception as e:
                    logger.error(f"Error al enviar mensaje a cliente en grupo {group}: {e}")
                    disconnected_websockets.append(websocket)

            # Eliminar conexiones desconectadas
            for websocket in disconnected_websockets:
                self.disconnect(websocket, group)

    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Enviar un mensaje a todos los clientes conectados."""
        for group in list(self.active_connections.keys()):
            await self.broadcast_to_group(group, message)

    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Enviar un mensaje a un cliente específico."""
        try:
            await websocket.send_json(message)
        except Exception as e:
            logger.error(f"Error al enviar mensaje personal: {e}")
            # No desconectamos aquí, ya que podría ser manejado en otro lugar

# Crear una instancia del gestor de conexiones
connection_manager = ConnectionManager()
