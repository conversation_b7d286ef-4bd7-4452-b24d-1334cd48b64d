# 5_notificaciones_automaticas.py
# Sistema de Notificaciones Automáticas para Intranet IMPA

"""
NOTIFICACIONES AUTOMÁTICAS
==========================

Funcionalidades:
- Notificaciones por email, SMS y push web
- Recordatorios automáticos de eventos
- Alertas de cumpleaños y aniversarios
- Notificaciones de cambios importantes
- Plantillas personalizables
- Programación de envíos
- Estadísticas de entrega
- Integración con WhatsApp Business

Inspirado en Software Redil pero adaptado para corporaciones de iglesias.
"""

from datetime import datetime, date, timedelta
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Date, Enum, JSON
from sqlalchemy.orm import relationship
from app import db
import json
from enum import Enum as PyEnum

# ============================================================================
# 1. ENUMS Y CONSTANTES
# ============================================================================

class NotificationType(PyEnum):
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WHATSAPP = "whatsapp"
    IN_APP = "in_app"

class NotificationStatus(PyEnum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    CANCELLED = "cancelled"

class NotificationPriority(PyEnum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

# ============================================================================
# 2. MODELOS DE BASE DE DATOS
# ============================================================================

class NotificationTemplate(db.Model):
    """Plantillas de notificaciones"""
    __tablename__ = 'notification_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    template_type = db.Column(db.Enum(NotificationType), nullable=False)
    category = db.Column(db.String(100))  # birthday, event_reminder, announcement, etc.
    
    # Contenido de la plantilla
    subject = db.Column(db.String(500))  # Para email/SMS
    body_template = db.Column(db.Text, nullable=False)  # Plantilla con variables
    html_template = db.Column(db.Text)  # Para emails HTML
    
    # Variables disponibles
    available_variables = db.Column(db.JSON)  # Lista de variables que se pueden usar
    
    # Configuración
    is_active = db.Column(db.Boolean, default=True)
    is_system = db.Column(db.Boolean, default=False)  # Plantillas del sistema
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=True)
    
    # Metadatos
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_modified = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    church = db.relationship('Church', backref='notification_templates')
    created_by = db.relationship('User', backref='created_templates')
    notifications = db.relationship('Notification', backref='template')
    
    def __repr__(self):
        return f"<NotificationTemplate(name='{self.name}', type='{self.template_type}')>"

class Notification(db.Model):
    """Notificaciones individuales"""
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Destinatario
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_email = db.Column(db.String(255))
    recipient_phone = db.Column(db.String(20))
    
    # Tipo y contenido
    notification_type = db.Column(db.Enum(NotificationType), nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('notification_templates.id'), nullable=True)
    
    subject = db.Column(db.String(500))
    message = db.Column(db.Text, nullable=False)
    html_content = db.Column(db.Text)  # Para emails HTML
    
    # Programación y estado
    scheduled_at = db.Column(db.DateTime, nullable=False)
    sent_at = db.Column(db.DateTime)
    delivered_at = db.Column(db.DateTime)
    status = db.Column(db.Enum(NotificationStatus), default=NotificationStatus.PENDING)
    priority = db.Column(db.Enum(NotificationPriority), default=NotificationPriority.NORMAL)
    
    # Metadatos
    attempts = db.Column(db.Integer, default=0)
    max_attempts = db.Column(db.Integer, default=3)
    error_message = db.Column(db.Text)
    external_id = db.Column(db.String(255))  # ID del proveedor externo
    
    # Contexto
    context_type = db.Column(db.String(50))  # event, birthday, announcement, etc.
    context_id = db.Column(db.Integer)  # ID del objeto relacionado
    context_data = db.Column(db.JSON)  # Datos adicionales del contexto
    
    # Origen
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    recipient = db.relationship('User', foreign_keys=[recipient_id], backref='received_notifications')
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='sent_notifications')
    
    def __repr__(self):
        return f"<Notification(type='{self.notification_type}', recipient='{self.recipient.email}')>"

class NotificationRule(db.Model):
    """Reglas automáticas de notificación"""
    __tablename__ = 'notification_rules'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    
    # Trigger (disparador)
    trigger_type = db.Column(db.String(50), nullable=False)  # birthday, event, anniversary, etc.
    trigger_conditions = db.Column(db.JSON)  # Condiciones específicas
    
    # Timing
    days_before = db.Column(db.Integer, default=0)  # Días antes del evento
    time_of_day = db.Column(db.String(5), default='09:00')  # Hora de envío
    
    # Plantilla y tipo
    template_id = db.Column(db.Integer, db.ForeignKey('notification_templates.id'), nullable=False)
    notification_types = db.Column(db.JSON)  # Lista de tipos a enviar
    
    # Alcance
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=True)
    target_roles = db.Column(db.JSON)  # Roles objetivo
    target_groups = db.Column(db.JSON)  # Grupos específicos
    
    # Estado
    is_active = db.Column(db.Boolean, default=True)
    last_executed = db.Column(db.DateTime)
    
    # Metadatos
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    template = db.relationship('NotificationTemplate', backref='rules')
    church = db.relationship('Church', backref='notification_rules')
    created_by = db.relationship('User', backref='created_rules')
    
    def __repr__(self):
        return f"<NotificationRule(name='{self.name}', trigger='{self.trigger_type}')>"

class NotificationLog(db.Model):
    """Log de actividad de notificaciones"""
    __tablename__ = 'notification_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    notification_id = db.Column(db.Integer, db.ForeignKey('notifications.id'), nullable=False)
    
    action = db.Column(db.String(50), nullable=False)  # created, sent, delivered, failed, etc.
    details = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    notification = db.relationship('Notification', backref='logs')

# ============================================================================
# 3. SERVICIOS DE NOTIFICACIÓN
# ============================================================================

class NotificationService:
    """Servicio principal de notificaciones"""
    
    @staticmethod
    def create_notification(recipient_id, notification_type, message, subject=None, 
                          scheduled_at=None, template_id=None, context_type=None, 
                          context_id=None, priority=NotificationPriority.NORMAL, **kwargs):
        """Crear nueva notificación"""
        
        if not scheduled_at:
            scheduled_at = datetime.utcnow()
        
        # Obtener datos del destinatario
        from app.models import User
        recipient = User.query.get(recipient_id)
        if not recipient:
            raise ValueError("Destinatario no encontrado")
        
        notification = Notification(
            recipient_id=recipient_id,
            recipient_email=recipient.email,
            recipient_phone=getattr(recipient, 'phone', None),
            notification_type=notification_type,
            template_id=template_id,
            subject=subject,
            message=message,
            scheduled_at=scheduled_at,
            priority=priority,
            context_type=context_type,
            context_id=context_id,
            **kwargs
        )
        
        db.session.add(notification)
        db.session.commit()
        
        # Log de creación
        NotificationService._log_action(notification.id, 'created', 'Notificación creada')
        
        return notification
    
    @staticmethod
    def send_notification(notification_id):
        """Enviar notificación individual"""
        notification = Notification.query.get(notification_id)
        if not notification:
            return False
        
        if notification.status != NotificationStatus.PENDING:
            return False
        
        try:
            success = False
            
            if notification.notification_type == NotificationType.EMAIL:
                success = EmailService.send_email(notification)
            elif notification.notification_type == NotificationType.SMS:
                success = SMSService.send_sms(notification)
            elif notification.notification_type == NotificationType.PUSH:
                success = PushService.send_push(notification)
            elif notification.notification_type == NotificationType.WHATSAPP:
                success = WhatsAppService.send_whatsapp(notification)
            elif notification.notification_type == NotificationType.IN_APP:
                success = InAppService.send_in_app(notification)
            
            if success:
                notification.status = NotificationStatus.SENT
                notification.sent_at = datetime.utcnow()
                NotificationService._log_action(notification.id, 'sent', 'Notificación enviada')
            else:
                notification.attempts += 1
                if notification.attempts >= notification.max_attempts:
                    notification.status = NotificationStatus.FAILED
                    NotificationService._log_action(notification.id, 'failed', 'Máximo de intentos alcanzado')
                else:
                    # Reprogramar para reintento
                    notification.scheduled_at = datetime.utcnow() + timedelta(minutes=30)
                    NotificationService._log_action(notification.id, 'retry_scheduled', 'Reintento programado')
            
            db.session.commit()
            return success
            
        except Exception as e:
            notification.attempts += 1
            notification.error_message = str(e)
            
            if notification.attempts >= notification.max_attempts:
                notification.status = NotificationStatus.FAILED
            
            db.session.commit()
            NotificationService._log_action(notification.id, 'error', f'Error: {str(e)}')
            return False
    
    @staticmethod
    def process_pending_notifications():
        """Procesar notificaciones pendientes"""
        pending = Notification.query.filter(
            Notification.status == NotificationStatus.PENDING,
            Notification.scheduled_at <= datetime.utcnow()
        ).order_by(Notification.priority.desc(), Notification.scheduled_at).all()
        
        processed = 0
        for notification in pending:
            if NotificationService.send_notification(notification.id):
                processed += 1
        
        return processed
    
    @staticmethod
    def create_from_template(template_id, recipient_id, variables=None, **kwargs):
        """Crear notificación desde plantilla"""
        template = NotificationTemplate.query.get(template_id)
        if not template:
            raise ValueError("Plantilla no encontrada")
        
        # Procesar variables en la plantilla
        message = template.body_template
        subject = template.subject
        
        if variables:
            for key, value in variables.items():
                message = message.replace(f"{{{key}}}", str(value))
                if subject:
                    subject = subject.replace(f"{{{key}}}", str(value))
        
        return NotificationService.create_notification(
            recipient_id=recipient_id,
            notification_type=template.template_type,
            message=message,
            subject=subject,
            template_id=template_id,
            **kwargs
        )
    
    @staticmethod
    def _log_action(notification_id, action, details):
        """Registrar acción en log"""
        log = NotificationLog(
            notification_id=notification_id,
            action=action,
            details=details
        )
        db.session.add(log)

# ============================================================================
# 4. SERVICIOS ESPECÍFICOS POR CANAL
# ============================================================================

class EmailService:
    """Servicio de envío de emails"""
    
    @staticmethod
    def send_email(notification):
        """Enviar email"""
        try:
            from flask_mail import Message
            from app import mail  # Asumir que Flask-Mail está configurado
            
            msg = Message(
                subject=notification.subject or "Notificación",
                recipients=[notification.recipient_email],
                body=notification.message,
                html=notification.html_content
            )
            
            mail.send(msg)
            return True
            
        except Exception as e:
            print(f"Error enviando email: {e}")
            return False

class SMSService:
    """Servicio de envío de SMS"""
    
    @staticmethod
    def send_sms(notification):
        """Enviar SMS usando Twilio o similar"""
        try:
            # Implementar integración con Twilio
            # from twilio.rest import Client
            # client = Client(account_sid, auth_token)
            # message = client.messages.create(
            #     body=notification.message,
            #     from_='+**********',
            #     to=notification.recipient_phone
            # )
            # notification.external_id = message.sid
            return True
            
        except Exception as e:
            print(f"Error enviando SMS: {e}")
            return False

class WhatsAppService:
    """Servicio de WhatsApp Business"""
    
    @staticmethod
    def send_whatsapp(notification):
        """Enviar mensaje por WhatsApp"""
        try:
            # Implementar integración con WhatsApp Business API
            return True
            
        except Exception as e:
            print(f"Error enviando WhatsApp: {e}")
            return False

class PushService:
    """Servicio de notificaciones push web"""
    
    @staticmethod
    def send_push(notification):
        """Enviar notificación push"""
        try:
            # Implementar Web Push notifications
            return True
            
        except Exception as e:
            print(f"Error enviando push: {e}")
            return False

class InAppService:
    """Servicio de notificaciones in-app"""
    
    @staticmethod
    def send_in_app(notification):
        """Crear notificación in-app"""
        try:
            # Las notificaciones in-app se marcan como enviadas inmediatamente
            # ya que se muestran en la interfaz web
            return True
            
        except Exception as e:
            print(f"Error creando notificación in-app: {e}")
            return False

# ============================================================================
# 5. AUTOMATIZACIONES
# ============================================================================

class NotificationAutomation:
    """Automatizaciones de notificaciones"""
    
    @staticmethod
    def process_birthday_notifications():
        """Procesar notificaciones de cumpleaños"""
        from app.models import User
        
        # Obtener cumpleaños de mañana
        tomorrow = date.today() + timedelta(days=1)
        
        birthday_users = User.query.filter(
            db.extract('month', User.date_of_birth) == tomorrow.month,
            db.extract('day', User.date_of_birth) == tomorrow.day
        ).all()
        
        # Buscar regla de cumpleaños
        birthday_rule = NotificationRule.query.filter_by(
            trigger_type='birthday',
            is_active=True
        ).first()
        
        if not birthday_rule:
            return 0
        
        notifications_created = 0
        
        for user in birthday_users:
            # Obtener destinatarios (pastores, líderes, etc.)
            recipients = NotificationAutomation._get_rule_recipients(birthday_rule, user.church_id)
            
            for recipient in recipients:
                variables = {
                    'user_name': f"{user.first_name} {user.last_name}",
                    'birthday_date': tomorrow.strftime('%d/%m/%Y'),
                    'age': NotificationAutomation._calculate_age(user.date_of_birth, tomorrow)
                }
                
                NotificationService.create_from_template(
                    template_id=birthday_rule.template_id,
                    recipient_id=recipient.id,
                    variables=variables,
                    context_type='birthday',
                    context_id=user.id,
                    scheduled_at=datetime.combine(tomorrow, datetime.strptime(birthday_rule.time_of_day, '%H:%M').time())
                )
                notifications_created += 1
        
        return notifications_created
    
    @staticmethod
    def process_event_reminders():
        """Procesar recordatorios de eventos"""
        from app.models import CalendarEvent
        
        # Obtener reglas de recordatorio de eventos
        event_rules = NotificationRule.query.filter_by(
            trigger_type='event_reminder',
            is_active=True
        ).all()
        
        notifications_created = 0
        
        for rule in event_rules:
            # Calcular fecha objetivo
            target_date = date.today() + timedelta(days=rule.days_before)
            
            # Obtener eventos para esa fecha
            events = CalendarEvent.query.filter(
                CalendarEvent.event_date == target_date
            ).all()
            
            for event in events:
                recipients = NotificationAutomation._get_rule_recipients(rule, event.user.church_id)
                
                for recipient in recipients:
                    variables = {
                        'event_title': event.title,
                        'event_date': event.event_date.strftime('%d/%m/%Y'),
                        'event_time': event.event_time.strftime('%H:%M') if event.event_time else 'No especificada',
                        'days_until': rule.days_before
                    }
                    
                    NotificationService.create_from_template(
                        template_id=rule.template_id,
                        recipient_id=recipient.id,
                        variables=variables,
                        context_type='event_reminder',
                        context_id=event.id,
                        scheduled_at=datetime.combine(date.today(), datetime.strptime(rule.time_of_day, '%H:%M').time())
                    )
                    notifications_created += 1
        
        return notifications_created
    
    @staticmethod
    def _get_rule_recipients(rule, church_id):
        """Obtener destinatarios según regla"""
        from app.models import User
        
        query = User.query.filter_by(is_active=True)
        
        # Filtrar por iglesia si es específica
        if rule.church_id:
            query = query.filter_by(church_id=rule.church_id)
        elif church_id:
            query = query.filter_by(church_id=church_id)
        
        # Filtrar por roles
        if rule.target_roles:
            query = query.filter(User.role.in_(rule.target_roles))
        
        return query.all()
    
    @staticmethod
    def _calculate_age(birth_date, reference_date):
        """Calcular edad"""
        return reference_date.year - birth_date.year - ((reference_date.month, reference_date.day) < (birth_date.month, birth_date.day))

# ============================================================================
# 6. TASK SCHEDULER
# ============================================================================

class NotificationScheduler:
    """Programador de tareas de notificaciones"""
    
    @staticmethod
    def run_daily_tasks():
        """Ejecutar tareas diarias"""
        print("Ejecutando tareas diarias de notificaciones...")
        
        # Procesar cumpleaños
        birthday_count = NotificationAutomation.process_birthday_notifications()
        print(f"Notificaciones de cumpleaños creadas: {birthday_count}")
        
        # Procesar recordatorios de eventos
        event_count = NotificationAutomation.process_event_reminders()
        print(f"Recordatorios de eventos creados: {event_count}")
        
        # Procesar notificaciones pendientes
        processed = NotificationService.process_pending_notifications()
        print(f"Notificaciones procesadas: {processed}")
        
        return {
            'birthday_notifications': birthday_count,
            'event_reminders': event_count,
            'processed_notifications': processed
        }

print("Sistema de Notificaciones Automáticas - Listo para implementar")
print("Dependencias adicionales:")
print("- Flask-Mail (para emails)")
print("- Twilio (para SMS)")
print("- Celery (para tareas asíncronas)")
print("- APScheduler (para programación de tareas)")
