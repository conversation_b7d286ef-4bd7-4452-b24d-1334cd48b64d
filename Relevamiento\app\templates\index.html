<!-- app/templates/index.html -->
{% extends "base.html" %}

{% block title %}Mapa Principal - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Mapa de Puntos</h1>
</div>

<div id="map"
 data-points='{{ points_data|default([])|tojson }}'
 data-center='{{ map_center|default([40.4167, -3.70325])|tojson }}'
 data-zoom='{{ map_zoom|default(6) }}'>
    <div class="text-center p-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Cargando mapa...</span>
        </div>
        <p class="mt-2">Cargando mapa...</p>
    </div>
</div>

<div class="mt-3 mb-3">
    <h5>Leyenda de Estados:</h5>
    <span class="badge text-bg-primary me-1"><span class="status-indicator status-azul me-1"></span> Por Hacer</span>
    <span class="badge text-bg-warning me-1"><span class="status-indicator status-amarillo me-1"></span> En Curso</span>
    <span class="badge text-bg-success me-1"><span class="status-indicator status-verde me-1"></span> Hecho</span>
    <span class="badge text-bg-danger me-1"><span class="status-indicator status-rojo me-1"></span> Incompleto</span>
    <span class="badge text-bg-secondary me-1"><span class="status-indicator status-grey me-1"></span> Otro</span>
</div>
<div class="accordion mb-3" id="filtersAccordion">

    <!-- Filtro por Origen -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingSource">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSource" aria-expanded="false" aria-controls="collapseSource">
          Filtrar por Origen
        </button>
      </h2>
      <div id="collapseSource" class="accordion-collapse collapse" aria-labelledby="headingSource" data-bs-parent="#filtersAccordion">
        <div class="accordion-body" id="filter-source">
          <div class="d-flex justify-content-end mb-2">
            <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="toggleCheckboxes('filter-source', true)">Seleccionar todo</button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleCheckboxes('filter-source', false)">Deseleccionar todo</button>
          </div>
          <div id="filter-source-checkboxes"></div>
        </div>
      </div>
    </div>

    <!-- Filtro por Ciudad -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingCity">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCity" aria-expanded="false" aria-controls="collapseCity">
          Filtrar por Ciudad
        </button>
      </h2>
      <div id="collapseCity" class="accordion-collapse collapse" aria-labelledby="headingCity" data-bs-parent="#filtersAccordion">
        <div class="accordion-body" id="filter-city">
          <div class="d-flex justify-content-end mb-2">
            <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="toggleCheckboxes('filter-city', true)">Seleccionar todo</button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleCheckboxes('filter-city', false)">Deseleccionar todo</button>
          </div>
          <div id="filter-city-checkboxes"></div>
        </div>
      </div>
    </div>

    <!-- Filtro por Tipo de Cámara -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingCameraType">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCameraType" aria-expanded="false" aria-controls="collapseCameraType">
          📹 Filtrar por Tipo de Cámara
        </button>
      </h2>
      <div id="collapseCameraType" class="accordion-collapse collapse" aria-labelledby="headingCameraType" data-bs-parent="#filtersAccordion">
        <div class="accordion-body" id="filter-camera-type">
          <div class="d-flex justify-content-end mb-2">
            <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="toggleCheckboxes('filter-camera-type', true)">Seleccionar todo</button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleCheckboxes('filter-camera-type', false)">Deseleccionar todo</button>
          </div>
          <div id="filter-camera-type-checkboxes"></div>
        </div>
      </div>
    </div>

  </div>
{% endblock %}

{% block scripts_extra %}
    <!-- Cargar script de iconos de cámaras -->
    <script src="{{ url_for('static', filename='js/camera-icons.js') }}"></script>
    {# El mapa se inicializa automáticamente desde main.js al detectar el div #map #}
{% endblock %}