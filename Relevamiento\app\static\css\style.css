/*app/static/css/style.css*/
/* Estilos generales */
body {
    padding-top: 56px; /* Ajuste para navbar fija si la usas */
    background-color: #f8f9fa; /* Un fondo suave */
}

/* Estilo para el mapa */
#map {
    height: 600px; /* Altura del contenedor del mapa */
    width: 100%;
    border: 1px solid #ccc;
    margin-bottom: 20px;
}

/* Estilos para los iconos de colores de Leaflet (usando filtros CSS) */
/* Puedes ajustar los filtros para obtener los colores exactos que deseas */
.leaflet-marker-icon.marker-blue {
    /* El azul por defecto suele estar bien, pero puedes ajustarlo */
    filter: hue-rotate(180deg) saturate(1.5);
}
.leaflet-marker-icon.marker-yellow {
    /* Combinar filtros para amarillo */
    filter: hue-rotate(0deg) saturate(3) brightness(1.4) contrast(1.1);
}
.leaflet-marker-icon.marker-green {
    filter: hue-rotate(60deg) saturate(2) brightness(0.9);
}
.leaflet-marker-icon.marker-red {
    filter: hue-rotate(300deg) saturate(2) brightness(1.1);
}
.leaflet-marker-icon.marker-grey {
    filter: grayscale(100%) opacity(0.7);
}


/* Estilos para la página de detalles del punto */
.point-detail-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

/* Contenedor de imagen para superposición del círculo */
.image-container {
    position: relative; /* Necesario para posicionar absolutamente el círculo */
    display: inline-block; /* Se ajusta al tamaño de la imagen */
    cursor: crosshair; /* Indica que se puede hacer clic para marcar */
    border: 1px solid #ddd; /* Borde sutil alrededor */
    line-height: 0; /* Evita espacio extra debajo de la imagen */
    max-width: 100%; /* Asegura que el contenedor no exceda el ancho */
    background-color: #fff; /* Fondo blanco por si la imagen es transparente */
}

.image-container img {
    max-width: 100%; /* Hacer imagen responsive dentro del contenedor */
    height: auto;
    display: block; /* Elimina espacio inferior */
}

/* El círculo rojo superpuesto */
.circle-overlay {
    position: absolute; /* Posición relativa al image-container */
    border: 2px solid red; /* Estilo del círculo (grosor y color) */
    border-radius: 50%; /* Lo hace circular */
    width: 20px; /* Diámetro del círculo (ajusta según necesites) */
    height: 20px;
    /* Centra el círculo en el punto X,Y:
       left/top definen la esquina superior izquierda,
       transform lo desplaza -50% de su propio tamaño */
    transform: translate(-50%, -50%);
    pointer-events: none; /* Permite hacer clic 'a través' del círculo si fuera necesario */
    box-sizing: border-box; /* Incluye el borde en el tamaño */
    z-index: 10; /* Asegura que esté sobre la imagen */
}

/* Indicadores de estado */
.status-indicator {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-left: 5px;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, 0.2); /* Borde sutil */
}
.status-azul { background-color: #0d6efd; } /* Azul Bootstrap */
.status-amarillo { background-color: #ffc107; border-color: #aaa; } /* Amarillo Bootstrap */
.status-verde { background-color: #198754; } /* Verde Bootstrap */
.status-rojo { background-color: #dc3545; } /* Rojo Bootstrap */
.status-grey { background-color: #6c757d; } /* Gris Bootstrap */


/* Mejoras para formularios */
form hr {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

/* Estilos para mensajes flash de Bootstrap */
.alert {
    margin-bottom: 1rem;
}

/* Opcional: Navbar fija superior */
/* .navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1030;
} */

/* Estilo para las tarjetas de imagen */
.card.image-card {
    margin-bottom: 1.5rem;
}

.card.image-card .card-body small {
    display: block;
    margin-top: 10px;
    color: #6c757d; /* Texto gris */
    font-style: italic;
}

.card.image-card .btn-danger {
    margin-top: 5px; /* Espacio sobre el botón eliminar */
}

/* Ocultar formularios que se envían con JS */
.hidden-form {
    display: none;
}

.marker-azul { filter: hue-rotate(220deg); }
.marker-amarillo { filter: hue-rotate(60deg); }
.marker-verde { filter: hue-rotate(120deg); }
.marker-rojo { filter: hue-rotate(0deg); }
.marker-gris { filter: grayscale(1); }