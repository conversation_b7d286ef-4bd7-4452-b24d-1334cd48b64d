// chat.js

import { socket, safeEmit } from './socketConnection.js';

// --- Obtener elementos del DOM ---
const messageInput = document.getElementById('chatInput');
const sendButton = document.getElementById('sendButton');
const messagesDiv = document.getElementById('messages');
const nodeName = document.body.dataset.nodeName;  // Necesario para loadMessages
const username = document.body.dataset.username; // Necesario para showNotification
const nodeId = parseInt(document.body.dataset.nodeId); //para safe
// --- Función para mostrar notificaciones (SIMPLIFICADA) ---
function showNotification(title, body, icon) {
    if (Notification.permission === "granted") { // SOLO si ya tenemos permiso
        new Notification(title, { body, icon });
    }
}

// --- Función para solicitar permiso de notificación (SE LLAMA AL HACER CLIC) ---
function requestNotificationPermission() {
    if (Notification.permission !== "granted") {
        Notification.requestPermission().then(permission => {
            if (permission === "granted") {
                showNotification("Notificaciones Activadas", "Recibirás notificaciones de mensajes.", "/static/RNCom.ico");
                notificationsButton.classList.add('active'); // Actualizar botón
            }
        });
    }
}

// --- BOTÓN DE NOTIFICACIONES (Simplificado) ---
//Ya que estamos creando este modulo, lo instanciamos desde 0.
const notificationsButton = document.createElement('button');
notificationsButton.id = 'notificationsButton';
notificationsButton.title = 'Activar/Desactivar Notificaciones';
const chatInputContainer = document.querySelector('.chat-input');
if (chatInputContainer) {
   chatInputContainer.appendChild(notificationsButton);
} else {
    document.querySelector('.container').appendChild(notificationsButton); //si no existe chatInputContainer, que lo agregue al contenedor general.
}


// --- ACTUALIZAR ESTADO DEL BOTÓN AL CARGAR ---
function updateNotificationButton() {
    if (Notification.permission === "granted") {
        notificationsButton.classList.add('active');
    } else {
        notificationsButton.classList.remove('active');
    }
}
updateNotificationButton(); // Llamar al cargar

// --- EVENT LISTENER PARA EL BOTÓN (Solicitar permiso al hacer clic) ---
notificationsButton.addEventListener('click', requestNotificationPermission);


// --- Función para enviar mensajes (chat) ---
function sendMessage() {
    const message = messageInput.value.trim();
    if (message !== '') {
        safeEmit('send_message', { data: message, user: username, node_id: nodeId });

        // Mostrar el mensaje localmente
        const newMessage = document.createElement('p');
        newMessage.className = 'message';
        const timestamp = new Date().toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
        newMessage.innerHTML = `<strong>${username}:</strong> ${message} <span class="timestamp">${timestamp}</span>`;
        messagesDiv.appendChild(newMessage);
        messagesDiv.scrollTop = messagesDiv.scrollHeight; // Auto-scroll

        messageInput.value = ''; // Limpiar el campo
    }
}

// --- Event listeners para el chat ---
sendButton.addEventListener('click', sendMessage);
messageInput.addEventListener('keypress', function (event) {
    if (event.key === 'Enter') { sendMessage(); }
});

// --- Función para cargar mensajes guardados (Mejorada) ---
function loadMessages() {
    fetch(`/get_messages/${nodeName}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            data.messages.forEach(message => {
                const newMessage = document.createElement('p');
                newMessage.className = 'message';
                // Si el mensaje contiene el marcador [FILE], se asume que ya está formateado en HTML
                if (message.indexOf('[FILE]') !== -1) {
                    // Se puede eliminar el marcador [FILE] antes de asignar el innerHTML
                    newMessage.innerHTML = message.replace('[FILE]', '');
                } else {
                    // Formateo estándar para mensajes de texto
                    const parts = message.split(': ');
                    if (parts.length >= 2) {
                        const user = parts[0];
                        const rest = parts.slice(1).join(': ');
                        const textParts = rest.split(' ');
                        const time = textParts.slice(-2).join(' ');
                        const text = textParts.slice(0, -2).join(' ');
                        newMessage.innerHTML = `<strong>${user}:</strong> ${text} <span class="timestamp">${time}</span>`;
                    } else {
                        newMessage.textContent = message;
                    }
                }
                messagesDiv.appendChild(newMessage);
            });
            messagesDiv.scrollTop = messagesDiv.scrollHeight; // Auto-scroll
        })
        .catch(error => {
            //logToFile('Error loading messages: ' + error); //logtofile se exporta desde socketConnection
            console.error('Error loading messages:', error);
        });
}

// --- Cargar mensajes al iniciar ---
loadMessages();

// --- Recibir y mostrar mensajes (chat y archivos) ---
socket.on('broadcast_message', function (data) {
    if (data.user !== username) {
        const newMessage = document.createElement('p');
        newMessage.className = 'message';
        newMessage.innerHTML = data.fileinfo
            ? `<strong>${data.user}:</strong> <a href="${data.fileinfo.filepath}" download="${data.fileinfo.filename}">Descargar archivo: ${data.fileinfo.filename}</a> <span class="timestamp">${data.timestamp || ''}</span>`
            : `<strong>${data.user}:</strong> ${data.data} <span class="timestamp">${data.timestamp}</span>`;
        showNotification(
            `Nuevo mensaje en ${nodeName}`,
            `${data.user}: ${data.fileinfo ? '[Archivo]' : data.data}`,
            "/static/RNCom.ico"
        );
        messagesDiv.appendChild(newMessage);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }
});

// --- Manejo de errores de carga de archivos ---
socket.on('file_upload_error', function(data) {
    alert(`Error al cargar archivo ${data.filename}: ${data.message}`);
});

// --- EVENTO: Recibir archivo ---
socket.on('receive_file', function(data) {
    const newMessage = document.createElement('p');
    newMessage.className = 'message';
    newMessage.innerHTML = `<strong>${data.user}</strong> envió el archivo <a href="${data.fileinfo.filepath}" download="${data.fileinfo.filename}">${data.fileinfo.filename}</a> <span class="timestamp">${data.timestamp || new Date().toLocaleTimeString()}</span>`;
    messagesDiv.appendChild(newMessage);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
});

//No exporto nada, porque este js solo se encarga de chat, no requiere ser importado por otros modulos.