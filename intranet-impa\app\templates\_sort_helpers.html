<!-- /app/templates/_sort_helpers.html -->
{% macro render_sortable_header(endpoint, column_key, display_text, current_sort_by, current_sort_dir, current_search_term) %}
    {# Determina la próxima dirección de ordenación #}
    {% set next_sort_dir = 'desc' if current_sort_by == column_key and current_sort_dir == 'asc' else 'asc' %}

    {# Construir args explícitamente #}
    {% set url_params = {
        'sort_by': column_key,
        'sort_dir': next_sort_dir,
        'page': request.args.get('page', 1, type=int)
    } %}
    {# Añadir search solo si tiene valor #}
    {% if current_search_term %}
        {% set _ = url_params.update({'search': current_search_term}) %}
    {% endif %}
    {# Ejemplo de cómo añadir otros args si fuera necesario (comentado correctamente fuera del diccionario) #}
    {#
    {% if request.view_args.get('category_id') %}
        {% set _ = url_params.update({'category_id': request.view_args.get('category_id')}) %}
    {% endif %}
    #}

    {# Construye la URL para el enlace usando los parámetros construidos #}
    {% set sort_url = url_for(endpoint, **url_params) %}

    {# Renderiza el encabezado como un enlace #}
    <th>
        <a href="{{ sort_url }}">{{ display_text }}</a>
        {# Muestra un icono si esta es la columna actualmente ordenada #}
        {% if current_sort_by == column_key %}
            {% if current_sort_dir == 'asc' %}
                <i class="fas fa-sort-up ml-1"></i> {# Icono flecha arriba #}
            {% else %}
                <i class="fas fa-sort-down ml-1"></i> {# Icono flecha abajo #}
            {% endif %}
        {% else %}
             <i class="fas fa-sort text-muted ml-1" style="opacity: 0.5;"></i> {# Icono sort genérico #}
        {% endif %}
    </th>
{% endmacro %}