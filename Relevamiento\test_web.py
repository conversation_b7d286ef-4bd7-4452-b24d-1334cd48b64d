#!/usr/bin/env python3
# --- Archivo: test_web.py ---
# Script para probar que la aplicación web funcione correctamente

import requests
import sys
import time

def test_web_app():
    """Probar que la aplicación web responda correctamente."""
    
    base_url = "https://patagoniaservers.com.ar:5006"
    
    print("🌐 Probando aplicación web...")
    print(f"📍 URL base: {base_url}")
    
    # Probar página principal
    try:
        print("\n🏠 Probando página principal...")
        response = requests.get(f"{base_url}/", verify=False, timeout=10)
        
        if response.status_code == 200:
            print("✅ Página principal responde correctamente")
        else:
            print(f"❌ Página principal error: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error conectando a página principal: {e}")
        return False
    
    # Probar página de login
    try:
        print("\n🔑 Probando página de login...")
        response = requests.get(f"{base_url}/auth/login", verify=False, timeout=10)
        
        if response.status_code == 200:
            print("✅ Página de login responde correctamente")
        else:
            print(f"❌ Página de login error: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error conectando a página de login: {e}")
        return False
    
    # Probar login con credenciales
    try:
        print("\n🔐 Probando login con credenciales...")
        
        session = requests.Session()
        
        # Obtener página de login para el token CSRF
        login_page = session.get(f"{base_url}/auth/login", verify=False)
        
        # Datos de login
        login_data = {
            'username': 'admin',
            'password': 'isaias52',
            'submit': 'Iniciar Sesión'
        }
        
        # Intentar login
        response = session.post(f"{base_url}/auth/login", data=login_data, verify=False, allow_redirects=False)
        
        if response.status_code in [302, 303]:  # Redirección después de login exitoso
            print("✅ Login exitoso")
            
            # Probar acceso a página protegida
            protected_response = session.get(f"{base_url}/points/list", verify=False)
            if protected_response.status_code == 200:
                print("✅ Acceso a página protegida exitoso")
            else:
                print(f"⚠️  Página protegida responde con: {protected_response.status_code}")
                
        else:
            print(f"❌ Login falló: {response.status_code}")
            print(f"Respuesta: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error probando login: {e}")
        return False
    
    return True

def check_app_running():
    """Verificar si la aplicación está corriendo."""
    
    try:
        response = requests.get("https://patagoniaservers.com.ar:5006/", verify=False, timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """Función principal."""
    print("🌐 Probador de Aplicación Web")
    print("=" * 40)
    
    # Verificar si la aplicación está corriendo
    if not check_app_running():
        print("❌ La aplicación no está corriendo o no responde")
        print("\n💡 Para iniciar la aplicación:")
        print("   python run.py &")
        print("\n💡 Para verificar procesos:")
        print("   ps aux | grep python")
        sys.exit(1)
    
    print("✅ La aplicación está corriendo")
    
    # Probar funcionalidades web
    if test_web_app():
        print("\n🎉 ¡Todas las pruebas web pasaron!")
        print("\n🚀 La aplicación está funcionando correctamente:")
        print("   URL: https://patagoniaservers.com.ar:5006/")
        print("   Usuario: admin")
        print("   Contraseña: isaias52")
        print("   Rol: administrador")
    else:
        print("\n❌ Algunas pruebas web fallaron")
        sys.exit(1)

if __name__ == "__main__":
    main()
