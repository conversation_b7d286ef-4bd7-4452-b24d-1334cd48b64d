# create_admin.py
import os
from getpass import getpass
from app import create_app, db, bcrypt # Asegúrate que bcrypt esté importado
from app.models import User

def create_admin_user(app):
    """Crea un usuario administrador utilizando Flask-Bcrypt para generar el hash."""
    with app.app_context():
        print("Creando usuario administrador...")
        username = input("Nombre de usuario (admin): ")

        if User.query.filter_by(username=username).first():
            print(f"Error: Ya existe un usuario con el nombre '{username}'.")
            return

        email = input("Correo electrónico: ")
        while True:
            password = getpass("Contraseña: ")
            password2 = getpass("Confirmar contraseña: ")
            if password == password2:
                break
            print("Las contraseñas no coinciden. Inténtalo de nuevo.")

        # --- CORRECCIÓN: Pedir nombre y apellido por separado ---
        first_name = input("Primer nombre: ")
        last_name = input("Apellido: ")
        # --- FIN CORRECCIÓN ---

        # Utilizamos bcrypt para generar el hash
        hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')

        # --- CORRECCIÓN: Usar first_name y last_name al crear el User ---
        admin_user = User(username=username,
                          email=email,
                          password=hashed_password,
                          first_name=first_name, # Usar first_name
                          last_name=last_name,   # Usar last_name
                          role='administrador')
        # --- FIN CORRECCIÓN ---

        db.session.add(admin_user)

        try:
            db.session.commit()
            print(f"Usuario administrador '{username}' creado exitosamente!")
        except Exception as e:
            db.session.rollback()
            print(f"Error al crear el usuario: {e}")

if __name__ == '__main__':
    # No necesitas crear la app aquí si ya la creas en el contexto
    app_instance = create_app()
    create_admin_user(app_instance) # Pasar la instancia creada