// --- Archivo: app/static/js/image_annotator.js ---

document.addEventListener('DOMContentLoaded', () => {
    console.log("Image Annotator cargado.");
    const konvaWrappers = document.querySelectorAll('.konva-container-wrapper');

    konvaWrappers.forEach(wrapper => {
        initializeAnnotatorForImage(wrapper);
    });
});

// Almacena referencias a los stages y herramientas por ID de imagen/cámara
const annotators = {};

/**
 * Inicializa todo lo necesario para el anotador de una imagen específica.
 * @param {HTMLElement} wrapper El elemento contenedor principal (konva-container-wrapper).
 */
function initializeAnnotatorForImage(wrapper) {
    const imageElement = wrapper.querySelector('img.annotated-image');
    const konvaContainer = wrapper.querySelector('.konva-container');
    // Obtener el ID de la imagen/cámara del atributo data del elemento imagen
    const imageId = imageElement ? imageElement.dataset.imageId : null; // Puede ser "11" o "cam-123"
    const toolbar = document.getElementById(`toolbar-${imageId}`);
    // El botón guardar puede tener data-image-id o data-camera-id, usaremos imageId como clave
    const saveButton = toolbar ? toolbar.querySelector('.save-annotations-button') : null;

    // Validar que todos los elementos necesarios existen
    if (!imageElement || !konvaContainer || !imageId || !toolbar || !saveButton) {
        console.error("Faltan elementos HTML para inicializar el anotador para ID:", imageId, {
            hasImage: !!imageElement,
            hasKonvaContainer: !!konvaContainer,
            hasImageId: !!imageId,
            hasToolbar: !!toolbar,
            hasSaveButton: !!saveButton
        });
        return;
    }

    // Función que se ejecuta una vez que la imagen ha cargado sus dimensiones
    const setupKonvaStage = () => {
        const width = imageElement.clientWidth;
        const height = imageElement.clientHeight;

        if (!width || !height) {
            console.warn(`Dimensiones inválidas para imagen ${imageId}. Esperando posible redimensionamiento.`);
            return;
        }

        konvaContainer.style.width = `${width}px`;
        konvaContainer.style.height = `${height}px`;

        const stage = new Konva.Stage({
            container: konvaContainer,
            width: width,
            height: height,
        });

        const layer = new Konva.Layer();
        stage.add(layer);

        annotators[imageId] = {
            stage: stage,
            layer: layer,
            currentTool: 'select',
            selectedShape: null,
            transformer: null,
            isDrawing: false,
            startX: 0,
            startY: 0,
            currentShape: null
        };

        loadAnnotations(imageId, layer, stage);
        initializeTransformer(imageId, layer, stage);
        setupStageListeners(imageId, stage, layer);
        setupToolbarListeners(imageId, toolbar, stage);
        // Pasar el imageId (que puede ser "11" o "cam-123") a setupSaveListener
        setupSaveListener(imageId, saveButton, stage);

        console.log(`Anotador inicializado para ID ${imageId} con tamaño ${width}x${height}`);
    };

    if (imageElement.complete && imageElement.naturalHeight !== 0) {
        setupKonvaStage();
    } else {
        imageElement.onload = setupKonvaStage;
        imageElement.onerror = () => {
            console.error(`Error cargando imagen ${imageId}`);
            konvaContainer.innerHTML = '<p class="text-danger">Error al cargar imagen</p>';
        }
    }
}

/**
 * Carga las anotaciones guardadas (JSON) desde el tag script y las dibuja en la capa Konva.
 * @param {string} imageId ID de la imagen ("11" o "cam-123").
 * @param {Konva.Layer} layer Capa Konva donde dibujar.
 * @param {Konva.Stage} stage Stage Konva.
 */
function loadAnnotations(imageId, layer, stage) {
    const dataElement = document.getElementById(`annotations-data-${imageId}`);
    if (!dataElement || !dataElement.textContent) {
        console.log(`[ID ${imageId}] Elemento <script> de datos no encontrado o vacío.`);
        return;
    }

    const annotationsJsonString = dataElement.textContent.trim();
    if (!annotationsJsonString || annotationsJsonString === 'null' || annotationsJsonString === '{}') {
        console.log(`[ID ${imageId}] No hay anotaciones existentes o JSON vacío.`);
        return;
    }

    try {
        console.log(`[ID ${imageId}] Intentando parsear JSON:`, annotationsJsonString);
        const parsedData = JSON.parse(annotationsJsonString);
        console.log(`[ID ${imageId}] JSON parseado OK:`, parsedData);

        if (typeof parsedData === 'object' && parsedData !== null && Array.isArray(parsedData.children)) {
            console.log(`[ID ${imageId}] 'children' es un array. Procesando ${parsedData.children.length} hijos.`);

            const shapesToLoad = parsedData.children.filter(child => child && child.className !== 'Transformer');
            console.log(`[ID ${imageId}] Formas a cargar (sin Transformer): ${shapesToLoad.length}`);

            if (shapesToLoad.length > 0) {
                let loadedCount = 0;
                shapesToLoad.forEach((nodeData, index) => {
                    console.log(`[ID ${imageId}] Procesando nodo ${index}:`, nodeData);
                    if (typeof nodeData !== 'object' || nodeData === null) {
                        console.warn(`[ID ${imageId}] Nodo ${index} inválido:`, nodeData);
                        return;
                    }

                    const shape = Konva.Node.create(nodeData);
                    if (shape) {
                        // Aplicar posición relativa si existe
                        if (nodeData.relX !== undefined && nodeData.relY !== undefined) {
                            shape.position({
                                x: nodeData.relX * stage.width(),
                                y: nodeData.relY * stage.height()
                            });
                        }

                        // Restaurar tamaño relativo según tipo
                        switch (shape.getClassName()) {
                            case 'Rect':
                                if (nodeData.relWidth !== undefined && nodeData.relHeight !== undefined) {
                                    shape.width(nodeData.relWidth * stage.width());
                                    shape.height(nodeData.relHeight * stage.height());
                                }
                                break;
                            case 'Circle':
                                if (nodeData.relRadius !== undefined) {
                                    shape.radius(nodeData.relRadius * Math.min(stage.width(), stage.height()));
                                }
                                break;
                            case 'Arrow':
                                if (Array.isArray(nodeData.relPoints)) {
                                    const absPoints = nodeData.relPoints.map((val, i) =>
                                        i % 2 === 0 ? val * stage.width() : val * stage.height()
                                    );
                                    shape.points(absPoints);
                                }
                                break;
                        }

                        shape.draggable(true);
                        layer.add(shape);
                        addShapeListeners(imageId, shape);
                        loadedCount++;
                    } else {
                         console.warn(`[ID ${imageId}] Konva.Node.create devolvió null/undefined para nodo ${index}:`, nodeData);
                    }
                });

                console.log(`[ID ${imageId}] Añadiendo ${loadedCount} formas a la capa. Redibujando...`);
                layer.draw();
                console.log(`[ID ${imageId}] Anotaciones cargadas.`);
            } else {
                 console.log(`[ID ${imageId}] No hay formas válidas para cargar (después de filtrar).`);
            }
        } else {
             console.warn(`[ID ${imageId}] La estructura del JSON parseado no contiene un array 'children' válido:`, parsedData);
        }

    } catch (e) {
        console.error(`[ID ${imageId}] Error EXCEPCIÓN parseando/cargando anotaciones:`, e, "\nContenido del script tag:", annotationsJsonString);
    }
}


/**
 * Inicializa y añade el objeto Transformer a la capa.
 * @param {string} imageId ID de la imagen ("11" o "cam-123").
 * @param {Konva.Layer} layer Capa Konva.
 * @param {Konva.Stage} stage Stage Konva.
 */
function initializeTransformer(imageId, layer, stage) {
    const transformer = new Konva.Transformer({
        nodes: [],
        keepRatio: true,
        rotateEnabled: true,
        rotationSnaps: [0, 45, 90, 135, 180, 225, 270, 315],
        borderStroke: 'dodgerblue',
        borderDash: [3, 3],
        anchorStroke: 'dodgerblue',
        anchorFill: 'white',
        anchorSize: 8,
    });
    layer.add(transformer);
    annotators[imageId].transformer = transformer;
}

/**
 * Configura los listeners del stage para manejar el dibujo y la selección.
 * @param {string} imageId ID de la imagen ("11" o "cam-123").
 * @param {Konva.Stage} stage Stage Konva.
 * @param {Konva.Layer} layer Capa Konva.
 */
function setupStageListeners(imageId, stage, layer) {
    const annotator = annotators[imageId];

    stage.on('mousedown touchstart', (e) => {
        const tool = annotator.currentTool;
        if (e.target !== stage || tool === 'select') {
            if (tool === 'select' && e.target === stage) {
                annotator.transformer.nodes([]);
                annotator.selectedShape = null;
                layer.draw();
            }
            return;
        }

        annotator.isDrawing = true;
        const pos = stage.getPointerPosition();
        annotator.startX = pos.x;
        annotator.startY = pos.y;

        let newShape;
        const strokeColor = 'red';
        const strokeWidth = 2;

        switch (tool) {
            case 'circle':
                newShape = new Konva.Circle({
                    x: annotator.startX, y: annotator.startY, radius: 0,
                    stroke: strokeColor, strokeWidth: strokeWidth,
                    name: 'annotation shape'
                });
                break;
            case 'rect':
                newShape = new Konva.Rect({
                    x: annotator.startX, y: annotator.startY, width: 0, height: 0,
                    stroke: strokeColor, strokeWidth: strokeWidth,
                    name: 'annotation shape'
                });
                break;
            case 'arrow':
                newShape = new Konva.Arrow({
                    points: [annotator.startX, annotator.startY, annotator.startX, annotator.startY],
                    pointerLength: 10, pointerWidth: 10,
                    fill: strokeColor, stroke: strokeColor, strokeWidth: strokeWidth,
                    name: 'annotation shape'
                });
                break;
            default:
                annotator.isDrawing = false;
                return;
        }

        annotator.currentShape = newShape;
        layer.add(newShape);
    });

    stage.on('mousemove touchmove', () => {
        if (!annotator.isDrawing || !annotator.currentShape) return;

        const pos = stage.getPointerPosition();
        const shape = annotator.currentShape;
        const tool = annotator.currentTool;

        switch (tool) {
            case 'circle':
                const dx = pos.x - annotator.startX;
                const dy = pos.y - annotator.startY;
                shape.radius(Math.sqrt(dx * dx + dy * dy));
                break;
            case 'rect':
                shape.width(pos.x - annotator.startX);
                shape.height(pos.y - annotator.startY);
                break;
            case 'arrow':
                shape.points([annotator.startX, annotator.startY, pos.x, pos.y]);
                break;
        }
        layer.batchDraw();
    });

    stage.on('mouseup touchend', () => {
        if (!annotator.isDrawing || !annotator.currentShape) {
            annotator.isDrawing = false;
            return;
        }

        const shape = annotator.currentShape;
        annotator.isDrawing = false;

        // Solo añadir listeners y hacer draggable si la forma tiene tamaño válido
        let isValid = true;
        switch (shape.getClassName()) {
             case 'Rect':
                 if (!shape.width() || !shape.height()) isValid = false;
                 break;
             case 'Circle':
                 if (!shape.radius() || shape.radius() < 0.5) isValid = false;
                 break;
             case 'Arrow':
                 const pts = shape.points();
                 if (pts.length < 4 || (pts[0] === pts[2] && pts[1] === pts[3])) isValid = false;
                 break;
        }

        if (isValid) {
            shape.draggable(true);
            addShapeListeners(imageId, shape);
        } else {
            // Si no es válida (ej. solo clic), eliminarla inmediatamente
            console.log("Forma inválida (sin tamaño) detectada y eliminada al soltar:", shape.getClassName());
            shape.destroy();
        }

        annotator.currentShape = null;
        layer.draw();
    });
}


/**
 * Añade listeners a una forma específica (para selección, drag, etc.).
 * @param {string} imageId ID de la imagen ("11" o "cam-123").
 * @param {Konva.Shape} shape La forma Konva a la que añadir listeners.
 */
function addShapeListeners(imageId, shape) {
     const annotator = annotators[imageId];
     const stage = annotator.stage;
     const layer = annotator.layer;
     const transformer = annotator.transformer;

     shape.on('click tap', (e) => {
         if (annotator.currentTool === 'select') {
             e.cancelBubble = true;
             annotator.selectedShape = shape;
             transformer.nodes([shape]);
             layer.draw();
            // Mejoras táctiles
            shape.hitStrokeWidth(20);
            if (window.innerWidth < 768) {
                const originalScale = shape.scale();
                shape.scale({ x: originalScale.x * 1.2, y: originalScale.y * 1.2 }); // Un poco menos agresivo
                layer.batchDraw();
                setTimeout(() => {
                    shape.scale(originalScale);
                    layer.batchDraw();
                }, 600); // Más corto
            }
        }
    });

     shape.on('dragstart', () => {
         shape.moveToTop();
         transformer.moveToTop();
         layer.draw();
     });

     shape.on('transformend', () => {
         console.log('[ID ${imageId}] Transformación finalizada');
     });
}

/**
 * Configura los listeners para los botones de la barra de herramientas.
 * @param {string} imageId ID de la imagen ("11" o "cam-123").
 * @param {HTMLElement} toolbar El elemento HTML de la barra de herramientas.
 * @param {Konva.Stage} stage Stage Konva.
 */
function setupToolbarListeners(imageId, toolbar, stage) {
    const annotator = annotators[imageId];
    const layer = annotator.layer;
    const transformer = annotator.transformer;

    toolbar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-button');
        if (!button) return;

        const tool = button.dataset.tool;
        const targetImageId = button.dataset.imageId; // El ID del botón SIEMPRE es el imageId

        if (targetImageId !== imageId) return; // Asegurarse que es para este anotador

        if (tool === 'delete') {
            if (annotator.selectedShape) {
                const shapeToDelete = annotator.selectedShape;
                transformer.nodes([]);
                shapeToDelete.destroy();
                annotator.selectedShape = null;
                layer.draw();
                console.log(`[ID ${imageId}] Forma eliminada`);
            } else {
                alert("Por favor, selecciona una forma para eliminar.");
            }
            return;
        }

        annotator.currentTool = tool;
        toolbar.querySelectorAll('.tool-button').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        if (tool === 'select') {
            stage.draggable(false);
            layer.children.forEach(shape => { if (shape !== transformer) shape.draggable(true); });
            transformer.nodes(annotator.selectedShape ? [annotator.selectedShape] : []);
            stage.container().style.cursor = 'default';
        } else {
            stage.draggable(false);
            transformer.nodes([]);
            layer.children.forEach(shape => { if (shape !== transformer) shape.draggable(false); });
            stage.container().style.cursor = 'crosshair';
        }
        layer.draw();
        console.log(`[ID ${imageId}] Herramienta cambiada a: ${tool}`);
    });
}

/**
 * Configura el listener para el botón de guardar anotaciones.
 * @param {string} imageId ID de la imagen ("11" o "cam-123").
 * @param {HTMLElement} saveButton El botón de guardar.
 * @param {Konva.Stage} stage Stage Konva.
 */
function setupSaveListener(imageId, saveButton, stage) {
    saveButton.addEventListener('click', () => {
        const annotator = annotators[imageId];
        if (!annotator) return;

        const layer = annotator.layer;
        const transformer = annotator.transformer;
        const originalSelection = annotator.selectedShape; // Guardar selección original

        transformer.nodes([]);
        annotator.selectedShape = null;
        layer.draw();

        const layerData = layer.toObject();

        // *** INICIO: Lógica de Filtrado y Mapeo ***
        if (layerData && layerData.children) {
            layerData.children = layerData.children
                .filter(child => { // Filtrar formas inválidas
                    if (child.className === 'Transformer') return false;
                    if (child.className === 'Rect' && (!child.attrs.width || !child.attrs.height)) {
                        console.log(`[ID ${imageId}] Filtrando Rectángulo sin tamaño.`); return false;
                    }
                    if (child.className === 'Circle' && (!child.attrs.radius || child.attrs.radius < 0.5)) {
                         console.log(`[ID ${imageId}] Filtrando Círculo sin tamaño.`); return false;
                     }
                    if (child.className === 'Arrow' && Array.isArray(child.attrs.points)) {
                        const pts = child.attrs.points;
                        if (pts.length < 4 || (pts[0] === pts[2] && pts[1] === pts[3])) {
                             console.log(`[ID ${imageId}] Filtrando Flecha sin longitud.`); return false;
                        }
                    }
                    return true;
                })
                .map(child => { // Calcular datos relativos para las formas válidas
                    const shapeX = child.attrs.x || 0;
                    const shapeY = child.attrs.y || 0;
                    const stageWidth = stage.width();
                    const stageHeight = stage.height();

                    // Guardar posición relativa
                    child.relX = shapeX / stageWidth;
                    child.relY = shapeY / stageHeight;

                    // Guardar tamaño/radio/puntos relativos
                    switch (child.className) {
                        case 'Rect':
                            child.relWidth = (child.attrs.width || 0) / stageWidth;
                            child.relHeight = (child.attrs.height || 0) / stageHeight;
                            break;
                        case 'Circle':
                            child.relRadius = (child.attrs.radius || 0) / Math.min(stageWidth, stageHeight);
                            break;
                        case 'Arrow':
                            if (Array.isArray(child.attrs.points)) {
                                child.relPoints = child.attrs.points.map((val, i) =>
                                    i % 2 === 0 ? val / stageWidth : val / stageHeight
                                );
                            }
                            break;
                    }
                    return child;
                });
        }
         // *** FIN: Lógica de Filtrado y Mapeo ***

        const annotationsJsonString = JSON.stringify(layerData);
        console.log(`[ID ${imageId}] Guardando anotaciones FILTRADAS:`, annotationsJsonString);

        // Determinar la URL correcta según el tipo de ID
        let url;
        let numericId;
        if (imageId.startsWith("cam-")) {
            numericId = imageId.replace("cam-", "");
            url = `/points/camera/${numericId}/save_annotations`; // URL correcta para cámaras
            console.log(`[ID ${imageId}] Es una cámara (ID: ${numericId}). Usando URL: ${url}`);
        } else {
            numericId = imageId; // Es un ID de imagen de punto
            url = `/points/image/${numericId}/save_annotations`; // URL para imágenes de punto
             console.log(`[ID ${imageId}] Es una imagen de punto. Usando URL: ${url}`);
        }

        // Enviar datos al backend
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // 'X-CSRFToken': getCsrfToken() // Descomentar si usas CSRF
            },
            body: annotationsJsonString
        })
        .then(response => {
            if (!response.ok) {
                 return response.json().then(errData => {
                     throw new Error(errData.message || `Error HTTP ${response.status}`);
                 }).catch(() => {
                     throw new Error(`Error HTTP ${response.status}`);
                 });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log(`[ID ${imageId}] Anotaciones guardadas exitosamente.`, data);
                showTemporaryMessage(saveButton.closest('.card-body') || saveButton.parentNode, 'Anotaciones guardadas!', 'success');
            } else {
                console.error(`[ID ${imageId}] Error guardando anotaciones (respuesta backend):`, data.message);
                showTemporaryMessage(saveButton.closest('.card-body') || saveButton.parentNode, `Error al guardar: ${data.message}`, 'danger');
            }
            // Restaurar selección y transformer después de la respuesta
            if (originalSelection) {
                 annotator.selectedShape = originalSelection;
                 transformer.nodes([originalSelection]);
            }
             layer.draw();
        })
        .catch(error => {
            console.error(`[ID ${imageId}] Error en fetch al guardar anotaciones:`, error);
            showTemporaryMessage(saveButton.closest('.card-body') || saveButton.parentNode, `Error de conexión: ${error.message}`, 'danger');
            // Restaurar selección y transformer en caso de error
             if (originalSelection) {
                  annotator.selectedShape = originalSelection;
                  transformer.nodes([originalSelection]);
             }
             layer.draw();
        });
    });
}

// --- Funciones de UI para mostrar mensajes ---
function showTemporaryMessage(element, message, type = 'info') {
     const messageArea = element.closest('.card-body') || element.parentNode;
     if (!messageArea) return;
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type} alert-dismissible fade show alert-sm mt-2`;
    messageDiv.setAttribute('role', 'alert');
    messageDiv.style.fontSize = '0.85rem';
    // Simple text assignment is safer if escapeHtml isn't globally available
    messageDiv.textContent = message; // Use textContent for safety
     // Add close button manually if needed, ensuring it's safe
     const closeButton = document.createElement('button');
     closeButton.type = 'button';
     closeButton.className = 'btn-close btn-sm';
     closeButton.setAttribute('data-bs-dismiss', 'alert');
     closeButton.setAttribute('aria-label', 'Close');
     messageDiv.appendChild(closeButton);

    const existingAlert = messageArea.querySelector('.alert');
    if(existingAlert) existingAlert.remove();
    messageArea.insertBefore(messageDiv, messageArea.firstChild);

    setTimeout(() => {
        // Use try-catch for robustness if bootstrap might not be loaded
        try {
            const alertInstance = bootstrap.Alert.getInstance(messageDiv);
            if (alertInstance) {
                alertInstance.close();
            } else {
                 messageDiv.remove();
            }
        } catch (e) {
            messageDiv.remove(); // Fallback removal
        }
    }, 5000);
}

// --- Fin del Archivo ---