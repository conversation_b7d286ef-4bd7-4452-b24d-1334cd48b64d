#!/usr/bin/env python3
"""
Script para crear un usuario específico con rol de administrador.
"""

from sqlalchemy import text
from app.models.user import User, UserRoleModel
from app.db.database import SessionLocal
from app.services.user_service import get_password_hash

def create_custom_admin_user():
    # Usar directamente la sesión de la base de datos
    db = SessionLocal()

    try:
        # Verificar los valores del enum en la base de datos
        enum_values = db.execute(text("""
        SELECT typname, enumlabel
        FROM pg_enum e
        JOIN pg_type t ON e.enumtypid = t.oid
        WHERE typname = 'roleenum'
        ORDER BY enumsortorder;
        """)).fetchall()

        print("\nValores del enum roleenum en la base de datos:")
        for row in enum_values:
            print(f"  - '{row[1]}'")

        # Verificar los roles disponibles en la base de datos
        roles_query = db.query(UserRoleModel).all()
        print("\nRoles disponibles en la base de datos:")
        roles_dict = {}
        for role in roles_query:
            print(f"  - ID: {role.id}, Nombre: '{role.name}'")
            roles_dict[str(role.name)] = role

        # Datos del usuario personalizado
        email = "<EMAIL>"  # Puedes cambiarlo a tu correo real si lo prefieres
        password = "isaias52"
        full_name = "Joaquín Gonzalez"
        phone_number = "5555555555"

        # Verificar si el usuario ya existe
        existing_user = db.query(User).filter(User.email == email).first()
        if existing_user:
            print(f"El usuario con email {email} ya existe. Actualizando contraseña y asegurando rol de administrador...")
            existing_user.hashed_password = get_password_hash(password)
            existing_user.is_superuser = True
            existing_user.is_active = True

            # Verificar si ya tiene el rol de administrador
            has_admin_role = False
            for role in existing_user.roles:
                if str(role.name).lower() == 'administrador':
                    has_admin_role = True
                    break

            # Asignar rol de administrador si no lo tiene
            if not has_admin_role:
                admin_role = None
                # Buscar el rol de administrador (probar con mayúsculas y minúsculas)
                for role_name, role in roles_dict.items():
                    if role_name.lower() == 'administrador' or role_name == 'ADMINISTRADOR':
                        admin_role = role
                        break

                if admin_role:
                    existing_user.roles.append(admin_role)
                    print(f"Rol de administrador asignado al usuario {email}")
                else:
                    print("No se encontró el rol 'administrador' en la base de datos")

            db.commit()
            print(f"Usuario {email} actualizado correctamente.")
        else:
            # Crear nuevo usuario
            new_user = User(
                email=email,
                full_name=full_name,
                hashed_password=get_password_hash(password),
                phone_number=phone_number,
                is_active=True,
                is_superuser=True
            )
            db.add(new_user)
            db.flush()

            # Asignar rol de administrador
            admin_role = None
            # Buscar el rol de administrador (probar con mayúsculas y minúsculas)
            for role_name, role in roles_dict.items():
                if role_name.lower() == 'administrador' or role_name == 'ADMINISTRADOR':
                    admin_role = role
                    break

            if admin_role:
                new_user.roles.append(admin_role)
                db.commit()
                print(f"Usuario administrador creado con email: {email} y contraseña: {password}")
            else:
                db.rollback()
                print("No se encontró el rol 'administrador' en la base de datos. No se pudo crear el usuario.")

                # Intentar crear el rol si no existe
                print("Intentando crear el rol 'administrador'...")
                try:
                    # Verificar si el enum tiene el valor 'administrador'
                    enum_values = db.execute(text("""
                    SELECT typname, enumlabel
                    FROM pg_enum e
                    JOIN pg_type t ON e.enumtypid = t.oid
                    WHERE typname = 'roleenum'
                    ORDER BY enumsortorder;
                    """)).fetchall()

                    print("Valores del enum roleenum:")
                    has_admin_enum = False
                    admin_enum_value = None
                    for row in enum_values:
                        print(f"  - '{row[1]}'")
                        if row[1].lower() == 'administrador':
                            has_admin_enum = True
                            admin_enum_value = row[1]  # Guardar el valor exacto del enum

                    if has_admin_enum:
                        # Crear el rol de administrador usando el valor exacto del enum
                        admin_role_model = UserRoleModel(
                            name=admin_enum_value if admin_enum_value else 'ADMINISTRADOR',
                            description='Rol de Administrador'
                        )
                        db.add(admin_role_model)
                        db.flush()

                        # Crear el usuario y asignarle el rol
                        new_user = User(
                            email=email,
                            full_name=full_name,
                            hashed_password=get_password_hash(password),
                            phone_number=phone_number,
                            is_active=True,
                            is_superuser=True
                        )
                        db.add(new_user)
                        db.flush()

                        new_user.roles.append(admin_role_model)
                        db.commit()
                        print(f"Rol 'administrador' creado y usuario {email} creado correctamente.")
                    else:
                        print("El enum roleenum no tiene el valor 'administrador'. No se puede crear el rol.")
                except Exception as e:
                    db.rollback()
                    print(f"Error al crear el rol 'administrador': {e}")
    except Exception as e:
        db.rollback()
        print(f"Error al crear el usuario: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("Creando usuario administrador personalizado...")
    create_custom_admin_user()
    print("Proceso completado.")
