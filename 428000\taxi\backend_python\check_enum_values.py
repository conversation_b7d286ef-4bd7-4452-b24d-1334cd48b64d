#!/usr/bin/env python3
"""
Script para verificar los valores del enum roleenum en la base de datos.
"""

from sqlalchemy import create_engine, text
from app.core.config import settings

def check_enum_values():
    # Obtener la URL de la base de datos
    db_url = settings.DATABASE_URL
    print(f"Conectando a la base de datos: {db_url}")
    
    # Crear conexión
    engine_instance = create_engine(db_url) # Renombrado para evitar confusión con el engine global

    with engine_instance.connect() as conn:
        try:
            # Verificar los valores del enum
            result = conn.execute(text("""
            SELECT typname, enumlabel
            FROM pg_enum e
            JOIN pg_type t ON e.enumtypid = t.oid
            WHERE typname = 'roleenum'
            ORDER BY enumsortorder;
            """))
            
            print("\nValores del enum roleenum:")
            for row in result:
                print(f"  - '{row[1]}'") # Acceso por índice está bien aquí
            
            # Verificar los roles en la tabla
            result = conn.execute(text("SELECT id, name, description FROM roles;"))
            
            print("\nRoles en la tabla roles:")
            for row in result: # row será una RowProxy, se puede acceder por nombre o índice
                print(f"  - ID: {row.id}, Nombre: '{row.name}', Descripción: '{row.description}'")
            
            # Verificar los usuarios y sus roles
            result = conn.execute(text("""
            SELECT u.id, u.email, r.name as role_name
            FROM users u
            JOIN user_roles_association ura ON u.id = ura.user_id
            JOIN roles r ON ura.role_id = r.id;
            """))
            
            print("\nUsuarios y sus roles:")
            for row in result:
                print(f"  - Usuario ID: {row.id}, Email: '{row.email}', Rol: '{row.role_name}'")
        except Exception as e:
            print(f"Error al verificar los valores: {e}")

if __name__ == "__main__":
    print("Verificando los valores del enum roleenum...")
    check_enum_values()