{# /backend/apps/core/templates/core/register.html #}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro – RN-Rural</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f8f9fa;
            margin: 0;
        }
        .register-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
            transition: transform 0.3s ease;
        }
        .register-container:hover {
            transform: translateY(-5px);
        }
        .register-header {
            background-color: #198754;
            color: white;
            padding: 15px;
            margin: -30px -30px 20px -30px;
            border-radius: 10px 10px 0 0;
            text-align: center;
        }
        h2 {
            margin-bottom: 0;
            font-weight: bold;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            border-color: #198754;
            box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
        }
        .btn-register {
            width: 100%;
            padding: 12px;
            background-color: #198754;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background-color 0.3s;
            margin-bottom: 15px;
        }
        .btn-register:hover {
            background-color: #157347;
        }
        .login-link {
            text-align: center;
            margin-top: 15px;
        }
        .login-link a {
            color: #0d6efd;
            text-decoration: none;
            display: inline-block;
            padding: 5px 10px;
            transition: all 0.3s;
        }
        .login-link a:hover {
            text-decoration: underline;
            transform: translateX(-5px);
        }
        .error-text {
            color: #dc3545;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h2><i class="fas fa-tree"></i> RN-Rural</h2>
        </div>

        <h4 class="text-center mb-4">Crear Cuenta</h4>

        <form method="post">
            {% csrf_token %}
            {% for field in form %}
                <div class="form-group">
                    <label for="{{ field.id_for_label }}">
                        {% if field.name == 'username' %}
                            <i class="fas fa-user"></i>
                        {% elif field.name == 'password1' or field.name == 'password2' %}
                            <i class="fas fa-lock"></i>
                        {% elif field.name == 'email' %}
                            <i class="fas fa-envelope"></i>
                        {% elif field.name == 'role' %}
                            <i class="fas fa-user-tag"></i>
                        {% else %}
                            <i class="fas fa-info-circle"></i>
                        {% endif %}
                        {{ field.label }}:
                    </label>
                    {{ field }}
                    {% if field.errors %}
                        <div class="error-text">{{ field.errors|striptags }}</div>
                    {% endif %}
                </div>
            {% endfor %}
            <button type="submit" class="btn-register"><i class="fas fa-user-plus"></i> Registrarme</button>
        </form>

        <div class="login-link">
            <a href="{% url 'login' %}"><i class="fas fa-arrow-left"></i> Volver a iniciar sesión</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
