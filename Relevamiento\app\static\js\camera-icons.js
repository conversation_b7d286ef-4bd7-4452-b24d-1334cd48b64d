// --- Archivo: app/static/js/camera-icons.js ---
// Responsabilidades: Generar iconos SVG para cámaras en mapas Leaflet

/**
 * Genera un icono SVG para cámaras según el tipo
 * @param {string} type - Tipo de cámara: 'domo', 'fija', 'otra'
 * @param {string} color - Color del icono (opcional, por defecto '#2563eb')
 * @param {number} size - Tamaño del icono (opcional, por defecto 24)
 * @returns {string} - SVG como string
 */
function generateCameraSVG(type, color = '#2563eb', size = 24) {
    const strokeWidth = 2;
    const fillColor = color;
    const strokeColor = '#ffffff';

    let svgContent = '';

    switch (type) {
        case 'domo':
            // Círculo para cámara domo
            svgContent = `
                <circle cx="${size/2}" cy="${size/2}" r="${(size-4)/2}"
                        fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
                <circle cx="${size/2}" cy="${size/2}" r="${(size-12)/2}"
                        fill="${strokeColor}" opacity="0.8"/>
                <text x="${size/2}" y="${size/2 + 3}" text-anchor="middle"
                      font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="${fillColor}">D</text>
            `;
            break;

        case 'fija':
            // Flecha para cámara fija
            const arrowPoints = `${size/2},4 ${size-4},${size-4} ${size/2},${size-8} 4,${size-4}`;
            svgContent = `
                <polygon points="${arrowPoints}"
                         fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
                <text x="${size/2}" y="${size/2 + 2}" text-anchor="middle"
                      font-family="Arial, sans-serif" font-size="6" font-weight="bold" fill="${strokeColor}">F</text>
            `;
            break;

        case 'otra':
        default:
            // Cuadrado para otras cámaras
            svgContent = `
                <rect x="3" y="3" width="${size-6}" height="${size-6}"
                      fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
                <text x="${size/2}" y="${size/2 + 3}" text-anchor="middle"
                      font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="${strokeColor}">O</text>
            `;
            break;
    }

    return `
        <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}"
             xmlns="http://www.w3.org/2000/svg">
            ${svgContent}
        </svg>
    `;
}

/**
 * Crea un icono de Leaflet para una cámara
 * @param {string} type - Tipo de cámara
 * @param {string} color - Color del icono
 * @param {number} size - Tamaño del icono
 * @returns {L.DivIcon} - Icono de Leaflet
 */
function createCameraIcon(type, color = '#2563eb', size = 24) {
    const svg = generateCameraSVG(type, color, size);
    const encodedSvg = encodeURIComponent(svg);

    return L.divIcon({
        html: svg,
        className: 'camera-marker-icon',
        iconSize: [size, size],
        iconAnchor: [size/2, size/2],
        popupAnchor: [0, -size/2]
    });
}

/**
 * Obtiene el color según el tipo de cámara
 * @param {string} type - Tipo de cámara
 * @returns {string} - Color hexadecimal
 */
function getCameraColor(type) {
    const colors = {
        'domo': '#10b981',    // Verde para domo
        'fija': '#f59e0b',    // Amarillo/naranja para fija
        'otra': '#8b5cf6'     // Púrpura para otra
    };
    return colors[type] || '#6b7280'; // Gris por defecto
}

/**
 * Crea un marcador de cámara para el mapa
 * @param {Object} camera - Datos de la cámara
 * @param {Object} point - Datos del punto asociado
 * @param {Object} options - Opciones adicionales (draggable, etc.)
 * @returns {L.Marker} - Marcador de Leaflet
 */
function createCameraMarker(camera, point, options = {}) {
    const coordinates = camera.coordinates || point.coordinates;
    if (!coordinates || typeof coordinates.lat !== 'number' || typeof coordinates.lon !== 'number') {
        console.warn('Coordenadas inválidas para cámara:', camera);
        return null;
    }

    const color = getCameraColor(camera.type);
    const icon = createCameraIcon(camera.type, color, 20);

    // Configurar opciones del marcador
    const markerOptions = {
        icon: icon,
        draggable: options.draggable || false,
        ...options
    };

    const marker = L.marker([coordinates.lat, coordinates.lon], markerOptions);

    // Crear contenido del popup
    let popupContent = `<div class="camera-popup">`;
    popupContent += `<h6><strong>📹 Cámara #${camera.id}</strong></h6>`;
    popupContent += `<p><strong>Tipo:</strong> <span class="badge" style="background-color: ${color};">${camera.type.toUpperCase()}</span></p>`;
    popupContent += `<p><strong>Dirección:</strong> ${camera.direction}</p>`;
    popupContent += `<p><strong>Punto:</strong> <a href="/points/${point.id}" target="_blank">${point.name || `Punto ${point.id}`}</a></p>`;

    // Información de coordenadas
    if (camera.coordinates && camera.coordinates.source !== 'point') {
        popupContent += `<p><strong>📍 Coordenadas:</strong> ${coordinates.lat.toFixed(6)}, ${coordinates.lon.toFixed(6)}`;
        if (camera.coordinates.source) {
            popupContent += ` <small class="text-muted">(${camera.coordinates.source.toUpperCase()})</small>`;
        }
        if (camera.coordinates.accuracy) {
            popupContent += ` <small class="text-muted">(±${camera.coordinates.accuracy.toFixed(1)}m)</small>`;
        }
        popupContent += `</p>`;
    } else {
        popupContent += `<p><strong>📍 Coordenadas:</strong> <small class="text-muted">Heredadas del punto</small></p>`;
    }

    popupContent += `<p><small class="text-muted">Creada: ${new Date(camera.created_at).toLocaleDateString()}</small></p>`;
    popupContent += `</div>`;

    marker.bindPopup(popupContent);

    // Agregar propiedades para filtrado
    marker.options.cameraType = camera.type;
    marker.options.pointId = point.id;
    marker.options.pointCity = point.city;
    marker.options.pointSource = point.source;
    marker.options.markerType = 'camera';
    marker.cameraId = camera.id;

    return marker;
}

// Los estilos CSS ahora están en el template HTML

/**
 * Habilita la edición de coordenadas para marcadores de cámaras
 * @param {Array} cameraMarkers - Array de marcadores de cámaras
 * @param {boolean} enable - true para habilitar, false para deshabilitar
 */
function enableCameraEditing(cameraMarkers, enable = true) {
    cameraMarkers.forEach(marker => {
        if (marker.cameraId) {
            marker.dragging[enable ? 'enable' : 'disable']();

            if (enable) {
                // Agregar evento de dragend si no existe
                if (!marker._editingEnabled) {
                    marker.on('dragend', function(e) {
                        const newPos = e.target.getLatLng();
                        updateCameraCoordinates(marker.cameraId, newPos.lat, newPos.lng);
                    });
                    marker._editingEnabled = true;
                }

                // Cambiar estilo visual para indicar que es editable
                const iconElement = marker.getElement();
                if (iconElement) {
                    iconElement.style.cursor = 'move';
                    iconElement.style.filter = 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))';
                }
            } else {
                // Restaurar estilo normal
                const iconElement = marker.getElement();
                if (iconElement) {
                    iconElement.style.cursor = 'pointer';
                    iconElement.style.filter = 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))';
                }
            }
        }
    });
}

/**
 * Actualiza las coordenadas de una cámara en el servidor
 * @param {number} cameraId - ID de la cámara
 * @param {number} latitude - Nueva latitud
 * @param {number} longitude - Nueva longitud
 */
function updateCameraCoordinates(cameraId, latitude, longitude) {
    const data = {
        latitude: latitude,
        longitude: longitude
    };

    fetch(`/points/camera/${cameraId}/update_coordinates`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken() // Función para obtener token CSRF si es necesario
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`Coordenadas de cámara ${cameraId} actualizadas:`, data.camera);
            showNotification(data.message, 'success');
        } else {
            console.error('Error actualizando coordenadas:', data.error);
            showNotification(`Error: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error en la petición:', error);
        showNotification('Error de conexión al actualizar coordenadas', 'error');
    });
}

// Las funciones de edición de punto se movieron a point-editor.js

/**
 * Obtiene el token CSRF si está disponible
 * @returns {string|null} - Token CSRF o null
 */
function getCsrfToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : null;
}

/**
 * Muestra una notificación al usuario
 * @param {string} message - Mensaje a mostrar
 * @param {string} type - Tipo de notificación ('success', 'error', 'info')
 */
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
    `;

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remover después de 5 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Crea un marcador de cámara editable para la página de detalle del punto
 * @param {Object} camera - Datos de la cámara
 * @param {Object} point - Datos del punto asociado
 * @param {L.Map} map - Instancia del mapa
 * @returns {L.Marker} - Marcador editable
 */
function createEditableCameraMarker(camera, point, map) {
    console.log('createEditableCameraMarker llamada con:', { camera, point });

    // Verificar que tenemos coordenadas válidas
    const coordinates = camera.coordinates || { lat: point.latitude, lon: point.longitude };
    console.log('Coordenadas para marcador:', coordinates);

    if (!coordinates || typeof coordinates.lat !== 'number' || typeof coordinates.lon !== 'number') {
        console.error('Coordenadas inválidas para cámara:', camera.id, coordinates);
        return null;
    }

    const marker = createCameraMarker(camera, point, { draggable: true });
    console.log('Marcador base creado:', marker);

    if (marker) {
        // Agregar evento de dragend
        marker.on('dragend', function(e) {
            const newPos = e.target.getLatLng();
            console.log('Cámara arrastrada a nueva posición:', newPos);
            updateCameraCoordinates(camera.id, newPos.lat, newPos.lng);
        });

        // Estilo visual para indicar que es editable
        marker.on('add', function() {
            console.log('Marcador agregado al mapa, aplicando estilos de edición');
            const iconElement = marker.getElement();
            if (iconElement) {
                iconElement.style.cursor = 'move';
                iconElement.style.filter = 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))';
                console.log('Estilos de edición aplicados');
            } else {
                console.warn('No se pudo obtener elemento del icono');
            }
        });

        marker.cameraId = camera.id;
        marker._editingEnabled = true;

        console.log('Marcador editable configurado para cámara:', camera.id);
    } else {
        console.error('No se pudo crear marcador base para cámara:', camera.id);
    }

    return marker;
}

// Exportar funciones para uso global
if (typeof window !== 'undefined') {
    window.CameraIcons = {
        generateCameraSVG,
        createCameraIcon,
        getCameraColor,
        createCameraMarker,
        enableCameraEditing,
        updateCameraCoordinates,
        createEditableCameraMarker,
        showNotification
    };
}
