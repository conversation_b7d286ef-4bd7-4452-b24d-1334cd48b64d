import enum
from sqlalchemy import <PERSON>umn, Integer, String, <PERSON>olean, DateTime, ForeignKey, Enum as SQLAlchemyEnum
# from geoalchemy2 import Geometry # Para PostGIS
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base

class BaseStation(Base):
    """
    Modelo para las bases o centrales de taxis.
    """
    __tablename__ = 'basestations'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    address = Column(String, nullable=True)
    phone_number = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)

    # Ubicación geográfica
    # location = Column(Geometry('POINT', srid=4326), nullable=True) # Para PostGIS
    latitude = Column(String, nullable=True)
    longitude = Column(String, nullable=True)

    # Relación con el usuario que gestiona esta base (rol BASE)
    manager_id = Column(Integer, ForeignKey('users.id'), nullable=True, unique=True)
    manager = relationship("User", back_populates="base_station_managed", foreign_keys=[manager_id])

    # Relación con los vehículos asignados a esta base
    vehicles = relationship("Vehicle", back_populates="base_station")

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
