#!/usr/bin/env python3
# monitor_conexiones.py - Monitor de conexiones WebSocket

import json
import socketio
import time
import threading
from datetime import datetime

def log_monitor(mensaje):
    """Log del monitor"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    with open("monitor_log.txt", "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {mensaje}\n")
    print(f"[{timestamp}] {mensaje}")

def cargar_config():
    """Carga configuración"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        log_monitor(f"Error cargando config: {e}")
        return None

class MonitorConexiones:
    def __init__(self, config):
        self.config = config
        self.sio_remoto = socketio.Client()
        self.sio_local = socketio.Client()
        self.conectado_remoto = False
        self.conectado_local = False
        self.eventos_recibidos = 0
        
    def setup_eventos_remotos(self):
        """Configura eventos para servidor remoto"""
        
        @self.sio_remoto.event
        def connect():
            self.conectado_remoto = True
            log_monitor("✓ CONECTADO AL SERVIDOR REMOTO")
        
        @self.sio_remoto.event
        def disconnect():
            self.conectado_remoto = False
            log_monitor("✗ DESCONECTADO DEL SERVIDOR REMOTO")
        
        @self.sio_remoto.event
        def connect_error(data):
            log_monitor(f"✗ ERROR CONEXION REMOTA: {data}")
        
        @self.sio_remoto.on('transmit_audio', namespace='/node')
        def on_transmit_audio(data):
            self.eventos_recibidos += 1
            user = data.get('user', 'desconocido')
            node = data.get('node_id', 'desconocido')
            audio_len = len(data.get('audio', ''))
            log_monitor(f"📡 AUDIO REMOTO: {user} (nodo {node}) - {audio_len} bytes")
        
        @self.sio_remoto.on('audio_start', namespace='/node')
        def on_audio_start(data):
            self.eventos_recibidos += 1
            log_monitor(f"🎤 INICIO AUDIO: {data.get('user', 'desconocido')}")
        
        @self.sio_remoto.on('audio_end', namespace='/node')
        def on_audio_end(data):
            self.eventos_recibidos += 1
            log_monitor(f"🔇 FIN AUDIO: {data.get('user', 'desconocido')}")
        
        @self.sio_remoto.on('update_users', namespace='/node')
        def on_update_users(users):
            log_monitor(f"👥 USUARIOS CONECTADOS: {users}")
    
    def setup_eventos_locales(self):
        """Configura eventos para servidor local"""
        
        @self.sio_local.event
        def connect():
            self.conectado_local = True
            log_monitor("✓ CONECTADO AL SERVIDOR LOCAL")
        
        @self.sio_local.event
        def disconnect():
            self.conectado_local = False
            log_monitor("✗ DESCONECTADO DEL SERVIDOR LOCAL")
        
        @self.sio_local.event
        def connect_error(data):
            log_monitor(f"✗ ERROR CONEXION LOCAL: {data}")
        
        @self.sio_local.on('receive_audio')
        def on_receive_audio(data):
            self.eventos_recibidos += 1
            user = data.get('user', 'desconocido')
            audio_len = len(data.get('audio', ''))
            log_monitor(f"🔊 AUDIO LOCAL: {user} - {audio_len} bytes")
        
        @self.sio_local.on('audio_received')
        def on_audio_received(data):
            self.eventos_recibidos += 1
            user = data.get('user', 'desconocido')
            log_monitor(f"📻 AUDIO RECIBIDO LOCAL: {user}")
    
    def conectar_remoto(self):
        """Conecta al servidor remoto"""
        try:
            self.setup_eventos_remotos()
            node_url = self.config['node_url']
            log_monitor(f"Conectando a servidor remoto: wss://{node_url}")
            
            self.sio_remoto.connect(
                f"wss://{node_url}",
                headers={
                    'node_id': self.config['node_id'], 
                    'username': self.config['username']
                },
                namespaces=['/node']
            )
            
        except Exception as e:
            log_monitor(f"Error conectando remoto: {e}")
    
    def conectar_local(self):
        """Conecta al servidor local"""
        try:
            self.setup_eventos_locales()
            log_monitor("Conectando a servidor local: http://127.0.0.1:5000")
            
            self.sio_local.connect('http://127.0.0.1:5000')
            
        except Exception as e:
            log_monitor(f"Error conectando local: {e}")
    
    def mostrar_estado(self):
        """Muestra estado de conexiones cada 10 segundos"""
        while True:
            time.sleep(10)
            log_monitor("=" * 50)
            log_monitor(f"ESTADO CONEXIONES:")
            log_monitor(f"  Remoto: {'✓' if self.conectado_remoto else '✗'}")
            log_monitor(f"  Local: {'✓' if self.conectado_local else '✗'}")
            log_monitor(f"  Eventos recibidos: {self.eventos_recibidos}")
            log_monitor(f"  Usuario: {self.config['username']}")
            log_monitor(f"  Nodo: {self.config['node_id']}")
    
    def iniciar_monitoreo(self):
        """Inicia el monitoreo completo"""
        log_monitor("INICIANDO MONITOR DE CONEXIONES")
        log_monitor("=" * 50)
        
        # Conectar a ambos servidores
        thread_remoto = threading.Thread(target=self.conectar_remoto)
        thread_local = threading.Thread(target=self.conectar_local)
        thread_estado = threading.Thread(target=self.mostrar_estado, daemon=True)
        
        thread_remoto.start()
        time.sleep(2)  # Esperar un poco entre conexiones
        thread_local.start()
        thread_estado.start()
        
        try:
            # Mantener el monitor corriendo
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            log_monitor("Deteniendo monitor...")
            self.sio_remoto.disconnect()
            self.sio_local.disconnect()

def test_conexion_basica():
    """Test básico de conexión"""
    config = cargar_config()
    if not config:
        log_monitor("No se puede hacer test sin configuración")
        return
    
    log_monitor("=== TEST CONEXION BASICA ===")
    
    # Test servidor remoto
    try:
        sio = socketio.Client()
        
        @sio.event
        def connect():
            log_monitor("✓ Test conexión remota exitosa")
            sio.disconnect()
        
        @sio.event
        def connect_error(data):
            log_monitor(f"✗ Test conexión remota falló: {data}")
        
        node_url = config['node_url']
        sio.connect(f"wss://{node_url}",
                   headers={'node_id': config['node_id'], 'username': config['username']},
                   namespaces=['/node'])
        
        time.sleep(3)
        
    except Exception as e:
        log_monitor(f"✗ Error test remoto: {e}")
    
    # Test servidor local
    try:
        sio_local = socketio.Client()
        
        @sio_local.event
        def connect():
            log_monitor("✓ Test conexión local exitosa")
            sio_local.disconnect()
        
        @sio_local.event
        def connect_error(data):
            log_monitor(f"✗ Test conexión local falló: {data}")
        
        sio_local.connect('http://127.0.0.1:5000')
        time.sleep(3)
        
    except Exception as e:
        log_monitor(f"✗ Error test local: {e}")

def main():
    """Función principal"""
    import os
    if os.path.exists("monitor_log.txt"):
        os.remove("monitor_log.txt")
    
    config = cargar_config()
    if not config:
        log_monitor("Error: No se puede cargar configuración")
        return
    
    print("Selecciona una opción:")
    print("1. Test básico de conexión")
    print("2. Monitor completo en tiempo real")
    
    try:
        opcion = input("Opción (1 o 2): ").strip()
        
        if opcion == "1":
            test_conexion_basica()
        elif opcion == "2":
            monitor = MonitorConexiones(config)
            monitor.iniciar_monitoreo()
        else:
            log_monitor("Opción inválida")
            
    except KeyboardInterrupt:
        log_monitor("Programa interrumpido por usuario")

if __name__ == '__main__':
    main()
