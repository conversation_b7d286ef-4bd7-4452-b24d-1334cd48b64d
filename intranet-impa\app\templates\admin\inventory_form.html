<!-- /app/templates/admin/inventory_form.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}{{ 'Agregar' if action == 'add' else 'Editar' }} Elemento de Inventario{% endblock %}

{% block content %}
    <h1>{{ 'Agregar' if action == 'add' else 'Editar' }} Elemento de Inventario ({{ church.name }})</h1>
    <form method="POST" action="">
        {{ form.hidden_tag() }}
        <div class="form-group">
            {{ render_field(form.item_name, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.item_type, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.quantity, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.description, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.purchase_date, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.purchase_price, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.current_value, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.serial_number, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.notes, class="form-control") }}
        </div>
        {{ form.submit(class="btn btn-primary") }}
    </form>
{% endblock %}