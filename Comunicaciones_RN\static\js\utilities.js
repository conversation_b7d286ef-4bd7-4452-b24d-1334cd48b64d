// utilities.js

/**
 * Función para loguear mensajes tanto en la consola como en el servidor.
 * @param {string} message - El mensaje a loguear.
 */
function logToFile(message) {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] ${message}\n`;
    console.log(formattedMessage); // Mostrar en consola

    // Enviar al servidor para guardar en archivo
    fetch('/log', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: formattedMessage }),
    })
    .catch(error => console.error('Error logging to file:', error));
}

export { logToFile };