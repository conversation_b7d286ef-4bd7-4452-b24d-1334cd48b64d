#!/usr/bin/env python3
"""
Script para asignar directamente el rol de administrador al usuario joacoabe
usando SQL directo para evitar problemas con los enums.
"""

import psycopg2
from app.core.config import settings

def fix_user_role():
    try:
        # Conectar a la base de datos
        conn = psycopg2.connect(settings.DATABASE_URL)
        cursor = conn.cursor()
        
        # Verificar si el usuario existe
        cursor.execute("SELECT id FROM users WHERE email = '<EMAIL>'")
        user_result = cursor.fetchone()
        
        if not user_result:
            print("<NAME_EMAIL> no existe en la base de datos.")
            return
        
        user_id = user_result[0]
        print(f"Usuario encontrado con ID: {user_id}")
        
        # Verificar si ya tiene asignado el rol de administrador
        cursor.execute("""
        SELECT ura.role_id, r.name 
        FROM user_roles_association ura 
        JOIN roles r ON ura.role_id = r.id 
        WHERE ura.user_id = %s
        """, (user_id,))
        
        roles = cursor.fetchall()
        print("Roles actuales del usuario:")
        has_admin_role = False
        admin_role_id = None
        
        for role_id, role_name in roles:
            print(f"  - ID: {role_id}, Nombre: '{role_name}'")
            if role_name.lower() == 'administrador':
                has_admin_role = True
                admin_role_id = role_id
        
        if has_admin_role:
            print("El usuario ya tiene el rol de administrador.")
        else:
            # Buscar el ID del rol administrador
            cursor.execute("SELECT id FROM roles WHERE name::text ILIKE 'administrador'")
            admin_role = cursor.fetchone()
            
            if admin_role:
                admin_role_id = admin_role[0]
                print(f"Rol de administrador encontrado con ID: {admin_role_id}")
                
                # Asignar rol de administrador
                cursor.execute(
                    "INSERT INTO user_roles_association (user_id, role_id) VALUES (%s, %s)",
                    (user_id, admin_role_id)
                )
                print(f"Rol de administrador asignado <NAME_EMAIL>")
            else:
                print("No se encontró el rol 'administrador' en la base de datos.")
                
                # Intentar crear el rol administrador
                print("Intentando crear el rol 'administrador'...")
                cursor.execute(
                    "INSERT INTO roles (name, description) VALUES ('administrador', 'Rol de Administrador') RETURNING id"
                )
                admin_role_id = cursor.fetchone()[0]
                
                # Asignar rol de administrador
                cursor.execute(
                    "INSERT INTO user_roles_association (user_id, role_id) VALUES (%s, %s)",
                    (user_id, admin_role_id)
                )
                print(f"Rol 'administrador' creado y asignado <NAME_EMAIL>")
        
        # Asegurarse de que el usuario es superusuario
        cursor.execute(
            "UPDATE users SET is_superuser = TRUE WHERE id = %s",
            (user_id,)
        )
        print("Usuario actualizado como superusuario.")
        
        # Confirmar cambios
        conn.commit()
        print("Cambios aplicados correctamente.")
        
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Asignando rol de administrador <NAME_EMAIL>...")
    fix_user_role()
    print("Proceso completado.")
