{% extends "base.html" %}

{% block title %}{{ title or ('Formulario de Punto' )}} - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <h1 class="mb-4">{{ title or 'Formulario de Punto' }}</h1>

        <form method="POST" action="{{ form_action }}" novalidate>
            {{ form.hidden_tag() }}

            <div class="mb-3">
                {{ form.name.label(class="form-label") }}
                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else ""), placeholder="Ej: Esquina Mitre y Roca") }}
                {% for error in form.name.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
            </div>

            <div class="mb-3">
                {{ form.city.label(class="form-label") }}
                {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else ""), placeholder="Ej: Viedma") }}
                {% for error in form.city.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
            </div>

            <div class="mb-3">
                {{ form.source.label(class="form-label") }}
                {{ form.source(class="form-control" + (" is-invalid" if form.source.errors else ""), placeholder="Ej: cámaras nuevas, importación manual, etc.") }}
                {% for error in form.source.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    {{ form.latitude.label(class="form-label") }}
                    {{ form.latitude(class="form-control" + (" is-invalid" if form.latitude.errors else ""), type="number", step="any", placeholder="-40.123456", id="latitude") }}
                    {% for error in form.latitude.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                </div>
                <div class="col-md-6">
                    {{ form.longitude.label(class="form-label") }}
                    {{ form.longitude(class="form-control" + (" is-invalid" if form.longitude.errors else ""), type="number", step="any", placeholder="-67.987654", id="longitude") }}
                    {% for error in form.longitude.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                </div>
            </div>
            <div class="mb-2 text-end">
                <label for="map-layer-selector" class="form-label me-2">Tipo de Mapa:</label>
                <select id="map-layer-selector" class="form-select form-select-sm d-inline-block" style="width: auto;">
                    <option value="standard" selected>Estándar</option>
                    <option value="satellite">Satelital</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Ubicación en el Mapa</label>
                <div id="mapa-ubicacion" style="height: 300px; border: 1px solid #ccc; width: 100%; z-index: 0;"></div>
                <div class="form-text">Podés arrastrar el marcador para cambiar la ubicación.</div>
            </div>

            <div class="mb-3">
                {{ form.status.label(class="form-label") }}
                {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                {% for error in form.status.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
            </div>

            <div class="mb-3">
                {{ form.description.label(class="form-label") }}
                {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows=4, placeholder="Notas adicionales...") }}
                {% for error in form.description.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
            </div>

            <hr>

            <div class="d-flex justify-content-between">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('points.list_all') }}" class="btn btn-secondary">Cancelar</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const latInput = document.getElementById("latitude");
        const lonInput = document.getElementById("longitude");
        const statusSelect = document.getElementById("status");

        const lat = parseFloat(latInput?.value) || -40.0;
        const lon = parseFloat(lonInput?.value) || -65.0;

        const estado = statusSelect?.value || 'azul';
        const iconUrl = `/static/img/marker_${estado}.png`;

        const map = L.map('mapa-ubicacion').setView([lat, lon], 14);

        const layers = {
            standard: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            }),
            satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri'
            })
        };

        // Capa por defecto
        layers.standard.addTo(map);

        const customIcon = L.icon({
            iconUrl: iconUrl,
            iconSize: [32, 40],
            iconAnchor: [16, 40],
            popupAnchor: [0, -40]
        });

        const marker = L.marker([lat, lon], {
            draggable: true,
            icon: customIcon
        }).addTo(map);

        marker.on('dragend', function (e) {
            const position = marker.getLatLng();
            latInput.value = position.lat.toFixed(6);
            lonInput.value = position.lng.toFixed(6);
        });

        // Selector de capa
        const selector = document.getElementById('map-layer-selector');
        selector.addEventListener('change', function () {
            const selected = this.value;
            map.eachLayer(layer => map.removeLayer(layer));
            layers[selected].addTo(map);
            marker.addTo(map);
        });
    });
</script>
{% endblock %}
