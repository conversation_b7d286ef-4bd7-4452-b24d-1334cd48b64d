{% extends "base.html" %}

{% block title %}Escuelas Bíblicas - Sistema Académico{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-school"></i> Escuelas Bíblicas
                </h1>
                {% if current_user.role in ['administrador', 'secretaria'] %}
                <a href="{{ url_for('routes.create_academic_school') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nueva Escuela
                </a>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('routes.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('routes.academic_dashboard') }}">Sistema Académico</a></li>
            <li class="breadcrumb-item active" aria-current="page">Escuelas Bíblicas</li>
        </ol>
    </nav>
    
    <!-- Estadísticas rápidas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                        Total Escuelas
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {{ schools|length }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        Estudiantes Totales
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {% set total_students = 0 %}
                        {% for school in schools %}
                            {% set total_students = total_students + school.enrollments|selectattr("status", "equalto", "active")|list|length %}
                        {% endfor %}
                        {{ total_students }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        Escuelas con Director
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {{ schools|selectattr("director")|list|length }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        Matrícula Automática
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                        {{ schools|selectattr("auto_enrollment")|list|length }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Lista de escuelas -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i> Lista de Escuelas Bíblicas
            </h6>
        </div>
        <div class="card-body">
            {% if schools %}
            <div class="table-responsive">
                <table class="table table-bordered" id="schoolsTable">
                    <thead>
                        <tr>
                            <th>Escuela</th>
                            <th>Iglesia</th>
                            <th>Director</th>
                            <th>Estudiantes</th>
                            <th>Capacidad</th>
                            <th>Estado</th>
                            <th>Configuración</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for school in schools %}
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ school.name }}</strong>
                                    {% if school.description %}
                                    <br>
                                    <small class="text-muted">{{ school.description[:100] }}{% if school.description|length > 100 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-secondary">{{ school.church.name }}</span>
                            </td>
                            <td>
                                {% if school.director %}
                                <div>
                                    <strong>{{ school.director.first_name }} {{ school.director.last_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ school.director.email }}</small>
                                </div>
                                {% else %}
                                <span class="text-muted">
                                    <i class="fas fa-user-slash"></i> Sin asignar
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                {% set active_students = school.enrollments|selectattr("status", "equalto", "active")|list|length %}
                                {% set completed_students = school.enrollments|selectattr("status", "equalto", "completed")|list|length %}
                                <div>
                                    <span class="badge badge-primary">{{ active_students }} activos</span>
                                    {% if completed_students > 0 %}
                                    <br>
                                    <span class="badge badge-success">{{ completed_students }} graduados</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if school.max_students > 0 %}
                                <div class="progress" style="height: 20px;">
                                    {% set usage_percent = (active_students / school.max_students * 100)|round(1) %}
                                    <div class="progress-bar {% if usage_percent > 90 %}bg-danger{% elif usage_percent > 70 %}bg-warning{% else %}bg-success{% endif %}" 
                                         role="progressbar" 
                                         style="width: {{ usage_percent }}%"
                                         aria-valuenow="{{ usage_percent }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ active_students }}/{{ school.max_students }}
                                    </div>
                                </div>
                                <small class="text-muted">{{ usage_percent }}% ocupado</small>
                                {% else %}
                                <span class="text-muted">Sin límite</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if school.is_active %}
                                <span class="badge badge-success">
                                    <i class="fas fa-check-circle"></i> Activa
                                </span>
                                {% else %}
                                <span class="badge badge-secondary">
                                    <i class="fas fa-pause-circle"></i> Inactiva
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    {% if school.auto_enrollment %}
                                    <span class="badge badge-info">
                                        <i class="fas fa-magic"></i> Auto-matrícula
                                    </span>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">
                                        Inicio: {{ school.start_date.strftime('%d/%m/%Y') }}
                                    </small>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('routes.academic_school_detail', school_id=school.id) }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="Ver detalles">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    {% if current_user.role in ['administrador', 'secretaria'] %}
                                    <a href="#" 
                                       class="btn btn-sm btn-outline-secondary" 
                                       title="Editar escuela">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    {% if school.auto_enrollment %}
                                    <button class="btn btn-sm btn-outline-success" 
                                            onclick="autoEnrollStudents({{ school.id }})"
                                            title="Ejecutar auto-matrícula">
                                        <i class="fas fa-user-plus"></i>
                                    </button>
                                    {% endif %}
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-school fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay escuelas bíblicas registradas</h5>
                <p class="text-muted">Comienza creando la primera escuela bíblica para tu iglesia.</p>
                {% if current_user.role in ['administrador', 'secretaria'] %}
                <a href="{{ url_for('routes.create_academic_school') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Crear Primera Escuela
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal para auto-matrícula -->
<div class="modal fade" id="autoEnrollModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> Auto-matrícula de Estudiantes
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>¿Estás seguro de que deseas ejecutar la auto-matrícula para esta escuela?</p>
                <p class="text-muted">
                    <small>
                        Esto matriculará automáticamente a todos los miembros elegibles de la iglesia 
                        que no estén ya matriculados en esta escuela.
                    </small>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-success" id="confirmAutoEnroll">
                    <i class="fas fa-check"></i> Ejecutar Auto-matrícula
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Inicializar DataTable
    $('#schoolsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json"
        },
        "order": [[ 0, "asc" ]],
        "pageLength": 25,
        "responsive": true
    });
});

let currentSchoolId = null;

function autoEnrollStudents(schoolId) {
    currentSchoolId = schoolId;
    $('#autoEnrollModal').modal('show');
}

$('#confirmAutoEnroll').click(function() {
    if (currentSchoolId) {
        // Aquí iría la llamada AJAX para ejecutar auto-matrícula
        $.ajax({
            url: '/api/academic/auto-enroll/' + currentSchoolId,
            method: 'POST',
            success: function(response) {
                $('#autoEnrollModal').modal('hide');
                if (response.success) {
                    alert('Auto-matrícula ejecutada exitosamente. ' + response.enrolled + ' estudiantes matriculados.');
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                alert('Error al ejecutar auto-matrícula');
            }
        });
    }
});
</script>
{% endblock %}
