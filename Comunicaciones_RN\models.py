# models.py
from flask_login import UserMixin
from extensions import db

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(150), nullable=False)
    role = db.Column(db.String(50), nullable=False)
    node_access = db.Column(db.String(500), nullable=False)  # JSON format for node access
    regional_units = db.Column(db.String(500), nullable=True)
    has_police_access = db.Column(db.<PERSON>, default=False)
    first_name = db.Column(db.String(150), nullable=False)
    last_name = db.Column(db.String(150), nullable=False)
    badge_number = db.Column(db.String(150), nullable=False)
    organization = db.Column(db.String(150), nullable=False)
    session_token = db.Column(db.String(150), unique=True, nullable=True)  # Nuevo campo

class Node(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(150), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('node.id'), nullable=True)
    parent = db.relationship('Node', remote_side=[id], backref='subnodes')
    listeners = db.Column(db.Integer, default=0)
