├── rn_rural
│   ├── README.md
│   ├── backend
│   │   ├── .env.example
│   │   ├── apps
│   │   │   ├── __init__.py
│   │   │   ├── chat
│   │   │   │   ├── __init__.py
│   │   │   │   ├── admin.py
│   │   │   │   ├── apps.py
│   │   │   │   ├── consumers.py
│   │   │   │   ├── models.py
│   │   │   │   ├── routing.py
│   │   │   │   ├── serializers.py
│   │   │   │   ├── tests.py
│   │   │   │   ├── urls.py
│   │   │   │   └── views.py
│   │   │   ├── core
│   │   │   │   ├── __init__.py
│   │   │   │   ├── admin.py
│   │   │   │   ├── apps.py
│   │   │   │   ├── forms.py
│   │   │   │   ├── models.py
│   │   │   │   ├── serializers.py
│   │   │   │   ├── templates
│   │   │   │   │   └── core
│   │   │   │   │       ├── dashboard_ciudadano.html
│   │   │   │   │       ├── dashboard_operador.html
│   │   │   │   │       └── login.html
│   │   │   │   ├── tests.py
│   │   │   │   ├── urls.py
│   │   │   │   └── views.py
│   │   │   ├── incidents
│   │   │   │   ├── __init__.py
│   │   │   │   ├── admin.py
│   │   │   │   ├── apps.py
│   │   │   │   ├── consumers.py
│   │   │   │   ├── models.py
│   │   │   │   ├── routing.py
│   │   │   │   ├── serializers.py
│   │   │   │   ├── tests.py
│   │   │   │   ├── urls.py
│   │   │   │   └── views.py
│   │   │   ├── locations
│   │   │   │   ├── __init__.py
│   │   │   │   ├── admin.py
│   │   │   │   ├── apps.py
│   │   │   │   ├── consumers.py
│   │   │   │   ├── models.py
│   │   │   │   ├── routing.py
│   │   │   │   ├── serializers.py
│   │   │   │   ├── tests.py
│   │   │   │   ├── urls.py
│   │   │   │   └── views.py
│   │   │   └── users
│   │   │       ├── __init__.py
│   │   │       ├── admin.py
│   │   │       ├── apps.py
│   │   │       ├── models.py
│   │   │       ├── serializers.py
│   │   │       ├── tests.py
│   │   │       ├── urls.py
│   │   │       └── views.py
│   │   ├── manage.py
│   │   ├── requirements.txt
│   │   └── rn_rural_project
│   │       ├── __init__.py
│   │       ├── asgi.py
│   │       ├── settings.py
│   │       ├── urls.py
│   │       └── wsgi.py
│   ├── mobile_app
│   └── web_panel




Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\.env.example
Contenido:
# File: backend/.env.example
# -----------------------------------------------

DEBUG=True
SECRET_KEY='isaias52'
DATABASE_URL='postgis://rn_rural_user:tu_password@localhost:5432/rn_rural_db'
ALLOWED_HOSTS='localhost,127.0.0.,0.0.0.0'

# Para Channels (si usas Redis como broker)
CHANNEL_LAYER_REDIS_HOST='localhost'
CHANNEL_LAYER_REDIS_PORT='6379'

# Para almacenamiento de medios (ej. S3) - añadir más adelante
# AWS_ACCESS_KEY_ID=''
# AWS_SECRET_ACCESS_KEY=''
# AWS_STORAGE_BUCKET_NAME=''
# AWS_S3_REGION_NAME=''
# AWS_S3_ENDPOINT_URL='' # Para MinIO u otros S3 compatibles


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\admin.py
Contenido:
# File: backend/apps/chat/admin.py
# -----------------------------------------------

from django.contrib import admin

# Register your models here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\apps.py
Contenido:
# File: backend/apps/chat/apps.py
# -----------------------------------------------

from django.apps import AppConfig

class ChatConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.chat'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\consumers.py
Contenido:
# File: backend/apps/chat/consumers.py
# -----------------------------------------------

import json
from channels.generic.websocket import AsyncWebsocketConsumer

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # self.room_name = self.scope['url_route']['kwargs']['room_name']
        # self.room_group_name = f'chat_{self.room_name}'
        # await self.channel_layer.group_add(
        #     self.room_group_name,
        #     self.channel_name
        # )
        await self.accept()
        await self.send(text_data=json.dumps({
            'message': 'Connected to Chat WebSocket!'
        }))


    async def disconnect(self, close_code):
        # await self.channel_layer.group_discard(
        #     self.room_group_name,
        #     self.channel_name
        # )
        print(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # # Send message to room group
        # await self.channel_layer.group_send(
        #     self.room_group_name,
        #     {
        #         'type': 'chat_message', # Corresponde a un método `async def chat_message(self, event)`
        #         'message': message
        #     }
        # )
        print(f"Received message: {message}")
        await self.send(text_data=json.dumps({
            'response_message': f"Server received: {message}"
        }))

    # # Ejemplo de manejador de mensajes de grupo
    # async def chat_message(self, event):
    #     message = event['message']
    #     # Send message to WebSocket
    #     await self.send(text_data=json.dumps({
    #         'message': message
    #     }))


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\models.py
Contenido:
# File: backend/apps/chat/models.py
# -----------------------------------------------


from django.db import models
from django.conf import settings
# from apps.incidents.models import Incidencia # Descomentar

class MensajeChat(models.Model):
    incidencia = models.ForeignKey('incidents.Incidencia', on_delete=models.CASCADE, related_name="mensajes_chat")
    remitente = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="mensajes_enviados")
    texto = models.TextField()
    fecha_envio = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Mensaje de {self.remitente.username} en Incidencia #{self.incidencia.id} a las {self.fecha_envio}"

    class Meta:
        ordering = ['fecha_envio']


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\routing.py
Contenido:
# File: backend/apps/chat/routing.py
# -----------------------------------------------

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # re_path(r'ws/chat/(?P<room_name>\w+)/$', consumers.ChatConsumer.as_asgi()),
    # re_path(r'ws/chat/$', consumers.ChatConsumer.as_asgi()),
]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\serializers.py
Contenido:
# File: backend/apps/chat/serializers.py
# -----------------------------------------------

from rest_framework import serializers
# from rest_framework_gis.serializers import GeoFeatureModelSerializer # Para GIS
# from .models import YourModel

# class YourModelSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = YourModel
#         fields = '__all__'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\tests.py
Contenido:
# File: backend/apps/chat/tests.py
# -----------------------------------------------

from django.test import TestCase

# Create your tests here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\urls.py
Contenido:
# File: backend/apps/chat/urls.py
# -----------------------------------------------

from django.urls import path, include
from rest_framework.routers import DefaultRouter
# from .views import YourViewSet

# router = DefaultRouter()
# router.register(r'yourmodel', YourViewSet)

urlpatterns = [
    # path('', include(router.urls)),
]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\views.py
Contenido:
# File: backend/apps/chat/views.py
# -----------------------------------------------

from django.shortcuts import render
from rest_framework import viewsets
# Create your views here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\chat\__init__.py
Contenido:
# File: backend/apps/chat/__init__.py
# -----------------------------------------------



Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\admin.py
Contenido:
# File: backend/apps/core/admin.py
# -----------------------------------------------

from django.contrib import admin

# Register your models here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\apps.py
Contenido:
# File: backend/apps/core/apps.py
# -----------------------------------------------

from django.apps import AppConfig

class CoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.core'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\forms.py
Contenido:
# File: apps/core/forms.py
# -----------------------------------------------

from django import forms
from django.contrib.auth.forms import AuthenticationForm

class CustomLoginForm(AuthenticationForm):
    # La siguiente línea está indentada (por ejemplo, con 4 espacios)
    pass 
    # Si tienes otros campos o métodos, también deben estar indentados:
    # por ejemplo:
    # def clean_username(self):
    #     username = self.cleaned_data.get('username')
    #     # ... tu lógica ...
    #     return username

Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\models.py
Contenido:
# File: backend/apps/core/models.py
# -----------------------------------------------

from django.db import models
# from django.contrib.gis.db import models as gis_models # Descomentar si usas GIS

# Create your models here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\serializers.py
Contenido:
# File: backend/apps/core/serializers.py
# -----------------------------------------------

from rest_framework import serializers
# from rest_framework_gis.serializers import GeoFeatureModelSerializer # Para GIS
# from .models import YourModel

# class YourModelSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = YourModel
#         fields = '__all__'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\templates\core\dashboard_ciudadano.html
Contenido:
{# File: apps/core/templates/core/dashboard_ciudadano.html #}

<!DOCTYPE html>
<html><head><title>Portal Ciudadano</title></head>
<body><h1>Bienvenido, {{ user.username }}</h1><p>Este es tu portal.</p><a href="{% url 'logout' %}">Cerrar Sesión</a></body></html>

Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\templates\core\dashboard_operador.html
Contenido:
{# File: apps/core/templates/core/dashboard_operador.html #}

<!DOCTYPE html>
<html><head><title>Panel Operador</title></head>
<body><h1>Bienvenido, Operador {{ user.username }}</h1><p>Este es tu panel.</p><a href="{% url 'logout' %}">Cerrar Sesión</a></body></html>

Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\templates\core\login.html
Contenido:
{# File: apps/core/templates/core/login.html #}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Sesión - RN-Rural</title>
    <style>
        body { font-family: sans-serif; display: flex; justify-content: center; align-items: center; min-height: 100vh; background-color: #f4f4f4; margin: 0; }
        .login-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 0 15px rgba(0,0,0,0.1); width: 100%; max-width: 400px; }
        h2 { text-align: center; color: #333; margin-bottom: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #555; }
        .form-group input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn-login { width: 100%; padding: 10px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn-login:hover { background-color: #0056b3; }
        .messages { list-style: none; padding: 0; margin-bottom: 20px; }
        .messages li { padding: 10px; margin-bottom: 10px; border-radius: 4px; }
        .messages li.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .messages li.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .messages li.info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>RN-Rural - Iniciar Sesión</h2>

        {% if messages %}
            <ul class="messages">
                {% for message in messages %}
                    <li class="{{ message.tags }}">{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}

        <form method="post" action="{% url 'login' %}">
            {% csrf_token %}
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}">Usuario:</label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div style="color:red; font-size:0.9em;">{{ form.username.errors }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label for="{{ form.password.id_for_label }}">Contraseña:</label>
                {{ form.password }}
                {% if form.password.errors %}
                    <div style="color:red; font-size:0.9em;">{{ form.password.errors }}</div>
                {% endif %}
            </div>
            {% if form.non_field_errors %}
                <div style="color:red; font-size:0.9em; margin-bottom:10px;">{{ form.non_field_errors }}</div>
            {% endif %}
            <button type="submit" class="btn-login">Ingresar</button>
        </form>
        {# Podrías añadir enlaces como "¿Olvidaste tu contraseña?" aquí a futuro #}
    </div>
</body>
</html>

Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\tests.py
Contenido:
# File: backend/apps/core/tests.py
# -----------------------------------------------

from django.test import TestCase

# Create your tests here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\urls.py
Contenido:
# File: backend/apps/core/urls.py
# -----------------------------------------------

from django.urls import path
# from django.urls import path, include # 'include' no se usa aquí, se puede quitar si no lo necesitas para otra cosa en este archivo.
# from rest_framework.routers import DefaultRouter # Esto es si usaras ViewSets de DRF en 'core', lo cual no es el caso para estas vistas.
from . import views  # <--- ESTA ES LA LÍNEA IMPORTANTE QUE SE AÑADIÓ/CORRIGIÓ

# Si no estás usando un router de DRF en esta app 'core' para estas vistas específicas,
# las siguientes líneas pueden ser eliminadas o comentadas para mayor claridad:
# router = DefaultRouter()
# router.register(r'yourmodel', YourViewSet) # Ejemplo, no aplica a tus vistas de login/logout/dashboard

urlpatterns = [
    # Si no usas el router en esta app, no necesitas incluirlo:
    # path('', include(router.urls)), # Comentado o eliminado si no hay router.urls para core

    path('login/', views.custom_login_view, name='login'),
    path('logout/', views.custom_logout_view, name='logout'),
    path('panel-operador/', views.dashboard_operador_view, name='dashboard_operador'),
    path('mi-portal/', views.dashboard_ciudadano_view, name='dashboard_ciudadano'),
    
    # Si decides implementar una landing page en la app 'core' y quieres que sea la raíz de esta app:
    # path('', views.landing_page_view, name='landing_page'),
    # Recuerda que la URL raíz general del proyecto se define en rn_rural_project/urls.py
]

Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\views.py
Contenido:
# File: apps/core/views.py
# -----------------------------------------------

from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate, logout # Corregido: 'logout' añadido
from django.contrib import messages
from .forms import CustomLoginForm
# from apps.users.models import User # No es estrictamente necesario aquí si solo usas request.user.role

def custom_login_view(request): # <--- ASEGÚRATE DE QUE ESTA FUNCIÓN EXISTA Y SE LLAME ASÍ
    if request.user.is_authenticated:
        # Si el usuario ya está autenticado, redirigir según rol
        if request.user.role == 'ADMIN':
            return redirect('admin:index')
        elif request.user.role == 'OPERADOR':
            return redirect('dashboard_operador')
        elif request.user.role == 'CIUDADANO':
            return redirect('dashboard_ciudadano')
        else:
            return redirect('/')

    if request.method == 'POST':
        form = CustomLoginForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f"Bienvenido de nuevo, {user.username}!")
                if user.role == 'ADMIN':
                    return redirect('admin:index')
                elif user.role == 'OPERADOR':
                    return redirect('dashboard_operador')
                elif user.role == 'CIUDADANO':
                    return redirect('dashboard_ciudadano')
                else:
                    return redirect('/')
            else:
                messages.error(request, "Usuario o contraseña incorrectos.")
        else:
            # Iterar sobre los errores del formulario para más detalle
            error_list = []
            for field, errors in form.errors.items():
                for error in errors:
                    error_list.append(f"{form.fields[field].label if field != '__all__' else ''}: {error}")
            messages.error(request, "Formulario inválido. " + " ".join(error_list))
    else:
        form = CustomLoginForm()
    
    return render(request, 'core/login.html', {'form': form})

def custom_logout_view(request): # <--- ASEGÚRATE DE QUE ESTA FUNCIÓN EXISTA
    logout(request)
    messages.info(request, "Has cerrado sesión exitosamente.")
    return redirect('login')

def dashboard_operador_view(request): # <--- ASEGÚRATE DE QUE ESTA FUNCIÓN EXISTA
    if not request.user.is_authenticated or request.user.role != 'OPERADOR':
        # messages.error(request, "Acceso no autorizado.") # Podrías usar el decorador @login_required
        return redirect('login')
    return render(request, 'core/dashboard_operador.html', {'user': request.user})

def dashboard_ciudadano_view(request): # <--- ASEGÚRATE DE QUE ESTA FUNCIÓN EXISTA
    if not request.user.is_authenticated or request.user.role != 'CIUDADANO':
        # messages.error(request, "Acceso no autorizado.")
        return redirect('login')
    return render(request, 'core/dashboard_ciudadano.html', {'user': request.user})

Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\core\__init__.py
Contenido:
# File: backend/apps/core/__init__.py
# -----------------------------------------------



Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\admin.py
Contenido:
# File: backend/apps/incidents/admin.py
# -----------------------------------------------

from django.contrib import admin

# Register your models here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\apps.py
Contenido:
# File: backend/apps/incidents/apps.py
# -----------------------------------------------

from django.apps import AppConfig

class IncidentsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.incidents'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\consumers.py
Contenido:
# File: backend/apps/incidents/consumers.py
# -----------------------------------------------

import json
from channels.generic.websocket import AsyncWebsocketConsumer

class IncidentsConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # self.room_name = self.scope['url_route']['kwargs']['room_name']
        # self.room_group_name = f'chat_{self.room_name}'
        # await self.channel_layer.group_add(
        #     self.room_group_name,
        #     self.channel_name
        # )
        await self.accept()
        await self.send(text_data=json.dumps({
            'message': 'Connected to Incidents WebSocket!'
        }))


    async def disconnect(self, close_code):
        # await self.channel_layer.group_discard(
        #     self.room_group_name,
        #     self.channel_name
        # )
        print(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # # Send message to room group
        # await self.channel_layer.group_send(
        #     self.room_group_name,
        #     {
        #         'type': 'chat_message', # Corresponde a un método `async def chat_message(self, event)`
        #         'message': message
        #     }
        # )
        print(f"Received message: {message}")
        await self.send(text_data=json.dumps({
            'response_message': f"Server received: {message}"
        }))

    # # Ejemplo de manejador de mensajes de grupo
    # async def chat_message(self, event):
    #     message = event['message']
    #     # Send message to WebSocket
    #     await self.send(text_data=json.dumps({
    #         'message': message
    #     }))


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\models.py
Contenido:
# File: backend/apps/incidents/models.py
# -----------------------------------------------


from django.contrib.gis.db import models as gis_models
from django.db import models
from django.conf import settings
# from apps.locations.models import Ciudad # Descomentar

class Incidencia(models.Model):
    class EstadoChoices(models.TextChoices):
        NUEVA = "NUEVA", "Nueva"
        ASIGNADA_OPERADOR = "ASIGNADA_OPERADOR", "Asignada a Operador"
        EN_PROCESO_OPERADOR = "EN_PROCESO_OPERADOR", "En Proceso por Operador"
        DERIVADA_BRIGADA = "DERIVADA_BRIGADA", "Derivada a Brigada"
        EN_PROCESO_BRIGADA = "EN_PROCESO_BRIGADA", "En Proceso por Brigada"
        CERRADA_RESUELTA = "CERRADA_RESUELTA", "Cerrada - Resuelta"
        CERRADA_NO_RESUELTA = "CERRADA_NO_RESUELTA", "Cerrada - No Resuelta"
        CANCELADA = "CANCELADA", "Cancelada por Usuario"

    usuario_reporta = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="incidencias_reportadas",
        limit_choices_to={'role': 'CIUDADANO'}
    )
    descripcion_texto = models.TextField(blank=True, null=True)
    audio_url = models.URLField(max_length=500, blank=True, null=True)
    foto_url = models.URLField(max_length=500, blank=True, null=True)
    ubicacion_incidencia = gis_models.PointField(srid=4326)
    # ciudad_reportada = models.ForeignKey('locations.Ciudad', on_delete=models.SET_NULL, null=True, blank=True)
    estado = models.CharField(max_length=30, choices=EstadoChoices.choices, default=EstadoChoices.NUEVA)
    fecha_creacion = models.DateTimeField(auto_now_add=True)
    fecha_actualizacion = models.DateTimeField(auto_now=True)
    operador_asignado = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name="incidencias_asignadas_operador",
        limit_choices_to={'role': 'OPERADOR'}
    )
    brigada_asignada = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name="incidencias_asignadas_brigada",
        limit_choices_to={'role': 'BRIGADA'}
    )
    objects = gis_models.Manager()

    def __str__(self):
        return f"Incidencia #{self.id} por {self.usuario_reporta.username} - {self.estado}"

    class Meta:
        ordering = ['-fecha_creacion']
        indexes = [
            gis_models.Index(fields=['ubicacion_incidencia']),
        ]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\routing.py
Contenido:
# File: backend/apps/incidents/routing.py
# -----------------------------------------------

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # re_path(r'ws/incidents/(?P<room_name>\w+)/$', consumers.IncidentsConsumer.as_asgi()),
    # re_path(r'ws/incidents/$', consumers.IncidentsConsumer.as_asgi()),
]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\serializers.py
Contenido:
# File: backend/apps/incidents/serializers.py
# -----------------------------------------------

from rest_framework import serializers
# from rest_framework_gis.serializers import GeoFeatureModelSerializer # Para GIS
# from .models import YourModel

# class YourModelSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = YourModel
#         fields = '__all__'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\tests.py
Contenido:
# File: backend/apps/incidents/tests.py
# -----------------------------------------------

from django.test import TestCase

# Create your tests here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\urls.py
Contenido:
# File: backend/apps/incidents/urls.py
# -----------------------------------------------

from django.urls import path, include
from rest_framework.routers import DefaultRouter
# from .views import YourViewSet

# router = DefaultRouter()
# router.register(r'yourmodel', YourViewSet)

urlpatterns = [
    # path('', include(router.urls)),
]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\views.py
Contenido:
# File: backend/apps/incidents/views.py
# -----------------------------------------------

from django.shortcuts import render
from rest_framework import viewsets
# Create your views here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\incidents\__init__.py
Contenido:
# File: backend/apps/incidents/__init__.py
# -----------------------------------------------



Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\admin.py
Contenido:
# File: backend/apps/locations/admin.py
# -----------------------------------------------

from django.contrib import admin

# Register your models here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\apps.py
Contenido:
# File: backend/apps/locations/apps.py
# -----------------------------------------------

from django.apps import AppConfig

class LocationsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.locations'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\consumers.py
Contenido:
# File: backend/apps/locations/consumers.py
# -----------------------------------------------

import json
from channels.generic.websocket import AsyncWebsocketConsumer

class LocationsConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # self.room_name = self.scope['url_route']['kwargs']['room_name']
        # self.room_group_name = f'chat_{self.room_name}'
        # await self.channel_layer.group_add(
        #     self.room_group_name,
        #     self.channel_name
        # )
        await self.accept()
        await self.send(text_data=json.dumps({
            'message': 'Connected to Locations WebSocket!'
        }))


    async def disconnect(self, close_code):
        # await self.channel_layer.group_discard(
        #     self.room_group_name,
        #     self.channel_name
        # )
        print(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # # Send message to room group
        # await self.channel_layer.group_send(
        #     self.room_group_name,
        #     {
        #         'type': 'chat_message', # Corresponde a un método `async def chat_message(self, event)`
        #         'message': message
        #     }
        # )
        print(f"Received message: {message}")
        await self.send(text_data=json.dumps({
            'response_message': f"Server received: {message}"
        }))

    # # Ejemplo de manejador de mensajes de grupo
    # async def chat_message(self, event):
    #     message = event['message']
    #     # Send message to WebSocket
    #     await self.send(text_data=json.dumps({
    #         'message': message
    #     }))


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\models.py
Contenido:
# File: backend/apps/locations/models.py
# -----------------------------------------------


from django.contrib.gis.db import models as gis_models
from django.db import models
from django.conf import settings

class UnidadRegional(models.Model):
    nombre = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.nombre

    class Meta:
        verbose_name = "Unidad Regional"
        verbose_name_plural = "Unidades Regionales"

class Ciudad(models.Model):
    nombre = models.CharField(max_length=100)
    unidad_regional = models.ForeignKey(UnidadRegional, on_delete=models.PROTECT, related_name="ciudades")
    # centro_geografico = gis_models.PointField(null=True, blank=True, srid=4326)

    def __str__(self):
        return f"{self.nombre} ({self.unidad_regional.nombre})"

    class Meta:
        verbose_name = "Ciudad"
        verbose_name_plural = "Ciudades"
        unique_together = ('nombre', 'unidad_regional')

class BrigadaMovilLocation(models.Model):
    brigada_usuario = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'role': 'BRIGADA'},
        related_name='ubicacion_movil'
    )
    ubicacion_actual = gis_models.PointField(srid=4326)
    ultima_actualizacion = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Ubicación de {self.brigada_usuario.username} a las {self.ultima_actualizacion.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        verbose_name = "Ubicación de Móvil de Brigada"
        verbose_name_plural = "Ubicaciones de Móviles de Brigada"
        indexes = [
            gis_models.Index(fields=['ubicacion_actual']),
        ]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\routing.py
Contenido:
# File: backend/apps/locations/routing.py
# -----------------------------------------------

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # re_path(r'ws/locations/(?P<room_name>\w+)/$', consumers.LocationsConsumer.as_asgi()),
    # re_path(r'ws/locations/$', consumers.LocationsConsumer.as_asgi()),
]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\serializers.py
Contenido:
# File: backend/apps/locations/serializers.py
# -----------------------------------------------

from rest_framework import serializers
# from rest_framework_gis.serializers import GeoFeatureModelSerializer # Para GIS
# from .models import YourModel

# class YourModelSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = YourModel
#         fields = '__all__'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\tests.py
Contenido:
# File: backend/apps/locations/tests.py
# -----------------------------------------------

from django.test import TestCase

# Create your tests here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\urls.py
Contenido:
# File: backend/apps/locations/urls.py
# -----------------------------------------------

from django.urls import path, include
from rest_framework.routers import DefaultRouter
# from .views import YourViewSet

# router = DefaultRouter()
# router.register(r'yourmodel', YourViewSet)

urlpatterns = [
    # path('', include(router.urls)),
]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\views.py
Contenido:
# File: backend/apps/locations/views.py
# -----------------------------------------------

from django.shortcuts import render
from rest_framework import viewsets
# Create your views here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\locations\__init__.py
Contenido:
# File: backend/apps/locations/__init__.py
# -----------------------------------------------



Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\admin.py
Contenido:
# File: backend/apps/users/admin.py
# -----------------------------------------------

from django.contrib import admin

# Register your models here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\apps.py
Contenido:
# File: backend/apps/users/apps.py
# -----------------------------------------------

from django.apps import AppConfig

class UsersConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.users'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\models.py
Contenido:
# File: backend/apps/users/models.py
# -----------------------------------------------


from django.contrib.auth.models import AbstractUser
from django.db import models
# from apps.locations.models import Ciudad # Descomentar cuando locations.models.Ciudad exista

class User(AbstractUser):
    class Role(models.TextChoices):
        CIUDADANO = "CIUDADANO", "Ciudadano"
        OPERADOR = "OPERADOR", "Operador 911"
        BRIGADA = "BRIGADA", "Brigada Rural"
        ADMIN = "ADMIN", "Administrador"

    role = models.CharField(max_length=20, choices=Role.choices, default=Role.CIUDADANO)
    # Otros campos como 'telefono', 'direccion_particular' pueden ir aquí o en UserProfile

    def __str__(self):
        return self.username

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    nombre_completo = models.CharField(max_length=255, blank=True)
    direccion = models.CharField(max_length=255, blank=True)
    # ciudad = models.ForeignKey('locations.Ciudad', on_delete=models.SET_NULL, null=True, blank=True) # Usar string para evitar import circular al inicio
    telefono = models.CharField(max_length=20, blank=True)
    edad = models.PositiveIntegerField(null=True, blank=True)

    def __str__(self):
        return f"Perfil de {self.user.username}"


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\serializers.py
Contenido:
# File: backend/apps/users/serializers.py
# -----------------------------------------------

from rest_framework import serializers
# from rest_framework_gis.serializers import GeoFeatureModelSerializer # Para GIS
# from .models import YourModel

# class YourModelSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = YourModel
#         fields = '__all__'


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\tests.py
Contenido:
# File: backend/apps/users/tests.py
# -----------------------------------------------

from django.test import TestCase

# Create your tests here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\urls.py
Contenido:
# File: backend/apps/users/urls.py
# -----------------------------------------------

from django.urls import path, include
from rest_framework.routers import DefaultRouter
# from .views import YourViewSet

# router = DefaultRouter()
# router.register(r'yourmodel', YourViewSet)

urlpatterns = [
    # path('', include(router.urls)),
]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\views.py
Contenido:
# File: backend/apps/users/views.py
# -----------------------------------------------

from django.shortcuts import render
from rest_framework import viewsets
# Create your views here.


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\users\__init__.py
Contenido:
# File: backend/apps/users/__init__.py
# -----------------------------------------------



Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\apps\__init__.py
Contenido:
# File: backend/apps/__init__.py
# -----------------------------------------------



Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\manage.py
Contenido:
# File: backend/manage.py
# -----------------------------------------------

#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rn_rural_project.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\requirements.txt
Contenido:
# File: backend/requirements.txt
# -----------------------------------------------

Django>=4.0
djangorestframework>=3.13
psycopg2-binary>=2.9
django-environ>=0.8
djangorestframework-gis>=1.0
channels>=4.0
channels-redis>=4.0
Pillow>=9.0
daphne>=4.0 # Para servir ASGI/WebSockets
# Celery (añadir más adelante si es necesario)
# celery>=5.2
# redis>=4.3


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\rn_rural_project\asgi.py
Contenido:
# File: backend/rn_rural_project/asgi.py
# -----------------------------------------------


import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack # O AuthMiddleware si no usas sesiones Django en WebSockets

# Importa tus archivos de routing de las apps aquí (asegúrate que los paths sean correctos)
import apps.incidents.routing
import apps.locations.routing
import apps.chat.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rn_rural_project.settings')

# application = get_asgi_application() # Esto es para HTTP solo

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack( # Usar AuthMiddlewareStack si se quiere acceso a request.user
        URLRouter(
            apps.incidents.routing.websocket_urlpatterns +
            apps.locations.routing.websocket_urlpatterns +
            apps.chat.routing.websocket_urlpatterns
            # Añade más patrones de URL de WebSocket de otras apps aquí
        )
    ),
})


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\rn_rural_project\settings.py
Contenido:
# File: backend/rn_rural_project/settings.py
# -----------------------------------------------


import environ
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent # Corrected BASE_DIR for project layout
env = environ.Env(
    DEBUG=(bool, False)
)
# Assuming .env is in the 'backend' directory, alongside manage.py
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

SECRET_KEY = env('SECRET_KEY')
DEBUG = env('DEBUG')
ALLOWED_HOSTS = env('ALLOWED_HOSTS', cast=lambda v: [s.strip() for s in v.split(',')])


INSTALLED_APPS = [
    'daphne',  # Debe ir primero para Channels
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.gis',

    # Dependencias
    'rest_framework',
    'rest_framework_gis',
    'channels',

    # Mis Apps
    'apps.core.apps.CoreConfig',
    'apps.users.apps.UsersConfig',
    'apps.incidents.apps.IncidentsConfig',
    'apps.locations.apps.LocationsConfig',
    'apps.chat.apps.ChatConfig',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'rn_rural_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

ASGI_APPLICATION = 'rn_rural_project.asgi.application'

DATABASES = {
    'default': env.db_url(
        'DATABASE_URL',
        default='postgis://user:pass@host:port/dbname'
    )
}
DATABASES['default']['ENGINE'] = 'django.contrib.gis.db.backends.postgis'

AUTH_USER_MODEL = 'users.User'

# Password validation
# ... (configuración estándar de Django)
AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]

LANGUAGE_CODE = 'es-ar'
TIME_ZONE = 'America/Argentina/Buenos_Aires'
USE_I18N = True
USE_TZ = True

STATIC_URL = 'static/'
# STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles_collected') # Para cuando ejecutes collectstatic
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        # 'rest_framework.renderers.BrowsableAPIRenderer', # Útil en desarrollo
    ),
    'DEFAULT_PARSER_CLASSES': (
        'rest_framework.parsers.JSONParser',
        #'rest_framework_gis.parsers.GeoJSONParser',
    )
}

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [(env('CHANNEL_LAYER_REDIS_HOST', default='localhost'), env.int('CHANNEL_LAYER_REDIS_PORT', default=6379))],
        },
    },
}
# Si no tienes Redis al inicio, comenta lo anterior y descomenta lo siguiente:
# CHANNEL_LAYERS = {
# "default": {
# "BACKEND": "channels.layers.InMemoryChannelLayer"
# }
# }


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\rn_rural_project\urls.py
Contenido:
# File: backend/rn_rural_project/urls.py
# -----------------------------------------------


from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Si quieres que /login/ sea la página principal:
    path('', include('apps.core.urls')), # Esto hará que /login/ sea accesible desde la raíz si 'login/' es el primer path en core.urls
    # O si quieres una landing page y /login/ como subruta:
    # path('', landing_page_view, name='landing_page'),
    # path('accounts/', include('apps.core.urls')), # Entonces /accounts/login/, /accounts/logout/, etc.

    path('admin/', admin.site.urls),
    path('api/users/', include('apps.users.urls')),
    path('api/incidents/', include('apps.incidents.urls')),
    path('api/locations/', include('apps.locations.urls')),
    path('api/chat/', include('apps.chat.urls')),
    # path('api/auth/', include('rest_framework.urls')), # Para login/logout en Browsable API
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    # Aquí podrías añadir swagger/redoc si lo usas
    # from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView
    # urlpatterns += [
    #     path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    #     path('api/schema/swagger-ui/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    #     path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    # ]


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\rn_rural_project\wsgi.py
Contenido:
# File: backend/rn_rural_project/wsgi.py
# -----------------------------------------------


import os
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rn_rural_project.settings')
application = get_asgi_application()


Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\backend\rn_rural_project\__init__.py
Contenido:
# File: backend/rn_rural_project/__init__.py
# -----------------------------------------------



Archivo: C:/Users/<USER>/Documents/GitHub/RN-Rural\rn_rural\README.md
Contenido:
# File: README.md
# -----------------------------------------------


# RN-Rural Project

Proyecto para la aplicación RN-Rural.

## Estructura

- `backend/`: Contiene el backend Django.
- `mobile_app/`: (Placeholder) Contendrá el código de la aplicación móvil.
- `web_panel/`: (Placeholder) Contendrá el código del panel web para operadores.

## Próximos Pasos (Backend)

1.  Navega a la carpeta `backend`:
    `cd rn_rural/backend`
2.  Crea y activa un entorno virtual:
    `python -m venv venv`
    `source venv/bin/activate` (Linux/macOS) o `venv\Scripts\activate` (Windows)
3.  Instala las dependencias:
    `pip install -r requirements.txt`
4.  Copia `.env.example` a `.env` y configura tus variables de entorno (especialmente `DATABASE_URL` y `SECRET_KEY`).
    `cp .env.example .env`
5.  Asegúrate de que tu base de datos PostgreSQL con PostGIS esté en funcionamiento y accesible.
6.  Ejecuta las migraciones:
    `python manage.py makemigrations`
    `python manage.py migrate`
7.  Crea un superusuario:
    `python manage.py createsuperuser`
8.  Inicia el servidor de desarrollo:
    `python manage.py runserver`

Y para los WebSockets (con Daphne):
    `daphne -p 8001 rn_rural_project.asgi:application`
    (Asegúrate de que Daphne esté instalado: `pip install daphne`)

