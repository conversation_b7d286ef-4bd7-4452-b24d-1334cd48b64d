{# File: backend/apps/incidents/templates/incidents/lista_incidencias_operador.html #}
{% extends "core/base.html" %}

{% block title %}{{ titulo_pagina }}{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
    .card-dashboard {
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    .card-header-custom {
        border-radius: 10px 10px 0 0;
        padding: 15px;
        font-weight: bold;
    }
    .bg-nueva { background-color: #0dcaf0; color: white; }
    .bg-proceso { background-color: #ffc107; color: black; }
    .bg-derivada { background-color: #fd7e14; color: white; }
    .bg-resuelta { background-color: #198754; color: white; }
    .bg-cerrada { background-color: #6c757d; color: white; }
    .estado-badge {
        font-size: 0.85rem;
        padding: 6px 10px;
        border-radius: 20px;
    }
    .table-custom {
        border-collapse: separate;
        border-spacing: 0 8px;
    }
    .table-custom thead th {
        border-bottom: none;
        background-color: #f8f9fa;
        padding: 12px 15px;
        font-weight: 600;
    }
    .table-custom tbody tr {
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border-radius: 8px;
        background-color: white;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    .table-custom tbody tr:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .table-custom td {
        padding: 15px;
        vertical-align: middle;
        border-top: none;
    }
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    .search-box {
        position: relative;
        margin-bottom: 20px;
    }
    .search-box input {
        padding-left: 40px;
        border-radius: 20px;
    }
    .search-box i {
        position: absolute;
        left: 15px;
        top: 10px;
        color: #6c757d;
    }
    .filters {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    .filter-btn {
        border-radius: 20px;
        padding: 6px 15px;
        font-size: 0.9rem;
    }
    .filter-btn.active {
        background-color: #0d6efd;
        color: white;
    }
    .pagination-custom {
        margin-top: 20px;
    }
    .pagination-custom .page-link {
        border-radius: 5px;
        margin: 0 3px;
    }
    .description-cell {
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    #mapIncidencias {
        height: 400px;
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .map-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        background: white;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .map-legend {
        position: absolute;
        bottom: 30px;
        right: 10px;
        z-index: 1000;
        background: white;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        font-size: 0.8rem;
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    .legend-color {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        border-radius: 50%;
    }
    .legend-nueva { background-color: #0dcaf0; }
    .legend-proceso { background-color: #ffc107; }
    .legend-derivada { background-color: #fd7e14; }
    .legend-resuelta { background-color: #198754; }
    .view-toggle-btn {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0">{{ titulo_pagina }}</h2>
        <a href="{% url 'dashboard_operador' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Volver al Dashboard
        </a>
    </div>

    <div class="btn-group view-toggle-btn" role="group" aria-label="Cambiar vista">
        <button type="button" class="btn btn-primary active" id="listViewBtn">
            <i class="fas fa-list"></i> Vista de Lista
        </button>
        <button type="button" class="btn btn-outline-primary" id="mapViewBtn">
            <i class="fas fa-map-marked-alt"></i> Vista de Mapa
        </button>
    </div>

    <!-- Vista de Mapa -->
    <div class="card card-dashboard" id="mapView" style="display: none;">
        <div class="card-header card-header-custom bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Mapa de Incidencias</h4>
                <div>
                    <button class="btn btn-light btn-sm" id="refreshMapBtn" title="Actualizar mapa">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0 position-relative">
            <div id="mapIncidencias"></div>

            <div class="map-legend">
                <h6 class="mb-2">Leyenda</h6>
                <div class="legend-item">
                    <div class="legend-color legend-nueva"></div>
                    <span><i class="fas fa-exclamation-circle"></i> Nueva</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-proceso"></div>
                    <span><i class="fas fa-cogs"></i> En Proceso (Operador)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-derivada"></div>
                    <span><i class="fas fa-truck"></i> Asignada a Brigada</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-resuelta"></div>
                    <span><i class="fas fa-check-circle"></i> Resuelta/Cerrada</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista de Lista -->
    <div class="card card-dashboard" id="listView">
        <div class="card-header card-header-custom bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-list"></i> Listado de Incidencias</h4>
                <div>
                    <button class="btn btn-light btn-sm" id="refreshBtn" title="Actualizar lista">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" class="form-control" placeholder="Buscar incidencia...">
            </div>

            <div class="filters">
                <a href="{% url 'incidents:lista_incidencias_operador' %}" class="btn btn-outline-secondary filter-btn {% if estado_filtro == 'todas' %}active{% endif %}">
                    <i class="fas fa-list"></i> Todas <span class="badge bg-secondary">{{ count_todas }}</span>
                </a>
                <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=nuevas" class="btn btn-outline-info filter-btn {% if estado_filtro == 'nuevas' %}active{% endif %}">
                    <i class="fas fa-exclamation-circle"></i> Nuevas <span class="badge bg-info">{{ count_nuevas }}</span>
                </a>
                <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=proceso" class="btn btn-outline-warning filter-btn {% if estado_filtro == 'proceso' %}active{% endif %}">
                    <i class="fas fa-cogs"></i> En Proceso <span class="badge bg-warning text-dark">{{ count_proceso }}</span>
                </a>
                <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=derivadas" class="btn btn-outline-danger filter-btn {% if estado_filtro == 'derivadas' %}active{% endif %}">
                    <i class="fas fa-truck"></i> Asignadas a Brigada <span class="badge bg-danger">{{ count_derivadas }}</span>
                </a>
                <a href="{% url 'incidents:lista_incidencias_operador' %}?estado=resueltas" class="btn btn-outline-success filter-btn {% if estado_filtro == 'resueltas' %}active{% endif %}">
                    <i class="fas fa-check-circle"></i> Resueltas/Cerradas <span class="badge bg-success">{{ count_resueltas }}</span>
                </a>
            </div>

            {% if incidencias %}
                <div class="table-responsive">
                    <table class="table table-custom" id="incidenciasTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Reportada por</th>
                                <th>Fecha</th>
                                <th>Estado</th>
                                <th>Descripción</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for incidencia in incidencias %}
                            <tr data-estado="{{ incidencia.estado }}"
                                {% if incidencia.ubicacion_incidencia %}
                                data-lat="{{ incidencia.ubicacion_incidencia.y }}"
                                data-lng="{{ incidencia.ubicacion_incidencia.x }}"
                                {% endif %}>
                                <td><strong>#{{ incidencia.id }}</strong></td>
                                <td>
                                    <i class="fas fa-user"></i> {{ incidencia.usuario_reporta.username }}
                                </td>
                                <td>
                                    <i class="fas fa-calendar-alt"></i> {{ incidencia.fecha_creacion|date:"d/m/Y H:i" }}
                                </td>
                                <td>
                                    {% if incidencia.estado == 'NUEVA' %}
                                        <span class="badge bg-nueva estado-badge">
                                            <i class="fas fa-exclamation-circle"></i> {{ incidencia.get_estado_display }}
                                        </span>
                                    {% elif incidencia.estado == 'ASIGNADA_OPERADOR' or incidencia.estado == 'EN_PROCESO_OPERADOR' %}
                                        <span class="badge bg-proceso estado-badge">
                                            <i class="fas fa-cogs"></i> {{ incidencia.get_estado_display }}
                                        </span>
                                    {% elif incidencia.estado == 'DERIVADA_BRIGADA' or incidencia.estado == 'EN_PROCESO_BRIGADA' %}
                                        <span class="badge bg-derivada estado-badge">
                                            <i class="fas fa-truck"></i> {{ incidencia.get_estado_display }}
                                        </span>
                                    {% elif incidencia.estado == 'CERRADA_RESUELTA' %}
                                        <span class="badge bg-resuelta estado-badge">
                                            <i class="fas fa-check-circle"></i> {{ incidencia.get_estado_display }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-cerrada estado-badge">
                                            <i class="fas fa-times-circle"></i> {{ incidencia.get_estado_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="description-cell" title="{{ incidencia.descripcion_texto }}">
                                    {{ incidencia.descripcion_texto|truncatewords:10 }}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{% url 'incidents:detalle_incidencia_operador' pk=incidencia.pk %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> Ver
                                        </a>
                                        {% if incidencia.estado == 'NUEVA' %}
                                        <a href="{% url 'incidents:detalle_incidencia_operador' pk=incidencia.pk %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i> Asignar
                                        </a>
                                        {% endif %}
                                        <a href="{% url 'chat:chat_incidencia' incidencia.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-comments"></i> Chat
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle"></i> No hay incidencias para mostrar en este momento.
                </div>
            {% endif %}

        {% if is_paginated %}
            <nav aria-label="Paginación de incidencias" class="pagination-custom">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Anterior">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Siguiente">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Cambio entre vista de lista y mapa
        const listViewBtn = document.getElementById('listViewBtn');
        const mapViewBtn = document.getElementById('mapViewBtn');
        const listView = document.getElementById('listView');
        const mapView = document.getElementById('mapView');

        listViewBtn.addEventListener('click', function() {
            listView.style.display = 'block';
            mapView.style.display = 'none';
            listViewBtn.classList.add('active');
            listViewBtn.classList.remove('btn-outline-primary');
            listViewBtn.classList.add('btn-primary');
            mapViewBtn.classList.remove('active');
            mapViewBtn.classList.remove('btn-primary');
            mapViewBtn.classList.add('btn-outline-primary');
        });

        mapViewBtn.addEventListener('click', function() {
            listView.style.display = 'none';
            mapView.style.display = 'block';
            mapViewBtn.classList.add('active');
            mapViewBtn.classList.remove('btn-outline-primary');
            mapViewBtn.classList.add('btn-primary');
            listViewBtn.classList.remove('active');
            listViewBtn.classList.remove('btn-primary');
            listViewBtn.classList.add('btn-outline-primary');

            // Asegurarse de que el mapa se renderice correctamente
            if (map) {
                map.invalidateSize();
            }
        });

        // Búsqueda en tiempo real
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('#incidenciasTable tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // El filtrado por estado ahora se hace en el servidor

        // Botón de actualizar lista
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                location.reload();
            });
        }

        // Botón de actualizar mapa
        const refreshMapBtn = document.getElementById('refreshMapBtn');
        if (refreshMapBtn) {
            refreshMapBtn.addEventListener('click', function() {
                location.reload();
            });
        }

        // Inicializar el mapa
        const mapElement = document.getElementById('mapIncidencias');
        let map = null;

        if (mapElement) {
            // Crear mapa con animación de zoom
            map = L.map('mapIncidencias', {
                zoomAnimation: true,
                fadeAnimation: true
            }).setView([-40.8, -63.0], 6); // Río Negro aprox

            // Añadir capa de mapa base con estilo más moderno
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // Añadir control de escala
            L.control.scale({imperial: false}).addTo(map);

            // Añadir marcadores para las incidencias
            const rows = document.querySelectorAll('#incidenciasTable tbody tr');
            const bounds = L.latLngBounds();
            let hasValidCoordinates = false;

            rows.forEach(row => {
                // Obtener datos de la incidencia
                const id = row.querySelector('td:first-child').textContent.trim().replace('#', '');
                const estado = row.getAttribute('data-estado');
                const descripcion = row.querySelector('.description-cell').getAttribute('title');

                // Obtener coordenadas (asumimos que están en atributos data)
                const lat = parseFloat(row.getAttribute('data-lat'));
                const lng = parseFloat(row.getAttribute('data-lng'));

                if (!isNaN(lat) && !isNaN(lng)) {
                    hasValidCoordinates = true;
                    bounds.extend([lat, lng]);

                    // Determinar el color según el estado
                    let iconColor;
                    if (estado === 'NUEVA') {
                        iconColor = '#0dcaf0'; // Azul
                    } else if (estado === 'ASIGNADA_OPERADOR' || estado === 'EN_PROCESO_OPERADOR') {
                        iconColor = '#ffc107'; // Amarillo
                    } else if (estado === 'DERIVADA_BRIGADA') {
                        iconColor = '#fd7e14'; // Naranja
                    } else if (estado === 'CERRADA_RESUELTA') {
                        iconColor = '#198754'; // Verde
                    } else {
                        iconColor = '#6c757d'; // Gris
                    }

                    // Crear icono personalizado
                    const customIcon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div style="background-color:${iconColor}; width:30px; height:30px; border-radius:50%; border:3px solid white; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;">${id}</div>`,
                        iconSize: [30, 30],
                        iconAnchor: [15, 15]
                    });

                    // Añadir marcador
                    const marker = L.marker([lat, lng], { icon: customIcon }).addTo(map);

                    // Añadir popup
                    marker.bindPopup(`
                        <div style="text-align:center;">
                            <h5 style="margin-bottom:8px;">Incidencia #${id}</h5>
                            <span class="badge" style="background-color:${iconColor}; padding:5px 10px; border-radius:20px;">
                                ${getEstadoDisplay(estado)}
                            </span>
                            <hr style="margin:10px 0;">
                            <p>${descripcion}</p>
                            <a href="/incidencias/operador/detalle/${id}/" class="btn btn-sm btn-primary">
                                Ver Detalle
                            </a>
                        </div>
                    `);
                }
            });

            // Si hay coordenadas válidas, ajustar la vista
            if (hasValidCoordinates) {
                map.fitBounds(bounds, { padding: [50, 50] });
            }
        }

        // Función para obtener el texto del estado
        function getEstadoDisplay(estado) {
            switch (estado) {
                case 'NUEVA':
                    return 'Nueva';
                case 'ASIGNADA_OPERADOR':
                    return 'Asignada a Operador';
                case 'EN_PROCESO_OPERADOR':
                    return 'En Proceso por Operador';
                case 'DERIVADA_BRIGADA':
                    return 'Derivada a Brigada';
                case 'CERRADA_RESUELTA':
                    return 'Cerrada - Resuelta';
                case 'CERRADA_NO_RESUELTA':
                    return 'Cerrada - No Resuelta';
                default:
                    return estado;
            }
        }
    });
</script>
{% endblock %}