# File: backend/rn_rural/settings_channels.py
# -----------------------------------------------

# Configuración de Django Channels
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [('127.0.0.1', 6379)],
        },
    },
}

# Añadir 'channels' y 'apps.notifications' a INSTALLED_APPS
INSTALLED_APPS += [
    'channels',
    'apps.notifications',
]

# Configurar ASGI application
ASGI_APPLICATION = 'rn_rural.asgi.application'
