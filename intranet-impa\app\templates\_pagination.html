<!-- app/templates/_pagination.html -->
{% macro render_pagination(pagination, endpoint, filters={}) %} {# <-- Eliminado , **kwargs #}
{#
    Renderiza controles de paginación.
    :param pagination: Objeto Pagination de Flask-SQLAlchemy o similar.
    :param endpoint: El nombre del endpoint de la ruta (ej. 'routes.list_users').
    :param filters: Un diccionario con los filtros actuales (ej. {'search': 'foo', 'status': 'active'}).
#}
{% if pagination and pagination.pages > 1 %} {# Solo mostrar si hay más de una página #}
<nav aria-label="Paginación">
  <ul class="pagination justify-content-center mt-3">
    {# Botón Anterior #}
    {% if pagination.has_prev %}
      <li class="page-item">
        {# Pasamos solo los filtros desempaquetados #}
        <a class="page-link"
           href="{{ url_for(endpoint, page=pagination.prev_num, **filters) }}"
           aria-label="Anterior">
          <span aria-hidden="true">«</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled"><span class="page-link">«</span></li>
    {% endif %}

    {# Números de Página #}
    {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
      {% if page_num %}
        {% if page_num == pagination.page %}
          <li class="page-item active"><span class="page-link">{{ page_num }}</span></li>
        {% else %}
          <li class="page-item">
             {# Pasamos solo los filtros desempaquetados #}
            <a class="page-link"
               href="{{ url_for(endpoint, page=page_num, **filters) }}">{{ page_num }}</a>
          </li>
        {% endif %}
      {% else %}
        {# Separador "..." #}
        <li class="page-item disabled"><span class="page-link">...</span></li>
      {% endif %}
    {% endfor %}

    {# Botón Siguiente #}
    {% if pagination.has_next %}
      <li class="page-item">
         {# Pasamos solo los filtros desempaquetados #}
        <a class="page-link"
           href="{{ url_for(endpoint, page=pagination.next_num, **filters) }}"
           aria-label="Siguiente">
          <span aria-hidden="true">»</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled"><span class="page-link">»</span></li>
    {% endif %}
  </ul>
</nav>
{% endif %} {# Fin de if pagination #}
{% endmacro %}