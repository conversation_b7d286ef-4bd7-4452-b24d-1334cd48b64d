<!-- /static/app-ws/templates/index.html -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cliente VHF Digital</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
</head>
<body class="body-background">
    <header>
        <h1>Cliente VHF Digital</h1>
        <p>Aplicación en ejecución automática para captura y recepción de audio.</p>
    </header>

    <main class="container">
        <!-- Sección de Información del Nodo -->
        <div class="node-section">
            <h3>Información del Nodo:</h3>
            <p id="node-info">Conectado al nodo 41</p>
        </div>

        <!-- Sección de Botón de Transmisión de Audio -->
        <div class="audio-section">
            <img id="transmitAudio" src="{{ url_for('static', filename='verde.png') }}" alt="Transmitir Audio" />
        </div>

        <!-- Contenedor Principal: Chat + Usuarios Conectados -->
        <div class="chat-container">
            <!-- Sección de Eventos de Audio -->
            <div class="events-section">
                <h3>Eventos de audio:</h3>
                <ul id="events_list">
                    <li>No hay eventos en este momento</li>
                </ul>
            </div>

            <!-- Sección de Usuarios Conectados -->
            <div class="users-section">
                <h3>Usuarios conectados:</h3>
                <ul id="users_list">
                    <li>joacoabe</li>
                    <li>ur1</li>
                    <li>admin</li>
                </ul>
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; 2024 Cliente VHF Digital</p>
    </footer>

    <script type="text/javascript">
        // Cargar los valores correctos desde Flask
        const nodeId = "{{ node_id }}";  // Pasar node_id desde Flask
        const username = "{{ username }}";  // Pasar username desde Flask

        // Hacerlos accesibles globalmente en el archivo index.js
        window.nodeId = nodeId;
        window.username = username;
    </script>
    
    <!-- Agregar librería Socket.IO -->
    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>

    <!-- Incluir el nuevo script index.js -->
    <script src="{{ url_for('static', filename='js/index.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dig-vhf.js') }}"></script>
    <script src="{{ url_for('static', filename='js/vhf-dig.js') }}"></script>
</body>
</html>