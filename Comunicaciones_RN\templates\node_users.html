<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Chat con {{ other_user.username }}</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
  <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.0/socket.io.min.js"></script>
  <style>
    .body-background::before {
      background: url("{{ url_for('static', filename='RNCom.png') }}") no-repeat center center;
    }
    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;
    }
    .audio-button img {
      cursor: pointer;
      width: 100px;
      height: 100px;
      margin-bottom: 5px;
    }
    .chat-container {
      display: flex;
      justify-content: center;
      width: 80%;
      margin-top: 5px;
    }
    .chat-box {
      border: 1px solid #726e6e;
      padding: 10px;
      height: 220px;
      width: 70%;
      overflow-y: auto;
      background-color: #f0f8ff;
      margin-bottom: 10px;
      border-radius: 10px;
    }
    .chat-input {
      display: flex;
      align-items: center;
      width: 80%;
      margin-bottom: 20px;
    }
    .chat-input input[type="text"] {
      flex: 1;
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 5px;
      margin-right: 10px;
    }
    .chat-input button {
      padding: 5px 10px;
      background-color: #006994;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    .chat-input button:hover {
      background-color: #004d73;
    }
    .message strong {
      color: #006994;
    }
    .timestamp {
      font-size: 0.8em;
      color: #888;
      margin-left: 10px;
    }
    #dragDropArea {
      border: 2px dashed #ccc;
      padding: 10px;
      text-align: center;
      margin-top: 10px;
    }
    #notificationsButton {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: red;
      cursor: pointer;
      margin-left: 10px;
      border: none;
      outline: none;
      transition: background-color: 0.3s ease;
    }
    #notificationsButton.active {
      background-color: green;
    }
  </style>
</head>
<body class="body-background">
  <header>
    <h1> Usuario {{ current_user.username }} En Chat con {{ other_user.username }}</h1>
  </header>
  <main>
    <div class="audio-buttons">
      <img id="transmitAudio" src="{{ url_for('static', filename='verde.png') }}" alt="Transmitir Audio">
    </div>
    <div class="chat-container">
      <div class="chat-box" id="messages"></div>
    </div>
    <div class="chat-input">
      <input type="text" id="chatInput" placeholder="Escribe tu mensaje...">
      <button id="sendButton">Enviar</button>
    </div>
    <div class="download-link">
      {% if current_user.role == 'admin' %}
      <!-- Input para selección de archivo (oculto) -->
      <input type="file" id="fileInput" style="display: none;">
      <!-- Botón visible para cargar archivo -->
      <button id="uploadFileButton">Cargar Archivo</button>
      </div>
      <!-- Área para drag & drop -->
      <div id="dragDropArea">Arrastra y suelta archivos aquí...</div>
      {% endif %}
    </div>
    <div class="navigation-buttons" style="text-align: center; margin-top: 20px;">
      <a href="javascript:history.back()" style="margin-right: 10px;">Volver</a>
      <a href="{{ url_for('views.dashboard') }}">Volver al Dashboard</a>
    </div>
  </main>
  <footer>
    <p>© 2024 Tu Sitio Web</p>
  </footer>
  <script>
    // Definir las URLs de las imágenes
    const rojoImageUrl = "{{ url_for('static', filename='rojo.png') }}";
    const verdeImageUrl = "{{ url_for('static', filename='verde.png') }}";
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Conexión al namespace /private utilizando el parámetro chat_room
      const socket = io.connect('https://' + document.domain + ':' + location.port + '/private', {
        query: {
          chat_room: "{{ chat_room }}",
          username: "{{ current_user.username }}"
        },
        transports: ['websocket']
      });

      // Elementos del DOM
      const messageInput = document.getElementById('chatInput');
      const sendButton = document.getElementById('sendButton');
      const messagesDiv = document.getElementById('messages');
      const transmitAudioButton = document.getElementById('transmitAudio');
      const fileInput = document.getElementById('fileInput');
      const uploadFileButton = document.getElementById('uploadFileButton');
      const dragDropArea = document.getElementById('dragDropArea');

      let mediaRecorder;
      let audioChunks = [];

      function logToFile(message) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
        fetch('/log', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message: `[${timestamp}] ${message}` })
        }).catch(error => console.error('Error logging to file:', error));
      }

      function showNotification(title, body, icon) {
        if (Notification.permission === "granted") {
          new Notification(title, { body, icon });
        }
      }

      function requestNotificationPermission() {
        if (Notification.permission !== "granted") {
          Notification.requestPermission().then(permission => {
            if (permission === "granted") {
              showNotification("Notificaciones Activadas", "Recibirás notificaciones de mensajes.", "/static/RNCom.ico");
            }
          });
        }
      }

      const notificationsButton = document.createElement('button');
      notificationsButton.id = 'notificationsButton';
      notificationsButton.title = 'Activar/Desactivar Notificaciones';
      const chatInputContainer = document.querySelector('.chat-input');
      if (chatInputContainer) {
        chatInputContainer.appendChild(notificationsButton);
      }
      notificationsButton.addEventListener('click', requestNotificationPermission);

      // Función para cargar mensajes privados desde el log
      function loadPrivateMessages() {
    fetch(`/get_private_messages/{{ chat_room }}`)
        .then(response => response.json())
        .then(data => {
            data.messages.forEach(message => {
                const newMessage = document.createElement('p');
                newMessage.className = 'message';
                if (message.indexOf('[FILE]') !== -1) {
                    // Se asume que el mensaje ya viene en formato HTML; quitar el marcador
                    newMessage.innerHTML = message.replace('[FILE]', '');
                } else {
                    // Procesamiento estándar para mensajes de texto
                    const parts = message.split(': ');
                    if (parts.length >= 2) {
                        const user = parts[0];
                        const rest = parts.slice(1).join(': ');
                        const textParts = rest.split(' ');
                        const time = textParts.slice(-2).join(' ');
                        const text = textParts.slice(0, -2).join(' ');
                        newMessage.innerHTML = `<strong>${user}:</strong> ${text} <span class="timestamp">${time}</span>`;
                    } else {
                        newMessage.textContent = message;
                    }
                }
                messagesDiv.appendChild(newMessage);
            });
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        })
        .catch(error => console.error('Error loading private messages:', error));
}

      loadPrivateMessages();

      // Función para enviar mensajes privados
      function sendMessage() {
        const message = messageInput.value.trim();
        if (message !== '') {
          socket.emit('send_private_message', {
            data: message,
            user: "{{ current_user.username }}",
            chat_room: "{{ chat_room }}"
          });
          const newMessage = document.createElement('p');
          newMessage.className = 'message';
          const timestamp = new Date().toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
          newMessage.innerHTML = `<strong>{{ current_user.username }}:</strong> ${message} <span class="timestamp">${timestamp}</span>`;
          messagesDiv.appendChild(newMessage);
          messagesDiv.scrollTop = messagesDiv.scrollHeight;
          messageInput.value = '';
        }
      }

      sendButton.addEventListener('click', sendMessage);
      messageInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') sendMessage();
      });

      // Recepción de mensajes privados
      socket.on('broadcast_private_message', function(data) {
        if (data.user !== "{{ current_user.username }}") {
          const newMessage = document.createElement('p');
          newMessage.className = 'message';
          if (data.fileinfo) {
            newMessage.innerHTML = `<strong>${data.user}:</strong> ha enviado el archivo <a href="${data.fileinfo.filepath}" download="${data.fileinfo.filename}">${data.fileinfo.filename}</a> <span class="timestamp">${data.timestamp || ''}</span>`;
          } else {
            newMessage.innerHTML = `<strong>${data.user}:</strong> ${data.data} <span class="timestamp">${data.timestamp}</span>`;
          }
          messagesDiv.appendChild(newMessage);
          messagesDiv.scrollTop = messagesDiv.scrollHeight;
          showNotification(`Mensaje de ${data.user}`, `${data.fileinfo ? '[Archivo]' : data.data}`, "/static/RNCom.ico");
        }
      });

      // Recepción de archivos privados
      socket.on('receive_private_file', function(data) {
        const newMessage = document.createElement('p');
        newMessage.className = 'message';
        newMessage.innerHTML = `<strong>${data.user}:</strong> envió el archivo <a href="${data.fileinfo.filepath}" download="${data.fileinfo.filename}">${data.fileinfo.filename}</a> <span class="timestamp">${data.timestamp || new Date().toLocaleTimeString()}</span>`;
        messagesDiv.appendChild(newMessage);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
      });

    // Recepción de audio privado: evitar reproducir audio propio y mostrar un reproductor
    socket.on('receive_private_audio', function (data) {
        // Evita reproducir el audio propio
        if (data.user === "{{ current_user.username }}") {
            return;
        }
        try {
            const byteCharacters = atob(data.audio);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const mimeType = data.mimeType || 'audio/wav';
            const audioBlob = new Blob([byteArray], { type: mimeType });
            const audioUrl = URL.createObjectURL(audioBlob);
            const audio = new Audio(audioUrl);
            audio.controls = true;
            // Intentar reproducir el audio; si el navegador lo bloquea, se mostrará el reproductor
            audio.play().catch(err => console.error("Error al reproducir el audio:", err));

            // Crea un mensaje que contenga el reproductor
            const newMessage = document.createElement('p');
            newMessage.className = 'message';
            const timestamp = data.timestamp || new Date().toLocaleTimeString();
            newMessage.innerHTML = `<strong>${data.user}:</strong> (audio) <span class="timestamp">${timestamp}</span><br>`;
            newMessage.appendChild(audio);
            messagesDiv.appendChild(newMessage);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        } catch (e) {
            console.error("Error en la conversión del audio:", e);
        }
    });

      // Función para grabar y transmitir audio en chat privado
      async function startRecording() {
        transmitAudioButton.src = rojoImageUrl;
        // Para el chat privado, usamos chat_room como identificador en lugar de node_id
        socket.emit('audio_start', { user: "{{ current_user.username }}", node_id: String("{{ chat_room }}") });
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          alert('getUserMedia is not supported.');
          return;
        }
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          mediaRecorder = new MediaRecorder(stream);
          mediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
              audioChunks.push(event.data);
            }
          };
          mediaRecorder.onstop = async function() {
            if (audioChunks.length > 0) {
              const mimeType = mediaRecorder.mimeType || 'audio/wav';
              const audioBlob = new Blob(audioChunks, { type: mimeType });
              audioChunks = [];
              const reader = new FileReader();
              reader.onload = function() {
                const base64AudioMessage = reader.result.split(',')[1];
                socket.emit('transmit_private_audio', {
                  audio: base64AudioMessage,
                  user: "{{ current_user.username }}",
                  chat_room: "{{ chat_room }}",
                  mimeType: mimeType
                });
                const timestamp = new Date().toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
                const newMessage = document.createElement('p');
                newMessage.className = 'message';
                newMessage.innerHTML = `<strong>{{ current_user.username }}:</strong> (audio) [AUDIO] <span class="timestamp">${timestamp}</span>`;
                messagesDiv.appendChild(newMessage);
                transmitAudioButton.src = verdeImageUrl;
                socket.emit('audio_end', { user: "{{ current_user.username }}", node_id: String("{{ chat_room }}") });
              };
              reader.readAsDataURL(audioBlob);
            }
          };
          mediaRecorder.start();
        } catch (error) {
          alert('Error accessing media devices: ' + error.message);
        }
      }
      
      function stopRecording() {
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
          mediaRecorder.stop();
        }
      }
      
      if (transmitAudioButton) {
        transmitAudioButton.addEventListener('mousedown', startRecording);
        transmitAudioButton.addEventListener('mouseup', stopRecording);
        transmitAudioButton.addEventListener('touchstart', function(event) {
          event.preventDefault();
          transmitAudioButton.src = rojoImageUrl;
          startRecording();
        });
        transmitAudioButton.addEventListener('touchend', function() {
          stopRecording();
        });
        transmitAudioButton.addEventListener('contextmenu', (e) => e.preventDefault());
      }
      
      // Manejo de carga de archivos en chat privado
      uploadFileButton.addEventListener('click', function() {
        fileInput.click();
      });
      fileInput.addEventListener('change', handleFileUpload);
      dragDropArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        dragDropArea.style.borderColor = "#000";
      });
      dragDropArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dragDropArea.style.borderColor = "#ccc";
      });
      dragDropArea.addEventListener('drop', function(e) {
        e.preventDefault();
        dragDropArea.style.borderColor = "#ccc";
        handleFileDrop(e);
      });
      
      function handleFileUpload(event) {
        const file = event.target.files[0];
        if (file) {
          uploadFile(file);
        }
      }
      
      function handleFileDrop(event) {
        const file = event.dataTransfer.files[0];
        if (file) {
          uploadFile(file);
        }
      }
      
      function uploadFile(file) {
        const MAX_SIZE = 10 * 1024 * 1024; // 10 MB
        if (file.size > MAX_SIZE) {
          alert("El archivo excede el tamaño máximo permitido de 10 MB.");
          return;
        }
        const reader = new FileReader();
        reader.onload = function(e) {
          const base64Data = e.target.result.split(',')[1];
          const payload = {
            fileData: base64Data,
            filename: file.name,
            user: "{{ current_user.username }}",
            chat_room: "{{ chat_room }}"
          };
          socket.emit('transmit_private_file', payload);
        };
        reader.readAsDataURL(file);
      }
      
      // Actualizar lista de usuarios conectados (opcional)
      socket.on('update_users', function(users) {
        const userListDiv = document.getElementById('userListDiv');
        if (userListDiv) {
          userListDiv.innerHTML = '<h3>Usuarios Conectados</h3>';
          if (users.length === 0) {
            userListDiv.innerHTML += '<p>No hay usuarios conectados a este chat.</p>';
          } else {
            users.forEach(user => {
              const userItem = document.createElement('div');
              userItem.className = 'user-item';
              userItem.innerHTML = `<a href="/private_chat/${user}">${user}</a> <img src="/static/audio-icon.png" class="audio-icon" id="audio-${user}">`;
              userListDiv.appendChild(userItem);
            });
          }
        }
      });
      
      socket.on('force_logout', function(data) {
        socket.io.opts.reconnection = false;
        alert(data.message);
        window.location.href = '/logout';
      });
    });
  </script>
</body>
</html>