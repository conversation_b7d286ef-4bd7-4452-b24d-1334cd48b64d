# --- Archivo: app/admin.py ---
# Rutas para administración de usuarios y permisos

from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import User
from app.models_permissions import UserRole, UserPermission, has_permission
from app.forms_permissions import (
    UserRoleForm, UserPermissionForm, UserManagementForm, 
    BulkPermissionForm, PermissionTemplateForm
)
from functools import wraps

bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """Decorador para requerir permisos de administrador"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Debe iniciar sesión para acceder a esta página.', 'warning')
            return redirect(url_for('auth.login'))
        
        if not has_permission('can_manage_users'):
            flash('No tiene permisos para acceder a la administración.', 'danger')
            return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def index():
    """Panel principal de administración"""
    # Estadísticas generales
    total_users = User.query.count()
    users_with_permissions = UserPermission.query.count()
    total_roles = UserRole.query.count()
    
    # Usuarios recientes
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    
    # Roles más utilizados
    role_usage = db.session.query(
        UserRole.name, 
        db.func.count(UserPermission.id).label('count')
    ).join(UserPermission).group_by(UserRole.id).order_by(
        db.func.count(UserPermission.id).desc()
    ).limit(5).all()
    
    return render_template('admin/index.html',
                         title="Administración",
                         total_users=total_users,
                         users_with_permissions=users_with_permissions,
                         total_roles=total_roles,
                         recent_users=recent_users,
                         role_usage=role_usage)

@bp.route('/roles')
@login_required
@admin_required
def list_roles():
    """Lista de roles del sistema"""
    roles = UserRole.query.order_by(UserRole.level.desc()).all()
    return render_template('admin/roles.html', title="Gestión de Roles", roles=roles)

@bp.route('/roles/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_role():
    """Crear nuevo rol"""
    form = UserRoleForm()
    
    if form.validate_on_submit():
        # Verificar que no exista un rol con el mismo nombre
        existing_role = UserRole.query.filter_by(name=form.name.data).first()
        if existing_role:
            flash(f'Ya existe un rol con el nombre "{form.name.data}".', 'danger')
            return render_template('admin/role_form.html', title="Crear Rol", form=form)
        
        role = UserRole(
            name=form.name.data,
            description=form.description.data,
            level=form.level.data
        )
        
        try:
            db.session.add(role)
            db.session.commit()
            flash(f'Rol "{role.name}" creado exitosamente.', 'success')
            current_app.logger.info(f"Rol creado por {current_user.username}: {role.name}")
            return redirect(url_for('admin.list_roles'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creando rol: {e}")
            flash('Error al crear el rol. Intente nuevamente.', 'danger')
    
    return render_template('admin/role_form.html', title="Crear Rol", form=form)

@bp.route('/roles/<int:role_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_role(role_id):
    """Editar rol existente"""
    role = UserRole.query.get_or_404(role_id)
    form = UserRoleForm(obj=role)
    
    if form.validate_on_submit():
        # Verificar que no exista otro rol con el mismo nombre
        existing_role = UserRole.query.filter(
            UserRole.name == form.name.data,
            UserRole.id != role_id
        ).first()
        
        if existing_role:
            flash(f'Ya existe otro rol con el nombre "{form.name.data}".', 'danger')
            return render_template('admin/role_form.html', title="Editar Rol", form=form, role=role)
        
        role.name = form.name.data
        role.description = form.description.data
        role.level = form.level.data
        
        try:
            db.session.commit()
            flash(f'Rol "{role.name}" actualizado exitosamente.', 'success')
            current_app.logger.info(f"Rol editado por {current_user.username}: {role.name}")
            return redirect(url_for('admin.list_roles'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error editando rol: {e}")
            flash('Error al actualizar el rol. Intente nuevamente.', 'danger')
    
    return render_template('admin/role_form.html', title="Editar Rol", form=form, role=role)

@bp.route('/roles/<int:role_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_role(role_id):
    """Eliminar rol"""
    role = UserRole.query.get_or_404(role_id)
    
    # Verificar que no haya usuarios usando este rol
    users_with_role = UserPermission.query.filter_by(role_id=role_id).count()
    if users_with_role > 0:
        flash(f'No se puede eliminar el rol "{role.name}" porque {users_with_role} usuario(s) lo están usando.', 'danger')
        return redirect(url_for('admin.list_roles'))
    
    try:
        db.session.delete(role)
        db.session.commit()
        flash(f'Rol "{role.name}" eliminado exitosamente.', 'success')
        current_app.logger.info(f"Rol eliminado por {current_user.username}: {role.name}")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error eliminando rol: {e}")
        flash('Error al eliminar el rol. Intente nuevamente.', 'danger')
    
    return redirect(url_for('admin.list_roles'))

@bp.route('/users')
@login_required
@admin_required
def list_users():
    """Lista de usuarios con sus permisos"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    users = User.query.outerjoin(UserPermission).add_columns(
        UserPermission.role_id,
        UserPermission.cities,
        UserPermission.sources
    ).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Obtener información de roles
    roles = {role.id: role for role in UserRole.query.all()}
    
    return render_template('admin/users.html', 
                         title="Gestión de Usuarios", 
                         users=users,
                         roles=roles)

@bp.route('/users/<int:user_id>/permissions', methods=['GET', 'POST'])
@login_required
@admin_required
def manage_user_permissions(user_id):
    """Gestionar permisos de un usuario específico"""
    user = User.query.get_or_404(user_id)
    permissions = UserPermission.query.filter_by(user_id=user_id).first()
    
    form = UserPermissionForm()
    
    if request.method == 'GET' and permissions:
        # Cargar datos existentes en el formulario
        form.role_id.data = permissions.role_id
        form.cities.data = permissions.get_allowed_cities()
        form.sources.data = permissions.get_allowed_sources()
        
        # Cargar permisos booleanos
        for field_name in form._fields:
            if field_name.startswith('can_') and hasattr(permissions, field_name):
                getattr(form, field_name).data = getattr(permissions, field_name)
    
    if form.validate_on_submit():
        if not permissions:
            permissions = UserPermission(user_id=user_id)
            db.session.add(permissions)
        
        # Actualizar datos básicos
        permissions.role_id = form.role_id.data if form.role_id.data else None
        permissions.set_allowed_cities(form.cities.data)
        permissions.set_allowed_sources(form.sources.data)
        
        # Actualizar permisos booleanos
        for field_name in form._fields:
            if field_name.startswith('can_') and hasattr(permissions, field_name):
                setattr(permissions, field_name, getattr(form, field_name).data)
        
        try:
            db.session.commit()
            flash(f'Permisos de {user.username} actualizados exitosamente.', 'success')
            current_app.logger.info(f"Permisos actualizados por {current_user.username} para usuario: {user.username}")
            return redirect(url_for('admin.list_users'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error actualizando permisos: {e}")
            flash('Error al actualizar los permisos. Intente nuevamente.', 'danger')
    
    return render_template('admin/user_permissions.html',
                         title=f"Permisos de {user.username}",
                         form=form,
                         user=user,
                         permissions=permissions)

@bp.route('/users/bulk-permissions', methods=['GET', 'POST'])
@login_required
@admin_required
def bulk_permissions():
    """Asignación masiva de permisos"""
    form = BulkPermissionForm()
    
    if form.validate_on_submit():
        role = UserRole.query.get(form.role_id.data)
        if not role:
            flash('Rol seleccionado no válido.', 'danger')
            return render_template('admin/bulk_permissions.html', title="Permisos Masivos", form=form)
        
        users_updated = 0
        errors = []
        
        for user_id in form.users.data:
            try:
                user = User.query.get(user_id)
                if not user:
                    continue
                
                # Buscar o crear permisos
                permissions = UserPermission.query.filter_by(user_id=user_id).first()
                if not permissions:
                    permissions = UserPermission(user_id=user_id)
                    db.session.add(permissions)
                elif not form.override_existing.data:
                    continue  # Saltar si ya tiene permisos y no se debe sobrescribir
                
                # Crear permisos basados en el rol
                new_permissions = UserPermission.create_default_permissions(user_id, role.name)
                
                # Aplicar restricciones adicionales si se especificaron
                if form.apply_city_restrictions.data and form.cities.data:
                    new_permissions.set_allowed_cities(form.cities.data)
                
                if form.apply_source_restrictions.data and form.sources.data:
                    new_permissions.set_allowed_sources(form.sources.data)
                
                # Copiar permisos al objeto existente
                for attr in ['role_id', 'cities', 'sources'] + [f for f in dir(new_permissions) if f.startswith('can_')]:
                    if hasattr(new_permissions, attr):
                        setattr(permissions, attr, getattr(new_permissions, attr))
                
                users_updated += 1
                
            except Exception as e:
                errors.append(f"Error con usuario {user_id}: {str(e)}")
        
        try:
            db.session.commit()
            flash(f'Permisos actualizados para {users_updated} usuario(s).', 'success')
            if errors:
                for error in errors:
                    flash(error, 'warning')
            
            current_app.logger.info(f"Permisos masivos aplicados por {current_user.username}: {users_updated} usuarios")
            return redirect(url_for('admin.list_users'))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error en permisos masivos: {e}")
            flash('Error al aplicar permisos masivos. Intente nuevamente.', 'danger')
    
    return render_template('admin/bulk_permissions.html', title="Permisos Masivos", form=form)

@bp.route('/initialize-system', methods=['POST'])
@login_required
@admin_required
def initialize_system():
    """Inicializar sistema de permisos con roles predeterminados"""
    try:
        # Crear roles predeterminados
        default_roles = UserRole.get_default_roles()
        roles_created = 0
        
        for role_data in default_roles:
            existing_role = UserRole.query.filter_by(name=role_data['name']).first()
            if not existing_role:
                role = UserRole(**role_data)
                db.session.add(role)
                roles_created += 1
        
        # Crear permisos para usuarios existentes sin permisos
        users_without_permissions = User.query.outerjoin(UserPermission).filter(
            UserPermission.user_id.is_(None)
        ).all()
        
        permissions_created = 0
        for user in users_without_permissions:
            # Asignar rol "Usuario Básico" por defecto
            UserPermission.create_default_permissions(user.id, 'Usuario Básico')
            permissions_created += 1
        
        db.session.commit()
        
        flash(f'Sistema inicializado: {roles_created} roles creados, {permissions_created} permisos asignados.', 'success')
        current_app.logger.info(f"Sistema de permisos inicializado por {current_user.username}")
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error inicializando sistema: {e}")
        flash('Error al inicializar el sistema. Intente nuevamente.', 'danger')
    
    return redirect(url_for('admin.index'))
