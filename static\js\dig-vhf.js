// /static/js/dig-vhf.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;

    console.log(`Conectado como ${username} al nodo ${nodeId} para Digital a VHF.`);

    const socket = io.connect('https://patagoniaservers.com.ar:5000/node', {
        transports: ['websocket'],
        query: {
            node_id: String(nodeId),  // Forzar a cadena
            username: username
        }
    });

    let isTransmittingAudio = false;

    socket.on('connect', () => {
        console.log('Conexión exitosa con Socket.IO para Digital a VHF.');
    });

    socket.on('receive_audio', (data) => {
        console.log('Recibiendo audio, activando PTT...', data);
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);
            return;
        }
        if (!data.audio || data.audio.trim() === "") {
            console.error("Error: Audio vacío recibido.");
            return;
        }
        console.log('Enviando evento ptt_control: Activando PTT en 1 segundo...');
        activarPTT();
        setTimeout(() => {
            try {
                const audioBlob = new Blob(
                    [new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))],
                    { type: 'audio/wav' }
                );
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);
                audio.play().then(() => {
                    console.log('Reproduciendo audio de otro usuario...');
                }).catch((error) => {
                    console.error('Error al reproducir el audio:', error);
                });
                audio.addEventListener('ended', () => {
                    console.log('Audio finalizado. Desactivando PTT en 1 segundo...');
                    setTimeout(() => {
                        desactivarPTT();
                    }, 1000);
                });
            } catch (error) {
                console.error('Error al decodificar o reproducir el audio:', error);
            }
        }, 1000);
    });

    socket.on('audio_start', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha comenzado a transmitir audio. Evitando activación de PTT...`);
            isTransmittingAudio = true;
        }
    });

    socket.on('audio_end', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha terminado la transmisión de audio.`);
            isTransmittingAudio = false;
        }
    });

    // Manejador para el evento audio_received emitido desde el servidor
    socket.on('audio_received', (data) => {
        console.log('Evento audio_received recibido:', data);
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);
            return;
        }
        if (!data.audio || data.audio.trim() === "") {
            console.error("Error: Audio vacío recibido en audio_received.");
            return;
        }

        console.log('Activando PTT desde audio_received...');
        activarPTT();

        setTimeout(() => {
            try {
                const audioBlob = new Blob(
                    [new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))],
                    { type: 'audio/wav' }
                );
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);

                audio.play().then(() => {
                    console.log('Reproduciendo audio desde audio_received...');
                }).catch((error) => {
                    console.error('Error al reproducir el audio desde audio_received:', error);
                });

                audio.addEventListener('ended', () => {
                    console.log('Audio finalizado desde audio_received. Desactivando PTT...');
                    setTimeout(() => {
                        desactivarPTT();
                    }, 1000);
                });
            } catch (error) {
                console.error('Error al procesar audio desde audio_received:', error);
                desactivarPTT();
            }
        }, 1000);
    });

    function activarPTT() {
        if (!isTransmittingAudio) {
            console.log("Activando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ptt_state: true })
            })
            .then(response => {
                if (!response.ok) { throw new Error('Error en la solicitud de activación del PTT'); }
                return response.json();
            })
            .then(data => { console.log('Respuesta del servidor:', data); })
            .catch((error) => { console.error('Error al activar el PTT:', error); });
        } else {
            console.log('PTT no activado ya que el usuario está transmitiendo audio.');
        }
    }

    function desactivarPTT() {
        if (!isTransmittingAudio) {
            console.log("Desactivando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ptt_state: false })
            })
            .then(response => {
                if (!response.ok) { throw new Error('Error en la solicitud de desactivación del PTT'); }
                return response.json();
            })
            .then(data => { console.log('Respuesta del servidor:', data); })
            .catch((error) => { console.error('Error al desactivar el PTT:', error); });
        } else {
            console.log('PTT no desactivado ya que el usuario está transmitiendo audio.');
        }
    }
});
