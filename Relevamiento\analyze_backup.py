#!/usr/bin/env python3
# --- Archivo: analyze_backup.py ---
# Script para analizar y restaurar desde app.db.bkp

import os
import sqlite3
import shutil
from datetime import datetime

def analyze_backup():
    """Analizar el contenido del backup app.db.bkp."""
    print("🔍 Analizando backup: instance/app.db.bkp")
    
    backup_path = "instance/app.db.bkp"
    current_path = "instance/app.db"
    
    if not os.path.exists(backup_path):
        print("❌ No existe instance/app.db.bkp")
        return False
    
    # Mostrar tamaños
    backup_size = os.path.getsize(backup_path)
    current_size = os.path.getsize(current_path) if os.path.exists(current_path) else 0
    
    print(f"📊 Tamaños:")
    print(f"  📄 app.db.bkp: {backup_size:,} bytes")
    print(f"  📄 app.db actual: {current_size:,} bytes")
    
    try:
        # <PERSON><PERSON><PERSON> backup
        print(f"\n🔍 Analizando backup...")
        conn = sqlite3.connect(backup_path)
        cursor = conn.cursor()
        
        # Obtener tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas en backup: {tables}")
        
        # Verificar datos en cada tabla
        backup_data = {}
        total_records = 0
        
        for table in tables:
            if table == 'sqlite_sequence':
                continue
                
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                backup_data[table] = count
                total_records += count
                
                print(f"  📊 {table}: {count} registros")
                
                # Mostrar muestras de datos importantes
                if count > 0:
                    if table == 'point':
                        cursor.execute("SELECT name, city, source FROM point LIMIT 5;")
                        points = cursor.fetchall()
                        print(f"    📍 Puntos ejemplo: {points}")
                    
                    elif table == 'image':
                        cursor.execute("SELECT filename FROM image LIMIT 5;")
                        images = cursor.fetchall()
                        print(f"    🖼️  Imágenes ejemplo: {[i[0] for i in images]}")
                    
                    elif table == 'camera':
                        cursor.execute("SELECT type, point_id FROM camera LIMIT 5;")
                        cameras = cursor.fetchall()
                        print(f"    📷 Cámaras ejemplo: {cameras}")
                    
                    elif table == 'user':
                        cursor.execute("SELECT username FROM user;")
                        users = cursor.fetchall()
                        print(f"    👥 Usuarios: {[u[0] for u in users]}")
                
            except Exception as e:
                print(f"    ❌ Error leyendo {table}: {e}")
                backup_data[table] = 0
        
        conn.close()
        
        print(f"\n📊 Total registros en backup: {total_records}")
        
        # Analizar base de datos actual para comparar
        print(f"\n🔍 Analizando base de datos actual...")
        
        if os.path.exists(current_path):
            conn = sqlite3.connect(current_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            current_tables = [table[0] for table in cursor.fetchall()]
            
            print(f"📋 Tablas actuales: {current_tables}")
            
            current_data = {}
            current_total = 0
            
            for table in current_tables:
                if table == 'sqlite_sequence':
                    continue
                    
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = cursor.fetchone()[0]
                    current_data[table] = count
                    current_total += count
                    print(f"  📊 {table}: {count} registros")
                except:
                    current_data[table] = 0
            
            conn.close()
            
            print(f"\n📊 Total registros actuales: {current_total}")
            
            # Comparar
            print(f"\n📈 Comparación:")
            for table in set(list(backup_data.keys()) + list(current_data.keys())):
                backup_count = backup_data.get(table, 0)
                current_count = current_data.get(table, 0)
                
                if backup_count > current_count:
                    print(f"  ✅ {table}: Backup tiene más datos ({backup_count} vs {current_count})")
                elif backup_count < current_count:
                    print(f"  ⚠️  {table}: Actual tiene más datos ({current_count} vs {backup_count})")
                else:
                    print(f"  ➖ {table}: Igual cantidad ({backup_count})")
        
        return backup_data, total_records > 0
        
    except Exception as e:
        print(f"❌ Error analizando backup: {e}")
        return None, False

def restore_from_backup():
    """Restaurar desde el backup preservando la estructura de permisos."""
    print("\n🔄 Restaurando desde backup...")
    
    backup_path = "instance/app.db.bkp"
    current_path = "instance/app.db"
    
    try:
        # Hacer backup de la base de datos actual
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safety_backup = f"safety_backup_{timestamp}.db"
        
        if os.path.exists(current_path):
            shutil.copy2(current_path, safety_backup)
            print(f"✅ Backup de seguridad creado: {safety_backup}")
        
        # Copiar backup como base
        shutil.copy2(backup_path, current_path)
        print("✅ Backup restaurado como base")
        
        # Ahora actualizar la estructura para incluir permisos
        conn = sqlite3.connect(current_path)
        cursor = conn.cursor()
        
        # Verificar estructura de tabla user
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print("🔧 Actualizando estructura de permisos...")
        
        # Agregar columnas de permisos si no existen
        if 'role' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN role VARCHAR(20) DEFAULT 'administrador';")
            print("  ✅ Columna 'role' agregada")
        
        if 'is_active' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFAULT TRUE;")
            print("  ✅ Columna 'is_active' agregada")
        
        if 'created_by' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN created_by INTEGER;")
            print("  ✅ Columna 'created_by' agregada")
        
        if 'created_at' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;")
            print("  ✅ Columna 'created_at' agregada")
        
        if 'updated_at' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;")
            print("  ✅ Columna 'updated_at' agregada")
        
        # Crear tablas de permisos
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        if 'user_city_permissions' not in tables:
            cursor.execute('''
                CREATE TABLE user_city_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    city VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, city)
                );
            ''')
            print("  ✅ Tabla user_city_permissions creada")
        
        if 'user_source_permissions' not in tables:
            cursor.execute('''
                CREATE TABLE user_source_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    source VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, source)
                );
            ''')
            print("  ✅ Tabla user_source_permissions creada")
        
        if 'user_permissions' not in tables:
            cursor.execute('''
                CREATE TABLE user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    permission_type VARCHAR(50) NOT NULL,
                    permission_value BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, permission_type)
                );
            ''')
            print("  ✅ Tabla user_permissions creada")
        
        # Actualizar usuarios existentes
        cursor.execute("UPDATE user SET role = 'administrador' WHERE role IS NULL OR role = '';")
        cursor.execute("UPDATE user SET is_active = TRUE WHERE is_active IS NULL;")
        cursor.execute("UPDATE user SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL;")
        cursor.execute("UPDATE user SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;")
        
        # Crear usuario admin si no existe
        cursor.execute("SELECT COUNT(*) FROM user WHERE username = 'admin';")
        admin_exists = cursor.fetchone()[0] > 0
        
        if not admin_exists:
            from werkzeug.security import generate_password_hash
            password_hash = generate_password_hash('isaias52')
            cursor.execute('''
                INSERT INTO user (username, password_hash, role, is_active, created_at, updated_at)
                VALUES ('admin', ?, 'administrador', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (password_hash,))
            print("  ✅ Usuario admin creado")
        
        conn.commit()
        conn.close()
        
        # Verificar resultado
        conn = sqlite3.connect(current_path)
        cursor = conn.cursor()
        
        print("\n📊 Verificando resultado:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        final_tables = [table[0] for table in cursor.fetchall()]
        
        for table in ['user', 'point', 'image', 'camera']:
            if table in final_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                print(f"  📊 {table}: {count} registros")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error restaurando: {e}")
        return False

def main():
    """Función principal."""
    print("🔍 Analizador de Backup")
    print("=" * 30)
    
    # Analizar backup
    backup_data, has_data = analyze_backup()
    
    if has_data:
        print("\n✅ El backup contiene datos")
        
        response = input("\n¿Restaurar desde backup? (s/n): ").strip().lower()
        
        if response in ['s', 'si', 'y', 'yes']:
            if restore_from_backup():
                print("\n🎉 ¡Backup restaurado exitosamente!")
                print("\n🚀 Próximos pasos:")
                print("   1. Reiniciar aplicación: systemctl restart relevamiento")
                print("   2. Verificar datos: python3 check_db.py")
                print("   3. Probar aplicación web")
                print("   4. Crear templates HTML para gestión de usuarios")
            else:
                print("\n❌ Error restaurando backup")
        else:
            print("❌ Restauración cancelada")
    else:
        print("\n❌ El backup no contiene datos útiles")

if __name__ == "__main__":
    main()
