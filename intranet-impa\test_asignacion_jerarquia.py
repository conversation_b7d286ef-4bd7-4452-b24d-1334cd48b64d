# test_asignacion_jerarquia.py

from app import create_app, db
from app.models import User, JerarquiaPastores, JerarquiaSuperintendentes

app = create_app()

def asignar_superintendente_a_pastor(pastor_id, supervisor_id):
    with app.app_context():
        pastor = User.query.get(pastor_id)
        supervisor = User.query.get(supervisor_id)

        if not pastor or not supervisor:
            print("❌ Usuario no encontrado.")
            return

        # Verificar si ya existe
        existente = JerarquiaPastores.query.filter_by(pastor_id=pastor_id, supervisor_id=supervisor_id).first()
        if existente:
            print("⚠️ Ya existe la relación Pastor -> Superintendente.")
            return

        # Crear y guardar
        nueva = JerarquiaPastores(pastor_id=pastor_id, supervisor_id=supervisor_id)
        db.session.add(nueva)
        db.session.commit()
        print(f"✅ Asignado {supervisor.full_name} como supervisor de {pastor.full_name}.")

def asignar_jefe_a_superintendente(superintendente_id, jefe_id):
    with app.app_context():
        superintendente = User.query.get(superintendente_id)
        jefe = User.query.get(jefe_id)

        if not superintendente or not jefe:
            print("❌ Usuario no encontrado.")
            return

        # Verificar si ya existe
        existente = JerarquiaSuperintendentes.query.filter_by(superintendente_id=superintendente_id, jefe_sector_id=jefe_id).first()
        if existente:
            print("⚠️ Ya existe la relación Superintendente -> Jefe de sector.")
            return

        # Crear y guardar
        nueva = JerarquiaSuperintendentes(superintendente_id=superintendente_id, jefe_sector_id=jefe_id)
        db.session.add(nueva)
        db.session.commit()
        print(f"✅ Asignado {jefe.full_name} como jefe de {superintendente.full_name}.")

if __name__ == '__main__':
    print("=== Asignación de Jerarquías ===")

    # Modificá estos IDs según tu base de datos
    pastor_id = 203
    superintendente_id = 202
    jefe_id = 201

    asignar_superintendente_a_pastor(pastor_id, superintendente_id)
    asignar_jefe_a_superintendente(superintendente_id, jefe_id)
