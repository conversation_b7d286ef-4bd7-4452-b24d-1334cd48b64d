#!/usr/bin/env python3
"""
Script para agregar (o actualizar) las funciones predefinidas para los miembros.
Este script se puede usar para insertar nuevas funciones en el futuro.
"""

from app import create_app, db  # Asumiendo que usas una fábrica de aplicación
from app.models import MemberFunction

app = create_app()  # Crea la aplicación
with app.app_context():
    funciones_predefinidas = [
        '<PERSON><PERSON> de Coro',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON>yu<PERSON><PERSON> de Pastor',
        '<PERSON><PERSON><PERSON> de Obra',
        'Maestra/o Escuela Dominical',
        'Director/a Escuela Dominical',
        'Ministro Alabanza',
        'Alabanza',
        'Pasillera/o',
        'Voluntario',
        'Dorca',
        'Encargado de Anexo',
        'Oficial',
        'Tesorero',
        'Sonidista',
        'Multimedia',
        'Relaciones Publicas',
        'Radiooperador',
        'Programa de Radio',
        'Je<PERSON> de Jovenes',
        'Ju<PERSON>ud',
        'Accion Social',
        '<PERSON><PERSON>',
        '<PERSON>yudanta de la Pastora'
    ]
    
    for nombre in funciones_predefinidas:
        if not MemberFunction.query.filter_by(name=nombre).first():
            nueva_funcion = MemberFunction(name=nombre)
            db.session.add(nueva_funcion)
    
    db.session.commit()
    print("Funciones predefinidas agregadas correctamente.")
