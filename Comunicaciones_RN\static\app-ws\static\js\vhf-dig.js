// /static/js/vhf-dig.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;

    console.log(`Conectado como ${username} al nodo ${nodeId}`);  // Log inicial para verificar el usuario y nodo

    // Conectar al servidor usando Socket.IO al nodo específico
    const socket = io.connect(`https://www.patagoniaservers.com.ar:5000/node`, {
        transports: ['websocket'],  // Usamos websocket en lugar de polling
        query: {
            node_id: nodeId,
            username: username
        }
    });

    let mediaRecorder;
    let audioChunks = [];
    let isRecording = false;
    let isTransmittingAudio = false;  // Variable para verificar si ya se está transmitiendo audio

    const volumeMeter = document.getElementById('volume_meter');
    const volumeThreshold = 5;  // Umbral de volumen para empezar a grabar

    function updateVolumeMeter(volume) {
        if (volumeMeter) {
            volumeMeter.style.width = `${volume}%`;
            volumeMeter.style.backgroundColor = volume >= 50 ? 'red' : 'green';
        }
    }

    async function captureAudioAutomatically() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);

            mediaRecorder.ondataavailable = (event) => {
                audioChunks.push(event.data);
            };

            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                audioChunks = [];
                const reader = new FileReader();
                reader.onload = function () {
                    const base64AudioMessage = reader.result.split(',')[1];
                    console.log(`Enviando audio desde ${username} al nodo ${nodeId}`);  // Log para verificar envío de audio

                    socket.emit('transmit_audio', {
                        audio: base64AudioMessage,
                        user: username,  // Enviamos el usuario que está transmitiendo
                        node_id: nodeId
                    });
                };
                reader.readAsDataURL(audioBlob);
            };

            const audioContext = new AudioContext();
            const source = audioContext.createMediaStreamSource(stream);
            const analyser = audioContext.createAnalyser();
            source.connect(analyser);

            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            const bufferLength = analyser.frequencyBinCount;

            const checkVolume = () => {
                analyser.getByteFrequencyData(dataArray);
                let sum = 0;
                for (let i = 0; i < bufferLength; i++) {
                    sum += dataArray[i];
                }
                const averageVolume = sum / bufferLength;
                updateVolumeMeter(averageVolume);

                if (!isTransmittingAudio) {  // Solo grabar si no estamos transmitiendo audio
                    if (averageVolume > volumeThreshold && mediaRecorder.state === 'inactive') {
                        console.log("Volumen alto detectado, iniciando grabación...");  // Log de inicio de grabación
                        mediaRecorder.start();
                        isRecording = true;
                    } else if (averageVolume <= volumeThreshold && mediaRecorder.state === 'recording') {
                        console.log("Volumen bajo detectado, deteniendo grabación...");
                        mediaRecorder.stop();
                        isRecording = false;
                    }
                } else {
                    console.log("Ignorando la grabación ya que el usuario está transmitiendo audio.");
                }
            };

            setInterval(checkVolume, 100);
        } catch (error) {
            console.error('Error al acceder al micrófono:', error);
        }
    }

    captureAudioAutomatically();

    // Evento desde el servidor que indica que un usuario está transmitiendo audio
    socket.on('audio_start', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha comenzado a transmitir audio. Evitando grabación...`);
            isTransmittingAudio = true;  // Si el usuario está transmitiendo, evitamos la grabación
        }
    });

    // Evento desde el servidor que indica que la transmisión de audio ha finalizado
    socket.on('audio_end', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha terminado la transmisión de audio.`);
            isTransmittingAudio = false;  // Ya no se está transmitiendo, podemos grabar de nuevo
        }
    });

    // Evento cuando se recibe audio del servidor
    socket.on('transmit_audio', (data) => {
        console.log('Recibiendo audio', data);  // Log para verificar datos del audio recibido

        // Comprobar si el usuario que envía el audio es el mismo que el usuario actual
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);  // Log para confirmar que estamos ignorando el audio propio
            return;  // Ignorar el audio si es el propio
        }

        // Aquí procesas el audio solo si proviene de otro usuario
        console.log('Reproduciendo audio de otro usuario...');  // Log para audio de otro usuario
        // Lógica para reproducir el audio si es necesario
    });
});
