### Índice del Manual del Usuario Final

1. **Introducción**
   - Descripción general de la aplicación
   - Requisitos del sistema
   - Objetivo del manual

2. **Acceso a la Aplicación**
   - Ingreso a la plataforma (pantalla de **login**)
   - Recuperación de contraseñas

3. **Panel de Control**
   - Vista general del **dashboard**
   - Acceso a nodos de comunicación
   - Visualización de nodos activos

4. **Gestión de Nodos de Comunicación**
   - Creación de nodos (**create_node.html**)
   - Edición de nodos (**edit_node.html**)
   - Eliminación de nodos
   - Configuración de PTT (Push-to-Talk) (**ptt_config.html**)
   - Vista detallada de un nodo y sus usuarios (**node.html**, **node_users.html**)

5. **Administración de Usuarios**
   - Creación de usuarios (**create_user.html**)
   - Edición y eliminación de usuarios (**edit_user.html**)
   - Visualización de usuarios conectados (**connected_users.html**)
   - Gestión de permisos de acceso a nodos

6. **Comunicación en Tiempo Real**
   - Envío de mensajes de texto en tiempo real
   - Conteo y visualización de oyentes activos

7. **Transmisión de Audio en Tiempo Real**
   - Configuración de la transmisión de audio
   - Gestión de la transmisión en nodos

8. **Gestión de Accesos y Permisos**
   - Definición de roles de usuario
   - Control de accesos a nodos


## 1. Introducción

### Descripción General de la Aplicación

Esta aplicación web, desarrollada en Python utilizando el microframework **Flask**, está diseñada para la **administración de nodos de comunicación** y la gestión de accesos controlados basados en permisos de usuario. Proporciona a los administradores la capacidad de **visualizar nodos de comunicación activos**, monitorear el **conteo de oyentes** y gestionar la **transmisión de audio en tiempo real**.

Además, la aplicación permite a los usuarios autorizados **enviar mensajes de texto en tiempo real** y gestionar los accesos a los nodos de comunicación. Su interfaz amigable y su sistema de permisos aseguran que cada usuario tenga un acceso controlado según sus roles asignados.

### Requisitos del Sistema

Para utilizar la aplicación, es necesario cumplir con los siguientes requisitos de hardware y software:

- **Sistema Operativo**: Windows, macOS o Linux.
- **Navegador Compatible**: Google Chrome, Mozilla Firefox, Microsoft Edge (versiones más recientes).
- **Conexión a Internet**: Se requiere una conexión estable para la transmisión de audio y mensajes en tiempo real.
- **Hardware Recomendado**: Micrófono y altavoces/auriculares para la función de transmisión de audio.
- **Resolución de Pantalla Mínima**: 1024x768.

### Objetivo del Manual

El objetivo de este manual es proporcionar una **guía clara y detallada** para los usuarios finales de la aplicación. Está estructurado para ofrecer instrucciones paso a paso sobre cómo utilizar las diversas funcionalidades, desde el acceso al sistema hasta la administración de nodos y usuarios. Este documento está dirigido a usuarios con distintos niveles de experiencia, asegurando que tanto los **administradores** como los **usuarios básicos** puedan aprovechar al máximo las capacidades de la plataforma.

En las siguientes secciones, se detallarán todas las funcionalidades de la aplicación y cómo cada usuario puede interactuar con ellas de manera eficiente. Si bien no se requieren conocimientos técnicos avanzados, se recomienda seguir las instrucciones cuidadosamente para asegurar un uso óptimo del sistema.

Claro, Ulises. A continuación, te desarrollo la sección de **Acceso a la Aplicación**:

## 2. Acceso a la Aplicación

### Ingreso a la Plataforma

Para acceder a la plataforma, los usuarios deben autenticarse mediante su **nombre de usuario** y **contraseña**. A continuación, se detalla el proceso para iniciar sesión en la aplicación:

1. **Abrir el navegador** web compatible y navegar hacia la URL proporcionada por el administrador del sistema.
2. En la página principal, se encontrará el **formulario de inicio de sesión**.
3. **Ingresar el nombre de usuario** en el campo correspondiente.
4. **Ingresar la contraseña** en el campo destinado para tal fin.
5. Hacer clic en el botón **"Iniciar sesión"** para acceder a la aplicación.

## 3. Panel de Control

### Vista General del Dashboard

Una vez que el usuario ha iniciado sesión correctamente, será redirigido al **panel de control** o **dashboard**, que actúa como la pantalla principal de la aplicación. El **dashboard** proporciona una vista rápida de la actividad actual de los nodos de comunicación y los usuarios conectados. 

#### Elementos principales del Panel de Control:

1. **Resumen de Nodos Activos**: 
   - El dashboard muestra una lista de los **nodos de comunicación activos**, proporcionando información clave como el nombre del nodo, estado (activo/inactivo), número de oyentes y usuarios conectados.
   
2. **Acceso Rápido a Funciones**:
   - Desde el panel de control, el usuario tiene acceso rápido a otras funcionalidades clave de la aplicación como:
     - **Gestión de nodos**: creación, edición y eliminación.
     - **Gestión de usuarios**: visualización de usuarios conectados, creación y edición de cuentas.
     - **Estadísticas en tiempo real**: conteo de oyentes activos y usuarios interactuando en los nodos.

3. **Envío de Mensajes en Tiempo Real**:
   - Los usuarios con permisos adecuados pueden utilizar el panel de control para **enviar mensajes de texto en tiempo real** a través de la plataforma. Esto puede hacerse hacia todos los nodos o solo a un nodo específico, dependiendo de las necesidades de comunicación.

### Acceso a Nodos de Comunicación

El panel de control proporciona acceso directo a los **nodos de comunicación**, donde los usuarios pueden:

- Acceder a detalles sobre los usuarios conectados al nodo.
- Iniciar, detener o configurar la **transmisión de audio en tiempo real** en cada nodo.
- Monitorear el **número de oyentes** en tiempo real y la actividad en los canales de comunicación.


## 4. Gestión de Nodos de Comunicación

La **gestión de nodos de comunicación** es una de las funciones principales de la aplicación, permitiendo a los administradores crear, editar, y gestionar los nodos a través de los cuales los usuarios pueden comunicarse en tiempo real. Desde la interfaz de administración, se pueden controlar todos los aspectos de los nodos, lo que incluye la configuración, activación y monitoreo de cada uno de ellos.

### Creación de Nodos

Para **crear un nuevo nodo de comunicación**, los administradores deben seguir estos pasos:

1. **Acceder a la página de creación de nodos**:
   - En el panel de control, hacer clic en la opción **"Crear nodo"** o acceder a través del menú de administración.
      
### Configuración de PTT (Push-to-Talk)

La aplicación soporta el uso de **Push-to-Talk (PTT)**, que permite a los usuarios enviar audio únicamente mientras se mantiene presionado un botón, similar a un radio bidireccional. Para **configurar PTT en un nodo**, sigue estos pasos:

1. **Acceder a la página de configuración PTT**:
   - Desde la página de configuración del nodo o en el menú de administración, seleccionar **"Configurar PTT"**.
   
### Vista Detallada de un Nodo

Cada nodo tiene una vista detallada que muestra información en tiempo real sobre los usuarios conectados y su actividad. Para acceder a esta vista:

1. **Seleccionar el nodo en la lista**:
   - Desde el panel de control o la página de nodos, hacer clic en el nodo que se desea inspeccionar.

2. **Visualizar la actividad del nodo**:
   - La vista detallada mostrará información como:
     - Usuarios actualmente conectados.
     - Conteo de oyentes en tiempo real.


## 5. Administración de Usuarios

La aplicación ofrece a los administradores la posibilidad de gestionar los usuarios que interactúan con los nodos de comunicación, incluyendo la creación, edición, eliminación y asignación de permisos. De esta manera, se garantiza un acceso controlado y adecuado según las responsabilidades o roles asignados a cada usuario.

### Creación de Usuarios

Para agregar un nuevo usuario a la plataforma, sigue estos pasos:

1. **Acceder a la página de creación de usuarios**:
   - Desde el panel de control o el menú de administración, hacer clic en **"Crear usuario"**.

2. **Llenar los detalles del usuario**:
   - En el formulario de creación de usuarios, se deben completar los siguientes campos:
     - **Nombre de usuario**: el identificador único que utilizará el usuario para acceder al sistema.
     - **Correo electrónico**: dirección de contacto del usuario.
     - **Contraseña**: establecer una contraseña inicial que el usuario podrá cambiar posteriormente.
     - **Rol**: asignar el rol correspondiente al usuario (por ejemplo, **administrador** o **usuario básico**).

3. **Guardar el usuario**:
   - Una vez ingresada toda la información, hacer clic en **"Guardar"** para crear la cuenta del usuario en el sistema.

4. **Notificación al usuario**:
   - Si está configurado, el sistema puede enviar un correo electrónico al nuevo usuario con las instrucciones de inicio de sesión, incluyendo el nombre de usuario y la contraseña inicial.

### Edición y Eliminación de Usuarios

Es posible modificar la información de un usuario o eliminarlo si ya no requiere acceso a la plataforma. A continuación se describen ambos procesos.

#### Edición de usuarios

1. **Acceder a la lista de usuarios**:
   - Desde el menú de administración o la página principal de usuarios, hacer clic en la opción **"Usuarios"**.

2. **Seleccionar el usuario a editar**:
   - En la lista de usuarios, hacer clic en el nombre del usuario que se desea modificar.

3. **Modificar la información**:
   - Se puede cambiar cualquier campo necesario, incluyendo el nombre de usuario, correo electrónico, permisos o rol. También es posible restablecer la contraseña si el usuario lo solicita.

4. **Guardar los cambios**:
   - Hacer clic en **"Guardar"** para actualizar la información del usuario.

#### Eliminación de usuarios

Si un usuario ya no requiere acceso al sistema, se puede eliminar su cuenta de la siguiente manera:

1. **Acceder a la lista de usuarios**:
   - Ir al menú de administración o al panel de usuarios.

2. **Seleccionar el usuario a eliminar**:
   - En la lista de usuarios, hacer clic en el usuario que se desea eliminar.

3. **Eliminar el usuario**:
   - Hacer clic en la opción **"Eliminar"** y confirmar la acción. La cuenta será eliminada permanentemente, y el usuario no podrá acceder al sistema.

### Visualización de Usuarios Conectados

La plataforma permite monitorear en tiempo real los **usuarios conectados** a los nodos de comunicación. Para acceder a esta información:

1. **Acceder a la sección de usuarios conectados**:
   - Desde el menú de administración o el panel de control, hacer clic en **"Usuarios Conectados"**.

2. **Ver información en tiempo real**:
   - La pantalla muestra una lista de los usuarios que están activos en la plataforma, con detalles como:
     - El nodo al que están conectados.
     - El tiempo de conexión.
     - La actividad reciente en el nodo (envío de mensajes o transmisión de audio).

3. **Desconectar usuarios**:
   - Si es necesario, el administrador puede forzar la desconexión de un usuario desde esta pantalla. Para hacerlo, seleccionar al usuario en cuestión y hacer clic en **"Desconectar"**.


## 6. Comunicación en Tiempo Real

Una de las características clave de la aplicación es la capacidad de permitir a los usuarios comunicarse en tiempo real a través de **mensajes de texto** y **transmisión de audio**. La plataforma está diseñada para facilitar una interacción fluida entre los usuarios conectados a los nodos de comunicación.

### Mensajería en Tiempo Real

La plataforma permite el envío de **mensajes de texto en tiempo real** entre usuarios conectados a los mismos nodos. Esta funcionalidad es útil para la coordinación rápida y la interacción sin la necesidad de transmitir audio.

#### Enviar Mensajes de Texto

1. **Acceder al nodo de comunicación**:
   - Para enviar un mensaje de texto, primero es necesario conectarse a uno de los nodos. Desde el panel de control, seleccionar el nodo correspondiente.

2. **Utilizar la interfaz de mensajería**:
   - Dentro del nodo seleccionado, hay un área de **entrada de texto** donde los usuarios pueden escribir sus mensajes.
   
3. **Enviar el mensaje**:
   - Una vez redactado el mensaje, hacer clic en **"Enviar"** o presionar la tecla **Enter** para que el mensaje sea enviado a los demás usuarios conectados al nodo.

4. **Visualización en tiempo real**:
   - Los mensajes enviados y recibidos se mostrarán en un panel de conversación en tiempo real, visible para todos los usuarios conectados al mismo nodo.

#### Recepción de Mensajes

- Los mensajes enviados por otros usuarios aparecerán inmediatamente en el panel de conversación de cada nodo, junto con el **nombre del remitente** y la **hora de envío**.
- El sistema también puede notificar a los usuarios mediante **sonidos** o **alertas visuales** cuando se recibe un nuevo mensaje.

### Transmisión de Audio en Tiempo Real

Además de la mensajería de texto, la aplicación permite la **transmisión de audio** en tiempo real. Esto es especialmente útil en situaciones donde es necesario realizar una comunicación rápida y fluida, similar a una llamada por radio.

#### Cómo Iniciar una Transmisión de Audio

1. **Conectar al nodo de comunicación**:
   - Al igual que con los mensajes de texto, primero es necesario acceder al nodo de comunicación desde el panel de control.

2. **Activar la transmisión de audio**:
   - En la interfaz del nodo, habrá un botón o interruptor para **iniciar la transmisión de audio**. Dependiendo de la configuración, la plataforma puede utilizar un sistema de **Push-to-Talk (PTT)** o permitir la transmisión continua.

3. **Usar Push-to-Talk (PTT)**:
   - Si el nodo está configurado con la funcionalidad **Push-to-Talk**, el usuario deberá presionar y mantener el botón de transmisión mientras habla. Al soltar el botón, la transmisión de audio se detendrá automáticamente.

4. **Transmisión continua**:
   - Si la transmisión continua está habilitada, el audio se transmitirá de forma continua mientras el usuario no la detenga manualmente.

#### Escuchar Transmisiones de Audio

Los usuarios conectados al mismo nodo podrán escuchar las transmisiones de audio en tiempo real. La transmisión de audio puede incluir:

- **Voz en directo**: para coordinar acciones o intercambiar información verbal rápidamente.
- **Audio pregrabado**: si está permitido, los usuarios pueden transmitir mensajes de audio previamente grabados.

#### Control del Volumen y Calidad de Audio

La plataforma permite a los usuarios ajustar ciertos parámetros de la transmisión de audio:

- **Volumen**: cada usuario puede ajustar el volumen de la transmisión de audio según su preferencia.
- **Calidad de audio**: dependiendo de la configuración del sistema y la capacidad de la red, los administradores pueden ajustar la **calidad de la transmisión** (baja, media o alta), optimizando la latencia y el uso del ancho de banda.

### Gestión de Conexiones en Tiempo Real

El sistema monitorea constantemente el estado de las conexiones de los usuarios para asegurar una experiencia sin interrupciones. En tiempo real, la plataforma ofrece la posibilidad de:

- **Visualizar usuarios conectados**: dentro de cada nodo, se puede ver una lista de todos los usuarios actualmente conectados, junto con su estado (activo, oyente, transmitiendo audio, etc.).
- **Desconectar usuarios**: en caso de necesitarlo, los administradores pueden forzar la desconexión de un usuario específico de un nodo si está causando problemas o ha infringido las políticas de uso.
- **Supervisión de la actividad**: el administrador tiene acceso a estadísticas en tiempo real que incluyen la cantidad de usuarios activos en cada nodo y el tráfico de mensajes y transmisiones de audio.


## 7. Transmisión de Audio en Tiempo Real

La funcionalidad de **transmisión de audio en tiempo real** permite a los usuarios conectados a un nodo de comunicación enviar y recibir audio instantáneamente, lo que facilita la interacción en situaciones donde es necesaria una respuesta rápida o coordinada. Este sistema está diseñado para funcionar de manera similar a una radio bidireccional, especialmente cuando se utiliza el modo **Push-to-Talk (PTT)**.

### Activación de la Transmisión de Audio

Para iniciar una transmisión de audio, los usuarios deben estar conectados a un nodo de comunicación habilitado para esta funcionalidad. A continuación se detallan los pasos para activar y utilizar la transmisión de audio:

1. **Acceso al nodo de comunicación**:
   - El primer paso es ingresar al nodo de comunicación desde el panel principal. Una vez conectado, la interfaz del nodo mostrará las opciones disponibles para la transmisión de audio.

2. **Iniciar la transmisión**:
   - Para comenzar a hablar, el usuario debe hacer clic en el botón **"Transmitir Audio"**. Dependiendo de la configuración del nodo, la transmisión puede funcionar de dos maneras:
     - **Push-to-Talk (PTT)**: el usuario debe presionar y mantener el botón PTT mientras habla. Al soltar el botón, la transmisión se detiene automáticamente.
     - **Transmisión continua**: en algunos nodos, la transmisión de audio puede ser continua, lo que significa que no es necesario mantener presionado ningún botón. La transmisión finalizará solo cuando el usuario haga clic en **"Detener Audio"**.

3. **Indicadores de transmisión activa**:
   - Mientras se transmite audio, un indicador visual en la interfaz informará al usuario que su transmisión está activa. Además, otros usuarios conectados al mismo nodo recibirán una notificación de que alguien está transmitiendo audio en ese momento.

### Recepción de Transmisiones de Audio

Todos los usuarios conectados al mismo nodo pueden escuchar las transmisiones de audio en tiempo real. La plataforma asegura que la calidad del audio sea óptima para garantizar una experiencia fluida.

1. **Escuchar la transmisión**:
   - Una vez que un usuario comienza a transmitir, los demás oyentes conectados al nodo podrán escuchar el audio sin necesidad de realizar ninguna acción adicional.

2. **Notificaciones de transmisión**:
   - Si el nodo está configurado con **notificaciones de transmisión**, los usuarios recibirán alertas visuales o sonoras cuando alguien inicie una transmisión de audio.

### Supervisión y Control de la Transmisión

La plataforma permite tanto a los usuarios como a los administradores tener control sobre la transmisión de audio en tiempo real. Los administradores, en particular, pueden monitorizar las transmisiones y gestionar el flujo de comunicación.

1. **Monitoreo en tiempo real**:
   - Desde el panel de control del nodo, los administradores pueden ver qué usuarios están actualmente transmitiendo audio y el tiempo que llevan haciéndolo. Esto ayuda a mantener un control eficiente sobre el flujo de comunicación y evitar monopolios en la transmisión.

2. **Desconexión de usuarios**:
   - Si un usuario está abusando de la función de transmisión o interrumpiendo la comunicación, el administrador puede forzar la desconexión de dicho usuario. Esto se hace accediendo al panel de usuarios conectados y seleccionando la opción **"Desconectar"**.

## 8. Gestión de Accesos y Permisos

La aplicación incluye un sistema robusto para gestionar los **accesos y permisos de los usuarios**, asegurando que cada persona tenga el nivel adecuado de control sobre los nodos de comunicación y funcionalidades según su rol. Esto permite a los administradores definir quién puede visualizar, modificar o administrar los nodos, y gestionar el acceso a las funcionalidades clave de la plataforma.

### Roles de Usuario

La gestión de permisos se basa en la asignación de **roles de usuario**. Existen dos roles principales dentro de la aplicación:

1. **Administrador**:
   - Tiene acceso completo a todas las funcionalidades del sistema.
   - Puede gestionar los nodos de comunicación, crear y modificar usuarios, y asignar o revocar permisos.
   - Controla la configuración global de la plataforma, incluidos los ajustes de audio y los permisos de acceso a nodos.

2. **Usuario Estándar**:
   - Tiene acceso limitado, según los permisos que se le otorguen.
   - Puede conectarse a nodos de comunicación para enviar mensajes de texto y participar en la transmisión de audio.
   - No puede modificar la configuración de los nodos ni gestionar otros usuarios.

### Asignación de Permisos

La plataforma permite asignar permisos personalizados a cada usuario. Los administradores pueden definir qué nodos estarán disponibles para un usuario específico y qué acciones puede realizar en ellos. A continuación se detallan los pasos para gestionar los permisos.

#### Modificar Permisos de Usuario

1. **Acceder a la lista de usuarios**:
   - Desde el panel de administración, hacer clic en **"Usuarios"** para ver la lista de usuarios registrados en el sistema.

2. **Seleccionar el usuario a modificar**:
   - Hacer clic en el nombre del usuario cuyo acceso se desea modificar.

3. **Editar los permisos**:
   - En la página de edición de usuario, encontrar la sección de **permisos de acceso**. Aquí, se pueden gestionar los permisos de los siguientes elementos:
     - **Nodos de comunicación**: asignar o restringir el acceso a nodos específicos.
     - **Transmisión de audio**: permitir o denegar la capacidad de transmitir audio en un nodo determinado.
     - **Mensajería en tiempo real**: otorgar o revocar la capacidad de enviar mensajes de texto a otros usuarios dentro del nodo.

4. **Guardar cambios**:
   - Una vez editados los permisos, hacer clic en **"Guardar"** para aplicar los cambios. El usuario verá los ajustes reflejados la próxima vez que inicie sesión.

### Configuración de Acceso a Nodos

Los nodos de comunicación son uno de los elementos centrales de la aplicación, y su acceso debe ser gestionado de manera eficiente para evitar que usuarios no autorizados intervengan en la comunicación o la transmisión de audio. A continuación se explica cómo configurar los permisos de acceso a los nodos.

#### Asignación de Nodos a Usuarios

1. **Acceder a la sección de nodos**:
   - Desde el panel de administración, hacer clic en **"Nodos"** para visualizar la lista de nodos disponibles.

2. **Asignar nodos a un usuario**:
   - Al crear o editar un usuario, seleccionar los nodos a los que el usuario tendrá acceso. Esto se puede hacer seleccionando las casillas correspondientes a los nodos en la página de edición de usuario.
   
3. **Definir tipo de acceso**:
   - Dentro de cada nodo, es posible especificar el tipo de acceso que el usuario tendrá:
     - **Solo lectura**: el usuario podrá ver los mensajes y las transmisiones de audio, pero no podrá participar.
     - **Participación**: el usuario podrá enviar mensajes y transmitir audio.
     - **Gestión**: el usuario podrá gestionar el nodo, incluyendo la modificación de configuraciones y permisos de otros usuarios en ese nodo.

4. **Guardar la configuración**:
   - Después de asignar los nodos y definir el tipo de acceso, hacer clic en **"Guardar"** para aplicar los cambios.

### Administración de Roles

El sistema de roles permite a los administradores gestionar de manera eficiente los derechos de acceso y las responsabilidades de los usuarios dentro de la plataforma.

1. **Creación de roles personalizados**:
   - En la configuración avanzada, los administradores pueden crear roles personalizados para definir un conjunto de permisos específico que se pueda asignar a varios usuarios. Estos roles pueden incluir combinaciones de permisos como:
     - Acceso total a nodos específicos.
     - Permisos de solo lectura en nodos sensibles.
     - Capacidad de administrar transmisiones de audio sin acceder a los mensajes.

2. **Asignación de roles**:
   - Al crear un nuevo usuario o editar uno existente, es posible asignarle un rol predefinido para facilitar la gestión de permisos. Esto otorga de manera automática los permisos asociados al rol.

### Restricciones de Acceso y Control de Seguridad

Para garantizar la seguridad del sistema y evitar accesos no autorizados, la plataforma permite implementar restricciones adicionales, tales como:

1. **Límites de acceso por IP**:
   - Los administradores pueden restringir el acceso a la aplicación desde rangos específicos de direcciones IP, garantizando que solo usuarios en ubicaciones autorizadas puedan conectarse.

2. **Bloqueo de usuarios**:
   - Si un usuario intenta acceder de manera inapropiada o ha sido identificado como un riesgo, el administrador puede bloquear su cuenta. Esto evita que el usuario vuelva a acceder al sistema.

3. **Monitoreo de actividad**:
   - El sistema mantiene un registro de las actividades de cada usuario, lo que permite a los administradores revisar acciones sospechosas o no autorizadas y tomar las medidas necesarias.

### Revocación de Permisos

En cualquier momento, los administradores pueden revocar los permisos de un usuario para acceder a un nodo o realizar ciertas acciones en la plataforma.

1. **Revocar acceso a nodos**:
   - En la página de edición de usuarios, desmarcar los nodos a los que ya no debe acceder el usuario y guardar los cambios.

2. **Deshabilitar permisos de administración**:
   - Si un usuario con permisos de administración ya no debe tener este nivel de acceso, el administrador puede cambiar su rol a uno más restringido, como **Usuario estándar**, o personalizar sus permisos.

3. **Suspender la cuenta**:
   - Si es necesario, el administrador puede suspender temporalmente o eliminar la cuenta de un usuario. Esto bloquea su acceso a la plataforma hasta que se reactive la cuenta o se cree una nueva.