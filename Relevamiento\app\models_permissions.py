# --- Archivo: app/models_permissions.py ---
# Modelos para sistema de permisos y roles de usuario

from app import db
from datetime import datetime
import json
from flask_login import current_user

class UserRole(db.Model):
    """Roles de usuario con niveles jerár<PERSON>cos"""
    __tablename__ = 'user_roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    description = db.Column(db.Text)
    level = db.Column(db.Integer, nullable=False, default=0)  # 0=básico, 100=admin
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relación con permisos
    permissions = db.relationship('UserPermission', backref='role', lazy='dynamic')
    
    def __repr__(self):
        return f'<UserRole {self.name}>'
    
    @staticmethod
    def get_default_roles():
        """Roles predeterminados del sistema"""
        return [
            {'name': 'Administrador', 'description': 'Acceso completo al sistema', 'level': 100},
            {'name': 'Supervisor', 'description': 'Acceso a múltiples ciudades y capas', 'level': 80},
            {'name': 'Usuario Regional', 'description': 'Acceso a ciudades específicas', 'level': 60},
            {'name': 'Usuario Básico', 'description': 'Acceso limitado a capas específicas', 'level': 40},
            {'name': 'Solo Lectura', 'description': 'Solo visualización, sin edición', 'level': 20}
        ]

class UserPermission(db.Model):
    """Permisos específicos por usuario"""
    __tablename__ = 'user_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('user_roles.id'))
    
    # Filtros geográficos y de datos
    cities = db.Column(db.Text)  # JSON array de ciudades permitidas
    sources = db.Column(db.Text)  # JSON array de capas/orígenes permitidos
    
    # Permisos para puntos
    can_view_points = db.Column(db.Boolean, default=True)
    can_create_points = db.Column(db.Boolean, default=False)
    can_edit_points = db.Column(db.Boolean, default=False)
    can_delete_points = db.Column(db.Boolean, default=False)
    
    # Permisos para cámaras
    can_view_cameras = db.Column(db.Boolean, default=True)
    can_create_cameras = db.Column(db.Boolean, default=False)
    can_edit_cameras = db.Column(db.Boolean, default=False)
    can_delete_cameras = db.Column(db.Boolean, default=False)
    
    # Permisos para imágenes
    can_view_images = db.Column(db.Boolean, default=True)
    can_upload_images = db.Column(db.Boolean, default=False)
    can_annotate_images = db.Column(db.Boolean, default=False)
    can_delete_images = db.Column(db.Boolean, default=False)
    
    # Permisos para reportes y administración
    can_view_reports = db.Column(db.Boolean, default=True)
    can_export_data = db.Column(db.Boolean, default=False)
    can_manage_users = db.Column(db.Boolean, default=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<UserPermission user_id={self.user_id}>'
    
    def get_allowed_cities(self):
        """Obtiene lista de ciudades permitidas"""
        if not self.cities:
            return []
        try:
            return json.loads(self.cities)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_allowed_cities(self, cities_list):
        """Establece lista de ciudades permitidas"""
        if cities_list:
            self.cities = json.dumps(cities_list)
        else:
            self.cities = None
    
    def get_allowed_sources(self):
        """Obtiene lista de capas/orígenes permitidos"""
        if not self.sources:
            return []
        try:
            return json.loads(self.sources)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_allowed_sources(self, sources_list):
        """Establece lista de capas/orígenes permitidos"""
        if sources_list:
            self.sources = json.dumps(sources_list)
        else:
            self.sources = None
    
    def is_city_allowed(self, city):
        """Verifica si una ciudad está permitida"""
        if not city:
            return True
        allowed_cities = self.get_allowed_cities()
        if not allowed_cities:  # Sin restricciones
            return True
        return city in allowed_cities
    
    def is_source_allowed(self, source):
        """Verifica si una capa/origen está permitida"""
        if not source:
            return True
        allowed_sources = self.get_allowed_sources()
        if not allowed_sources:  # Sin restricciones
            return True
        return source in allowed_sources
    
    def has_permission(self, permission_name):
        """Verifica si tiene un permiso específico"""
        return getattr(self, permission_name, False)
    
    @staticmethod
    def get_user_permissions(user_id):
        """Obtiene permisos de un usuario específico"""
        return UserPermission.query.filter_by(user_id=user_id).first()
    
    @staticmethod
    def create_default_permissions(user_id, role_name='Usuario Básico'):
        """Crea permisos predeterminados para un usuario"""
        role = UserRole.query.filter_by(name=role_name).first()
        
        # Permisos según el rol
        permissions_by_role = {
            'Administrador': {
                'can_view_points': True, 'can_create_points': True, 'can_edit_points': True, 'can_delete_points': True,
                'can_view_cameras': True, 'can_create_cameras': True, 'can_edit_cameras': True, 'can_delete_cameras': True,
                'can_view_images': True, 'can_upload_images': True, 'can_annotate_images': True, 'can_delete_images': True,
                'can_view_reports': True, 'can_export_data': True, 'can_manage_users': True
            },
            'Supervisor': {
                'can_view_points': True, 'can_create_points': True, 'can_edit_points': True, 'can_delete_points': False,
                'can_view_cameras': True, 'can_create_cameras': True, 'can_edit_cameras': True, 'can_delete_cameras': False,
                'can_view_images': True, 'can_upload_images': True, 'can_annotate_images': True, 'can_delete_images': False,
                'can_view_reports': True, 'can_export_data': True, 'can_manage_users': False
            },
            'Usuario Regional': {
                'can_view_points': True, 'can_create_points': True, 'can_edit_points': True, 'can_delete_points': False,
                'can_view_cameras': True, 'can_create_cameras': True, 'can_edit_cameras': True, 'can_delete_cameras': False,
                'can_view_images': True, 'can_upload_images': True, 'can_annotate_images': True, 'can_delete_images': False,
                'can_view_reports': True, 'can_export_data': False, 'can_manage_users': False
            },
            'Usuario Básico': {
                'can_view_points': True, 'can_create_points': False, 'can_edit_points': False, 'can_delete_points': False,
                'can_view_cameras': True, 'can_create_cameras': False, 'can_edit_cameras': False, 'can_delete_cameras': False,
                'can_view_images': True, 'can_upload_images': False, 'can_annotate_images': False, 'can_delete_images': False,
                'can_view_reports': True, 'can_export_data': False, 'can_manage_users': False
            },
            'Solo Lectura': {
                'can_view_points': True, 'can_create_points': False, 'can_edit_points': False, 'can_delete_points': False,
                'can_view_cameras': True, 'can_create_cameras': False, 'can_edit_cameras': False, 'can_delete_cameras': False,
                'can_view_images': True, 'can_upload_images': False, 'can_annotate_images': False, 'can_delete_images': False,
                'can_view_reports': False, 'can_export_data': False, 'can_manage_users': False
            }
        }
        
        permissions_data = permissions_by_role.get(role_name, permissions_by_role['Usuario Básico'])
        
        permission = UserPermission(
            user_id=user_id,
            role_id=role.id if role else None,
            **permissions_data
        )
        
        db.session.add(permission)
        return permission

# Funciones de utilidad para verificar permisos
def has_permission(permission_name):
    """Verifica si el usuario actual tiene un permiso específico"""
    if not current_user.is_authenticated:
        return False
    
    # Los administradores tienen todos los permisos
    if getattr(current_user, 'is_admin', False):
        return True
    
    permissions = UserPermission.get_user_permissions(current_user.id)
    if not permissions:
        return False
    
    return permissions.has_permission(permission_name)

def get_user_allowed_cities():
    """Obtiene ciudades permitidas para el usuario actual"""
    if not current_user.is_authenticated:
        return []
    
    # Los administradores ven todas las ciudades
    if getattr(current_user, 'is_admin', False):
        return []  # Lista vacía = sin restricciones
    
    permissions = UserPermission.get_user_permissions(current_user.id)
    if not permissions:
        return []
    
    return permissions.get_allowed_cities()

def get_user_allowed_sources():
    """Obtiene capas/orígenes permitidos para el usuario actual"""
    if not current_user.is_authenticated:
        return []
    
    # Los administradores ven todas las capas
    if getattr(current_user, 'is_admin', False):
        return []  # Lista vacía = sin restricciones
    
    permissions = UserPermission.get_user_permissions(current_user.id)
    if not permissions:
        return []
    
    return permissions.get_allowed_sources()

def filter_by_user_permissions(query, model_class):
    """Filtra una query según los permisos del usuario actual"""
    if not current_user.is_authenticated:
        return query.filter(False)  # No mostrar nada
    
    # Los administradores ven todo
    if getattr(current_user, 'is_admin', False):
        return query
    
    permissions = UserPermission.get_user_permissions(current_user.id)
    if not permissions:
        return query.filter(False)  # No mostrar nada
    
    # Aplicar filtros de ciudad
    allowed_cities = permissions.get_allowed_cities()
    if allowed_cities:
        query = query.filter(model_class.city.in_(allowed_cities))
    
    # Aplicar filtros de origen/capa
    allowed_sources = permissions.get_allowed_sources()
    if allowed_sources:
        query = query.filter(model_class.source.in_(allowed_sources))
    
    return query
