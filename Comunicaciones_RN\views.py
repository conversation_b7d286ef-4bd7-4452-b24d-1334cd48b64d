from flask import Blueprint, render_template, abort, flash, redirect, url_for, session, current_app  # Import current_app
import os # Import os
from flask_login import login_required, current_user
from models import Node, User
import logging

# Configurar logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

views_bp = Blueprint('views', __name__)

def user_has_access_to_city(user, node):
    """Verifica si el usuario tiene acceso directo a una ciudad (nodo sin subnodos)."""
    # Si el usuario es administrador o tiene acceso policial, puede acceder a todas las ciudades
    if user.role == 'admin':
        return True
    if user.has_police_access:
        return True

    # Verificar si la ciudad está en la lista de accesos específicos del usuario
    if node.name in (user.node_access or '').split(','):
        return True

    # Verificar si el nodo padre (unidad regional) está en la lista de unidades regionales del usuario
    if node.parent and node.parent.name in (user.regional_units or '').split(','):
        return True

    return False

def user_can_access_node(user, node):
    """Comprueba recursivamente si el usuario puede acceder a un nodo o a alguno de sus subnodos.
       Devuelve True si el nodo es una ciudad a la que el usuario tiene acceso o
       si alguno de sus subnodos es accesible.
    """
    subnodes = Node.query.filter_by(parent_id=node.id).all()
    # Si no hay subnodos, es una ciudad. Comprobar acceso directo.
    if not subnodes:
        return user_has_access_to_city(user, node)
    else:
        # Si el nodo tiene subnodos, verificar si alguno de ellos es accesible.
        for subnode in subnodes:
            if user_can_access_node(user, subnode):
                return True
        return False

@views_bp.route('/dashboard')
@login_required
def dashboard():
    logger.debug("Loading dashboard for user.")
    logger.debug(f"User authenticated: {current_user.is_authenticated}")
    logger.debug(f"User session token: {session.get('session_token')}")
    logger.debug(f"User database session token: {current_user.session_token}")

    # Obtener los nodos raíz
    root_nodes = Node.query.filter_by(parent_id=None).all()

    # Filtrar los nodos para incluir sólo aquellos a los que el usuario puede acceder
    # (directa o indirectamente a través de subnodos/ciudades)
    accessible_root_nodes = [node for node in root_nodes if user_can_access_node(current_user, node)]

    return render_template('dashboard.html', nodes=accessible_root_nodes)

@views_bp.route('/node/<int:node_id>')
@login_required
def node_view(node_id):
    logger.debug(f"Loading node view for node ID: {node_id}")
    node = Node.query.get_or_404(node_id)

    # Obtener subnodos del nodo actual
    subnodes = Node.query.filter_by(parent_id=node_id).all()
    # Determinar si el nodo actual es una ciudad (sin subnodos)
    is_city = not subnodes

    # Si es ciudad y el usuario no tiene acceso, redirigir al dashboard
    if is_city and not user_has_access_to_city(current_user, node):
        logger.debug(f"Access denied for user {current_user.username} to city {node.name}")
        flash(f'Usted no tiene permisos para acceder a la ciudad "{node.name}".', 'error')
        return redirect(url_for('views.dashboard'))

    # Filtrar subnodos para mostrar únicamente aquellos a los que el usuario tiene acceso
    filtered_subnodes = [sn for sn in subnodes if user_can_access_node(current_user, sn)]

    return render_template('node.html', node=node, subnodes=filtered_subnodes, is_city=is_city)

@views_bp.route('/private_chat/<username>')
@login_required
def private_chat(username):
    other_user = User.query.filter_by(username=username).first_or_404()
    chat_room = f"{current_user.username}-{other_user.username}" if current_user.username < other_user.username else f"{other_user.username}-{current_user.username}"
    return render_template('node_users.html', chat_room=chat_room, other_user=other_user)

@views_bp.route('/private_chats')
@login_required
def private_chats():
    """Muestra la lista de conversaciones privadas del usuario actual."""

    # 1. Obtener la lista de *todos* los archivos de chat privado.
    private_logs_dir = os.path.join(current_app.root_path, 'logs', 'Mensajes', 'Privados')
    if not os.path.exists(private_logs_dir):
        return render_template('private_chats.html', conversations=[]) # No hay logs

    all_private_chat_files = os.listdir(private_logs_dir)

    # 2. Filtrar para obtener solo las conversaciones del usuario actual.
    my_conversations = []
    for filename in all_private_chat_files:
        # Los nombres de archivo son como "user1-user2.log" o "user2-user1.log"
        if current_user.username in filename:
            # Extraer el nombre del *otro* usuario.
            parts = filename[:-4].split('-')  # Quita ".log" y divide
            other_username = parts[0] if parts[1] == current_user.username else parts[1]
            my_conversations.append(other_username)

    # 3. Eliminar duplicados (si los hubiera) y ordenar.
    my_conversations = sorted(list(set(my_conversations)))

    return render_template('private_chats.html', conversations=my_conversations)