/* styles.css */

/* Reset de estilos por defecto */
body, h1, p, ul, li, a, form, label, input, button {
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
  text-decoration: none;
  box-sizing: border-box;
}

body {
  background-color: #f0f8ff; /* Fondo celeste claro */
  color: #333;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.body-background::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: no-repeat center center;
  background-size: cover; /* Ocupar todo el fondo */
  opacity: 0.3; /* Transparencia del logo */
  z-index: -1;
}

/* --- Estilos para la nueva barra de navegación --- */
header {
    display: flex;
    justify-content: space-between; /* Distribuye el espacio */
    align-items: center; /* Centra verticalmente */
    padding: 10px 20px;
    background-color: #006994;
    color: white;
}

header h1 {
    margin: 0;
    flex-grow: 1; /* Permite que el h1 ocupe el espacio disponible */
    text-align: left; /* Alinea el texto a la izquierda*/
}

.navigation-links {
  /*  text-align: right;  <-  Ya no es necesario */
    display: flex; /* Usamos flexbox */
    align-items: center; /* Centrado vertical */
    gap: 15px; /* Espacio entre los enlaces */
}


/* Eliminamos los estilos del dropdown */
.dropdown, .dropbtn, .dropdown-content {
    display: none !important; /* Oculta completamente el dropdown original */
}


/* Estilos para los enlaces de la barra de navegación */
.navigation-links a {
    color: white;
    text-decoration: none;
    padding: 8px 12px; /* Espaciado interno */
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.navigation-links a:hover {
    background-color: #004d73; /* Color más oscuro al pasar el mouse */
}

/* Estilos específicos para pantallas pequeñas (responsive) */
@media (max-width: 768px) {
    header {
        flex-direction: column; /* Apila los elementos en columna */
        align-items: stretch; /* Estira los elementos para que ocupen todo el ancho */
    }

    .navigation-links {
        flex-direction: column; /* Apila los enlaces en columna */
        width: 100%; /* Ocupa todo el ancho disponible */
        gap: 5px; /* Reduce el espaciado*/

    }
    .navigation-links a {
        text-align: center;  /* Centra el texto en pantallas pequeñas */
        display: block; /*Hace que los links ocupen todo el ancho*/
        width: 100%;
    }
}


header img.logo {
  display: none; /* Ocultar el logo en el header */
}

header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

header p {
  font-size: 14px;
}

main {
  flex: 1;
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
  position: relative; /* Asegurarnos de que el contenido principal est� sobre la imagen de fondo */
  z-index: 1;
  overflow: auto; /* Asegurar que el contenido principal pueda desplazarse si es necesario */
}

main h1 {
  font-size: 32px;
  color: #004d73; /* Azul intermedio */
  margin-bottom: 20px;
}

main p {
  font-size: 18px;
  margin-bottom: 20px;
}

main ul {
  list-style-type: none;
}

main ul li {
  background-color: #006994; /* Azul oscuro */
  color: #fff; /* Texto en blanco */
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #004d73; /* Borde azul intermedio */
  border-radius: 10px; /* Esquinas redondeadas */
  transition: background-color 0.3s, color 0.3s;
}

main ul li:hover {
  background-color: #004d73; /* Cambio de color al pasar el mouse */
  color: #f0f8ff; /* Texto en celeste claro */
}

main ul li a {
  color: #fff; /* Texto en blanco */
  font-size: 18px;
}

main ul li a:hover {
  color: #f0f8ff; /* Texto en celeste claro al pasar el mouse */
}

a {
  display: inline-block;
  padding: 10px 20px;
  margin-top: 20px;
  background-color: #006994; /* Azul oscuro */
  color: #fff; /* Texto en blanco */
  border-radius: 5px;
  transition: background-color 0.3s, color 0.3s;
  text-align: center;
}

a:hover {
  background-color: #004d73; /* Azul intermedio */
  color: #f0f8ff; /* Texto en celeste claro */
}

form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #ffffff; /* Fondo blanco para el formulario */
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  position: relative; /* Asegurarnos de que el formulario est� sobre la imagen de fondo */
  z-index: 1;
}

.form-group {
  display: flex;
  flex-direction: column;
}

label {
  font-size: 18px; /* Reducir el tama�o de fuente de las etiquetas */
  color: #006994; /* Azul oscuro */
  margin-bottom: 5px; /* Espacio debajo de la etiqueta */
}

input {
  padding: 10px; /* Ajustar el padding para m�s espacio */
  border: 1px solid #004d73; /* Borde azul intermedio */
  border-radius: 5px;
  transition: border-color 0.3s;
  font-size: 16px; /* Reducir el tama�o de fuente de los inputs */
}

input:focus {
  border-color: #006994; /* Azul oscuro */
  outline: none;
}

button {
  padding: 10px 20px;
  background-color: #006994; /* Azul oscuro */
  color: #fff; /* Texto en blanco */
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #004d73; /* Azul intermedio */
  color: #f0f8ff; /* Texto en celeste claro */
}

/* Footer */
footer {
  background-color: #006994; /* Azul oscuro */
  color: #fff;
  text-align: center;
  padding: 10px;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Estilos espec�ficos para la p�gina de login */
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.login-container img.logo-large {
  height: 300px; /* Ajustar el tama�o del logo */
  margin-bottom: 10px; /* Reducir el espacio entre el logo y el formulario */
}

form {
  margin-top: -20px; /* Ajustar el espacio entre el logo y el formulario */
}

/* Estilos responsivos */
@media (max-width: 768px) {
  main {
    padding: 10px;
    max-width: 100%;
  }

  main h1 {
    font-size: 24px;
  }

  main ul li {
    font-size: 16px;
    padding: 8px;
  }

  form {
    padding: 15px;
  }

  label {
    font-size: 16px;
  }

  input {
    padding: 10px;
    font-size: 14px;
  }

  button {
    padding: 10px 15px;
  }

  .login-container img.logo-large {
    height: 250px; /* Ajustar el tama�o del logo */
  }
}

@media (max-width: 480px) {
  main h1 {
    font-size: 20px;
  }

  main ul li {
    font-size: 14px;
    padding: 6px;
  }

  a {
    padding: 8px 15px;
    font-size: 14px;
  }

  form {
    padding: 10px;
  }

  label {
    font-size: 14px;
  }

  input {
    padding: 8px;
    font-size: 12px;
  }

  button {
    padding: 8px 10px;
  }

  .login-container img.logo-large {
    height: 200px; /* Ajustar el tama�o del logo */
  }
}

/* ---------- Estilos para el chat ----------- */
.chat-box {
  border: 1px solid #ccc;
  padding: 10px;
  height: 300px;
  overflow-y: auto;
  margin-top: 20px;
  background-color: #ffffff; /* Fondo blanco */
  word-wrap: break-word; /* Forzar ajuste de palabras largas */
  white-space: pre-wrap; /* Ajustar el espacio en blanco para el texto */
  max-width: 100%; /* Evitar que se expanda m�s all� del contenedor */
  width: 70%; /* Ancho fijo */
}

.chat-input {
  display: flex;
  margin-top: 10px;
}

.chat-input input {
  flex: 1;
  padding: 5px;
}

.chat-input button {
  padding: 5px 10px;
}

.audio-buttons {
  text-align: center;
  margin-bottom: 20px;
}

.audio-buttons button {
  margin: 5px;
}

.message {
  margin-bottom: 10px;
  word-wrap: break-word; /* Forzar ajuste de palabras largas */
}

.message strong {
  color: #006994; /* Azul oscuro */
}

.audio-icon {
  display: none; /* Ocultar el icono de audio por defecto */
  width: 20px;
  height: 20px;
  margin-left: 10px;
}

/* ---------- Ajustes de la p�gina node.html ----------- */
.chat-container {
  /* Centrar horizontalmente el contenedor de chat */
  display: flex;
  justify-content: center;
  align-items: flex-start;  /* Alinea la lista de usuarios y el chat arriba */
  width: 80%;              /* Ancho que permita margen */
  max-width: 1200px;
  margin: 0 auto;          /* Centramos el contenedor en la p�gina */
  margin-top: 5px;
}

/* Ajustes para el chat-box en node.html */
.chat-box {
  border: 1px solid #726e6e;        /* Ajuste de borde */
  padding: 10px;
  height: 420px;                   /* Ajusta seg�n tu preferencia */
  width: 70%;
  max-width: 800px;
  min-width: 400px;
  overflow-y: auto;
  background-color: #f0f8ff;
  margin-bottom: 10px;
  border-radius: 10px;
  word-wrap: break-word; /* Esto permite que el texto largo se envuelva */
}

/* Ajustes para la lista de usuarios en node.html */
.user-list {
  border: 1px solid #a89999;
  padding: 10px;
  height: 420px;
  width: 25%;
  max-width: 300px;
  min-width: 200px;
  overflow-y: auto;
  background-color: #f0f8ff;
  margin-bottom: 10px;
  margin-left: 10px;
  border-radius: 10px;
}

.user-list h3 {
  margin-top: 0;
}

.user-item {
  display: flex;
  align-items: center;
}

/* Icono de audio activo */
.audio-icon.active {
  display: inline;
}

    /* Estilos para "Comunicaciones RN" (eliminar borde) */
    .node-list-container {
        /* overflow-y: auto;  <-  Ya lo tienes, no es necesario repetirlo */
        max-height: 50vh;
        margin-bottom: 20px;
        /* border: 1px solid #ddd; <- ELIMINADO:  Aquí estaba el borde */
        padding: 10px;
        border-radius: 5px;
    }
     .node-list-container ul{
        margin: 0;
        padding: 0;
        list-style: none;
    }
    .node-list-container li{
        margin-bottom: 5px;
    }

    /* Resto de tus estilos (sin cambios) */
    .download-link {
        margin-top: auto;
        text-align: center;
        padding-bottom: 1em;
    }
    footer{
        text-align: center;
        margin-top: 40px;
        color: #6c757d;
    }
    *, *::before, *::after{
        box-sizing: border-box;
    }
    .flash-error{
        color:red;
        font-weight: bold;
    }