#!/usr/bin/env python3
# --- Archivo: check_db.py ---
# Script para verificar y crear la estructura de base de datos

import sqlite3
import os
import sys

def check_database():
    """Verifica el estado de la base de datos y muestra información."""
    
    db_path = 'app.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Base de datos {db_path} no existe")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"✅ Conectado a base de datos: {db_path}")
        print()
        
        # Verificar tablas existentes
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("📋 Tablas existentes:")
        for table in tables:
            print(f"  - {table[0]}")
        print()
        
        # Verificar estructura de tabla user si existe
        if ('user',) in tables:
            print("👤 Estructura de tabla 'user':")
            cursor.execute("PRAGMA table_info(user);")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
            
            # Contar usuarios
            cursor.execute("SELECT COUNT(*) FROM user;")
            user_count = cursor.fetchone()[0]
            print(f"  📊 Total usuarios: {user_count}")
            
            if user_count > 0:
                cursor.execute("SELECT username, role FROM user;")
                users = cursor.fetchall()
                print("  👥 Usuarios existentes:")
                for user in users:
                    role = user[1] if len(user) > 1 and user[1] else 'sin rol'
                    print(f"    - {user[0]} ({role})")
        else:
            print("❌ Tabla 'user' no existe")
        
        print()
        
        # Verificar tablas de permisos
        permission_tables = ['user_city_permissions', 'user_source_permissions', 'user_permissions']
        print("🔐 Tablas de permisos:")
        for table in permission_tables:
            if (table,) in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                print(f"  ✅ {table}: {count} registros")
            else:
                print(f"  ❌ {table}: no existe")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {e}")
        return False

def create_initial_user():
    """Crea un usuario administrador inicial si no existe ninguno."""
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Verificar si ya hay usuarios
        cursor.execute("SELECT COUNT(*) FROM user;")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            print("👤 Creando usuario administrador inicial...")
            
            # Importar werkzeug para hash de contraseña
            try:
                from werkzeug.security import generate_password_hash
            except ImportError:
                print("❌ No se puede importar werkzeug. Instala las dependencias.")
                return False
            
            username = input("Nombre de usuario para admin: ").strip()
            if not username:
                username = "admin"
            
            password = input("Contraseña para admin: ").strip()
            if not password:
                password = "admin123"
            
            password_hash = generate_password_hash(password)
            
            cursor.execute("""
                INSERT INTO user (username, password_hash, role, is_active, created_at, updated_at)
                VALUES (?, ?, 'administrador', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, (username, password_hash))
            
            conn.commit()
            print(f"✅ Usuario administrador '{username}' creado exitosamente")
            
        else:
            print(f"ℹ️  Ya existen {user_count} usuarios en la base de datos")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creando usuario inicial: {e}")
        return False

def main():
    """Función principal."""
    print("🔍 Verificando estado de la base de datos...")
    print("=" * 50)
    
    if check_database():
        print("\n" + "=" * 50)
        
        # Preguntar si crear usuario inicial
        response = input("\n¿Crear usuario administrador inicial si no existe? (s/n): ").strip().lower()
        if response in ['s', 'si', 'y', 'yes']:
            create_initial_user()
    
    print("\n✅ Verificación completada")

if __name__ == "__main__":
    main()
