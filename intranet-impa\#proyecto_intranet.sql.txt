#proyecto_intranet.sql

```sql
-- ****************************************************************************
-- Archivo: proyecto_intranet.sql
-- Descripción:
-- Este script crea la base de datos completa según los requerimientos y 
-- lineamientos discutidos. Incluye la distinción de iglesias principales 
-- y anexos, usuarios (miembros y pastores) en una sola tabla, roles, 
-- jera<PERSON><PERSON><PERSON><PERSON>, inventarios, documentos, mensajería interna, y un historial 
-- de ascensos (ascension_history).
-- ****************************************************************************

-- ****************************************************************************
-- 1. Creación de la base de datos
-- ****************************************************************************
DROP DATABASE IF EXISTS intranet;
CREATE DATABASE IF NOT EXISTS intranet 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;
USE intranet;

-- ****************************************************************************
-- 2. Tabla principal de Iglesias (churches)
--    - Se maneja tanto Iglesia Principal como Anexos en una sola tabla,
--      diferenciándolas por el campo 'tipo' (ej. 'Principal' o 'Anexo').
--    - 'pastor_id' hace referencia a un usuario (users.id) que sea pastor.
-- ****************************************************************************
CREATE TABLE churches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address VARCHAR(255),
    district VARCHAR(255),
    city VARCHAR(255),
    province VARCHAR(255),
    country VARCHAR(255),
    latitude FLOAT,
    longitude FLOAT,
    tipo ENUM('Principal','Anexo') DEFAULT 'Principal',
    pastor_id INT DEFAULT NULL,
    CONSTRAINT fk_churches_users
        FOREIGN KEY (pastor_id) REFERENCES users(id)
        ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- NOTA IMPORTANTE:
-- La FK (foreign key) anterior referencia a 'users.id', pero la tabla 'users'
-- se creará después. Para resolverlo, podemos:
--   a) Crear primero 'users' y luego alterar la tabla 'churches' para agregar la FK.
--   b) Omitir temporalmente la restricción y añadirla tras crear 'users'.
-- En este script, para mayor claridad, creamos la tabla 'users' antes y luego 'churches'.
-- REESTRUCTURAMOS el orden según sea más conveniente.

-- ****************************************************************************
-- 2 (reajuste). Creamos primero la tabla de Usuarios (users)
--    - Aquí conviven miembros y pastores, diferenciados por:
--      - Un campo 'is_pastor' o se maneja vía roles. 
--      - El 'role' general (p.e. 'administrador', 'secretaria', 'miembro', etc.).
--    - Campos para datos personales y eclesiásticos.
-- ****************************************************************************

-- Eliminamos y creamos la tabla 'users' primero para poder referenciar desde 'churches'.
DROP TABLE IF EXISTS users;
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,

    -- Roles básicos de la plataforma (pueden complementar con la tabla de roles múltiples).
    role ENUM('administrador','secretaria','pastorado','miembro') NOT NULL DEFAULT 'miembro',

    -- Datos personales comunes:
    full_name VARCHAR(255),
    dni VARCHAR(50),
    date_of_birth DATE,
    place_of_birth VARCHAR(255),
    date_accepted_lord DATE,    -- Fecha de aceptar al Señor (puede ser NULL)
    date_baptism DATE,          -- Fecha de bautismo (puede ser NULL)
    allergies VARCHAR(255),
    profession VARCHAR(255),
    social_security VARCHAR(255), -- Obra social
    phone_number VARCHAR(255),
    emergency_contact_phone VARCHAR(255),
    address VARCHAR(255),
    city VARCHAR(255),
    province VARCHAR(255),
    country VARCHAR(255),

    -- En caso de pastor, ubicación de la casa pastoral (puede ser NULL si no aplica).
    latitude_casa FLOAT,
    longitude_casa FLOAT,

    -- Estado de usuario
    is_active BOOLEAN DEFAULT TRUE,
    date_of_death DATE NULL,    -- Si fallece, se registra la fecha

    -- Iglesia principal a la que pertenece el usuario (miembro o pastor).
    church_id INT DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- Ahora sí, creamos la tabla 'churches' con la FK pastor_id -> users.id
DROP TABLE IF EXISTS churches;
CREATE TABLE churches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address VARCHAR(255),
    district VARCHAR(255),
    city VARCHAR(255),
    province VARCHAR(255),
    country VARCHAR(255),
    latitude FLOAT,
    longitude FLOAT,
    tipo ENUM('Principal','Anexo') DEFAULT 'Principal',
    pastor_id INT DEFAULT NULL,
    CONSTRAINT fk_churches_users
        FOREIGN KEY (pastor_id) REFERENCES users(id)
        ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Relacionamos 'users.church_id' con 'churches.id'
ALTER TABLE users
    ADD CONSTRAINT fk_users_churches
    FOREIGN KEY (church_id)
    REFERENCES churches(id)
    ON DELETE SET NULL;

-- ****************************************************************************
-- 3. Inventario de la Iglesia: church_inventory
--    - Cada iglesia o anexo puede poseer bienes materiales, 
--      registrados con tipo, descripción, cantidad, etc.
-- ****************************************************************************
DROP TABLE IF EXISTS church_inventory;
CREATE TABLE church_inventory (
    id INT AUTO_INCREMENT PRIMARY KEY,
    church_id INT NOT NULL,
    nombre_objeto VARCHAR(255) NOT NULL,
    tipo VARCHAR(100),      -- p.e. 'Instrumento', 'Mobiliario', 'Sonido', etc.
    cantidad INT DEFAULT 1,
    CONSTRAINT fk_inventory_church
        FOREIGN KEY (church_id) REFERENCES churches(id)
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ****************************************************************************
-- 4. Tabla de Roles / Funciones Eclesiásticas
--    - Para que un usuario (miembro o pastor) pueda tener múltiples funciones
--      (ej. 'jefe de jóvenes', 'tesorero', 'corista', etc.).
-- ****************************************************************************
DROP TABLE IF EXISTS roles;
CREATE TABLE roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description VARCHAR(255)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla intermedia para asignar roles/funciones a usuarios
DROP TABLE IF EXISTS user_roles;
CREATE TABLE user_roles (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    CONSTRAINT fk_userroles_user
        FOREIGN KEY (user_id) REFERENCES users(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_userroles_role
        FOREIGN KEY (role_id) REFERENCES roles(id)
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ****************************************************************************
-- 5. Historial de ascensos (ascension_history)
--    - Registra los cambios en el "rango eclesiástico" o estado 
--      (por ejemplo, de "hermano" a "pastor probando", luego "diacono", etc.).
-- ****************************************************************************
DROP TABLE IF EXISTS ascension_history;
CREATE TABLE ascension_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    fecha_ascenso DATE NOT NULL,
    antiguo_rango VARCHAR(255) NOT NULL,
    nuevo_rango VARCHAR(255) NOT NULL,
    CONSTRAINT fk_ascension_user
        FOREIGN KEY (user_id) REFERENCES users(id)
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ****************************************************************************
-- 6. Relaciones familiares (user_relationships)
--    - Una sola tabla que relacione a dos usuarios (miembros o pastores) 
--      indicando el tipo de parentesco: Esposa, Hermano, Hijo, etc.
-- ****************************************************************************
DROP TABLE IF EXISTS user_relationships;
CREATE TABLE user_relationships (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id_1 INT NOT NULL,
    user_id_2 INT NOT NULL,
    tipo_de_relacion ENUM(
        'Esposo','Esposa','Padre','Madre','Hijo','Hija',
        'Hermano','Hermana','Abuelo','Abuela','Nieto','Nieta',
        'Tio','Tia','Sobrino','Sobrina','Cuñado','Cuñada',
        'Primo','Prima','Yerno','Nuera','Concuñado','ConCuñada'
    ) NOT NULL,
    CONSTRAINT fk_rel_user1
        FOREIGN KEY (user_id_1) REFERENCES users(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_rel_user2
        FOREIGN KEY (user_id_2) REFERENCES users(id)
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ****************************************************************************
-- 7. Mensajes (messages)
--    - Para el envío de mensajes interno. 
--      Restricciones lógicas se manejarán en la aplicación (enviar sólo al pastor, etc.).
-- ****************************************************************************
DROP TABLE IF EXISTS messages;
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message_text TEXT NOT NULL,
    sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE,
    CONSTRAINT fk_messages_sender
        FOREIGN KEY (sender_id) REFERENCES users(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_messages_receiver
        FOREIGN KEY (receiver_id) REFERENCES users(id)
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ****************************************************************************
-- 8. Documentos (documents) y control de versiones
--    - Se mantiene la FK a una versión anterior (previous_version_id) 
--      para conservar historial. 
--    - 'uploaded_by' referencia al usuario que lo subió.
-- ****************************************************************************
DROP TABLE IF EXISTS documents;
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_path VARCHAR(255) NOT NULL,
    upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT,
    category ENUM('acta','informe_financiero','circular','informe_miembros','diploma') NOT NULL,
    topic VARCHAR(255),
    version INT DEFAULT 1,
    previous_version_id INT,
    CONSTRAINT fk_docs_user
        FOREIGN KEY (uploaded_by) REFERENCES users(id)
        ON DELETE SET NULL,
    CONSTRAINT fk_docs_previous
        FOREIGN KEY (previous_version_id) REFERENCES documents(id)
        ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ****************************************************************************
-- 9. Tabla de traslados de miembros (opcional - sólo si se requiere historial)
--    - Permite registrar transferencias de un usuario entre iglesias y/o pastores.
-- ****************************************************************************
DROP TABLE IF EXISTS member_transfers;
CREATE TABLE member_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    old_church_id INT,
    new_church_id INT,
    transfer_date DATE NOT NULL DEFAULT (CURRENT_DATE),
    notes TEXT,
    CONSTRAINT fk_transfer_user
        FOREIGN KEY (user_id) REFERENCES users(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_transfer_old
        FOREIGN KEY (old_church_id) REFERENCES churches(id)
        ON DELETE SET NULL,
    CONSTRAINT fk_transfer_new
        FOREIGN KEY (new_church_id) REFERENCES churches(id)
        ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ****************************************************************************
-- (Opcional) Insert de roles de ejemplo
-- ****************************************************************************
INSERT INTO roles (name, description) VALUES
('Jefe de Jóvenes', 'Responsable de los grupos de jóvenes'),
('Corista', 'Miembro del coro'),
('Tesorero', 'Responsable de finanzas'),
('Ayudante de Pastor', 'Asiste al pastor en actividades variadas'),
('Encargado de Anexo', 'Responsable operativo de un anexo'),
('Secretaria', 'Secretaria administrativa de la iglesia'),
('Obispo Presidente', 'Máxima autoridad eclesiástica'),
('Presbitero', 'Cargo de presbiterio dentro de la iglesia');

-- ****************************************************************************
-- Fin del script
-- ****************************************************************************

-- NOTA FINAL:
-- Se recomienda revisar el orden de ejecución de cada sentencia si se incluye 
-- este script en un proceso automatizado (algunas FK requieren que exista la 
-- tabla referenciada previamente). Aquí se ha tratado de crear primero 'users',
-- luego 'churches', etc., pero puede ajustarse según el motor de ejecución 
-- (por ejemplo, quitando temporalmente las restricciones para añadirlas 
-- tras la creación completa de tablas).
```

---

### Comentarios finales

1. **Historial de Ascensos**:  
   - La tabla `ascension_history` registra el **cambio de rango** (p. ej., de “hermano” a “pastor probando”). Cada vez que ocurra un ascenso, puedes insertar una fila con la fecha, el rango anterior y el nuevo.

2. **Restricciones de Mensajería**:  
   - El control de “quién puede enviar a quién” se maneja en la lógica de negocio. En la DB, simplemente guardamos `sender_id` y `receiver_id`.

3. **Manejo de Roles Múltiples**:  
   - La tabla `user_roles` permite asignar múltiples funciones o cargos a un usuario. Esto complementa o sustituye el `role` principal si deseas más detalle.

4. **Traslados de Miembros**:  
   - Si se desea conservar un historial detallado, la tabla `member_transfers` funcionará para almacenar la fecha y la iglesia anterior y nueva.

Si consideras que hay más detalles por ajustar, ¡házmelo saber! De lo contrario, este modelo base debería cubrir la mayoría de tus requerimientos y ser un buen punto de partida para tu proyecto.