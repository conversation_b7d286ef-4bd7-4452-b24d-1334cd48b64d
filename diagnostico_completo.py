#!/usr/bin/env python3
# diagnostico_completo.py - Script para diagnosticar problemas de audio

import json
import pyaudio
import serial
import requests
import socketio
import time
import sys
import os
from datetime import datetime

def log_resultado(mensaje):
    """Escribe resultado en archivo de log"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open("diagnostico_resultado.txt", "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {mensaje}\n")
    print(f"[{timestamp}] {mensaje}")

def verificar_configuracion():
    """Verifica archivos de configuración"""
    log_resultado("=== VERIFICANDO CONFIGURACION ===")
    
    # Verificar config.json
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        log_resultado(f"✓ config.json encontrado: {config}")
        
        campos_requeridos = ['username', 'node_id', 'node_url', 'port_number', 'input_device_index', 'volume_level']
        for campo in campos_requeridos:
            if campo in config:
                log_resultado(f"✓ Campo {campo}: {config[campo]}")
            else:
                log_resultado(f"✗ Campo faltante: {campo}")
                
    except Exception as e:
        log_resultado(f"✗ Error leyendo config.json: {e}")
        return None
    
    # Verificar ptt_client_config.json
    try:
        with open('ptt_client_config.json', 'r', encoding='utf-8') as f:
            ptt_config = json.load(f)
        log_resultado(f"✓ ptt_client_config.json encontrado: {ptt_config}")
    except Exception as e:
        log_resultado(f"✗ Error leyendo ptt_client_config.json: {e}")
    
    return config

def verificar_dispositivos_audio():
    """Verifica dispositivos de audio disponibles"""
    log_resultado("=== VERIFICANDO DISPOSITIVOS DE AUDIO ===")
    
    try:
        p = pyaudio.PyAudio()
        log_resultado(f"✓ PyAudio inicializado correctamente")
        
        # Listar dispositivos de entrada
        log_resultado("Dispositivos de entrada disponibles:")
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                log_resultado(f"  [{i}] {info['name']} - Canales: {info['maxInputChannels']}")
        
        # Listar dispositivos de salida
        log_resultado("Dispositivos de salida disponibles:")
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            if info['maxOutputChannels'] > 0:
                log_resultado(f"  [{i}] {info['name']} - Canales: {info['maxOutputChannels']}")
        
        p.terminate()
        return True
        
    except Exception as e:
        log_resultado(f"✗ Error con PyAudio: {e}")
        return False

def verificar_puerto_serie(config):
    """Verifica conexión del puerto serie para PTT"""
    log_resultado("=== VERIFICANDO PUERTO SERIE ===")
    
    if not config or 'port_number' not in config:
        log_resultado("✗ No hay configuración de puerto")
        return False
    
    com_port = f"COM{config['port_number']}"
    log_resultado(f"Probando puerto: {com_port}")
    
    try:
        ser = serial.Serial(com_port, baudrate=9600, timeout=1)
        log_resultado(f"✓ Puerto {com_port} abierto correctamente")
        
        # Probar activar/desactivar PTT
        ser.setDTR(True)
        log_resultado(f"✓ PTT activado en {com_port}")
        time.sleep(1)
        
        ser.setDTR(False)
        log_resultado(f"✓ PTT desactivado en {com_port}")
        
        ser.close()
        log_resultado(f"✓ Puerto {com_port} cerrado correctamente")
        return True
        
    except Exception as e:
        log_resultado(f"✗ Error con puerto {com_port}: {e}")
        return False

def verificar_servidor_local():
    """Verifica si el servidor Flask local está funcionando"""
    log_resultado("=== VERIFICANDO SERVIDOR LOCAL ===")
    
    try:
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        log_resultado(f"✓ Servidor local responde: {response.status_code}")
        return True
    except Exception as e:
        log_resultado(f"✗ Error conectando al servidor local: {e}")
        return False

def verificar_conexion_servidor_remoto(config):
    """Verifica conexión al servidor remoto"""
    log_resultado("=== VERIFICANDO SERVIDOR REMOTO ===")
    
    if not config or 'node_url' not in config:
        log_resultado("✗ No hay configuración de servidor remoto")
        return False
    
    node_url = config['node_url']
    log_resultado(f"Probando conexión a: {node_url}")
    
    try:
        # Probar conexión HTTP básica
        url_base = node_url.replace('/node', '').replace('wss://', 'https://').replace('ws://', 'http://')
        response = requests.get(f"https://{url_base}", timeout=10)
        log_resultado(f"✓ Servidor remoto responde: {response.status_code}")
        
        # Probar conexión WebSocket
        sio = socketio.Client()
        
        @sio.event
        def connect():
            log_resultado("✓ Conexión WebSocket establecida")
            sio.disconnect()
        
        @sio.event
        def connect_error(data):
            log_resultado(f"✗ Error de conexión WebSocket: {data}")
        
        sio.connect(f"wss://{node_url}",
                   headers={'node_id': config['node_id'], 'username': config['username']},
                   namespaces=['/node'])
        
        time.sleep(2)
        return True
        
    except Exception as e:
        log_resultado(f"✗ Error conectando al servidor remoto: {e}")
        return False

def test_captura_audio_basico(config):
    """Test básico de captura de audio"""
    log_resultado("=== TEST CAPTURA DE AUDIO ===")
    
    if not config:
        log_resultado("✗ No hay configuración")
        return False
    
    try:
        p = pyaudio.PyAudio()
        
        device_index = config.get('input_device_index', 0)
        log_resultado(f"Usando dispositivo de entrada: {device_index}")
        
        stream = p.open(format=pyaudio.paInt16,
                       channels=1,
                       rate=8000,
                       input=True,
                       input_device_index=device_index,
                       frames_per_buffer=512)
        
        log_resultado("✓ Stream de audio abierto")
        log_resultado("Capturando audio por 5 segundos...")
        
        max_volume = 0
        for i in range(50):  # 5 segundos
            data = stream.read(512)
            import numpy as np
            audio_data = np.frombuffer(data, dtype=np.int16)
            volume = np.linalg.norm(audio_data) / len(audio_data)
            max_volume = max(max_volume, volume)
            time.sleep(0.1)
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        log_resultado(f"✓ Volumen máximo detectado: {max_volume}")
        log_resultado(f"Umbral configurado: {config.get('volume_level', 5)}")
        
        if max_volume > config.get('volume_level', 5):
            log_resultado("✓ Audio detectado por encima del umbral")
        else:
            log_resultado("⚠ Audio por debajo del umbral - verificar micrófono")
        
        return True
        
    except Exception as e:
        log_resultado(f"✗ Error en test de captura: {e}")
        return False

def main():
    """Función principal de diagnóstico"""
    # Limpiar archivo de log anterior
    if os.path.exists("diagnostico_resultado.txt"):
        os.remove("diagnostico_resultado.txt")
    
    log_resultado("INICIANDO DIAGNOSTICO COMPLETO DE RNCom")
    log_resultado("=" * 50)
    
    # Verificar configuración
    config = verificar_configuracion()
    
    # Verificar dispositivos de audio
    audio_ok = verificar_dispositivos_audio()
    
    # Verificar puerto serie
    if config:
        serie_ok = verificar_puerto_serie(config)
    else:
        serie_ok = False
    
    # Verificar servidor local
    servidor_local_ok = verificar_servidor_local()
    
    # Verificar servidor remoto
    if config:
        servidor_remoto_ok = verificar_conexion_servidor_remoto(config)
    else:
        servidor_remoto_ok = False
    
    # Test de captura de audio
    if config and audio_ok:
        captura_ok = test_captura_audio_basico(config)
    else:
        captura_ok = False
    
    # Resumen final
    log_resultado("=" * 50)
    log_resultado("RESUMEN DEL DIAGNOSTICO:")
    log_resultado(f"Configuración: {'✓' if config else '✗'}")
    log_resultado(f"Audio: {'✓' if audio_ok else '✗'}")
    log_resultado(f"Puerto Serie: {'✓' if serie_ok else '✗'}")
    log_resultado(f"Servidor Local: {'✓' if servidor_local_ok else '✗'}")
    log_resultado(f"Servidor Remoto: {'✓' if servidor_remoto_ok else '✗'}")
    log_resultado(f"Captura Audio: {'✓' if captura_ok else '✗'}")
    
    log_resultado("Diagnóstico completado. Revisa 'diagnostico_resultado.txt' para detalles.")

if __name__ == '__main__':
    main()
