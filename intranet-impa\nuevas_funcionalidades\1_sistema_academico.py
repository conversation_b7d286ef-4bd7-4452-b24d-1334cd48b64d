# 1_sistema_academico.py
# Sistema Académico Completo para Intranet IMPA

"""
SISTEMA ACADÉMICO COMPLETO
==========================

Funcionalidades:
- Escuelas bíblicas por iglesia
- Pensums académicos personalizables
- Gestión de materias y niveles
- Matrículas y auto-matrículas
- Calificaciones y historial académico
- Certificados automáticos
- Reportes académicos

Inspirado en Software Redil pero adaptado para corporaciones de iglesias.
"""

from datetime import datetime, date
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Float, Date, Enum
from sqlalchemy.orm import relationship
from app import db

# ============================================================================
# 1. MODELOS DE BASE DE DATOS
# ============================================================================

class AcademicSchool(db.Model):
    """Escuelas bíblicas por iglesia"""
    __tablename__ = 'academic_schools'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=False)
    director_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    max_students = db.Column(db.Integer, default=50)
    auto_enrollment = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    church = db.relationship('Church', backref='academic_schools')
    director = db.relationship('User', backref='directed_schools')
    curriculums = db.relationship('AcademicCurriculum', backref='school', cascade='all, delete-orphan')
    enrollments = db.relationship('AcademicEnrollment', backref='school', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<AcademicSchool(name='{self.name}', church='{self.church.name}')>"

class AcademicCurriculum(db.Model):
    """Pensums académicos"""
    __tablename__ = 'academic_curriculums'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    school_id = db.Column(db.Integer, db.ForeignKey('academic_schools.id'), nullable=False)
    duration_months = db.Column(db.Integer, nullable=False)
    total_credits = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    subjects = db.relationship('AcademicSubject', backref='curriculum', cascade='all, delete-orphan')
    enrollments = db.relationship('AcademicEnrollment', backref='curriculum')

class AcademicSubject(db.Model):
    """Materias del pensum"""
    __tablename__ = 'academic_subjects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(20), nullable=False)
    description = db.Column(db.Text)
    curriculum_id = db.Column(db.Integer, db.ForeignKey('academic_curriculums.id'), nullable=False)
    credits = db.Column(db.Integer, default=1)
    level = db.Column(db.Integer, default=1)  # Nivel o semestre
    prerequisites = db.Column(db.Text)  # JSON con IDs de materias prerequisito
    is_mandatory = db.Column(db.Boolean, default=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    teacher = db.relationship('User', backref='taught_subjects')
    grades = db.relationship('AcademicGrade', backref='subject', cascade='all, delete-orphan')

class AcademicEnrollment(db.Model):
    """Matrículas de estudiantes"""
    __tablename__ = 'academic_enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    school_id = db.Column(db.Integer, db.ForeignKey('academic_schools.id'), nullable=False)
    curriculum_id = db.Column(db.Integer, db.ForeignKey('academic_curriculums.id'), nullable=False)
    enrollment_date = db.Column(db.Date, default=date.today)
    status = db.Column(db.Enum('active', 'completed', 'dropped', 'suspended'), default='active')
    completion_date = db.Column(db.Date, nullable=True)
    final_grade = db.Column(db.Float, nullable=True)
    certificate_issued = db.Column(db.Boolean, default=False)
    certificate_date = db.Column(db.Date, nullable=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    student = db.relationship('User', backref='academic_enrollments')
    grades = db.relationship('AcademicGrade', backref='enrollment', cascade='all, delete-orphan')

class AcademicGrade(db.Model):
    """Calificaciones por materia"""
    __tablename__ = 'academic_grades'
    
    id = db.Column(db.Integer, primary_key=True)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('academic_enrollments.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('academic_subjects.id'), nullable=False)
    grade_value = db.Column(db.Float, nullable=False)
    grade_type = db.Column(db.Enum('partial', 'final', 'makeup'), default='partial')
    evaluation_date = db.Column(db.Date, default=date.today)
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    comments = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    teacher = db.relationship('User', backref='assigned_grades')

# ============================================================================
# 2. SERVICIOS ACADÉMICOS
# ============================================================================

class AcademicService:
    """Servicio para gestión académica"""
    
    @staticmethod
    def create_school(church_id, name, description, director_id=None, **kwargs):
        """Crear nueva escuela bíblica"""
        school = AcademicSchool(
            name=name,
            description=description,
            church_id=church_id,
            director_id=director_id,
            **kwargs
        )
        db.session.add(school)
        db.session.commit()
        return school
    
    @staticmethod
    def create_curriculum(school_id, name, description, duration_months, subjects_data):
        """Crear pensum académico con materias"""
        curriculum = AcademicCurriculum(
            name=name,
            description=description,
            school_id=school_id,
            duration_months=duration_months
        )
        db.session.add(curriculum)
        db.session.flush()  # Para obtener el ID
        
        total_credits = 0
        for subject_data in subjects_data:
            subject = AcademicSubject(
                curriculum_id=curriculum.id,
                **subject_data
            )
            total_credits += subject_data.get('credits', 1)
            db.session.add(subject)
        
        curriculum.total_credits = total_credits
        db.session.commit()
        return curriculum
    
    @staticmethod
    def enroll_student(student_id, school_id, curriculum_id):
        """Matricular estudiante"""
        # Verificar si ya está matriculado
        existing = AcademicEnrollment.query.filter_by(
            student_id=student_id,
            school_id=school_id,
            status='active'
        ).first()
        
        if existing:
            raise ValueError("El estudiante ya está matriculado en esta escuela")
        
        enrollment = AcademicEnrollment(
            student_id=student_id,
            school_id=school_id,
            curriculum_id=curriculum_id
        )
        db.session.add(enrollment)
        db.session.commit()
        return enrollment
    
    @staticmethod
    def auto_enroll_eligible_students(school_id):
        """Auto-matrícula de estudiantes elegibles"""
        school = AcademicSchool.query.get(school_id)
        if not school.auto_enrollment:
            return []
        
        # Obtener miembros activos de la iglesia que no estén matriculados
        from app.models import Member, User
        
        eligible_students = db.session.query(User).join(Member).filter(
            Member.church_id == school.church_id,
            Member.is_active == True,
            ~User.id.in_(
                db.session.query(AcademicEnrollment.student_id).filter(
                    AcademicEnrollment.school_id == school_id,
                    AcademicEnrollment.status == 'active'
                )
            )
        ).limit(school.max_students).all()
        
        enrolled = []
        curriculum = school.curriculums[0] if school.curriculums else None
        
        if curriculum:
            for student in eligible_students:
                try:
                    enrollment = AcademicService.enroll_student(
                        student.id, school_id, curriculum.id
                    )
                    enrolled.append(enrollment)
                except ValueError:
                    continue
        
        return enrolled
    
    @staticmethod
    def assign_grade(enrollment_id, subject_id, grade_value, teacher_id, grade_type='partial', comments=None):
        """Asignar calificación"""
        grade = AcademicGrade(
            enrollment_id=enrollment_id,
            subject_id=subject_id,
            grade_value=grade_value,
            grade_type=grade_type,
            teacher_id=teacher_id,
            comments=comments
        )
        db.session.add(grade)
        
        # Verificar si el estudiante completó todas las materias
        if grade_type == 'final':
            AcademicService.check_completion(enrollment_id)
        
        db.session.commit()
        return grade
    
    @staticmethod
    def check_completion(enrollment_id):
        """Verificar si el estudiante completó el pensum"""
        enrollment = AcademicEnrollment.query.get(enrollment_id)
        curriculum = enrollment.curriculum
        
        # Obtener materias obligatorias
        mandatory_subjects = AcademicSubject.query.filter_by(
            curriculum_id=curriculum.id,
            is_mandatory=True
        ).all()
        
        # Verificar calificaciones finales
        final_grades = AcademicGrade.query.filter_by(
            enrollment_id=enrollment_id,
            grade_type='final'
        ).all()
        
        completed_subjects = {grade.subject_id for grade in final_grades if grade.grade_value >= 3.0}
        required_subjects = {subject.id for subject in mandatory_subjects}
        
        if required_subjects.issubset(completed_subjects):
            # Calcular promedio final
            total_grade = sum(grade.grade_value for grade in final_grades)
            enrollment.final_grade = total_grade / len(final_grades)
            enrollment.status = 'completed'
            enrollment.completion_date = date.today()
            
            # Generar certificado automáticamente
            AcademicService.generate_certificate(enrollment_id)
    
    @staticmethod
    def generate_certificate(enrollment_id):
        """Generar certificado automático"""
        enrollment = AcademicEnrollment.query.get(enrollment_id)
        
        if enrollment.status == 'completed' and not enrollment.certificate_issued:
            enrollment.certificate_issued = True
            enrollment.certificate_date = date.today()
            
            # Aquí se podría integrar con el sistema de documentos existente
            # para generar el PDF del certificado
            
            db.session.commit()
            return True
        return False

# ============================================================================
# 3. FORMULARIOS ACADÉMICOS
# ============================================================================

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, BooleanField, FloatField, DateField
from wtforms.validators import DataRequired, Optional, NumberRange

class AcademicSchoolForm(FlaskForm):
    """Formulario para crear/editar escuela bíblica"""
    name = StringField('Nombre de la Escuela', validators=[DataRequired()])
    description = TextAreaField('Descripción', validators=[Optional()])
    church_id = SelectField('Iglesia', coerce=int, validators=[DataRequired()])
    director_id = SelectField('Director', coerce=int, validators=[Optional()])
    start_date = DateField('Fecha de Inicio', validators=[DataRequired()])
    end_date = DateField('Fecha de Fin', validators=[Optional()])
    max_students = IntegerField('Máximo de Estudiantes', validators=[NumberRange(min=1, max=500)], default=50)
    auto_enrollment = BooleanField('Auto-matrícula')
    submit = SubmitField('Guardar Escuela')

class AcademicSubjectForm(FlaskForm):
    """Formulario para materias"""
    name = StringField('Nombre de la Materia', validators=[DataRequired()])
    code = StringField('Código', validators=[DataRequired()])
    description = TextAreaField('Descripción', validators=[Optional()])
    credits = IntegerField('Créditos', validators=[NumberRange(min=1, max=10)], default=1)
    level = IntegerField('Nivel/Semestre', validators=[NumberRange(min=1, max=20)], default=1)
    is_mandatory = BooleanField('Materia Obligatoria', default=True)
    teacher_id = SelectField('Profesor', coerce=int, validators=[Optional()])
    submit = SubmitField('Guardar Materia')

class AcademicGradeForm(FlaskForm):
    """Formulario para calificaciones"""
    student_id = SelectField('Estudiante', coerce=int, validators=[DataRequired()])
    subject_id = SelectField('Materia', coerce=int, validators=[DataRequired()])
    grade_value = FloatField('Calificación', validators=[DataRequired(), NumberRange(min=0, max=5)])
    grade_type = SelectField('Tipo', choices=[
        ('partial', 'Parcial'),
        ('final', 'Final'),
        ('makeup', 'Recuperación')
    ], validators=[DataRequired()])
    comments = TextAreaField('Comentarios', validators=[Optional()])
    submit = SubmitField('Asignar Calificación')

# ============================================================================
# 4. RUTAS ACADÉMICAS (para routes.py)
# ============================================================================

ACADEMIC_ROUTES_EXAMPLE = """
# Añadir estas rutas a routes.py

from nuevas_funcionalidades.sistema_academico import (
    AcademicService, AcademicSchoolForm, AcademicSubjectForm, AcademicGradeForm,
    AcademicSchool, AcademicCurriculum, AcademicEnrollment
)

@routes_bp.route('/academic/schools')
@login_required
@admin_or_secretary_required
def academic_schools():
    schools = AcademicSchool.query.all()
    return render_template('academic/schools.html', schools=schools)

@routes_bp.route('/academic/schools/create', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def create_academic_school():
    form = AcademicSchoolForm()
    form.church_id.choices = [(c.id, c.name) for c in Church.query.all()]
    form.director_id.choices = [(0, 'Sin Director')] + [
        (u.id, f"{u.first_name} {u.last_name}") 
        for u in User.query.filter_by(role='pastorado').all()
    ]
    
    if form.validate_on_submit():
        school = AcademicService.create_school(
            church_id=form.church_id.data,
            name=form.name.data,
            description=form.description.data,
            director_id=form.director_id.data if form.director_id.data != 0 else None,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            max_students=form.max_students.data,
            auto_enrollment=form.auto_enrollment.data
        )
        flash('Escuela bíblica creada exitosamente', 'success')
        return redirect(url_for('routes.academic_schools'))
    
    return render_template('academic/create_school.html', form=form)

@routes_bp.route('/academic/schools/<int:school_id>/enroll')
@login_required
def academic_enroll():
    school = AcademicSchool.query.get_or_404(school_id)
    
    # Auto-matrícula si está habilitada
    if school.auto_enrollment:
        enrolled = AcademicService.auto_enroll_eligible_students(school_id)
        flash(f'{len(enrolled)} estudiantes matriculados automáticamente', 'success')
    
    return redirect(url_for('routes.academic_school_detail', school_id=school_id))

@routes_bp.route('/academic/grades/assign', methods=['GET', 'POST'])
@login_required
def assign_academic_grade():
    form = AcademicGradeForm()
    
    # Poblar opciones según permisos del usuario
    if current_user.role in ['administrador', 'secretaria']:
        enrollments = AcademicEnrollment.query.filter_by(status='active').all()
    else:
        # Solo estudiantes de su iglesia
        enrollments = AcademicEnrollment.query.join(AcademicSchool).filter(
            AcademicSchool.church_id == current_user.church_id,
            AcademicEnrollment.status == 'active'
        ).all()
    
    form.student_id.choices = [
        (e.student_id, f"{e.student.first_name} {e.student.last_name}") 
        for e in enrollments
    ]
    
    if form.validate_on_submit():
        grade = AcademicService.assign_grade(
            enrollment_id=form.student_id.data,  # Necesitarías ajustar esto
            subject_id=form.subject_id.data,
            grade_value=form.grade_value.data,
            teacher_id=current_user.id,
            grade_type=form.grade_type.data,
            comments=form.comments.data
        )
        flash('Calificación asignada exitosamente', 'success')
        return redirect(url_for('routes.academic_grades'))
    
    return render_template('academic/assign_grade.html', form=form)
"""

# ============================================================================
# 5. REPORTES ACADÉMICOS
# ============================================================================

class AcademicReports:
    """Generador de reportes académicos"""
    
    @staticmethod
    def school_statistics(school_id):
        """Estadísticas de una escuela"""
        school = AcademicSchool.query.get(school_id)
        
        total_enrolled = AcademicEnrollment.query.filter_by(
            school_id=school_id, status='active'
        ).count()
        
        completed = AcademicEnrollment.query.filter_by(
            school_id=school_id, status='completed'
        ).count()
        
        dropped = AcademicEnrollment.query.filter_by(
            school_id=school_id, status='dropped'
        ).count()
        
        return {
            'school': school,
            'total_enrolled': total_enrolled,
            'completed': completed,
            'dropped': dropped,
            'completion_rate': (completed / (completed + dropped)) * 100 if (completed + dropped) > 0 else 0
        }
    
    @staticmethod
    def student_transcript(enrollment_id):
        """Generar transcripción de notas"""
        enrollment = AcademicEnrollment.query.get(enrollment_id)
        grades = AcademicGrade.query.filter_by(enrollment_id=enrollment_id).all()
        
        transcript = {
            'student': enrollment.student,
            'school': enrollment.school,
            'curriculum': enrollment.curriculum,
            'enrollment_date': enrollment.enrollment_date,
            'status': enrollment.status,
            'grades': grades,
            'final_grade': enrollment.final_grade
        }
        
        return transcript

print("Sistema Académico Completo - Listo para implementar")
print("Archivos necesarios:")
print("1. Migración de base de datos")
print("2. Templates HTML")
print("3. Integración en routes.py")
print("4. Actualización de requirements.txt")
