<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Editar Usuario</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
  <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
  <style>
    .body-background::before {
      background: url('{{ url_for('static', filename='RNCom.png') }}') no-repeat center center;
    }
  </style>
</head>
<body class="body-background">
  <header>
    <h1>Editar Usuario</h1>
  </header>
  <main>
    <form method="POST">
      <div class="form-group">
        <label for="username">Usuario:</label>
        <input type="text" id="username" name="username" value="{{ user.username }}" required>
      </div>
      <div class="form-group">
        <label for="password">Password (deje en blanco para mantener el password actual):</label>
        <input type="password" id="password" name="password">
      </div>
      <div class="form-group">
        <label for="first_name">Nombre:</label>
        <input type="text" id="first_name" name="first_name" value="{{ user.first_name }}" required>
      </div>
      <div class="form-group">
        <label for="last_name">Apellido:</label>
        <input type="text" id="last_name" name="last_name" value="{{ user.last_name }}" required>
      </div>
      <div class="form-group">
        <label for="badge_number">Legajo Numero:</label>
        <input type="text" id="badge_number" name="badge_number" value="{{ user.badge_number }}" required>
      </div>
      <div class="form-group">
        <label for="organization">Organismo:</label>
        <select id="organization" name="organization" required>
          {% for org in organismos %}
            <option value="{{ org }}" {% if user.organization == org %}selected{% endif %}>{{ org }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group">
        <label for="role">Rol:</label>
        <select id="role" name="role" required>
          {% for role in roles %}
            <option value="{{ role }}" {% if user.role == role %}selected{% endif %}>{{ role }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group">
        <label for="node_access">Acceso a Nodos:</label>
        <div>
          {% for node in nodes %}
            <input type="checkbox" id="node_{{ node.id }}" name="node_access" value="{{ node.name }}" {% if node.name in user.node_access.split(',') %}checked{% endif %}>
            <label for="node_{{ node.id }}">{{ node.name }}</label><br>
          {% endfor %}
        </div>
      </div>
      <div class="form-group">
        <label for="regional_units">Unidades Regionales:</label>
        <div>
          {% for node in nodes %}
            {% if node.name.startswith('Unidad Regional') %}
              <input type="checkbox" id="unit_{{ node.id }}" name="regional_units" value="{{ node.name }}" {% if node.name in user.regional_units.split(',') %}checked{% endif %}>
              <label for="unit_{{ node.id }}">{{ node.name }}</label><br>
            {% endif %}
          {% endfor %}
        </div>
      </div>
      <div class="form-group">
        <input type="checkbox" id="police_access" name="police_access" {% if user.has_police_access %}checked{% endif %}>
        <label for="police_access">Acceso a Policía</label>
      </div>
      <button type="submit">Actualizar Usuario</button>
    </form>
    <a href="{{ url_for('admin.list_users') }}">Volver a la Lista de Usuarios</a>
  </main>
  <footer>
    <p>© 2024 Tu Sitio Web</p>
  </footer>
</body>
</html>
