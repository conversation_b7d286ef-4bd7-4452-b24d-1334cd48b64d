rn_rural/
├── backend/
│   ├── rn_rural_project/      # Directorio del proyecto Django
│   │   ├── __init__.py
│   │   ├── asgi.py            # Para Django Channels (WebSockets)
│   │   ├── settings.py
│   │   ├── urls.py
│   │   └── wsgi.py
│   │
│   ├── apps/                  # Directorio para las aplicaciones Django
│   │   ├── __init__.py
│   │   ├── core/              # App para lógica común, modelos base si es necesario
│   │   │   ├── __init__.py
│   │   │   ├── admin.py
│   │   │   ├── apps.py
│   │   │   ├── models.py
│   │   │   ├── tests.py
│   │   │   └── views.py
│   │   │
│   │   ├── users/             # App para usuarios, perfiles, roles
│   │   │   ├── __init__.py
│   │   │   ├── admin.py
│   │   │   ├── apps.py
│   │   │   ├── models.py
│   │   │   ├── serializers.py
│   │   │   ├── urls.py
│   │   │   └── views.py
│   │   │
│   │   ├── incidents/         # App para incidencias
│   │   │   ├── __init__.py
│   │   │   ├── admin.py
│   │   │   ├── apps.py
│   │   │   ├── models.py
│   │   │   ├── serializers.py
│   │   │   ├── consumers.py   # Para WebSockets de incidencias
│   │   │   ├── routing.py     # Para WebSockets de incidencias
│   │   │   ├── urls.py
│   │   │   └── views.py
│   │   │
│   │   ├── locations/         # App para Unidades Regionales, Ciudades y tracking de Brigadas
│   │   │   ├── __init__.py
│   │   │   ├── admin.py
│   │   │   ├── apps.py
│   │   │   ├── models.py
│   │   │   ├── serializers.py
│   │   │   ├── consumers.py   # Para WebSockets de ubicación de Brigadas
│   │   │   ├── routing.py     # Para WebSockets de ubicación de Brigadas
│   │   │   ├── urls.py
│   │   │   └── views.py
│   │   │
│   │   └── chat/              # App para la mensajería
│   │       ├── __init__.py
│   │       ├── admin.py
│   │       ├── apps.py
│   │       ├── models.py
│   │       ├── serializers.py
│   │       ├── consumers.py   # Para WebSockets de chat
│   │       ├── routing.py     # Para WebSockets de chat
│   │       ├── urls.py
│   │       └── views.py
│   │
│   ├── manage.py              # Script de gestión de Django
│   ├── requirements.txt       # Dependencias de Python
│   └── .env.example           # Variables de entorno (copiar a .env)
│
├── mobile_app/                # Espacio para el código de la app móvil (React Native/Flutter)
│   └── ...
│
├── web_panel/                 # Espacio para el código del panel web (React/Vue/Angular)
│   └── ...
│
└── README.md
└── .gitignore