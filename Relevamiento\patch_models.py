#!/usr/bin/env python3
# --- Archivo: patch_models.py ---
# Script para patchear temporalmente los modelos

import os
import sys

def backup_original_models():
    """Hacer backup del archivo models.py original."""
    try:
        import shutil
        shutil.copy2('app/models.py', 'app/models.py.backup')
        print("✅ Backup de models.py creado")
        return True
    except Exception as e:
        print(f"❌ Error creando backup: {e}")
        return False

def create_simple_models():
    """Crear modelos simplificados que coincidan con la base de datos."""
    
    simple_models = '''# --- Archivo: app/models.py (TEMPORAL) ---
# Modelos simplificados para coincidir con la base de datos

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """Modelo de usuario simplificado."""
    __tablename__ = 'user'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(256), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='operador')
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    created_by = db.Column(db.Integer, nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def set_password(self, password):
        """Establecer contraseña hasheada."""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Verificar contraseña."""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """Verificar si es administrador."""
        return self.role == 'administrador'
    
    def is_supervisor(self):
        """Verificar si es supervisor."""
        return self.role == 'supervisor'
    
    def is_operator(self):
        """Verificar si es operador."""
        return self.role == 'operador'
    
    def is_viewer(self):
        """Verificar si es visualizador."""
        return self.role == 'visualizador'
    
    def can_manage_users(self):
        """Verificar si puede gestionar usuarios."""
        return self.is_admin()
    
    def can_view_points(self):
        """Verificar si puede ver puntos."""
        return True  # Todos pueden ver puntos por ahora
    
    def can_edit_points(self):
        """Verificar si puede editar puntos."""
        return self.role in ['administrador', 'supervisor', 'operador']
    
    def can_create_points(self):
        """Verificar si puede crear puntos."""
        return self.role in ['administrador', 'supervisor', 'operador']
    
    def can_delete_points(self):
        """Verificar si puede eliminar puntos."""
        return self.role in ['administrador', 'supervisor']
    
    def can_view_cameras(self):
        """Verificar si puede ver cámaras."""
        return True  # Todos pueden ver cámaras por ahora
    
    def can_edit_cameras(self):
        """Verificar si puede editar cámaras."""
        return self.role in ['administrador', 'supervisor', 'operador']
    
    def can_create_cameras(self):
        """Verificar si puede crear cámaras."""
        return self.role in ['administrador', 'supervisor', 'operador']
    
    def can_delete_cameras(self):
        """Verificar si puede eliminar cámaras."""
        return self.role in ['administrador', 'supervisor']
    
    def can_view_reports(self):
        """Verificar si puede ver reportes."""
        return True  # Todos pueden ver reportes por ahora
    
    def can_export_data(self):
        """Verificar si puede exportar datos."""
        return self.role in ['administrador', 'supervisor']
    
    def get_allowed_cities(self):
        """Obtener ciudades permitidas (simplificado)."""
        return []  # Por ahora, sin restricciones
    
    def get_allowed_sources(self):
        """Obtener fuentes permitidas (simplificado)."""
        return []  # Por ahora, sin restricciones
    
    def can_access_city(self, city):
        """Verificar acceso a ciudad (simplificado)."""
        return True  # Por ahora, sin restricciones
    
    def can_access_source(self, source):
        """Verificar acceso a fuente (simplificado)."""
        return True  # Por ahora, sin restricciones
    
    def __repr__(self):
        return f'<User {self.username}>'

# Tablas de permisos (simplificadas)
class UserCityPermission(db.Model):
    """Permisos de ciudad por usuario."""
    __tablename__ = 'user_city_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    city = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UserSourcePermission(db.Model):
    """Permisos de fuente por usuario."""
    __tablename__ = 'user_source_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    source = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UserPermission(db.Model):
    """Permisos específicos por usuario."""
    __tablename__ = 'user_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    permission_type = db.Column(db.String(50), nullable=False)
    permission_value = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Modelos existentes (simplificados)
class Point(db.Model):
    """Modelo de punto simplificado."""
    __tablename__ = 'point'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    status = db.Column(db.String(20), default='azul')
    city = db.Column(db.String(100))
    source = db.Column(db.String(100))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Image(db.Model):
    """Modelo de imagen simplificado."""
    __tablename__ = 'image'
    
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    point_id = db.Column(db.Integer, db.ForeignKey('point.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    annotations_json = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Camera(db.Model):
    """Modelo de cámara simplificado."""
    __tablename__ = 'camera'
    
    id = db.Column(db.Integer, primary_key=True)
    point_id = db.Column(db.Integer, db.ForeignKey('point.id'))
    type = db.Column(db.String(20), default='otra')
    direction = db.Column(db.Float)
    photo_filename = db.Column(db.String(255))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    location_source = db.Column(db.String(50))
    location_accuracy = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Constantes para compatibilidad
PERMISSION_TYPES = {
    'points_view': 'Ver Puntos',
    'points_edit': 'Editar Puntos',
    'points_create': 'Crear Puntos',
    'points_delete': 'Eliminar Puntos',
    'cameras_view': 'Ver Cámaras',
    'cameras_edit': 'Editar Cámaras',
    'cameras_create': 'Crear Cámaras',
    'cameras_delete': 'Eliminar Cámaras',
    'reports_view': 'Ver Reportes',
    'reports_export': 'Exportar Datos'
}

DEFAULT_PERMISSIONS = {
    'administrador': {
        'points_view': True,
        'points_edit': True,
        'points_create': True,
        'points_delete': True,
        'cameras_view': True,
        'cameras_edit': True,
        'cameras_create': True,
        'cameras_delete': True,
        'reports_view': True,
        'reports_export': True
    },
    'supervisor': {
        'points_view': True,
        'points_edit': True,
        'points_create': True,
        'points_delete': True,
        'cameras_view': True,
        'cameras_edit': True,
        'cameras_create': True,
        'cameras_delete': True,
        'reports_view': True,
        'reports_export': True
    },
    'operador': {
        'points_view': True,
        'points_edit': True,
        'points_create': True,
        'points_delete': False,
        'cameras_view': True,
        'cameras_edit': True,
        'cameras_create': True,
        'cameras_delete': False,
        'reports_view': True,
        'reports_export': False
    },
    'visualizador': {
        'points_view': True,
        'points_edit': False,
        'points_create': False,
        'points_delete': False,
        'cameras_view': True,
        'cameras_edit': False,
        'cameras_create': False,
        'cameras_delete': False,
        'reports_view': True,
        'reports_export': False
    }
}
'''
    
    try:
        with open('app/models.py', 'w', encoding='utf-8') as f:
            f.write(simple_models)
        
        print("✅ Modelos simplificados creados")
        return True
        
    except Exception as e:
        print(f"❌ Error creando modelos: {e}")
        return False

def test_patched_models():
    """Probar que los modelos patcheados funcionen."""
    print("🧪 Probando modelos patcheados...")
    
    try:
        # Limpiar módulos importados
        modules_to_remove = []
        for module_name in sys.modules:
            if module_name.startswith('app'):
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            del sys.modules[module_name]
        
        # Importar de nuevo
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            # Probar consulta básica
            users = User.query.all()
            print(f"✅ Modelos funcionan: {len(users)} usuarios encontrados")
            
            for user in users:
                print(f"  👤 {user.username} ({user.role}) - Activo: {user.is_active}")
                print(f"    🔐 Es admin: {user.is_admin()}")
                print(f"    🔐 Puede gestionar usuarios: {user.can_manage_users()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error probando modelos: {e}")
        return False

def main():
    """Función principal."""
    print("🔧 Patcher de Modelos")
    print("=" * 30)
    
    # Hacer backup
    if not backup_original_models():
        print("⚠️  No se pudo hacer backup, continuando...")
    
    # Crear modelos simplificados
    if not create_simple_models():
        sys.exit(1)
    
    # Probar modelos
    if test_patched_models():
        print("\n🎉 ¡Modelos patcheados funcionan!")
        print("\n🚀 Próximos pasos:")
        print("   1. Reiniciar app: systemctl restart relevamiento")
        print("   2. Verificar: python3 verify_final.py")
        print("\n💡 Para restaurar modelos originales:")
        print("   cp app/models.py.backup app/models.py")
    else:
        print("\n❌ Los modelos patcheados no funcionan")

if __name__ == "__main__":
    main()
