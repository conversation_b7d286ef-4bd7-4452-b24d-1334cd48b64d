#!/usr/bin/env python3
"""
Script para crear usuarios iniciales con diferentes roles.
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User, UserRoleModel, RoleEnum
from app.services.user_service import get_password_hash

# Obtener la URL de la base de datos desde las variables de entorno
def get_db_url():
    return settings.DATABASE_URL

def create_users():
    engine = create_engine(get_db_url())
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Verificar los roles disponibles en la base de datos
        roles_query = db.query(UserRoleModel).all()
        print("\nRoles disponibles en la base de datos:")
        roles_dict = {}
        for role in roles_query:
            print(f"  - ID: {role.id}, Nombre: '{role.name}'")
            roles_dict[role.name] = role

        # Crear usuario administrador
        admin_email = "<EMAIL>"
        admin_password = "admin123"
        admin_user = db.query(User).filter(User.email == admin_email).first()
        if not admin_user:
            admin_user = User(
                email=admin_email,
                full_name="Administrador",
                hashed_password=get_password_hash(admin_password),
                phone_number="1234567890",
                is_active=True,
                is_superuser=True
            )
            db.add(admin_user)
            db.flush()

            # Asignar rol de administrador usando el rol directamente de la base de datos
            admin_role = roles_dict.get('administrador')
            if admin_role:
                admin_user.roles.append(admin_role)
                print(f"Usuario administrador creado con email: {admin_email} y contraseña: {admin_password}")
            else:
                print("No se encontró el rol 'administrador' en la base de datos")

        # Crear usuario operador
        operator_email = "<EMAIL>"
        operator_password = "operador123"
        operator_user = db.query(User).filter(User.email == operator_email).first()
        if not operator_user:
            operator_user = User(
                email=operator_email,
                full_name="Operador",
                hashed_password=get_password_hash(operator_password),
                phone_number="2345678901",
                is_active=True,
                is_superuser=False
            )
            db.add(operator_user)
            db.flush()

            # Asignar rol de operador usando el rol directamente de la base de datos
            operator_role = roles_dict.get('operador')
            if operator_role:
                operator_user.roles.append(operator_role)
                print(f"Usuario operador creado con email: {operator_email} y contraseña: {operator_password}")
            else:
                print("No se encontró el rol 'operador' en la base de datos")

        # Crear usuario taxista
        taxi_email = "<EMAIL>"
        taxi_password = "taxi123"
        taxi_user = db.query(User).filter(User.email == taxi_email).first()
        if not taxi_user:
            taxi_user = User(
                email=taxi_email,
                full_name="Taxista",
                hashed_password=get_password_hash(taxi_password),
                phone_number="3456789012",
                is_active=True,
                is_superuser=False
            )
            db.add(taxi_user)
            db.flush()

            # Asignar rol de taxista usando el rol directamente de la base de datos
            taxi_role = roles_dict.get('taxi')
            if taxi_role:
                taxi_user.roles.append(taxi_role)
                print(f"Usuario taxista creado con email: {taxi_email} y contraseña: {taxi_password}")
            else:
                print("No se encontró el rol 'taxi' en la base de datos")

        # Crear usuario pasajero
        user_email = "<EMAIL>"
        user_password = "usuario123"
        user_user = db.query(User).filter(User.email == user_email).first()
        if not user_user:
            user_user = User(
                email=user_email,
                full_name="Usuario",
                hashed_password=get_password_hash(user_password),
                phone_number="4567890123",
                is_active=True,
                is_superuser=False
            )
            db.add(user_user)
            db.flush()

            # Asignar rol de usuario usando el rol directamente de la base de datos
            user_role = roles_dict.get('usuario')
            if user_role:
                user_user.roles.append(user_role)
                print(f"Usuario pasajero creado con email: {user_email} y contraseña: {user_password}")
            else:
                print("No se encontró el rol 'usuario' en la base de datos")

        db.commit()
        print("Usuarios creados correctamente.")
    except Exception as e:
        db.rollback()
        print(f"Error al crear los usuarios: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("Creando usuarios iniciales...")
    create_users()
    print("Proceso completado.")