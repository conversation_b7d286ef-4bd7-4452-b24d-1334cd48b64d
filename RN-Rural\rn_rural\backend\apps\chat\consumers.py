# File: backend/apps/chat/consumers.py
# -----------------------------------------------

import json
import logging
from datetime import datetime
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)

class ChatConsumer(AsyncWebsocketConsumer):
    """
    Consumer para el chat general (no usado actualmente)
    """
    async def connect(self):
        await self.accept()
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connected to Chat WebSocket!'
        }))

    async def disconnect(self, close_code):
        logger.info(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message = text_data_json.get('message', '')

            logger.info(f"Received message: {message}")
            await self.send(text_data=json.dumps({
                'type': 'response',
                'message': f"Server received: {message}"
            }))
        except json.JSONDecodeError:
            logger.error(f"Error decoding JSON: {text_data}")
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")

class IncidenciaChatConsumer(AsyncWebsocketConsumer):
    """
    Consumer para el chat de una incidencia específica
    """
    async def connect(self):
        self.incidencia_id = self.scope['url_route']['kwargs']['incidencia_id']
        self.chat_group_name = f'chat_incidencia_{self.incidencia_id}'

        # Verificar que el usuario tenga permiso para ver esta incidencia
        user = self.scope['user']
        if not user.is_authenticated:
            logger.warning(f"Usuario no autenticado intentó conectarse al chat de incidencia {self.incidencia_id}")
            await self.close()
            return

        # Verificar que la incidencia exista y el usuario tenga permiso
        incidencia = await self.get_incidencia()
        if not incidencia:
            logger.warning(f"Incidencia {self.incidencia_id} no encontrada o usuario {user.id} sin permiso")
            await self.close()
            return

        # Verificar permisos según el rol
        tiene_permiso = await self.verificar_permisos(user, incidencia)
        if not tiene_permiso:
            logger.warning(f"Usuario {user.id} sin permiso para chat de incidencia {self.incidencia_id}")
            await self.close()
            return

        # Unirse al grupo de chat de la incidencia
        await self.channel_layer.group_add(
            self.chat_group_name,
            self.channel_name
        )

        await self.accept()

        # Enviar mensaje de conexión exitosa
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': f'Conectado al chat de la incidencia #{self.incidencia_id}'
        }))

        # Enviar historial de mensajes
        await self.enviar_historial_mensajes()

    async def disconnect(self, close_code):
        # Abandonar el grupo de chat
        await self.channel_layer.group_discard(
            self.chat_group_name,
            self.channel_name
        )
        logger.info(f"Usuario desconectado del chat de incidencia {self.incidencia_id}, código: {close_code}")

    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            mensaje_tipo = text_data_json.get('tipo', 'TEXTO')

            # Si es un ping para mantener la conexión activa, responder con un pong
            if mensaje_tipo == 'PING':
                logger.debug(f"Ping recibido de {self.scope['user'].username}")
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat()
                }))
                return

            # Si es una notificación de escritura, no guardar en la base de datos
            if mensaje_tipo == 'TYPING':
                logger.debug(f"Notificación de escritura de {self.scope['user'].username}")
                # Enviar notificación de escritura a todos los miembros del grupo
                await self.channel_layer.group_send(
                    self.chat_group_name,
                    {
                        'type': 'typing_notification',
                        'remitente_id': self.scope['user'].id,
                        'remitente_nombre': self.scope['user'].username,
                    }
                )
                return

            # Para mensajes normales, obtener los datos según el tipo
            mensaje_texto = text_data_json.get('texto', '')
            mensaje_audio_url = text_data_json.get('audio_url', '')
            mensaje_imagen_url = text_data_json.get('imagen_url', '')
            mensaje_ubicacion_lat = text_data_json.get('ubicacion_lat', None)
            mensaje_ubicacion_lng = text_data_json.get('ubicacion_lng', None)

            # Guardar el mensaje en la base de datos
            mensaje = await self.guardar_mensaje(
                tipo=mensaje_tipo,
                texto=mensaje_texto,
                audio_url=mensaje_audio_url,
                imagen_url=mensaje_imagen_url,
                ubicacion_lat=mensaje_ubicacion_lat,
                ubicacion_lng=mensaje_ubicacion_lng
            )

            if not mensaje:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'No se pudo guardar el mensaje'
                }))
                return

            # Enviar el mensaje a todos los miembros del grupo
            mensaje_data = {
                'type': 'chat_message',
                'id': mensaje.id,
                'remitente_id': mensaje.remitente.id,
                'remitente_nombre': mensaje.remitente.username,
                'tipo': mensaje.tipo,
                'texto': mensaje.texto,
                'fecha_envio': mensaje.fecha_envio.isoformat(),
                'leido': mensaje.leido,
                'exportado_brigada': mensaje.exportado_brigada
            }

            # Añadir campos específicos según el tipo de mensaje
            if mensaje.tipo == 'AUDIO':
                mensaje_data['audio_url'] = mensaje.audio_url
            elif mensaje.tipo == 'IMAGEN':
                mensaje_data['imagen_url'] = mensaje.imagen_url
            elif mensaje.tipo == 'UBICACION':
                mensaje_data['ubicacion_lat'] = mensaje.ubicacion_lat
                mensaje_data['ubicacion_lng'] = mensaje.ubicacion_lng

            await self.channel_layer.group_send(
                self.chat_group_name,
                mensaje_data
            )
        except json.JSONDecodeError:
            logger.error(f"Error decoding JSON: {text_data}")
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Error al procesar el mensaje: {str(e)}'
            }))

    async def chat_message(self, event):
        """
        Enviar mensaje de chat al WebSocket
        """
        mensaje_data = {
            'type': 'chat_message',
            'id': event['id'],
            'remitente_id': event['remitente_id'],
            'remitente_nombre': event['remitente_nombre'],
            'tipo': event['tipo'],
            'texto': event['texto'],
            'fecha_envio': event['fecha_envio'],
            'leido': event.get('leido', False),
            'exportado_brigada': event.get('exportado_brigada', False)
        }

        # Añadir campos específicos según el tipo de mensaje
        if event['tipo'] == 'AUDIO' and 'audio_url' in event:
            mensaje_data['audio_url'] = event['audio_url']
        elif event['tipo'] == 'IMAGEN' and 'imagen_url' in event:
            mensaje_data['imagen_url'] = event['imagen_url']
        elif event['tipo'] == 'UBICACION':
            if 'ubicacion_lat' in event and 'ubicacion_lng' in event:
                mensaje_data['ubicacion_lat'] = event['ubicacion_lat']
                mensaje_data['ubicacion_lng'] = event['ubicacion_lng']

        await self.send(text_data=json.dumps(mensaje_data))

    async def typing_notification(self, event):
        """
        Enviar notificación de escritura al WebSocket
        """
        await self.send(text_data=json.dumps({
            'type': 'typing',
            'remitente_id': event['remitente_id'],
            'remitente_nombre': event['remitente_nombre']
        }))

    @database_sync_to_async
    def get_incidencia(self):
        """
        Obtener la incidencia de la base de datos
        """
        from apps.incidents.models import Incidencia
        try:
            return Incidencia.objects.get(id=self.incidencia_id)
        except Incidencia.DoesNotExist:
            return None

    @database_sync_to_async
    def verificar_permisos(self, user, incidencia):
        """
        Verificar que el usuario tenga permiso para acceder al chat de la incidencia
        """
        # El usuario que reportó la incidencia siempre tiene permiso
        if user.id == incidencia.usuario_reporta.id:
            return True

        # Operadores asignados tienen permiso
        if incidencia.operador_asignado and user.id == incidencia.operador_asignado.id:
            return True

        # Brigadas asignadas tienen permiso
        if incidencia.brigada_asignada and user.id == incidencia.brigada_asignada.id:
            return True

        # Administradores tienen permiso
        if user.is_superuser or user.is_staff:
            return True

        # Operadores (no asignados) también tienen permiso
        if user.role == 'OPERADOR':
            return True

        return False

    @database_sync_to_async
    def guardar_mensaje(self, tipo, texto=None, audio_url=None, imagen_url=None, ubicacion_lat=None, ubicacion_lng=None):
        """
        Guardar un mensaje en la base de datos
        """
        from apps.chat.models import MensajeChat
        from apps.incidents.models import Incidencia

        try:
            incidencia = Incidencia.objects.get(id=self.incidencia_id)
            user = self.scope['user']

            mensaje = MensajeChat(
                incidencia=incidencia,
                remitente=user,
                tipo=tipo,
                texto=texto if tipo in ['TEXTO', 'SISTEMA'] else None,
                audio_url=audio_url if tipo == 'AUDIO' else None,
                imagen_url=imagen_url if tipo == 'IMAGEN' else None,
                ubicacion_lat=ubicacion_lat if tipo == 'UBICACION' else None,
                ubicacion_lng=ubicacion_lng if tipo == 'UBICACION' else None
            )
            mensaje.save()
            return mensaje
        except Exception as e:
            logger.error(f"Error al guardar mensaje: {str(e)}")
            return None

    @database_sync_to_async
    def obtener_historial_mensajes(self):
        """
        Obtener el historial de mensajes de la base de datos
        """
        from apps.chat.models import MensajeChat

        try:
            mensajes = MensajeChat.objects.filter(incidencia_id=self.incidencia_id).order_by('fecha_envio')

            # Marcar mensajes como leídos si el usuario actual no es el remitente
            user_id = self.scope['user'].id
            for mensaje in mensajes:
                if mensaje.remitente.id != user_id and not mensaje.leido:
                    mensaje.leido = True
                    mensaje.save()

            # Preparar datos para enviar
            mensajes_data = []
            for mensaje in mensajes:
                mensaje_data = {
                    'id': mensaje.id,
                    'remitente_id': mensaje.remitente.id,
                    'remitente_nombre': mensaje.remitente.username,
                    'tipo': mensaje.tipo,
                    'texto': mensaje.texto,
                    'fecha_envio': mensaje.fecha_envio.isoformat(),
                    'leido': mensaje.leido,
                    'exportado_brigada': mensaje.exportado_brigada
                }

                # Añadir campos específicos según el tipo de mensaje
                if mensaje.tipo == 'AUDIO' and mensaje.audio_url:
                    mensaje_data['audio_url'] = mensaje.audio_url
                elif mensaje.tipo == 'IMAGEN' and mensaje.imagen_url:
                    mensaje_data['imagen_url'] = mensaje.imagen_url
                elif mensaje.tipo == 'UBICACION' and mensaje.ubicacion_lat and mensaje.ubicacion_lng:
                    mensaje_data['ubicacion_lat'] = mensaje.ubicacion_lat
                    mensaje_data['ubicacion_lng'] = mensaje.ubicacion_lng

                mensajes_data.append(mensaje_data)

            return mensajes_data
        except Exception as e:
            logger.error(f"Error al obtener historial de mensajes: {str(e)}")
            return []

    async def enviar_historial_mensajes(self):
        """
        Enviar el historial de mensajes al cliente que se conecta
        """
        try:
            mensajes_data = await self.obtener_historial_mensajes()

            await self.send(text_data=json.dumps({
                'type': 'historial_mensajes',
                'mensajes': mensajes_data
            }))
        except Exception as e:
            logger.error(f"Error al enviar historial de mensajes: {str(e)}")
