# dig-vhf.py

import json
import serial
import time
import os
import ctypes
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

# Función para cargar la configuración del usuario
def load_config():
    try:
        with open('ptt_client_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: No se encontró el archivo de configuración.")
        return {}

# Función para iniciar sesión y navegar al nodo usando Selenium
def login_and_navigate(config):
    chrome_options = Options()
    chrome_options.add_argument("--use-fake-ui-for-media-stream")  # Permitir el acceso a la cámara y el micrófono sin preguntar
    chrome_options.add_argument("--disable-gpu")  # Deshabilitar la aceleración por hardware
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-software-rasterizer")
    chrome_options.add_argument("--headless")  # Ejecutar en modo headless
    chrome_options.add_argument("--window-size=1920,1080")  # Tamaño de la ventana
    chrome_options.add_argument("--autoplay-policy=no-user-gesture-required")

    # Iniciar el driver de Chrome con webdriver_manager
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

    # Navegar al sitio web
    driver.get('https://www.patagoniaservers.com.ar:5000/login')

    # Iniciar sesión en el sitio web
    username = driver.find_element(By.NAME, 'username')
    password = driver.find_element(By.NAME, 'password')

    username.send_keys(config['username'])
    password.send_keys(config['password'])

    password.send_keys(Keys.RETURN)  # Usando correctamente Keys.RETURN para enviar la contraseña

    # Esperar un momento para que el inicio de sesión se complete
    time.sleep(5)

    # Navegar a la página del nodo específico
    driver.get(config['node_url'])

    # Esperar a que la página cargue completamente
    time.sleep(5)
    
    return driver

# Función para controlar el PTT en el puerto COM
def set_ptt(state, config, ser=None):
    com_port = f"COM{config.get('port_number', '1')}"
    try:
        if state:
            # Abre el puerto si no está abierto ya
            if ser is None or not ser.is_open:
                ser = serial.Serial(com_port, baudrate=9600, timeout=1)
                print(f"Puerto {com_port} abierto con éxito.")
            
            ser.setDTR(True)
            print(f"PTT activado en {com_port}. Mide el voltaje en el pin 7.")
            return ser  # Devuelve el objeto Serial para uso futuro
        else:
            if ser is not None and ser.is_open:
                ser.setDTR(False)
                print(f"PTT desactivado en {com_port}. Verifica si el voltaje vuelve a 0.")
                ser.close()
                print(f"Puerto {com_port} cerrado.")
            return None  # Devuelve None cuando el puerto está cerrado
    except serial.SerialException as e:
        print(f"Error al intentar abrir el puerto {com_port}: {e}")
        return None

# Función para escuchar eventos de audio desde la página web
def listen_to_audio_events(driver, config):
    ser = None  # Mantiene el puerto abierto mientras dura el audio

    # Inyecta script que escucha los eventos y reproduce el audio
    driver.execute_script("""
        const socket = io.connect('https://' + document.domain + ':' + location.port + '/node', {
            query: {
                node_id: arguments[0],
                username: arguments[1]
            }
        });

        socket.on('global_audio_start', (data) => {
            if (data.node_id === arguments[0]) {
                console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
                localStorage.setItem('audio_state', 'started');
            }
        });

        socket.on('global_audio_end', (data) => {
            if (data.node_id === arguments[0]) {
                console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
                localStorage.setItem('audio_state', 'ended');
            }
        });

        // Escucha el evento de audio y lo reproduce
        socket.on('receive_audio', (data) => {
            // Activa PTT un segundo antes de reproducir el audio
            localStorage.setItem('audio_playing', 'pending');
            setTimeout(() => {
                const audioBlob = new Blob([new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))], { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);
                audio.play();

                audio.onplay = () => {
                    localStorage.setItem('audio_playing', 'true');
                };
                
                audio.onended = () => {
                    localStorage.setItem('audio_playing', 'false');
                };
            }, 1000); // Retrasa la reproducción de audio por 1 segundo
        });
    """, config['node_id'], config['username'])

    # Monitorizar el localStorage para cambiar el estado del PTT
    try:
        while True:
            audio_playing = driver.execute_script("return localStorage.getItem('audio_playing');")
            if audio_playing == 'pending':
                ser = set_ptt(True, config, ser)  # Activa el PTT un segundo antes de la reproducción
                driver.execute_script("localStorage.setItem('audio_playing', 'true');")  # Actualiza el estado a true
            elif audio_playing == 'true':
                pass  # Mantiene el PTT activo mientras se reproduce el audio
            elif audio_playing == 'false':
                ser = set_ptt(False, config, ser)  # Cierra el puerto cuando el audio termina
                driver.execute_script("localStorage.removeItem('audio_playing');")  # Limpia el estado después de procesarlo
            time.sleep(0.5)  # Ajusta el intervalo de tiempo según tus necesidades
    except KeyboardInterrupt:
        set_ptt(False, config, ser)  # Asegúrate de cerrar el puerto al salir
        print("Script detenido manualmente. PTT desactivado.")

if __name__ == '__main__':
    config = load_config()

    if 'username' not in config or 'password' not in config or 'node_url' not in config or 'node_id' not in config:
        print("Error: Configuración incompleta. Por favor, asegúrate de tener usuario, contraseña y nodo configurados.")
        exit(1)

    # Iniciar sesión y navegar al nodo usando Selenium
    driver = login_and_navigate(config)

    # Escuchar los eventos de audio y activar/desactivar el PTT
    listen_to_audio_events(driver, config)
