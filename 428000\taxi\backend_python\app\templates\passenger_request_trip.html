{% extends "base_layout_passenger.html" %}

{% block title %}Solicitar Viaje - Panel de Pasajero{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .location-card {
        border-left: 4px solid #0d6efd;
        margin-bottom: 10px;
        transition: all 0.3s;
    }
    .location-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .location-card.origin {
        border-left-color: #198754;
    }
    .location-card.destination {
        border-left-color: #dc3545;
    }
    .fare-estimate {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .fare-amount {
        font-size: 2rem;
        font-weight: bold;
        color: #0d6efd;
    }
    .trip-options {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('passenger_dashboard_route') }}">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Solicitar Viaje</li>
            </ol>
        </nav>
        <h1 class="display-5 mb-4">Solicitar Viaje</h1>
        <p class="lead">Selecciona tu origen y destino para solicitar un taxi.</p>
    </div>
</div>

<div class="row">
    <!-- Mapa para seleccionar ubicaciones -->
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Selecciona en el Mapa</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
                <div class="mt-3">
                    <div class="d-flex justify-content-between">
                        <button class="btn btn-success" onclick="setOriginMode()">
                            <i class="bi bi-geo-alt"></i> Marcar Origen
                        </button>
                        <button class="btn btn-danger" onclick="setDestinationMode()">
                            <i class="bi bi-geo-alt"></i> Marcar Destino
                        </button>
                        <button class="btn btn-secondary" onclick="clearMarkers()">
                            <i class="bi bi-x-circle"></i> Limpiar Marcadores
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Formulario de solicitud -->
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Detalles del Viaje</h5>
            </div>
            <div class="card-body">
                <form id="request-trip-form">
                    <div class="mb-3">
                        <label for="origin-address" class="form-label">Dirección de Origen</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="origin-address" placeholder="Ingresa la dirección de origen">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchOriginAddress()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        <input type="hidden" id="origin-latitude">
                        <input type="hidden" id="origin-longitude">
                    </div>
                    
                    <div class="mb-3">
                        <label for="destination-address" class="form-label">Dirección de Destino</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="destination-address" placeholder="Ingresa la dirección de destino">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchDestinationAddress()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        <input type="hidden" id="destination-latitude">
                        <input type="hidden" id="destination-longitude">
                    </div>
                    
                    <div class="mb-3">
                        <label for="trip-time" class="form-label">¿Cuándo necesitas el viaje?</label>
                        <select class="form-select" id="trip-time">
                            <option value="now" selected>Ahora</option>
                            <option value="schedule">Programar para más tarde</option>
                        </select>
                    </div>
                    
                    <div class="mb-3 d-none" id="schedule-time-container">
                        <label for="schedule-time" class="form-label">Fecha y Hora</label>
                        <input type="datetime-local" class="form-control" id="schedule-time">
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment-method" class="form-label">Método de Pago</label>
                        <select class="form-select" id="payment-method">
                            <option value="cash" selected>Efectivo</option>
                            <option value="card">Tarjeta (al finalizar el viaje)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notas para el Conductor</label>
                        <textarea class="form-control" id="notes" rows="2" placeholder="Instrucciones especiales, referencias, etc."></textarea>
                    </div>
                    
                    <div class="fare-estimate mb-3">
                        <h6>Tarifa Estimada</h6>
                        <div class="fare-amount" id="fare-amount">$---.--</div>
                        <small class="text-muted">La tarifa final puede variar según la ruta tomada.</small>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg" onclick="requestTrip()">
                            <i class="bi bi-taxi-front me-2"></i> Solicitar Taxi
                        </button>
                        <a href="{{ url_for('passenger_dashboard_route') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i> Volver al Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 12); // Buenos Aires como ejemplo

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Variables para los marcadores
    let originMarker = null;
    let destinationMarker = null;
    let currentMode = 'origin'; // 'origin' o 'destination'

    // Iconos personalizados
    const originIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    const destinationIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon-2x.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    // Función para establecer modo de selección de origen
    function setOriginMode() {
        currentMode = 'origin';
        map.getContainer().style.cursor = 'crosshair';
    }

    // Función para establecer modo de selección de destino
    function setDestinationMode() {
        currentMode = 'destination';
        map.getContainer().style.cursor = 'crosshair';
    }

    // Función para limpiar marcadores
    function clearMarkers() {
        if (originMarker) {
            map.removeLayer(originMarker);
            originMarker = null;
            document.getElementById('origin-address').value = '';
            document.getElementById('origin-latitude').value = '';
            document.getElementById('origin-longitude').value = '';
        }
        
        if (destinationMarker) {
            map.removeLayer(destinationMarker);
            destinationMarker = null;
            document.getElementById('destination-address').value = '';
            document.getElementById('destination-latitude').value = '';
            document.getElementById('destination-longitude').value = '';
        }
        
        // Resetear tarifa estimada
        document.getElementById('fare-amount').textContent = '$---.--';
    }

    // Evento de clic en el mapa
    map.on('click', function(e) {
        const latlng = e.latlng;
        
        if (currentMode === 'origin') {
            // Eliminar marcador anterior si existe
            if (originMarker) {
                map.removeLayer(originMarker);
            }
            
            // Crear nuevo marcador de origen
            originMarker = L.marker([latlng.lat, latlng.lng], {icon: originIcon}).addTo(map);
            
            // Actualizar campos del formulario
            document.getElementById('origin-latitude').value = latlng.lat;
            document.getElementById('origin-longitude').value = latlng.lng;
            
            // Obtener dirección mediante geocodificación inversa
            reverseGeocode(latlng.lat, latlng.lng, 'origin');
        } else if (currentMode === 'destination') {
            // Eliminar marcador anterior si existe
            if (destinationMarker) {
                map.removeLayer(destinationMarker);
            }
            
            // Crear nuevo marcador de destino
            destinationMarker = L.marker([latlng.lat, latlng.lng], {icon: destinationIcon}).addTo(map);
            
            // Actualizar campos del formulario
            document.getElementById('destination-latitude').value = latlng.lat;
            document.getElementById('destination-longitude').value = latlng.lng;
            
            // Obtener dirección mediante geocodificación inversa
            reverseGeocode(latlng.lat, latlng.lng, 'destination');
        }
        
        // Dibujar ruta si ambos puntos están definidos
        if (originMarker && destinationMarker) {
            drawRoute();
            estimateFare();
        }
        
        // Restaurar cursor
        map.getContainer().style.cursor = '';
    });

    // Función para geocodificación inversa (obtener dirección a partir de coordenadas)
    async function reverseGeocode(lat, lng, type) {
        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);
            const data = await response.json();
            
            if (data && data.display_name) {
                if (type === 'origin') {
                    document.getElementById('origin-address').value = data.display_name;
                } else {
                    document.getElementById('destination-address').value = data.display_name;
                }
            }
        } catch (error) {
            console.error('Error en geocodificación inversa:', error);
        }
    }

    // Función para buscar dirección de origen
    async function searchOriginAddress() {
        const address = document.getElementById('origin-address').value;
        if (!address) return;
        
        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`);
            const data = await response.json();
            
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);
                
                // Eliminar marcador anterior si existe
                if (originMarker) {
                    map.removeLayer(originMarker);
                }
                
                // Crear nuevo marcador de origen
                originMarker = L.marker([lat, lng], {icon: originIcon}).addTo(map);
                
                // Actualizar campos del formulario
                document.getElementById('origin-latitude').value = lat;
                document.getElementById('origin-longitude').value = lng;
                
                // Centrar mapa en la ubicación
                map.setView([lat, lng], 15);
                
                // Dibujar ruta si ambos puntos están definidos
                if (originMarker && destinationMarker) {
                    drawRoute();
                    estimateFare();
                }
            }
        } catch (error) {
            console.error('Error en búsqueda de dirección:', error);
        }
    }

    // Función para buscar dirección de destino
    async function searchDestinationAddress() {
        const address = document.getElementById('destination-address').value;
        if (!address) return;
        
        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`);
            const data = await response.json();
            
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);
                
                // Eliminar marcador anterior si existe
                if (destinationMarker) {
                    map.removeLayer(destinationMarker);
                }
                
                // Crear nuevo marcador de destino
                destinationMarker = L.marker([lat, lng], {icon: destinationIcon}).addTo(map);
                
                // Actualizar campos del formulario
                document.getElementById('destination-latitude').value = lat;
                document.getElementById('destination-longitude').value = lng;
                
                // Centrar mapa en la ubicación
                map.setView([lat, lng], 15);
                
                // Dibujar ruta si ambos puntos están definidos
                if (originMarker && destinationMarker) {
                    drawRoute();
                    estimateFare();
                }
            }
        } catch (error) {
            console.error('Error en búsqueda de dirección:', error);
        }
    }

    // Función para dibujar ruta entre origen y destino
    function drawRoute() {
        // Obtener coordenadas
        const originLat = document.getElementById('origin-latitude').value;
        const originLng = document.getElementById('origin-longitude').value;
        const destLat = document.getElementById('destination-latitude').value;
        const destLng = document.getElementById('destination-longitude').value;
        
        if (!originLat || !originLng || !destLat || !destLng) return;
        
        // Dibujar línea directa entre puntos (simplificado)
        const routeLine = L.polyline([
            [originLat, originLng],
            [destLat, destLng]
        ], {
            color: '#0d6efd',
            weight: 3,
            opacity: 0.7,
            dashArray: '5, 10'
        }).addTo(map);
        
        // Ajustar vista para mostrar toda la ruta
        map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });
    }

    // Función para estimar tarifa
    function estimateFare() {
        // Obtener coordenadas
        const originLat = document.getElementById('origin-latitude').value;
        const originLng = document.getElementById('origin-longitude').value;
        const destLat = document.getElementById('destination-latitude').value;
        const destLng = document.getElementById('destination-longitude').value;
        
        if (!originLat || !originLng || !destLat || !destLng) return;
        
        // Calcular distancia en línea recta (simplificado)
        const distance = calculateDistance(originLat, originLng, destLat, destLng);
        
        // Calcular tarifa estimada (ejemplo simplificado)
        const baseFare = 200; // Tarifa base
        const perKmRate = 50; // Tarifa por km
        const estimatedFare = baseFare + (distance * perKmRate);
        
        // Actualizar tarifa en la interfaz
        document.getElementById('fare-amount').textContent = `$${estimatedFare.toFixed(2)}`;
    }

    // Función para calcular distancia entre dos puntos (fórmula de Haversine)
    function calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Radio de la Tierra en km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = 
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
            Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c; // Distancia en km
        return distance;
    }

    // Función para solicitar viaje
    async function requestTrip() {
        // Obtener datos del formulario
        const originLat = document.getElementById('origin-latitude').value;
        const originLng = document.getElementById('origin-longitude').value;
        const originAddress = document.getElementById('origin-address').value;
        const destLat = document.getElementById('destination-latitude').value;
        const destLng = document.getElementById('destination-longitude').value;
        const destAddress = document.getElementById('destination-address').value;
        const tripTime = document.getElementById('trip-time').value;
        const paymentMethod = document.getElementById('payment-method').value;
        const notes = document.getElementById('notes').value;
        
        // Validar datos
        if (!originLat || !originLng || !destLat || !destLng) {
            alert('Por favor selecciona un origen y un destino');
            return;
        }
        
        // Obtener fecha programada si es necesario
        let scheduledTime = null;
        if (tripTime === 'schedule') {
            scheduledTime = document.getElementById('schedule-time').value;
            if (!scheduledTime) {
                alert('Por favor selecciona una fecha y hora para el viaje programado');
                return;
            }
        }
        
        try {
            // Preparar datos para la API
            const tripData = {
                origin_latitude: originLat,
                origin_longitude: originLng,
                origin_address: originAddress,
                destination_latitude: destLat,
                destination_longitude: destLng,
                destination_address: destAddress,
                scheduled_for: scheduledTime,
                payment_method: paymentMethod,
                notes: notes
            };
            
            // Enviar solicitud a la API
            const response = await fetchWithAuth('/api/v1/trips', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(tripData)
            });
            
            if (response.ok) {
                const data = await response.json();
                alert('Viaje solicitado correctamente. ID: ' + data.id);
                
                // Redirigir a la página de detalles del viaje
                window.location.href = `/web/passenger/trip/${data.id}`;
            } else {
                const error = await response.json();
                alert('Error al solicitar viaje: ' + (error.detail || 'Error desconocido'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error de red al solicitar viaje');
        }
    }

    // Mostrar/ocultar selector de fecha programada
    document.getElementById('trip-time').addEventListener('change', function() {
        const scheduleContainer = document.getElementById('schedule-time-container');
        if (this.value === 'schedule') {
            scheduleContainer.classList.remove('d-none');
        } else {
            scheduleContainer.classList.add('d-none');
        }
    });

    // Inicializar fecha mínima para viajes programados
    document.addEventListener('DOMContentLoaded', function() {
        const now = new Date();
        now.setMinutes(now.getMinutes() + 30); // Mínimo 30 minutos en el futuro
        
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        const minDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
        document.getElementById('schedule-time').min = minDateTime;
    });
</script>
{% endblock %}
