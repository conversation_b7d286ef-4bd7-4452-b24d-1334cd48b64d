import enum
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, Enum as SQLAlchemyEnum
# from geoalchemy2 import Geometry # Para PostGIS
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base

class TripStatusEnum(str, enum.Enum):
    SOLICITADO = "solicitado"
    ACEPTADO = "aceptado"
    EN_CAMINO_PASAJERO = "en_camino_pasajero"
    EN_DESTINO_PASAJERO = "en_destino_pasajero" # Conductor llegó a recoger
    EN_VIAJE = "en_viaje"
    COMPLETADO = "completado"
    CANCELADO_PASAJERO = "cancelado_pasajero"
    CANCELADO_CONDUCTOR = "cancelado_conductor"
    NO_DISPONIBLE = "no_disponible" # No se encontraron conductores
    PROGRAMADO = "programado" # Viaje programado para el futuro

class Trip(Base):
    id = Column(Integer, primary_key=True, index=True)

    passenger_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    passenger = relationship("User", foreign_keys=[passenger_id])

    driver_id = Column(Integer, ForeignKey('users.id'), nullable=True) # Se asigna despu�s
    driver = relationship("User", foreign_keys=[driver_id])

    vehicle_id = Column(Integer, ForeignKey('vehicles.id'), nullable=True) # Se asigna con el conductor
    vehicle = relationship("Vehicle")

    origin_latitude = Column(String, nullable=False)
    origin_longitude = Column(String, nullable=False)
    origin_address = Column(String, nullable=True)
    # origin_location = Column(Geometry('POINT', srid=4326), nullable=False)

    destination_latitude = Column(String, nullable=False)
    destination_longitude = Column(String, nullable=False)
    destination_address = Column(String, nullable=True)
    # destination_location = Column(Geometry('POINT', srid=4326), nullable=False)

    # route_polyline = Column(Geometry('LINESTRING', srid=4326), nullable=True) # Ruta trazada

    estimated_fare = Column(Float, nullable=True)
    actual_fare = Column(Float, nullable=True)
    estimated_distance_meters = Column(Integer, nullable=True)
    actual_distance_meters = Column(Integer, nullable=True)
    estimated_duration_seconds = Column(Integer, nullable=True)
    actual_duration_seconds = Column(Integer, nullable=True)

    status = Column(SQLAlchemyEnum(TripStatusEnum), default=TripStatusEnum.SOLICITADO)

    requested_at = Column(DateTime(timezone=True), server_default=func.now())
    scheduled_for = Column(DateTime(timezone=True), nullable=True) # Para viajes programados
    accepted_at = Column(DateTime(timezone=True), nullable=True)
    started_at = Column(DateTime(timezone=True), nullable=True) # Cuando el pasajero sube
    completed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)

    # Campos para viajes con múltiples paradas
    has_stops = Column(Boolean, default=False) # Indica si el viaje tiene paradas intermedias

    passenger_rating_for_driver = Column(Integer, nullable=True)
    passenger_comment_for_driver = Column(String, nullable=True)
    driver_rating_for_passenger = Column(Integer, nullable=True)
    driver_comment_for_passenger = Column(String, nullable=True)

    # Relación con las paradas intermedias
    stops = relationship("TripStop", back_populates="trip", cascade="all, delete-orphan")
