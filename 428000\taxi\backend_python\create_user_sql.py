#!/usr/bin/env python3
"""
Script para crear un usuario administrador usando SQL directo.
"""

import psycopg2
from app.core.config import settings
from app.services.user_service import get_password_hash

def create_admin_user_with_sql():
    # Datos del usuario
    email = "joa<PERSON><EMAIL>"
    password = "isaias52"
    hashed_password = get_password_hash(password)
    full_name = "<PERSON>aq<PERSON><PERSON>"
    phone_number = "5555555555"
    is_active = True
    is_superuser = True
    
    # Conectar a la base de datos
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cursor = conn.cursor()
        
        # Verificar si el usuario ya existe
        cursor.execute("SELECT id FROM users WHERE email = %s", (email,))
        user_result = cursor.fetchone()
        
        if user_result:
            user_id = user_result[0]
            print(f"El usuario {email} ya existe con ID {user_id}. Actualizando contraseña...")
            
            # Actualizar contraseña y asegurar que está activo
            cursor.execute(
                "UPDATE users SET hashed_password = %s, is_active = %s, is_superuser = %s WHERE id = %s",
                (hashed_password, is_active, is_superuser, user_id)
            )
            
            # Verificar si ya tiene rol de administrador
            cursor.execute(
                """
                SELECT role_id FROM user_roles_association 
                JOIN roles ON user_roles_association.role_id = roles.id
                WHERE user_id = %s AND roles.name = 'administrador'
                """, 
                (user_id,)
            )
            
            if not cursor.fetchone():
                # Buscar el ID del rol administrador
                cursor.execute("SELECT id FROM roles WHERE name = 'administrador'")
                admin_role = cursor.fetchone()
                
                if admin_role:
                    admin_role_id = admin_role[0]
                    # Asignar rol de administrador
                    cursor.execute(
                        "INSERT INTO user_roles_association (user_id, role_id) VALUES (%s, %s)",
                        (user_id, admin_role_id)
                    )
                    print(f"Rol de administrador asignado al usuario {email}")
                else:
                    print("No se encontró el rol 'administrador' en la base de datos")
            else:
                print(f"El usuario {email} ya tiene el rol de administrador")
                
        else:
            # Crear nuevo usuario
            cursor.execute(
                """
                INSERT INTO users (email, full_name, hashed_password, phone_number, is_active, is_superuser)
                VALUES (%s, %s, %s, %s, %s, %s) RETURNING id
                """,
                (email, full_name, hashed_password, phone_number, is_active, is_superuser)
            )
            
            user_id = cursor.fetchone()[0]
            print(f"Usuario creado con ID {user_id}")
            
            # Buscar el ID del rol administrador
            cursor.execute("SELECT id FROM roles WHERE name = 'administrador'")
            admin_role = cursor.fetchone()
            
            if admin_role:
                admin_role_id = admin_role[0]
                # Asignar rol de administrador
                cursor.execute(
                    "INSERT INTO user_roles_association (user_id, role_id) VALUES (%s, %s)",
                    (user_id, admin_role_id)
                )
                print(f"Rol de administrador asignado al usuario {email}")
            else:
                print("No se encontró el rol 'administrador' en la base de datos")
                
                # Intentar crear el rol administrador
                print("Intentando crear el rol 'administrador'...")
                cursor.execute(
                    "INSERT INTO roles (name, description) VALUES ('administrador', 'Rol de Administrador') RETURNING id"
                )
                admin_role_id = cursor.fetchone()[0]
                
                # Asignar rol de administrador
                cursor.execute(
                    "INSERT INTO user_roles_association (user_id, role_id) VALUES (%s, %s)",
                    (user_id, admin_role_id)
                )
                print(f"Rol 'administrador' creado y asignado al usuario {email}")
        
        # Confirmar cambios
        conn.commit()
        print(f"Usuario {email} configurado correctamente con contraseña: {password}")
        
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Creando usuario administrador con SQL directo...")
    create_admin_user_with_sql()
    print("Proceso completado.")
