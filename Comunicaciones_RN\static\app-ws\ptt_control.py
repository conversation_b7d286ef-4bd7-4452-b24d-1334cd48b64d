# ptt_control.py

import serial

def set_ptt(state, config, ser=None):
    com_port = f"COM{config.get('port_number', '1')}"
    try:
        # Si vamos a activar el PTT
        if state:
            if ser is None or not ser.is_open:
                # Abre el puerto serie si no está abierto
                ser = serial.Serial(com_port, baudrate=9600, timeout=1)
                print(f"Puerto {com_port} abierto.", flush=True)
            ser.setDTR(True)  # Activa el DTR para habilitar el PTT
            print(f"PTT activado en {com_port}.", flush=True)
            return ser
        else:
            # Si vamos a desactivar el PTT
            if ser is not None and ser.is_open:
                ser.setDTR(False)  # Desactiva el DTR para deshabilitar el PTT
                print(f"PTT desactivado en {com_port}.", flush=True)
                ser.close()  # Cierra el puerto serie
            return None
    except serial.SerialException as e:
        print(f"Error al abrir el puerto {com_port}: {e}", flush=True)
        return None
    except Exception as e:
        print(f"Error inesperado al manejar el puerto {com_port}: {e}", flush=True)
        return None
