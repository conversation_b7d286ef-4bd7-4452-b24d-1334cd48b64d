{# /backend/apps/core/templates/core/user_form.html #}
{% extends "core/base.html" %}

{% block title %}{{ view.object|yesno:"Editar Usuario,Nuevo Usuario" }} - RN-Rural{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-3">{{ view.object|yesno:"Editar Usuario,Nuevo Usuario" }}</h2>
    <hr>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-user-edit"></i> Datos del Usuario</h4>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% for field in form %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label">
                                    {% if field.name == 'username' %}
                                        <i class="fas fa-user"></i>
                                    {% elif field.name == 'password1' or field.name == 'password2' or field.name == 'password' %}
                                        <i class="fas fa-lock"></i>
                                    {% elif field.name == 'email' %}
                                        <i class="fas fa-envelope"></i>
                                    {% elif field.name == 'role' %}
                                        <i class="fas fa-user-tag"></i>
                                    {% elif field.name == 'is_active' %}
                                        <i class="fas fa-toggle-on"></i>
                                    {% elif field.name == 'is_staff' %}
                                        <i class="fas fa-user-shield"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle"></i>
                                    {% endif %}
                                    {{ field.label }}
                                </label>
                                {{ field }}
                                {% if field.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ field.errors|striptags }}
                                    </div>
                                {% endif %}
                                {% if field.help_text %}
                                    <small class="form-text text-muted">{{ field.help_text }}</small>
                                {% endif %}
                            </div>
                        {% endfor %}

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'user_list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left"></i> Volver
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Guardar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
