 <!-- app/templates/base.html -->
 {% extends "base.html" %}
 {# Opcional: Importar macros de Flask-Bootstrap si lo usas
 {% import 'bootstrap/wtf.html' as wtf %}
 #}
 
 {% block title %}Iniciar <PERSON><PERSON><PERSON> - {{ super() }}{% endblock %}
 
 {% block content %}
 <div class="row justify-content-center">
     <div class="col-md-6 col-lg-4">
         <div class="card mt-5">
             <div class="card-body">
                 <h2 class="card-title text-center mb-4">Iniciar Se<PERSON>ón</h2>
                 <form action="{{ url_for('auth.login', next=request.args.get('next')) }}" method="post" novalidate>
                     {{ form.hidden_tag() }} {# CSRF protection #}
 
                     <div class="mb-3">
                         {{ form.username.label(class="form-label") }}
                         {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""), size=32, placeholder="Nombre de usuario") }}
                         {% for error in form.username.errors %}
                             <div class="invalid-feedback">{{ error }}</div>
                         {% endfor %}
                     </div>
 
                     <div class="mb-3">
                         {{ form.password.label(class="form-label") }}
                         {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), size=32, placeholder="Contraseña") }}
                         {% for error in form.password.errors %}
                             <div class="invalid-feedback">{{ error }}</div>
                         {% endfor %}
                     </div>
 
                     <div class="mb-3 form-check">
                         {{ form.remember_me(class="form-check-input") }}
                         {{ form.remember_me.label(class="form-check-label") }}
                     </div>
 
                     <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-block") }}
                     </div>
                 </form>
             </div>
              <div class="card-footer text-center">
                 <small>¿No tienes cuenta? <a href="{{ url_for('auth.register') }}">Regístrate aquí</a></small>
                 {# <br> <a href="#">¿Olvidaste tu contraseña?</a> #}
             </div>
         </div>
     </div>
 </div>
 {% endblock %}