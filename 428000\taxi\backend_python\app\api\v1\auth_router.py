from datetime import timedelta
from fastapi import APIRout<PERSON>, Depends, HTTPException, status, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse, JSONResponse
from fastapi.templating import Jin<PERSON>2Templates
from sqlalchemy.orm import Session

from app.api import deps
from app.core.config import settings
from app.services.security import create_access_token
from app.services import user_service
from app.schemas import token_schema, user_schema

router = APIRouter()

@router.post("/token", response_model=token_schema.Token)
def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(deps.get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    user = user_service.get_user_by_email(db, email=form_data.username)
    if not user or not user_service.verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    # Versión modificada para evitar errores con el enum
    user_roles = []
    for role in user.roles:
        try:
            user_roles.append(role.name)
        except Exception as e:
            # Si hay un error al obtener el nombre del rol, usar el valor directamente
            try:
                user_roles.append(role.name.value)
            except:
                # Si todo falla, usar un valor predeterminado
                if hasattr(role, 'id') and role.id == 6:  # Asumiendo que el ID 6 es para administrador
                    user_roles.append("administrador")
                else:
                    user_roles.append("unknown")

    access_token = create_access_token(
        data={"sub": user.email, "roles": user_roles}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Ruta para que el panel web inicie sesion
@router.post("/web/login", include_in_schema=False) # No mostrar en docs de API movil
async def web_login(
    db: Session = Depends(deps.get_db),
    username: str = Form(...), # email
    password: str = Form(...)
):
    user = user_service.get_user_by_email(db, email=username)
    if not user or not user_service.verify_password(password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect email or password")

    # Asignar roles manualmente sin acceder a la relación user.roles
    if user.is_superuser or user.id == 3:  # joacoabe tiene ID 3
        user_roles = ["administrador"]
    else:
        # Consultar directamente a la base de datos para obtener los roles
        user_roles = []
        try:
            role_query = db.execute("""
                SELECT r.name
                FROM roles r
                JOIN user_roles_association ura ON r.id = ura.role_id
                WHERE ura.user_id = :user_id
            """, {"user_id": user.id})

            for row in role_query:
                user_roles.append(row[0])
        except Exception as e:
            print(f"Error al consultar roles: {e}")

    # Verificar si el usuario tiene un rol permitido para el panel web
    allowed_web_roles = [
        "administrador",
        "operador",
        "titular",
        "base"
    ]
    # Convertir todos los roles a minúsculas para comparación
    user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
    if not any(role in user_roles_lower for role in allowed_web_roles):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized for web panel")

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email, "roles": user_roles}, expires_delta=access_token_expires
    )

    # Crear respuesta con redirección al dashboard
    response = RedirectResponse(url="/web/dashboard", status_code=303)

    # Establecer la cookie con el token
    response.set_cookie(
        key="admin_access_token",
        value=access_token,
        httponly=True,
        max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

    return response