/* Reset de estilos por defecto */
body, h1, p, ul, li, a, form, label, input, button {
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
  text-decoration: none;
  box-sizing: border-box;
}

body {
  background-color: #f0f8ff; /* Fondo celeste claro */
  color: #333;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.body-background::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../static/RNCom.png') no-repeat center center;
  background-size: contain; /* Ajustar el tamaño del logo */
  opacity: 0.3; /* Transparencia del logo */
  z-index: -1;
}

header {
  background-color: #006994; /* Azul oscuro */
  color: #fff;
  padding: 10px 20px;
  text-align: center;
}

header img.logo {
  display: none; /* Ocultar el logo en el header */
}

header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

header p {
  font-size: 14px;
}

main {
  flex: 1;
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  overflow: auto;
}

/* Estilo para la sección de información del nodo */
.node-section {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column; /* Cambiar a columna para que los elementos se dispongan uno debajo del otro */
  justify-content: center;
  align-items: center;
  text-align: center;
}

.node-info-container {
  margin-top: 10px; /* Espacio entre el título y el contenido */
}

.node-section h3 {
  font-size: 18px;
  color: #006994;
  margin-bottom: 0;  /* Asegura que no haya mucho espacio entre el título y el contenido */
}

/* Estilo para el contenido de la información del nodo */
.node-section p {
  font-size: 16px;
  color: #fff;
  background-color: #006994; /* Fondo azul oscuro */
  padding: 10px 20px;  /* Padding para dar más espacio */
  border-radius: 5px;  /* Esquinas redondeadas */
  display: inline-block;
  border: 1px solid #004d73; /* Borde azul intermedio */
  transition: background-color 0.3s, color 0.3s;
}

.node-section p:hover {
  background-color: #004d73; /* Cambiar el color de fondo al pasar el mouse */
  color: #f0f8ff;  /* Cambiar el color del texto al pasar el mouse */
}

/* Contenedor principal que incluye chat y usuarios conectados */
.chat-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 80%;
  max-width: 1200px;
  margin-top: 20px;
  margin-bottom: 20px;
}

/* Sección de eventos de audio (messages) con barra de desplazamiento */
.events-section {
  flex: 2;
  border: 1px solid #726e6e;
  padding: 10px;
  height: 300px; /* Ajuste la altura para que sea más corta por defecto */
  overflow-y: auto; /* Barra de scroll para eventos */
  background-color: #f0f8ff;
  margin-right: 20px;
  border-radius: 10px;
  word-wrap: break-word;
}

/* Sección de usuarios conectados */
.users-section {
  flex: 1;
  border: 1px solid #a89999;
  padding: 10px;
  height: 300px; /* Ajuste la altura para que sea más corta por defecto */
  overflow-y: auto; /* Barra de scroll para usuarios */
  background-color: #f0f8ff;
  border-radius: 10px;
}

/* Estilos para las listas dentro de los contenedores */
.users-section ul,
.events-section ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

/* Estilo para los elementos dentro de la lista de usuarios */
.users-section ul li {
  background-color: #006994;
  color: #fff;
  padding: 10px;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.users-section ul li:hover {
  background-color: #004d73;
}

/* Estilo para los mensajes en eventos de audio */
.events-section ul li {
  background-color: #fff;
  color: #006994;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  border: 1px solid #004d73;
}

/* Sección de botón de transmisión de audio */
.audio-section img {
  cursor: pointer;
  display: block;
  margin: 0 auto;
  width: 100px;
  height: 100px;
}

/* Estilo del pie de página */
footer {
  background-color: #006994;
  color: #fff;
  text-align: center;
  padding: 10px;
  width: 100%;
}

/* Responsividad */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }

  .events-section,
  .users-section {
    margin-bottom: 20px;
    height: 250px; /* Ajuste de altura para pantallas más pequeñas */
  }
}

@media (max-width: 480px) {
  .users-section ul li, .events-section ul li {
    font-size: 12px;
  }

  .audio-section img {
    width: 60px;
    height: 60px;
  }

  .volume-bar-container {
    height: 20px;
  }
}


@media (max-width: 480px) {
  .users-section ul li, .events-section ul li {
    font-size: 12px;
  }

  .audio-section img {
    width: 60px;
    height: 60px;
  }

  .volume-bar-container {
    height: 20px;
  }
}
/* Añadido para los campos de formulario */
.config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: #ffffff; /* Fondo blanco para el formulario */
  border-radius: 15px; /* Bordes redondeados */
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  max-width: 600px; /* Limitar el ancho del formulario */
  margin: 0 auto; /* Centrar el formulario */
}

.form-group {
  display: flex;
  flex-direction: column;
}

label {
  font-size: 18px; /* Tamaño de fuente más grande */
  color: #006994; /* Azul oscuro */
  margin-bottom: 5px; /* Espacio debajo de la etiqueta */
}

.input-field {
  padding: 12px; /* Ajuste del padding para más espacio */
  border: 1px solid #004d73; /* Borde azul intermedio */
  border-radius: 8px; /* Bordes redondeados */
  font-size: 16px;
  transition: border-color 0.3s;
}

.input-field:focus {
  border-color: #006994; /* Azul oscuro */
  outline: none;
}

.submit-btn {
  padding: 12px 20px;
  background-color: #006994; /* Azul oscuro */
  color: #fff;
  border: none;
  border-radius: 8px; /* Bordes redondeados para el botón */
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.submit-btn:hover {
  background-color: #004d73; /* Azul intermedio */
}

/* Aumentar el espacio alrededor del formulario */
.container {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-height: calc(100vh - 120px); /* Espacio para header y footer */
}

.node-section {
  margin-bottom: 20px;
  text-align: center;
}

h3 {
  font-size: 24px;
  color: #004d73; /* Azul intermedio */
}

footer {
  background-color: #006994; /* Azul oscuro */
  color: #fff;
  text-align: center;
  padding: 10px;
  width: 100%;
  position: relative;
  bottom: 0;
}
