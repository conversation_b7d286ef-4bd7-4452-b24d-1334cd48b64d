# File: backend/apps/locations/models.py
# -----------------------------------------------


from django.contrib.gis.db import models as gis_models
from django.db import models
from django.conf import settings

class UnidadRegional(models.Model):
    nombre = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.nombre

    class Meta:
        verbose_name = "Unidad Regional"
        verbose_name_plural = "Unidades Regionales"

class Ciudad(models.Model):
    nombre = models.CharField(max_length=100)
    unidad_regional = models.ForeignKey(UnidadRegional, on_delete=models.PROTECT, related_name="ciudades")
    # centro_geografico = gis_models.PointField(null=True, blank=True, srid=4326)

    def __str__(self):
        return f"{self.nombre} ({self.unidad_regional.nombre})"

    class Meta:
        verbose_name = "Ciudad"
        verbose_name_plural = "Ciudades"
        unique_together = ('nombre', 'unidad_regional')

class BrigadaMovilLocation(models.Model):
    brigada_usuario = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'role': 'BRIGADA'},
        related_name='ubicacion_movil'
    )
    ubicacion_actual = gis_models.PointField(srid=4326)
    ultima_actualizacion = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Ubicación de {self.brigada_usuario.username} a las {self.ultima_actualizacion.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        verbose_name = "Ubicación de Móvil de Brigada"
        verbose_name_plural = "Ubicaciones de Móviles de Brigada"
        indexes = [
            gis_models.Index(fields=['ubicacion_actual']),
        ]
class UbicacionUsuario(models.Model):
    usuario = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'role': 'CIUDADANO'},
        related_name='ubicacion_usuario'
    )
    posicion_actual = gis_models.PointField(srid=4326)
    ultima_actualizacion = models.DateTimeField(auto_now=True)