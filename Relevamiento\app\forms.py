# app/forms.py
from flask_wtf import FlaskForm
from wtforms import (StringField, PasswordField, BooleanField,
                     SubmitField, TextAreaField, SelectField, HiddenField, IntegerField, FloatField, DateField, SelectMultipleField)
from wtforms.validators import (DataRequired, Email, EqualTo,
                                ValidationError, Length, Optional, NumberRange)
from flask_wtf.file import FileField, FileAllowed, FileRequired
from app.models import User
# Importa Config directamente desde el archivo config.py en la raíz
from config import Config

class LoginForm(FlaskForm):
    username = <PERSON><PERSON>ield('Usuario', validators=[DataRequired(message="El nombre de usuario es obligatorio.")])
    password = PasswordField('Contraseña', validators=[DataRequired(message="La contraseña es obligatoria.")])
    remember_me = BooleanField('Recordarme')
    submit = SubmitField('Iniciar <PERSON>')

class RegistrationForm(FlaskForm):
    username = <PERSON><PERSON>ield('Usuario', validators=[DataRequired(message="El nombre de usuario es obligatorio.")])
    email = StringField('Email (Opcional)', validators=[Optional(), Email(message="Introduce un email válido.")])
    password = PasswordField('Contraseña', validators=[DataRequired(message="La contraseña es obligatoria."), Length(min=6, message="La contraseña debe tener al menos 6 caracteres.")])
    password2 = PasswordField(
        'Repetir Contraseña', validators=[DataRequired(message="Confirma la contraseña."), EqualTo('password', message="Las contraseñas deben coincidir.")])
    submit = SubmitField('Registrarse')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Este nombre de usuario ya está en uso. Por favor, elige otro.')

    def validate_email(self, email):
        if email.data: # Solo validar si se proporcionó un email
            user = User.query.filter_by(email=email.data).first()
            if user is not None:
                raise ValidationError('Este email ya está registrado. Por favor, usa otro.')

class ImageUploadForm(FlaskForm):
    image = FileField('Seleccionar Imagen', validators=[
        FileRequired(message="Debes seleccionar un archivo de imagen."),
        # Corrección: Accede a la configuración estática desde la clase Config
        FileAllowed(Config.ALLOWED_EXTENSIONS, '¡Solo se permiten imágenes (png, jpg, jpeg, gif)!')
    ])
    notes = TextAreaField('Notas (Opcional)', validators=[Length(max=500, message="Las notas no pueden exceder los 500 caracteres.")])
    submit = SubmitField('Subir Imagen')

class PointStatusForm(FlaskForm): # Renombramos este para que sea solo de estado/descripción
    status = SelectField('Estado', choices=[
        ('azul', 'Por Hacer'), ('amarillo', 'En Curso'),
        ('verde', 'Hecho'), ('rojo', 'Incompleto')
    ], validators=[DataRequired(message="Debes seleccionar un estado.")])
    description = TextAreaField('Descripción (Opcional)', validators=[Optional(), Length(max=1000)])
    submit_status = SubmitField('Actualizar Punto')

# --- NUEVO FORMULARIO CRUD para Puntos ---
class PointForm(FlaskForm):
    name = StringField('Nombre/Dirección', validators=[
        DataRequired(message="El nombre o dirección es obligatorio."),
        Length(max=128)
    ])
    city = StringField('Ciudad', validators=[Optional(), Length(max=80)]) # Opcional o DataRequired según necesites
    latitude = FloatField('Latitud', validators=[
        DataRequired(message="La latitud es obligatoria."),
        NumberRange(min=-90.0, max=90.0, message="Latitud debe estar entre -90 y 90.")
    ])
    longitude = FloatField('Longitud', validators=[
        DataRequired(message="La longitud es obligatoria."),
        NumberRange(min=-180.0, max=180.0, message="Longitud debe estar entre -180 y 180.")
    ])
    status = SelectField('Estado', choices=[
        ('azul', 'Por Hacer'), ('amarillo', 'En Curso'),
        ('verde', 'Hecho'), ('rojo', 'Incompleto')
    ], validators=[DataRequired()])
    source = StringField('Origen del Archivo', validators=[Optional(), Length(max=128)]) # origen del archivo
    description = TextAreaField('Descripción (Opcional)', validators=[Optional(), Length(max=1000)])
    submit = SubmitField('Guardar Punto')

# ... (SetCircleForm - puede eliminarse si ya no se usa el círculo simple) ...
# class SetCircleForm(FlaskForm):
#     circle_x = HiddenField('Circle X', validators=[DataRequired()])
#     circle_y = HiddenField('Circle Y', validators=[DataRequired()])


class CameraForm(FlaskForm):
    type = SelectField('Tipo de cámara', choices=[('domo', 'Domo'), ('fija', 'Fija'), ('otra', 'Otra')], validators=[DataRequired()])
    direction = StringField('Dirección de enfoque', validators=[DataRequired()])
    photo = FileField('Foto de la cámara', validators=[FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Solo imágenes')])

    # Campos de coordenadas
    latitude = FloatField('Latitud', validators=[Optional(), NumberRange(min=-90.0, max=90.0, message="Latitud debe estar entre -90 y 90.")])
    longitude = FloatField('Longitud', validators=[Optional(), NumberRange(min=-180.0, max=180.0, message="Longitud debe estar entre -180 y 180.")])
    location_source = SelectField('Origen de Coordenadas', choices=[
        ('', 'Usar coordenadas del punto'),
        ('gps', 'GPS del dispositivo'),
        ('manual', 'Ingreso manual')
    ], validators=[Optional()])
    location_accuracy = FloatField('Precisión GPS (metros)', validators=[Optional(), NumberRange(min=0.0)])

    submit = SubmitField('Agregar Cámara')

class FileImportForm(FlaskForm):
    csv_file = FileField('Archivo CSV', validators=[
        FileAllowed(['csv'], 'Solo se permiten archivos .csv')
    ])
    submit_csv = SubmitField('Importar CSV')

    dbf_file = FileField('Archivo DBF', validators=[
        FileAllowed(['dbf'], 'Solo se permiten archivos .dbf')
    ])
    submit_dbf = SubmitField('Importar DBF')

# --- FORMULARIO DE FILTROS PARA PUNTOS ---
class PointsFilterForm(FlaskForm):
    # Filtro por ID de punto
    point_id = IntegerField('ID Punto', validators=[Optional(), NumberRange(min=1)])

    # Filtro por nombre del punto
    name = StringField('Nombre del Punto', validators=[Optional(), Length(max=200)])

    # Filtro por ciudad
    city = SelectField('Ciudad', choices=[('', 'Todas las ciudades')], validators=[Optional()])

    # Filtro por estado
    status = SelectField('Estado', choices=[
        ('', 'Todos los estados'),
        ('azul', 'Por Hacer'),
        ('amarillo', 'En Curso'),
        ('verde', 'Hecho'),
        ('rojo', 'Incompleto')
    ], validators=[Optional()])

    # Filtro por origen/capa
    source = SelectField('Origen (Capa)', choices=[('', 'Todos los orígenes')], validators=[Optional()])

    # Filtro por descripción
    description = StringField('Descripción (contiene)', validators=[Optional(), Length(max=500)])

    # Filtro por tipo de cámara asociada
    camera_type = SelectField('Tipo de Cámara', choices=[
        ('', 'Todos los tipos'),
        ('domo', 'Con Domo'),
        ('fija', 'Con Fija'),
        ('otra', 'Con Otra')
    ], validators=[Optional()])

    # Filtro por presencia de imágenes
    has_images = SelectField('Tiene Imágenes', choices=[
        ('', 'Todos'),
        ('si', 'Con imágenes'),
        ('no', 'Sin imágenes')
    ], validators=[Optional()])

    # Filtro por presencia de cámaras
    has_cameras = SelectField('Tiene Cámaras', choices=[
        ('', 'Todos'),
        ('si', 'Con cámaras'),
        ('no', 'Sin cámaras')
    ], validators=[Optional()])

    # Filtro por número de cámaras
    cameras_count = SelectField('Cantidad de Cámaras', choices=[
        ('', 'Cualquier cantidad'),
        ('1', '1 cámara'),
        ('2', '2 cámaras'),
        ('3', '3 cámaras'),
        ('4+', '4 o más cámaras')
    ], validators=[Optional()])

    # Filtro por presencia de anotaciones
    has_annotations = SelectField('Tiene Anotaciones', choices=[
        ('', 'Todos'),
        ('si', 'Con anotaciones'),
        ('no', 'Sin anotaciones')
    ], validators=[Optional()])

    # Filtro por usuario creador
    created_by = SelectField('Creado por', choices=[('', 'Todos los usuarios')], validators=[Optional()])

    # Filtro por usuario que modificó
    modified_by = SelectField('Modificado por', choices=[('', 'Todos los usuarios')], validators=[Optional()])

    # Filtros de coordenadas (rango)
    lat_min = FloatField('Latitud Mínima', validators=[Optional(), NumberRange(min=-90, max=90)])
    lat_max = FloatField('Latitud Máxima', validators=[Optional(), NumberRange(min=-90, max=90)])
    lon_min = FloatField('Longitud Mínima', validators=[Optional(), NumberRange(min=-180, max=180)])
    lon_max = FloatField('Longitud Máxima', validators=[Optional(), NumberRange(min=-180, max=180)])

    # Filtros de fecha
    date_from = DateField('Fecha Creación Desde', validators=[Optional()], format='%Y-%m-%d')
    date_to = DateField('Fecha Creación Hasta', validators=[Optional()], format='%Y-%m-%d')

    # Filtros de fecha de modificación
    modified_from = DateField('Fecha Modificación Desde', validators=[Optional()], format='%Y-%m-%d')
    modified_to = DateField('Fecha Modificación Hasta', validators=[Optional()], format='%Y-%m-%d')

    # Botones
    submit = SubmitField('Aplicar Filtros')
    clear = SubmitField('Limpiar Filtros')

# --- FORMULARIO DE FILTROS PARA REPORTES ---
class ReportsFilterForm(FlaskForm):
    # Filtro por ID de punto
    point_id = IntegerField('ID Punto', validators=[Optional(), NumberRange(min=1)])

    # Filtro por ciudad
    city = SelectField('Ciudad', choices=[('', 'Todas las ciudades')], validators=[Optional()])

    # Filtro por estado
    status = SelectField('Estado', choices=[
        ('', 'Todos los estados'),
        ('azul', 'Por Hacer'),
        ('amarillo', 'En Curso'),
        ('verde', 'Hecho'),
        ('rojo', 'Incompleto')
    ], validators=[Optional()])

    # Filtro por origen/capa
    source = SelectField('Origen (Capa)', choices=[('', 'Todos los orígenes')], validators=[Optional()])

    # Filtro por tipo de cámara
    camera_type = SelectField('Tipo de Cámara', choices=[
        ('', 'Todos los tipos'),
        ('domo', 'Domo'),
        ('fija', 'Fija'),
        ('otra', 'Otra')
    ], validators=[Optional()])

    # Filtro por presencia de imágenes
    has_images = SelectField('Tiene Imágenes', choices=[
        ('', 'Todos'),
        ('si', 'Con imágenes'),
        ('no', 'Sin imágenes')
    ], validators=[Optional()])

    # Filtro por presencia de cámaras
    has_cameras = SelectField('Tiene Cámaras', choices=[
        ('', 'Todos'),
        ('si', 'Con cámaras'),
        ('no', 'Sin cámaras')
    ], validators=[Optional()])

    # Filtros de fecha
    date_from = DateField('Fecha Creación Desde', validators=[Optional()], format='%Y-%m-%d')
    date_to = DateField('Fecha Creación Hasta', validators=[Optional()], format='%Y-%m-%d')

    # Filtros de fecha de modificación
    modified_from = DateField('Fecha Modificación Desde', validators=[Optional()], format='%Y-%m-%d')
    modified_to = DateField('Fecha Modificación Hasta', validators=[Optional()], format='%Y-%m-%d')

    # Botones
    submit = SubmitField('Aplicar Filtros')
    clear = SubmitField('Limpiar Filtros')

# --- FORMULARIOS PARA GESTIÓN DE USUARIOS ---
class UserCreateForm(FlaskForm):
    """Formulario para crear nuevos usuarios."""
    username = StringField('Nombre de Usuario', validators=[
        DataRequired(message='El nombre de usuario es obligatorio'),
        Length(min=3, max=64, message='El nombre debe tener entre 3 y 64 caracteres')
    ])

    email = StringField('Email', validators=[
        Optional(),
        Email(message='Formato de email inválido'),
        Length(max=120, message='Email demasiado largo')
    ])

    password = PasswordField('Contraseña', validators=[
        DataRequired(message='La contraseña es obligatoria'),
        Length(min=6, message='La contraseña debe tener al menos 6 caracteres')
    ])

    password_confirm = PasswordField('Confirmar Contraseña', validators=[
        DataRequired(message='Debe confirmar la contraseña'),
        EqualTo('password', message='Las contraseñas no coinciden')
    ])

    role = SelectField('Rol', choices=[
        ('visualizador', 'Visualizador'),
        ('operador', 'Operador'),
        ('supervisor', 'Supervisor'),
        ('administrador', 'Administrador')
    ], validators=[DataRequired()])

    is_active = BooleanField('Usuario Activo', default=True)

    # Permisos de ciudades (se llenarán dinámicamente)
    cities = SelectMultipleField('Ciudades Permitidas', choices=[], validators=[Optional()])

    # Permisos de fuentes/capas (se llenarán dinámicamente)
    sources = SelectMultipleField('Capas/Orígenes Permitidos', choices=[], validators=[Optional()])

    # Permisos específicos
    points_view = BooleanField('Ver Puntos')
    points_edit = BooleanField('Editar Puntos')
    points_create = BooleanField('Crear Puntos')
    points_delete = BooleanField('Eliminar Puntos')

    cameras_view = BooleanField('Ver Cámaras')
    cameras_edit = BooleanField('Editar Cámaras')
    cameras_create = BooleanField('Crear Cámaras')
    cameras_delete = BooleanField('Eliminar Cámaras')

    reports_view = BooleanField('Ver Reportes')
    reports_export = BooleanField('Exportar Datos')

    submit = SubmitField('Crear Usuario')

class UserEditForm(FlaskForm):
    """Formulario para editar usuarios existentes."""
    username = StringField('Nombre de Usuario', validators=[
        DataRequired(message='El nombre de usuario es obligatorio'),
        Length(min=3, max=64, message='El nombre debe tener entre 3 y 64 caracteres')
    ])

    email = StringField('Email', validators=[
        Optional(),
        Email(message='Formato de email inválido'),
        Length(max=120, message='Email demasiado largo')
    ])

    password = PasswordField('Nueva Contraseña', validators=[
        Optional(),
        Length(min=6, message='La contraseña debe tener al menos 6 caracteres')
    ])

    password_confirm = PasswordField('Confirmar Nueva Contraseña', validators=[
        EqualTo('password', message='Las contraseñas no coinciden')
    ])

    role = SelectField('Rol', choices=[
        ('visualizador', 'Visualizador'),
        ('operador', 'Operador'),
        ('supervisor', 'Supervisor'),
        ('administrador', 'Administrador')
    ], validators=[DataRequired()])

    is_active = BooleanField('Usuario Activo')

    # Permisos de ciudades (se llenarán dinámicamente)
    cities = SelectMultipleField('Ciudades Permitidas', choices=[], validators=[Optional()])

    # Permisos de fuentes/capas (se llenarán dinámicamente)
    sources = SelectMultipleField('Capas/Orígenes Permitidos', choices=[], validators=[Optional()])

    # Permisos específicos
    points_view = BooleanField('Ver Puntos')
    points_edit = BooleanField('Editar Puntos')
    points_create = BooleanField('Crear Puntos')
    points_delete = BooleanField('Eliminar Puntos')

    cameras_view = BooleanField('Ver Cámaras')
    cameras_edit = BooleanField('Editar Cámaras')
    cameras_create = BooleanField('Crear Cámaras')
    cameras_delete = BooleanField('Eliminar Cámaras')

    reports_view = BooleanField('Ver Reportes')
    reports_export = BooleanField('Exportar Datos')

    submit = SubmitField('Actualizar Usuario')

class UsersFilterForm(FlaskForm):
    """Formulario para filtrar usuarios."""
    username = StringField('Nombre de Usuario', validators=[Optional()])
    role = SelectField('Rol', choices=[
        ('', 'Todos los roles'),
        ('visualizador', 'Visualizador'),
        ('operador', 'Operador'),
        ('supervisor', 'Supervisor'),
        ('administrador', 'Administrador')
    ], validators=[Optional()])

    is_active = SelectField('Estado', choices=[
        ('', 'Todos'),
        ('true', 'Activos'),
        ('false', 'Inactivos')
    ], validators=[Optional()])

    city = SelectField('Ciudad Asignada', choices=[('', 'Todas las ciudades')], validators=[Optional()])
    source = SelectField('Capa Asignada', choices=[('', 'Todas las capas')], validators=[Optional()])

    submit = SubmitField('Aplicar Filtros')
    clear = SubmitField('Limpiar Filtros')

# --- FORMULARIO DE FILTROS PARA CÁMARAS ---
class CamerasFilterForm(FlaskForm):
    # Filtro por ID de cámara
    camera_id = IntegerField('ID Cámara', validators=[Optional(), NumberRange(min=1)])

    # Filtro por ID de punto
    point_id = IntegerField('ID Punto', validators=[Optional(), NumberRange(min=1)])

    # Filtro por tipo de cámara
    camera_type = SelectField('Tipo de Cámara', choices=[
        ('', 'Todos los tipos'),
        ('domo', 'Domo'),
        ('fija', 'Fija'),
        ('otra', 'Otra')
    ], validators=[Optional()])

    # Filtro por ciudad del punto
    city = SelectField('Ciudad', choices=[('', 'Todas las ciudades')], validators=[Optional()])

    # Filtro por origen del punto
    source = SelectField('Origen (Capa)', choices=[('', 'Todos los orígenes')], validators=[Optional()])

    # Filtro por fuente de coordenadas
    location_source = SelectField('Fuente de Coordenadas', choices=[
        ('', 'Todas las fuentes'),
        ('gps', 'GPS'),
        ('manual', 'Manual'),
        ('point', 'Del Punto')
    ], validators=[Optional()])

    # Filtro por presencia de foto
    has_photo = SelectField('Tiene Foto', choices=[
        ('', 'Todos'),
        ('si', 'Con foto'),
        ('no', 'Sin foto')
    ], validators=[Optional()])

    # Filtro por coordenadas propias
    has_coordinates = SelectField('Coordenadas Propias', choices=[
        ('', 'Todos'),
        ('si', 'Con coordenadas propias'),
        ('no', 'Usa coordenadas del punto')
    ], validators=[Optional()])

    # Filtros de fecha
    date_from = DateField('Fecha Creación Desde', validators=[Optional()], format='%Y-%m-%d')
    date_to = DateField('Fecha Creación Hasta', validators=[Optional()], format='%Y-%m-%d')

    # Botones
    submit = SubmitField('Aplicar Filtros')
    clear = SubmitField('Limpiar Filtros')