# Ejemplo de script para crear datos iniciales
from app.db.database import SessionLocal
from app.models.user import User, UserRoleModel, RoleEnum
from app.services.security import get_password_hash

def init_db():
    db = SessionLocal()
    try:
        # Crear roles si no existen
        for role_enum in RoleEnum:
            role = db.query(UserRoleModel).filter(UserRoleModel.name == role_enum).first()
            if not role:
                role = UserRoleModel(name=role_enum)
                db.add(role)
        
        # Crear usuario administrador si no existe
        admin = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin:
            admin = User(
                email="<EMAIL>",
                full_name="Administrador",
                hashed_password=get_password_hash("password"),
                is_active=True,
                is_superuser=True
            )
            db.add(admin)
            db.commit()
            
            # Asignar rol de administrador
            admin_role = db.query(UserRoleModel).filter(UserRoleModel.name == RoleEnum.ADMINISTRADOR).first()
            admin.roles.append(admin_role)
        
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error al inicializar la base de datos: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    init_db()