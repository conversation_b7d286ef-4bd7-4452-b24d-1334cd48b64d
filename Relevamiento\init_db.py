#!/usr/bin/env python3
# --- Archivo: init_db.py ---
# Script para inicializar la base de datos con Flask-SQLAlchemy

import os
import sys

# Agregar el directorio actual al path para importar la app
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def init_database():
    """Inicializa la base de datos usando Flask-SQLAlchemy."""
    
    try:
        from app import create_app, db
        from app.models import User, Point, Image, Camera, UserCityPermission, UserSourcePermission, UserPermission
        
        print("🚀 Inicializando base de datos con Flask-SQLAlchemy...")
        
        # Crear aplicación Flask
        app = create_app()
        
        with app.app_context():
            print("📋 Creando todas las tablas...")
            
            # Crear todas las tablas
            db.create_all()
            
            print("✅ Tablas creadas exitosamente")
            
            # Verificar si hay usuarios
            user_count = User.query.count()
            print(f"👥 Usuarios existentes: {user_count}")
            
            if user_count == 0:
                print("👤 Creando usuario administrador inicial...")
                
                username = input("Nombre de usuario para admin (default: admin): ").strip()
                if not username:
                    username = "admin"
                
                password = input("Contraseña para admin (default: admin123): ").strip()
                if not password:
                    password = "admin123"
                
                email = input("Email para admin (opcional): ").strip()
                if not email:
                    email = None
                
                # Crear usuario administrador
                admin_user = User(
                    username=username,
                    email=email,
                    role='administrador',
                    is_active=True
                )
                admin_user.set_password(password)
                
                db.session.add(admin_user)
                db.session.commit()
                
                print(f"✅ Usuario administrador '{username}' creado exitosamente")
                print(f"🔑 Credenciales: {username} / {password}")
            
            # Mostrar estadísticas
            print("\n📊 Estadísticas de la base de datos:")
            print(f"  👥 Usuarios: {User.query.count()}")
            print(f"  📍 Puntos: {Point.query.count()}")
            print(f"  📷 Cámaras: {Camera.query.count()}")
            print(f"  🖼️  Imágenes: {Image.query.count()}")
            print(f"  🏙️  Permisos de ciudad: {UserCityPermission.query.count()}")
            print(f"  📋 Permisos de fuente: {UserSourcePermission.query.count()}")
            print(f"  🔐 Permisos específicos: {UserPermission.query.count()}")
            
        return True
        
    except ImportError as e:
        print(f"❌ Error importando módulos: {e}")
        print("💡 Asegúrate de que las dependencias estén instaladas:")
        print("   pip install flask flask-sqlalchemy flask-login flask-migrate")
        return False
    
    except Exception as e:
        print(f"❌ Error inicializando base de datos: {e}")
        return False

def check_dependencies():
    """Verifica que las dependencias necesarias estén instaladas."""
    
    required_modules = [
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'flask_migrate',
        'werkzeug'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ Módulos faltantes:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n💡 Instala las dependencias con:")
        print("   pip install flask flask-sqlalchemy flask-login flask-migrate werkzeug")
        return False
    
    print("✅ Todas las dependencias están instaladas")
    return True

def main():
    """Función principal."""
    print("🔧 Inicializador de Base de Datos - Sistema de Relevamiento")
    print("=" * 60)
    
    # Verificar dependencias
    if not check_dependencies():
        sys.exit(1)
    
    print()
    
    # Verificar si ya existe la base de datos
    if os.path.exists('app.db'):
        print("⚠️  La base de datos 'app.db' ya existe")
        response = input("¿Continuar de todas formas? (s/n): ").strip().lower()
        if response not in ['s', 'si', 'y', 'yes']:
            print("❌ Operación cancelada")
            sys.exit(0)
    
    # Inicializar base de datos
    if init_database():
        print("\n✅ Base de datos inicializada correctamente")
        print("\n🚀 Puedes iniciar la aplicación con:")
        print("   python run.py")
    else:
        print("\n❌ Error inicializando la base de datos")
        sys.exit(1)

if __name__ == "__main__":
    main()
