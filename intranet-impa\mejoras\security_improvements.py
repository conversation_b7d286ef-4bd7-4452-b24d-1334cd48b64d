# security_improvements.py
# Mejoras de seguridad para Intranet IMPA

import secrets
import os
from datetime import timedelta
from functools import wraps
from flask import request, jsonify, current_app
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import magic
import hashlib
from werkzeug.utils import secure_filename

# 1. CONFIGURACIÓN DE SEGURIDAD MEJORADA
class SecurityConfig:
    """Configuración de seguridad mejorada"""
    
    # Generar SECRET_KEY segura
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    
    # Configuración de sesiones seguras
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    SESSION_COOKIE_SECURE = True  # Solo HTTPS en producción
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # Headers de seguridad
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    }
    
    # Límites de archivos
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'.pdf', '.png', '.jpg', '.jpeg', '.gif', '.txt', '.docx'}
    ALLOWED_MIME_TYPES = {
        'image/jpeg', 'image/png', 'image/gif',
        'application/pdf', 'text/plain',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }

# 2. RATE LIMITING
def setup_rate_limiting(app):
    """Configurar rate limiting"""
    limiter = Limiter(
        app,
        key_func=get_remote_address,
        default_limits=["200 per day", "50 per hour"]
    )
    
    # Rate limiting específico para login
    @limiter.limit("5 per minute")
    def login_rate_limit():
        pass
    
    return limiter

# 3. VALIDACIÓN SEGURA DE ARCHIVOS
class SecureFileValidator:
    """Validador seguro de archivos"""
    
    @staticmethod
    def validate_file(file):
        """Validar archivo de forma segura"""
        if not file or not file.filename:
            return False, "No se seleccionó archivo"
        
        # Verificar extensión
        filename = secure_filename(file.filename)
        if not any(filename.lower().endswith(ext) for ext in SecurityConfig.ALLOWED_EXTENSIONS):
            return False, "Tipo de archivo no permitido"
        
        # Verificar tipo MIME real
        file_content = file.read()
        file.seek(0)  # Resetear puntero
        
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            if mime_type not in SecurityConfig.ALLOWED_MIME_TYPES:
                return False, f"Tipo MIME no permitido: {mime_type}"
        except:
            return False, "No se pudo verificar el tipo de archivo"
        
        # Verificar tamaño
        if len(file_content) > SecurityConfig.MAX_CONTENT_LENGTH:
            return False, "Archivo demasiado grande"
        
        # Verificar que no sea ejecutable
        if file_content.startswith(b'\x4d\x5a'):  # PE executable
            return False, "Archivos ejecutables no permitidos"
        
        return True, "Archivo válido"
    
    @staticmethod
    def generate_safe_filename(original_filename):
        """Generar nombre de archivo seguro"""
        filename = secure_filename(original_filename)
        name, ext = os.path.splitext(filename)
        
        # Añadir hash para evitar colisiones
        hash_suffix = hashlib.md5(f"{name}{secrets.token_hex(8)}".encode()).hexdigest()[:8]
        
        return f"{name}_{hash_suffix}{ext}"

# 4. MIDDLEWARE DE SEGURIDAD
def setup_security_middleware(app):
    """Configurar middleware de seguridad"""
    
    @app.before_request
    def security_headers():
        """Añadir headers de seguridad a todas las respuestas"""
        pass
    
    @app.after_request
    def apply_security_headers(response):
        """Aplicar headers de seguridad"""
        for header, value in SecurityConfig.SECURITY_HEADERS.items():
            response.headers[header] = value
        return response
    
    @app.before_request
    def check_request_security():
        """Verificaciones de seguridad en cada request"""
        # Verificar User-Agent sospechoso
        user_agent = request.headers.get('User-Agent', '')
        suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan']
        
        if any(agent in user_agent.lower() for agent in suspicious_agents):
            current_app.logger.warning(f"Suspicious User-Agent detected: {user_agent} from {request.remote_addr}")
            return jsonify({'error': 'Access denied'}), 403
        
        # Verificar tamaño de request
        if request.content_length and request.content_length > SecurityConfig.MAX_CONTENT_LENGTH:
            return jsonify({'error': 'Request too large'}), 413

# 5. DECORADOR PARA VALIDACIÓN DE ROLES MEJORADA
def enhanced_role_required(*allowed_roles):
    """Decorador mejorado para validación de roles"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from flask_login import current_user
            
            if not current_user.is_authenticated:
                return jsonify({'error': 'Authentication required'}), 401
            
            if not current_user.is_active:
                return jsonify({'error': 'Account disabled'}), 403
            
            if current_user.role not in allowed_roles:
                current_app.logger.warning(
                    f"Unauthorized access attempt by user {current_user.username} "
                    f"with role {current_user.role} to {request.endpoint}"
                )
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# 6. LOGGER DE SEGURIDAD
class SecurityLogger:
    """Logger especializado para eventos de seguridad"""
    
    @staticmethod
    def log_login_attempt(username, success, ip_address):
        """Registrar intento de login"""
        status = "SUCCESS" if success else "FAILED"
        current_app.logger.info(f"LOGIN {status}: {username} from {ip_address}")
    
    @staticmethod
    def log_file_upload(username, filename, success, reason=""):
        """Registrar subida de archivo"""
        status = "SUCCESS" if success else "FAILED"
        message = f"FILE_UPLOAD {status}: {username} uploaded {filename}"
        if reason:
            message += f" - {reason}"
        current_app.logger.info(message)
    
    @staticmethod
    def log_permission_violation(username, endpoint, role):
        """Registrar violación de permisos"""
        current_app.logger.warning(
            f"PERMISSION_VIOLATION: {username} (role: {role}) "
            f"attempted to access {endpoint}"
        )

# 7. FUNCIÓN DE INICIALIZACIÓN
def initialize_security(app):
    """Inicializar todas las mejoras de seguridad"""
    
    # Aplicar configuración de seguridad
    app.config.update(SecurityConfig.__dict__)
    
    # Configurar middleware
    setup_security_middleware(app)
    
    # Configurar rate limiting
    limiter = setup_rate_limiting(app)
    
    # Configurar logging de seguridad
    import logging
    from logging.handlers import RotatingFileHandler
    
    if not app.debug:
        # Log de seguridad separado
        security_handler = RotatingFileHandler(
            'logs/security.log', maxBytes=10240000, backupCount=10
        )
        security_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [%(pathname)s:%(lineno)d]'
        ))
        security_handler.setLevel(logging.INFO)
        app.logger.addHandler(security_handler)
    
    return limiter

# 8. EJEMPLO DE USO EN ROUTES
"""
# En routes.py, usar así:

from mejoras.security_improvements import (
    enhanced_role_required, 
    SecureFileValidator, 
    SecurityLogger
)

@routes_bp.route('/upload_document', methods=['POST'])
@login_required
@enhanced_role_required('administrador', 'secretaria')
def upload_document():
    file = request.files.get('file')
    
    # Validar archivo de forma segura
    is_valid, message = SecureFileValidator.validate_file(file)
    if not is_valid:
        SecurityLogger.log_file_upload(
            current_user.username, 
            file.filename if file else "None", 
            False, 
            message
        )
        flash(message, 'danger')
        return redirect(request.url)
    
    # Generar nombre seguro
    safe_filename = SecureFileValidator.generate_safe_filename(file.filename)
    
    # Procesar archivo...
    SecurityLogger.log_file_upload(current_user.username, safe_filename, True)
    
    return redirect(url_for('routes.documents'))
"""
