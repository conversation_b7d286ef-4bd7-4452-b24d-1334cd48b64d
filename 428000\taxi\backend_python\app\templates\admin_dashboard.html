{% extends "base_layout_admin.html" %}

{% block title %}Panel de Administración - Taxis{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Panel de Administración</h1>
        <p class="lead">Bienvenido al panel de administración del sistema de taxis.</p>
    </div>
</div>

<!-- Estadísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-people card-icon"></i>
                <div class="stats-value">{{ total_users }}</div>
                <div class="stats-label text-white-50">Usuarios Totales</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-taxi-front card-icon"></i>
                <div class="stats-value">{{ total_taxis }}</div>
                <div class="stats-label text-white-50">Vehículos Registrados</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-geo-alt card-icon"></i>
                <div class="stats-value">{{ total_trips }}</div>
                <div class="stats-label text-white-50">Viajes Totales</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-cash-coin card-icon"></i>
                <div class="stats-value">${{ total_revenue|default('0') }}</div>
                <div class="stats-label text-white-50">Ingresos Totales</div>
            </div>
        </div>
    </div>
</div>

<!-- Mapa y Viajes Activos -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mapa de Vehículos Activos</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Viajes Activos</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Estado</th>
                                <th>Conductor</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for trip in active_trips|default([]) %}
                            <tr>
                                <td>{{ trip.id }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ trip.status }}</span>
                                </td>
                                <td>{{ trip.driver_name if trip.driver_name else 'Sin asignar' }}</td>
                                <td>
                                    <a href="{{ url_for('trip_details_route', trip_id=trip.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">No hay viajes activos</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Acciones Rápidas y Mi Información -->
<div class="row">
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Acciones Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('users_management_route') }}" class="btn btn-lg btn-outline-primary">
                        <i class="bi bi-people me-2"></i> Gestionar Usuarios
                    </a>
                    <a href="{{ url_for('vehicles_management_route') }}" class="btn btn-lg btn-outline-success">
                        <i class="bi bi-taxi-front me-2"></i> Gestionar Vehículos
                    </a>
                    <a href="{{ url_for('trips_management_route') }}" class="btn btn-lg btn-outline-info">
                        <i class="bi bi-geo-alt me-2"></i> Gestionar Viajes
                    </a>
                    <a href="{{ url_for('fare_settings_route') }}" class="btn btn-lg btn-outline-warning">
                        <i class="bi bi-cash-coin me-2"></i> Configurar Tarifas
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mi Información (API)</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary mb-3" onclick="fetchMyInfo()">
                    <i class="bi bi-person-circle"></i> Cargar mi información
                </button>
                <pre id="my-info-result" class="bg-light p-3 rounded">Los datos se mostrarán aquí...</pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 12); // Buenos Aires como ejemplo

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Función para cargar vehículos activos
    async function loadActiveVehicles() {
        try {
            const response = await fetchWithAuth('/api/v1/vehicles/active');
            if (response.ok) {
                const vehicles = await response.json();

                // Limpiar marcadores existentes
                map.eachLayer(layer => {
                    if (layer instanceof L.Marker) {
                        map.removeLayer(layer);
                    }
                });

                // Añadir marcadores para cada vehículo
                vehicles.forEach(vehicle => {
                    if (vehicle.last_latitude && vehicle.last_longitude) {
                        const marker = L.marker([vehicle.last_latitude, vehicle.last_longitude])
                            .addTo(map)
                            .bindPopup(`
                                <strong>${vehicle.plate_number}</strong><br>
                                Conductor: ${vehicle.driver_name || 'Sin asignar'}<br>
                                Estado: ${vehicle.status}
                            `);
                    }
                });
            }
        } catch (error) {
            console.error('Error al cargar vehículos:', error);
        }
    }

    // Cargar vehículos al iniciar
    loadActiveVehicles();

    // Actualizar cada 30 segundos
    setInterval(loadActiveVehicles, 30000);

    // Función para cargar información del usuario
    async function fetchMyInfo() {
        try {
            document.getElementById('my-info-result').textContent = 'Cargando...';
            const response = await fetchWithAuth("{{ url_for('read_users_me_api') }}");
            if (!response.ok) {
                const errorData = await response.json();
                document.getElementById('my-info-result').textContent = 'Error: ' + (errorData.detail || response.statusText);
                return;
            }
            const data = await response.json();
            document.getElementById('my-info-result').textContent = JSON.stringify(data, null, 2);
        } catch (error) {
            document.getElementById('my-info-result').textContent = 'Error de red: ' + error.message;
        }
    }
</script>
{% endblock %}
