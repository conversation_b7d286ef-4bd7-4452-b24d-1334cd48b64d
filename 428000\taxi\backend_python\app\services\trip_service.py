from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import logging
import math

from app.models.trip import Trip, TripStatusEnum
from app.models.user import User
from app.models.vehicle import Vehicle, VehicleStatus
from app.schemas.trip_schema import TripCreate, TripUpdate
from app.services import vehicle_service, route_service

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_trip(db: Session, trip_in: TripCreate, passenger_id: int) -> Trip:
    """
    Crea un nuevo viaje solicitado por un pasajero.
    Puede ser un viaje inmediato o programado para el futuro.
    También puede incluir paradas intermedias.
    """
    # Determinar si es un viaje programado
    is_scheduled = trip_in.scheduled_for is not None

    # Crear el objeto Trip
    db_trip = Trip(
        passenger_id=passenger_id,
        origin_latitude=trip_in.origin_latitude,
        origin_longitude=trip_in.origin_longitude,
        origin_address=trip_in.origin_address,
        destination_latitude=trip_in.destination_latitude,
        destination_longitude=trip_in.destination_longitude,
        destination_address=trip_in.destination_address,
        status=TripStatusEnum.PROGRAMADO if is_scheduled else TripStatusEnum.SOLICITADO,
        requested_at=datetime.utcnow(),
        scheduled_for=trip_in.scheduled_for,
        has_stops=trip_in.stops is not None and len(trip_in.stops) > 0
    )

    # Calcular ruta, distancia y duración usando el servicio de rutas
    try:
        route_data = route_service.calculate_route(
            origin_lat=db_trip.origin_latitude,
            origin_lng=db_trip.origin_longitude,
            destination_lat=db_trip.destination_latitude,
            destination_lng=db_trip.destination_longitude
        )

        # Extraer información de la ruta
        db_trip.estimated_distance_meters = int(route_data.get('distance_meters', 5000))
        db_trip.estimated_duration_seconds = int(route_data.get('duration_seconds', 900))

        # Calcular tarifa estimada (base + distancia + tiempo)
        # En una implementación real, esto vendría de una tabla de tarifas
        base_fare = 300  # Tarifa base en pesos
        per_km_fare = 50  # Tarifa por km en pesos
        per_minute_fare = 5  # Tarifa por minuto en pesos

        distance_km = db_trip.estimated_distance_meters / 1000
        duration_minutes = db_trip.estimated_duration_seconds / 60

        fare = base_fare + (distance_km * per_km_fare) + (duration_minutes * per_minute_fare)
        db_trip.estimated_fare = round(fare, 2)

        # Guardar la geometría de la ruta (en una implementación real)
        # db_trip.route_polyline = json.dumps(route_data.get('geometry', []))

        logger.info(f"Ruta calculada para viaje: {db_trip.estimated_distance_meters}m, {db_trip.estimated_duration_seconds}s, ${db_trip.estimated_fare}")
    except Exception as e:
        logger.error(f"Error al calcular ruta: {e}")
        # Valores predeterminados en caso de error
        db_trip.estimated_fare = 10.0  # Valor de ejemplo
        db_trip.estimated_distance_meters = 5000  # 5 km de ejemplo
        db_trip.estimated_duration_seconds = 900  # 15 minutos de ejemplo

    # Guardar en la base de datos
    db.add(db_trip)
    db.commit()
    db.refresh(db_trip)

    # Si hay paradas intermedias, crearlas
    if trip_in.stops and len(trip_in.stops) > 0:
        from app.models.trip_stop import TripStop, TripStopStatusEnum

        # Crear las paradas en el orden especificado
        for i, stop_data in enumerate(trip_in.stops):
            db_stop = TripStop(
                trip_id=db_trip.id,
                order=stop_data.order if stop_data.order else i + 1,
                latitude=stop_data.latitude,
                longitude=stop_data.longitude,
                address=stop_data.address,
                status=TripStopStatusEnum.PENDIENTE,
                wait_time_seconds=stop_data.wait_time_seconds if stop_data.wait_time_seconds else 60,
                notes=stop_data.notes
            )
            db.add(db_stop)

        db.commit()
        db.refresh(db_trip)

    # Aquí se podría implementar la lógica para buscar conductores cercanos
    # y notificarles de la solicitud de viaje

    return db_trip

def get_trip(db: Session, trip_id: int) -> Optional[Trip]:
    """
    Obtiene un viaje por su ID.
    """
    return db.query(Trip).filter(Trip.id == trip_id).first()

def get_trips(db: Session, skip: int = 0, limit: int = 100) -> List[Trip]:
    """
    Obtiene una lista de viajes, ordenados por fecha de solicitud (más recientes primero).
    """
    return db.query(Trip).order_by(desc(Trip.requested_at)).offset(skip).limit(limit).all()

def get_trips_by_passenger(db: Session, passenger_id: int, skip: int = 0, limit: int = 100) -> List[Trip]:
    """
    Obtiene los viajes de un pasajero específico.
    """
    return db.query(Trip).filter(Trip.passenger_id == passenger_id).order_by(desc(Trip.requested_at)).offset(skip).limit(limit).all()

def get_trips_by_driver(db: Session, driver_id: int, skip: int = 0, limit: int = 100) -> List[Trip]:
    """
    Obtiene los viajes de un conductor específico.
    """
    return db.query(Trip).filter(Trip.driver_id == driver_id).order_by(desc(Trip.requested_at)).offset(skip).limit(limit).all()

def get_active_trips(db: Session, skip: int = 0, limit: int = 100) -> List[Trip]:
    """
    Obtiene los viajes activos (solicitados, aceptados, en camino o en viaje).
    """
    active_statuses = [
        TripStatusEnum.SOLICITADO,
        TripStatusEnum.ACEPTADO,
        TripStatusEnum.EN_CAMINO_PASAJERO,
        TripStatusEnum.EN_DESTINO_PASAJERO,
        TripStatusEnum.EN_VIAJE
    ]
    return db.query(Trip).filter(Trip.status.in_(active_statuses)).order_by(desc(Trip.requested_at)).offset(skip).limit(limit).all()

def update_trip(db: Session, trip_id: int, trip_update: TripUpdate) -> Optional[Trip]:
    """
    Actualiza un viaje existente.
    """
    db_trip = get_trip(db, trip_id)
    if not db_trip:
        return None

    # Actualizar los campos proporcionados
    # Convertir el modelo Pydantic a diccionario, excluyendo valores None
    update_data = {k: v for k, v in trip_update.__dict__.items() if v is not None}

    # Manejar cambios de estado
    if "status" in update_data:
        new_status = update_data["status"]

        # Actualizar timestamps según el cambio de estado
        if new_status == TripStatusEnum.ACEPTADO and db_trip.status == TripStatusEnum.SOLICITADO:
            update_data["accepted_at"] = datetime.utcnow()

            # Si se asigna un conductor, actualizar su estado a OCUPADO
            if "driver_id" in update_data and update_data["driver_id"]:
                driver_id = update_data["driver_id"]
                vehicle_id = update_data.get("vehicle_id")

                if vehicle_id:
                    vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
                    if vehicle:
                        vehicle.status = VehicleStatus.OCUPADO
                        vehicle.driver_id = driver_id

        elif new_status == TripStatusEnum.EN_VIAJE:
            update_data["started_at"] = datetime.utcnow()

        elif new_status == TripStatusEnum.COMPLETADO:
            update_data["completed_at"] = datetime.utcnow()

            # Liberar al conductor y su vehículo
            if db_trip.driver_id and db_trip.vehicle_id:
                vehicle = db.query(Vehicle).filter(Vehicle.id == db_trip.vehicle_id).first()
                if vehicle:
                    vehicle.status = VehicleStatus.LIBRE

        elif new_status in [TripStatusEnum.CANCELADO_PASAJERO, TripStatusEnum.CANCELADO_CONDUCTOR]:
            update_data["cancelled_at"] = datetime.utcnow()

            # Liberar al conductor y su vehículo si ya estaban asignados
            if db_trip.driver_id and db_trip.vehicle_id:
                vehicle = db.query(Vehicle).filter(Vehicle.id == db_trip.vehicle_id).first()
                if vehicle:
                    vehicle.status = VehicleStatus.LIBRE

    # Aplicar las actualizaciones
    for key, value in update_data.items():
        setattr(db_trip, key, value)

    db.commit()
    db.refresh(db_trip)
    return db_trip

def rate_trip(db: Session, trip_id: int, is_passenger_rating: bool, rating: int, comment: Optional[str] = None) -> Optional[Trip]:
    """
    Califica un viaje, ya sea por el pasajero o por el conductor.
    """
    db_trip = get_trip(db, trip_id)
    if not db_trip:
        return None

    # Verificar que el viaje esté completado
    if db_trip.status != TripStatusEnum.COMPLETADO:
        return None

    # Actualizar la calificación
    if is_passenger_rating:
        db_trip.passenger_rating_for_driver = rating
        db_trip.passenger_comment_for_driver = comment
    else:
        db_trip.driver_rating_for_passenger = rating
        db_trip.driver_comment_for_passenger = comment

    db.commit()
    db.refresh(db_trip)
    return db_trip

def find_nearby_drivers(db: Session, latitude: str, longitude: str, radius_meters: int = 1000) -> List[User]:
    """
    Encuentra conductores cercanos a una ubicación.
    En una implementación real, esto usaría consultas geoespaciales con PostGIS.

    Args:
        db: Sesión de base de datos
        latitude: Latitud en formato string
        longitude: Longitud en formato string
        radius_meters: Radio de búsqueda en metros

    Returns:
        List[User]: Lista de conductores cercanos
    """
    # Implementación simplificada: devuelve todos los conductores disponibles
    # En una implementación real, se filtrarían por distancia usando PostGIS
    from app.models.user import User
    from sqlalchemy import text

    # Obtener conductores con vehículos disponibles
    sql_query = text("""
        SELECT u.id, u.email, u.full_name, v.id as vehicle_id, v.last_latitude, v.last_longitude
        FROM users u
        JOIN user_roles_association ura ON u.id = ura.user_id
        JOIN roles r ON ura.role_id = r.id
        JOIN vehicles v ON v.driver_id = u.id
        WHERE LOWER(r.name::text) = 'taxi'
        AND v.status = 'libre'
        AND u.is_active = true
        AND v.last_latitude IS NOT NULL
        AND v.last_longitude IS NOT NULL
    """)

    result = db.execute(sql_query).fetchall()

    # Convertir resultados a objetos User
    drivers = []
    for row in result:
        # En una implementación real, aquí calcularíamos la distancia
        # y filtraríamos por radio
        driver = db.query(User).filter(User.id == row.id).first()
        if driver:
            drivers.append(driver)

    logger.info(f"Se encontraron {len(drivers)} conductores cercanos a {latitude}, {longitude}")
    return drivers

def calculate_distance(lat1: str, lon1: str, lat2: str, lon2: str) -> float:
    """
    Calcula la distancia en metros entre dos puntos geográficos.
    Utiliza la fórmula de Haversine para calcular la distancia sobre la superficie terrestre.

    Args:
        lat1: Latitud del punto 1 en formato string
        lon1: Longitud del punto 1 en formato string
        lat2: Latitud del punto 2 en formato string
        lon2: Longitud del punto 2 en formato string

    Returns:
        float: Distancia en metros
    """
    try:
        # Convertir coordenadas a float
        lat1, lon1 = float(lat1), float(lon1)
        lat2, lon2 = float(lat2), float(lon2)

        # Radio de la Tierra en metros
        R = 6371000

        # Convertir latitudes y longitudes de grados a radianes
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # Diferencias de latitud y longitud
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad

        # Fórmula de Haversine
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c

        return distance
    except Exception as e:
        logger.error(f"Error al calcular distancia: {e}")
        # En caso de error, devolver una distancia muy grande
        return float('inf')

def find_nearby_drivers(db: Session, latitude: str, longitude: str, radius_meters: int = 1000) -> List[User]:
    """
    Encuentra conductores cercanos a una ubicación.
    En una implementación real, esto usaría consultas geoespaciales con PostGIS.
    """
    # Implementación simplificada: devuelve todos los conductores disponibles
    # En una implementación real, se filtrarían por distancia usando PostGIS
    from app.models.user import User
    from sqlalchemy import text

    # Obtener conductores con vehículos disponibles
    sql_query = text("""
        SELECT u.id, u.full_name, u.email, v.id as vehicle_id
        FROM users u
        JOIN user_roles_association ura ON u.id = ura.user_id
        JOIN roles r ON ura.role_id = r.id
        JOIN vehicles v ON v.driver_id = u.id
        WHERE LOWER(r.name::text) = 'taxi'
        AND v.status = 'libre'
        AND u.is_active = true
    """)

    result = db.execute(sql_query).fetchall()

    # Convertir el resultado a una lista de usuarios
    drivers = []
    for row in result:
        driver = db.query(User).filter(User.id == row.id).first()
        if driver:
            drivers.append(driver)

    return drivers
