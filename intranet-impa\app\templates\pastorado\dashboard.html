<!-- /app/templates/pastorado/dashboard.html -->
{% extends "base.html" %}
{% block title %}Escritorio del Pastorado{% endblock %}

{% block content %}
<div class="container my-4">
    <h1><i class="fas fa-user-tie mr-2"></i>Escritorio del Pastorado</h1> {# Icono Pastor #}
    {% if view_user.role == 'pastorado' and view_user.pastor %}
        {% set grado = view_user.pastor.grado or "Sin grado" %}
        <p><PERSON><PERSON><PERSON><PERSON>, Pastor {{ grado }} {{ view_user.first_name }} {{ view_user.last_name }}!</p>
    {% else %}
        <p>Bienvenido, {{ view_user.first_name }} {{ view_user.last_name }}!</p> {# Fallback #}
    {% endif %}

    <!-- ############################################## -->
    <!-- ## SECCIÓN DE WIDGETS (Pastor)                ## -->
    <!-- ############################################## -->
    <div class="row mb-4">
        <!-- Widget Miembros Iglesia -->
        <div class="col-md-3">
            <div class="card text-white bg-primary mb-3 shadow">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-users mr-2"></i>Miembros Iglesia</h5>
                    <p class="card-text display-4">{{ members_count if members_count is defined else 'N/A' }}</p>
                </div>
                 <a href="{{ url_for('routes.pastor_members') }}" class="card-footer text-white">Ver listado <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Saldo Iglesia -->
        <div class="col-md-3">
            <div class="card text-white bg-success mb-3 shadow">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-hand-holding-usd mr-2"></i>Saldo Iglesia</h5>
                    {# ***** CORRECCIÓN AQUÍ ***** #}
                    <p class="card-text display-4">
                        {% if church_balance is not none %}
                            ${{ "%.2f"|format(church_balance) }}
                        {% else %}
                            N/A
                        {% endif %}
                    </p>
                    {# ***** FIN CORRECCIÓN ***** #}
                    <small>Cuenta Principal</small>
                </div>
                 {% if current_user.church_id %}
                    <a href="{{ url_for('routes.pastor_economia') }}" class="card-footer text-white">Ver detalles <i class="fas fa-arrow-circle-right"></i></a>
                 {% else %}
                    <span class="card-footer text-white bg-secondary">Sin iglesia asignada</span>
                 {% endif %}
            </div>
        </div>
        <!-- Widget Mensajes Nuevos (Común) -->
        <div class="col-md-3">
            <div class="card text-white bg-info mb-3 shadow">
                 <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-envelope-open-text mr-2"></i>Mensajes Nuevos</h5>
                    <p class="card-text display-4">{{ unread_count if unread_count is defined else 'N/A' }}</p>
                    <small>Sin leer</small>
                </div>
                <a href="{{ url_for('routes.inbox') }}" class="card-footer text-white">Ir al Buzón <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Cumpleaños (Común) -->
        <div class="col-md-3">
            <div class="card bg-light mb-3 shadow">
                <div class="card-header"><i class="fas fa-birthday-cake mr-2"></i>Próximos Cumpleaños (Iglesia)</div>
                <div class="card-body" style="max-height: 150px; overflow-y: auto;">
                    {% if upcoming_birthdays %}
                        <ul class="list-unstyled mb-0">
                            {% for user_bday in upcoming_birthdays %}
                                <li class="mb-1">
                                     {# El enlace user_detail es seguro, redirige si no hay permiso #}
                                    <a href="{{ url_for('routes.user_detail', user_id=user_bday.id) }}">{{ user_bday.full_name }}</a>
                                    <span class="badge badge-pill badge-secondary float-right">{{ user_bday.date_of_birth.strftime('%d/%m') }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted small">No hay cumpleaños próximos en tu iglesia.</p>
                    {% endif %}
                </div>
                 <a href="{{ url_for('routes.calendar_view') }}" class="card-footer text-muted">Ver Calendario Completo <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>
    <!-- ############################################## -->
    <!-- ## FIN SECCIÓN DE WIDGETS                     ## -->
    <!-- ############################################## -->

    <hr>

    <!-- ############################################## -->
    <!-- ## SECCIÓN ACCIONES RÁPIDAS Y ANUNCIOS      ## -->
    <!-- ############################################## -->
    <div class="row mb-4">
        <!-- Columna Acciones Rápidas (Pastor) -->
        <div class="col-lg-4">
             <div class="card shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <i class="fas fa-bolt mr-2"></i>Acciones Rápidas
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('routes.register_member') }}" class="list-group-item list-group-item-action"><i class="fas fa-user-plus fa-fw mr-2 text-success"></i>Registrar Miembro</a>
                    <a href="{{ url_for('routes.pastor_members') }}" class="list-group-item list-group-item-action"><i class="fas fa-list-ul fa-fw mr-2 text-primary"></i>Listar Miembros</a>
                     {% if current_user.church_id %}
                        <a href="{{ url_for('routes.inventory', church_id=current_user.church_id) }}" class="list-group-item list-group-item-action"><i class="fas fa-boxes fa-fw mr-2 text-info"></i>Inventario Iglesia</a>
                        <a href="{{ url_for('routes.pastor_economia') }}" class="list-group-item list-group-item-action"><i class="fas fa-dollar-sign fa-fw mr-2 text-success"></i>Economía Iglesia</a>
                     {% endif %}
                    <a href="{{ url_for('routes.upload_document') }}" class="list-group-item list-group-item-action"><i class="fas fa-file-upload fa-fw mr-2 text-secondary"></i>Subir Documento</a>
                    <a href="{{ url_for('routes.manage_announcement') }}" class="list-group-item list-group-item-action"><i class="fas fa-bullhorn fa-fw mr-2 text-warning"></i>Crear Anuncio</a>
                    <a href="{{ url_for('routes.choose_review_user') }}" class="list-group-item list-group-item-action"><i class="fas fa-history fa-fw mr-2 text-danger"></i>Agregar Histórico (Miembro)</a>
                    <a href="{{ url_for('routes.send_message') }}" class="list-group-item list-group-item-action"><i class="fas fa-paper-plane fa-fw mr-2 text-info"></i>Enviar Mensaje</a>
                </div>
            </div>
        </div>

        <!-- Columna Anuncios Recientes -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                 <div class="card-header bg-light">
                    <i class="fas fa-bullhorn mr-2"></i>Anuncios Recientes
                 </div>
                 <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% if announcements %}
                        {% for announcement in announcements %}
                            <div class="alert alert-{{ 'secondary' if loop.index % 2 == 0 else 'light' }} border mb-2" role="alert">
                                <h5 class="alert-heading">{{ announcement.title }}</h5>
                                <p class="mb-1 small">{{ announcement.content | nl2br | safe }}</p>
                                <hr class="my-1">
                                <p class="mb-0 small text-muted d-flex justify-content-between">
                                    <span>
                                        <i class="fas fa-user fa-fw"></i> {{ announcement.author.full_name if announcement.author else 'Sistema' }}
                                        <i class="far fa-clock fa-fw ml-2"></i> {{ announcement.created_at | to_local }}
                                    </span>
                                     {# El pastor puede editar sus propios anuncios #}
                                    {% if announcement.author_id == current_user.id or current_user.role in ['administrador', 'secretaria'] %}
                                     <span>
                                         <a href="{{ url_for('routes.manage_announcement', announcement_id=announcement.id) }}" class="badge badge-primary"><i class="fas fa-edit"></i> Editar</a>
                                     </span>
                                    {% endif %}
                                </p>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No hay anuncios para mostrar.</p>
                    {% endif %}
                 </div>
             </div>
        </div>
    </div>
    <!-- ############################################## -->
    <!-- ## FIN SECCIÓN ACCIONES/ANUNCIOS            ## -->
    <!-- ############################################## -->

    <hr>
    <h2>Otras Secciones</h2>
    <!-- Mantener las cards originales si aportan valor adicional -->
     <div class="row mt-4">
          <!-- Ya cubierto en Acciones Rápidas <div class="col-md-3">...Miembros...</div> -->
          <!-- Ya cubierto en Acciones Rápidas <div class="col-md-3">...Inventario...</div> -->
          <!-- Ya cubierto en Acciones Rápidas <div class="col-md-3">...Economía...</div> -->
          <div class="col-md-3">
            <div class="card mb-3">
              <div class="card-header bg-light">Mapa</div>
              <div class="card-body">
                <a href="{{ url_for('routes.map_view') }}" class="btn btn-outline-dark btn-block">Ver Mapa</a>
              </div>
            </div>
          </div>
          <!-- Ya cubierto en Acciones Rápidas <div class="col-md-3">...Correo...</div> -->
          <div class="col-md-3">
            <div class="card mb-3">
              <div class="card-header bg-light">Documentación</div>
              <div class="card-body">
                <a href="{{ url_for('routes.list_documents') }}" class="btn btn-outline-primary btn-block">Ver Documentación</a>
                <a href="{{ url_for('routes.diploma_preview') }}" class="btn btn-outline-secondary btn-block">Diplomas</a>
                <a href="{{ url_for('routes.acta_preview') }}" class="btn btn-outline-secondary btn-block">Actas</a>
              </div>
            </div>
          </div>
           <!-- Ya cubierto en Acciones Rápidas <div class="col-md-3">...Histórico...</div> -->
          <div class="col-md-3">
            <div class="card mb-3">
              <div class="card-header bg-light">Calendario</div>
              <div class="card-body">
                <a href="{{ url_for('routes.calendar_view') }}" class="btn btn-outline-dark btn-block">Ver Calendario</a>
              </div>
            </div>
          </div>
     </div>

</div> {# Fin container #}
{% endblock %}