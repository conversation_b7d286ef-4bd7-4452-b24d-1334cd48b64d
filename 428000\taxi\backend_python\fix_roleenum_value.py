#!/usr/bin/env python3
"""
Script para corregir el valor del enum RoleEnum.ADMINISTRADOR.
"""

import os
import re
from pathlib import Path

def fix_roleenum():
    print("Corrigiendo el valor del enum RoleEnum.ADMINISTRADOR...")
    
    # Ruta al archivo
    file_path = Path("app/models/user.py")

    if not file_path.exists():
        print(f"Error: El archivo {file_path} no existe.")
        return

    # Leer el archivo
    with open(file_path, "r", encoding="utf-8") as file:
        content = file.read()
    
    # Buscar y reemplazar el valor del enum
    pattern = r"(ADMINISTRADOR\s*=\s*[\"'])administrad([\"'])"
    replacement = r"\1administrador\2"
    
    # Verificar si se encuentra el patrón
    if re.search(pattern, content):
        # Reemplazar el valor
        new_content = re.sub(pattern, replacement, content)
        
        # Guardar el archivo
        with open(file_path, "w", encoding="utf-8") as file:
            file.write(new_content)
        
        print(f"Archivo {file_path} modificado correctamente.")
        print("Se cambió 'administrad' a 'administrador' en RoleEnum.ADMINISTRADOR.")
    else:
        print(f"No se encontró el patrón en el archivo {file_path}.")
        print("Verificando el contenido actual...")
        
        # Mostrar las líneas relevantes
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "ADMINISTRADOR" in line:
                print(f"Línea {i+1}: {line}")
        
        # Intentar una modificación manual
        manual_pattern = r"(ADMINISTRADOR\s*=\s*[\"'])(.*?)([\"'])"
        matches = re.findall(manual_pattern, content)
        if matches:
            for match in matches:
                print(f"Encontrado: {match[0]}{match[1]}{match[2]}")
                print(f"Se cambiará a: {match[0]}administrador{match[2]}")
            
            # Reemplazar manualmente
            new_content = re.sub(manual_pattern, r"\1administrador\3", content)
            
            # Guardar el archivo
            with open(file_path, "w", encoding="utf-8") as file:
                file.write(new_content)
            
            print(f"Archivo {file_path} modificado manualmente.")
        else:
            print("No se pudo encontrar el patrón para modificar manualmente.")
            print("Por favor, edita el archivo manualmente.")

if __name__ == "__main__":
    fix_roleenum()