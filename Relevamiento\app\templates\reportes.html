<!-- app/templates/reportes.html -->
{% extends "base.html" %}
{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-bar-chart-line"></i> Reportes
            </h1>
        </div>
    </div>

    <!-- Formulario de Filtros -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-funnel"></i> Filtros de Búsqueda
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('main.reportes') }}">
                        {{ form.hidden_tag() }}

                        <div class="row">
                            <!-- Primera fila de filtros -->
                            <div class="col-md-3 mb-3">
                                {{ form.point_id.label(class="form-label") }}
                                {{ form.point_id(class="form-control") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.city.label(class="form-label") }}
                                {{ form.city(class="form-select") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.source.label(class="form-label") }}
                                {{ form.source(class="form-select") }}
                            </div>
                        </div>

                        <div class="row">
                            <!-- Segunda fila de filtros -->
                            <div class="col-md-3 mb-3">
                                {{ form.camera_type.label(class="form-label") }}
                                {{ form.camera_type(class="form-select") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.has_images.label(class="form-label") }}
                                {{ form.has_images(class="form-select") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.has_cameras.label(class="form-label") }}
                                {{ form.has_cameras(class="form-select") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                <!-- Espacio para alineación -->
                            </div>
                        </div>

                        <div class="row">
                            <!-- Tercera fila - Fechas de creación -->
                            <div class="col-md-3 mb-3">
                                {{ form.date_from.label(class="form-label") }}
                                {{ form.date_from(class="form-control") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.date_to.label(class="form-label") }}
                                {{ form.date_to(class="form-control") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.modified_from.label(class="form-label") }}
                                {{ form.modified_from(class="form-control") }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.modified_to.label(class="form-label") }}
                                {{ form.modified_to(class="form-control") }}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    {{ form.submit(class="btn btn-primary") }}
                                    {{ form.clear(class="btn btn-secondary") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Mapa de Resultados -->
    {% if total_points is defined and total_points > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-map"></i> Mapa de Resultados
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="reportes-map"
                         data-points='{{ points_data|tojson }}'
                         style="height: 400px; width: 100%;">
                        <div class="text-center p-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando mapa...</span>
                            </div>
                            <p class="mt-2">Cargando mapa...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leyenda de Estados -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">Leyenda de Estados:</h6>
                    <span class="badge bg-primary me-2"><span class="status-indicator status-azul me-1"></span> Por Hacer</span>
                    <span class="badge bg-warning text-dark me-2"><span class="status-indicator status-amarillo me-1"></span> En Curso</span>
                    <span class="badge bg-success me-2"><span class="status-indicator status-verde me-1"></span> Hecho</span>
                    <span class="badge bg-danger me-2"><span class="status-indicator status-rojo me-1"></span> Incompleto</span>
                    <span class="badge bg-secondary me-2"><span class="status-indicator status-grey me-1"></span> Otro</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de Resultados -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul"></i> Tabla de Resultados
                    </h5>
                    <span class="badge bg-primary">{{ total_points }} punto(s) encontrado(s)</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Nombre</th>
                                    <th>Ciudad</th>
                                    <th>Estado</th>
                                    <th>Origen</th>
                                    <th>Imágenes</th>
                                    <th>Cámaras</th>
                                    <th>Creado</th>
                                    <th>Modificado</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for point in points_data %}
                                <tr>
                                    <td>
                                        <a href="https://patagoniaservers.com.ar:5006/points/{{ point.id }}"
                                           target="_blank" class="text-decoration-none">
                                            <strong>{{ point.id }}</strong>
                                        </a>
                                    </td>
                                    <td>{{ point.name }}</td>
                                    <td>{{ point.city or '-' }}</td>
                                    <td>
                                        {% if point.status == 'verde' %}
                                            <span class="badge bg-success">Hecho</span>
                                        {% elif point.status == 'amarillo' %}
                                            <span class="badge bg-warning text-dark">En Curso</span>
                                        {% elif point.status == 'azul' %}
                                            <span class="badge bg-primary">Por Hacer</span>
                                        {% elif point.status == 'rojo' %}
                                            <span class="badge bg-danger">Incompleto</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ point.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ point.source or '-' }}</td>
                                    <td>
                                        {% if point.has_images %}
                                            <span class="text-success">
                                                <i class="bi bi-check-circle"></i> {{ point.image_count }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">
                                                <i class="bi bi-x-circle"></i> 0
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if point.has_cameras %}
                                            <div class="small">
                                                {% for camera in point.cameras %}
                                                    <div>
                                                        <a href="https://patagoniaservers.com.ar:5006/points/{{ point.id }}"
                                                           target="_blank" class="text-decoration-none">
                                                            Cámara {{ camera.id }}
                                                        </a>
                                                        <span class="badge bg-info text-dark">{{ camera.type }}</span>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        {% else %}
                                            <span class="text-muted">Sin cámaras</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ point.created_at.strftime('%d/%m/%Y %H:%M') if point.created_at else '-' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ point.updated_at.strftime('%d/%m/%Y %H:%M') if point.updated_at else '-' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% elif total_points is defined and total_points == 0 %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> No se encontraron puntos que coincidan con los filtros aplicados.
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-secondary">
                <i class="bi bi-search"></i> Utilice los filtros de arriba para buscar puntos específicos.
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Agregar iconos de Bootstrap si no están incluidos -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

<!-- Estilos para los indicadores de estado -->
<style>
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}
.status-azul { background-color: #0d6efd; }
.status-amarillo { background-color: #ffc107; }
.status-verde { background-color: #198754; }
.status-rojo { background-color: #dc3545; }
.status-grey { background-color: #6c757d; }
</style>
{% endblock %}

{% block scripts_extra %}
<!-- Cargar script de iconos de cámaras -->
<script src="{{ url_for('static', filename='js/camera-icons.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log("Reportes page loaded");

    const mapElement = document.getElementById('reportes-map');
    if (mapElement) {
        if (typeof L !== 'undefined') {
            initializeReportesMap(mapElement);
        } else {
            console.error('Leaflet no está disponible');
            mapElement.innerHTML = '<p class="text-danger text-center mt-5">Error: Leaflet no está cargado.</p>';
        }
    }
});

function initializeReportesMap(mapElement) {
    const pointsData = JSON.parse(mapElement.dataset.points || '[]');
    console.log(`Inicializando mapa de reportes con ${pointsData.length} puntos.`);
    console.log('Datos de puntos:', pointsData);
    console.log('Leaflet disponible:', typeof L !== 'undefined');

    // Limpiar el contenido de "Cargando mapa..."
    mapElement.innerHTML = '';

    try {
        // Calcular centro del mapa basado en los puntos
        let mapCenter = [-40.086, -67.738]; // Centro por defecto
        let mapZoom = 6;

        if (pointsData.length > 0) {
            // Filtrar puntos con coordenadas válidas
            const validPoints = pointsData.filter(p =>
                p && typeof p.lat === 'number' && typeof p.lon === 'number'
            );

            if (validPoints.length > 0) {
                const lats = validPoints.map(p => p.lat);
                const lons = validPoints.map(p => p.lon);

                // Calcular centroide
                mapCenter = [
                    lats.reduce((a, b) => a + b, 0) / lats.length,
                    lons.reduce((a, b) => a + b, 0) / lons.length
                ];

                // Calcular zoom apropiado
                if (validPoints.length === 1) {
                    mapZoom = 14;
                } else {
                    const latRange = Math.max(...lats) - Math.min(...lats);
                    const lonRange = Math.max(...lons) - Math.min(...lons);

                    if (latRange < 0.5 && lonRange < 0.5) mapZoom = 13;
                    else if (latRange < 1 && lonRange < 1) mapZoom = 12;
                    else if (latRange < 5 && lonRange < 5) mapZoom = 8;
                    else mapZoom = 6;
                }
            }
        }

        // Crear el mapa
        const map = L.map(mapElement).setView(mapCenter, mapZoom);

        // Agregar capa de tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Agregar marcadores de puntos y cámaras
        const markers = [];
        pointsData.forEach(function(point) {
            if (point && typeof point.lat === 'number' && typeof point.lon === 'number') {
                // Marcador del punto
                const estado = point.status || 'gris';
                const iconUrl = `/static/img/marker_${estado}.png`;

                const customIcon = L.icon({
                    iconUrl: iconUrl,
                    iconSize: [30, 40],
                    iconAnchor: [15, 40],
                    popupAnchor: [0, -35],
                    shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
                    shadowSize: [41, 41],
                    shadowAnchor: [12, 41]
                });

                const marker = L.marker([point.lat, point.lon], { icon: customIcon });

                // Crear contenido del popup
                const safeName = (point.name || 'Punto ' + point.id).replace(/[\r\n]+/g, ' ');
                const safeStatus = (point.status || 'N/A').replace(/[\r\n]+/g, ' ');
                const imageCount = point.image_count !== undefined ? point.image_count : 'N/A';
                const pointDetailUrl = `https://patagoniaservers.com.ar:5006/points/${point.id}`;

                let popupContent = `<b>${safeName}</b><br>`;
                popupContent += `Estado: ${safeStatus}<br>`;
                popupContent += `Ciudad: ${point.city || 'N/A'}<br>`;
                popupContent += `Origen: ${point.source || 'N/A'}<br>`;
                popupContent += `Imágenes: ${imageCount}<br>`;

                // Agregar información de cámaras si las hay
                if (point.cameras && point.cameras.length > 0) {
                    popupContent += `Cámaras: `;
                    const cameraInfo = point.cameras.map(cam =>
                        `${cam.id} (${cam.type})`
                    ).join(', ');
                    popupContent += `${cameraInfo}<br>`;
                }

                popupContent += `<a href="${pointDetailUrl}" target="_blank">Ver Detalles</a>`;

                marker.bindPopup(popupContent);
                marker.addTo(map);
                markers.push(marker);

                // Agregar marcadores de cámaras si existen
                if (point.cameras && point.cameras.length > 0 && typeof window.CameraIcons !== 'undefined') {
                    point.cameras.forEach(function(camera) {
                        const cameraMarker = window.CameraIcons.createCameraMarker(camera, point);
                        if (cameraMarker) {
                            cameraMarker.addTo(map);
                            markers.push(cameraMarker);
                        }
                    });
                }
            }
        });

        // Ajustar vista si hay múltiples marcadores
        if (markers.length > 1) {
            const group = L.featureGroup(markers);
            map.fitBounds(group.getBounds().pad(0.1));
        }

        console.log("Mapa de reportes inicializado correctamente.");

    } catch (error) {
        console.error("Error durante la inicialización del mapa de reportes:", error);
        mapElement.innerHTML = '<p class="text-danger text-center mt-5">Error al cargar el mapa.</p>';
    }
}
</script>
{% endblock %}
