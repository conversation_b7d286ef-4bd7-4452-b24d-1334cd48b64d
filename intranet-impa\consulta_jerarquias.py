# /scripts/consulta_jerarquias.py

import sys
from app import create_app, db
from app.models import User, JerarquiaPastores, JerarquiaSuperintendentes

app = create_app()

def mostrar_jerarquias(user_id):
    with app.app_context():
        user = User.query.get(user_id)
        if not user:
            print(f"Usuario con ID {user_id} no encontrado.")
            return

        print(f"==== Usuario: {user.full_name} (ID: {user.id}) ====")
        print(f"Rol: {user.role}")
        print("")

        # --- Si es un PASTOR, ¿quién lo supervisa?
        jerarquia_pastor = JerarquiaPastores.query.filter_by(pastor_id=user.id).first()
        if jerarquia_pastor:
            supervisor = User.query.get(jerarquia_pastor.supervisor_id)
            print(f"➡️  Es pastor y su SUPERINTENDENTE es: {supervisor.full_name} (ID: {supervisor.id})")
        else:
            print("❌ No tiene un superintendente asignado (JerarquiaPastores)")

        # --- Si es un SUPERINTENDENTE, ¿a quiénes supervisa?
        jerarquias_supervision = JerarquiaPastores.query.filter_by(supervisor_id=user.id).all()
        if jerarquias_supervision:
            print(f"➡️  Supervisa a los siguientes pastores:")
            for jer in jerarquias_supervision:
                pastor = User.query.get(jer.pastor_id)
                print(f"   - {pastor.full_name} (ID: {pastor.id})")
        else:
            print("❌ No supervisa a ningún pastor (JerarquiaPastores)")

        print("")

        # --- Si es SUPERINTENDENTE, ¿quién es su jefe de sector?
        jerarquia_super = JerarquiaSuperintendentes.query.filter_by(superintendente_id=user.id).first()
        if jerarquia_super:
            jefe_sector = User.query.get(jerarquia_super.jefe_sector_id)
            print(f"➡️  Es superintendente y su JEFE DE SECTOR es: {jefe_sector.full_name} (ID: {jefe_sector.id})")
        else:
            print("❌ No tiene jefe de sector asignado (JerarquiaSuperintendentes)")

        # --- Si es JEFE DE SECTOR, ¿a qué superintendentes supervisa?
        jerarquias_jefe = JerarquiaSuperintendentes.query.filter_by(jefe_sector_id=user.id).all()
        if jerarquias_jefe:
            print(f"➡️  Supervisa a los siguientes superintendentes:")
            for jer in jerarquias_jefe:
                superintendente = User.query.get(jer.superintendente_id)
                print(f"   - {superintendente.full_name} (ID: {superintendente.id})")
        else:
            print("❌ No supervisa a ningún superintendente (JerarquiaSuperintendentes)")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Uso: python consulta_jerarquias.py <ID_usuario>")
        sys.exit(1)
    try:
        uid = int(sys.argv[1])
    except ValueError:
        print("El ID debe ser un número.")
        sys.exit(1)

    mostrar_jerarquias(uid)
