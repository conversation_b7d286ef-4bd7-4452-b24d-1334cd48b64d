#!/usr/bin/env python3
# --- Archivo: restore_original_db.py ---
# Script para restaurar la base de datos original con todos los datos

import os
import sys
import sqlite3
import json
import glob
from datetime import datetime

def list_available_backups():
    """Listar todos los backups disponibles."""
    print("📋 Backups disponibles:")
    
    # Buscar backups de base de datos
    db_backups = glob.glob('*backup*.db')
    json_backups = glob.glob('*backup*.json')
    
    print("\n🗃️  Backups de base de datos (.db):")
    for backup in sorted(db_backups):
        size = os.path.getsize(backup)
        mtime = datetime.fromtimestamp(os.path.getmtime(backup))
        print(f"  📁 {backup} ({size:,} bytes) - {mtime}")
    
    print("\n📄 Backups JSON (.json):")
    for backup in sorted(json_backups):
        size = os.path.getsize(backup)
        mtime = datetime.fromtimestamp(os.path.getmtime(backup))
        print(f"  📁 {backup} ({size:,} bytes) - {mtime}")
    
    return db_backups, json_backups

def analyze_db_backup(backup_file):
    """Analizar el contenido de un backup de base de datos."""
    print(f"\n🔍 Analizando: {backup_file}")
    
    try:
        conn = sqlite3.connect(backup_file)
        cursor = conn.cursor()
        
        # Obtener tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas: {tables}")
        
        # Contar registros en cada tabla
        for table in tables:
            if table != 'sqlite_sequence':
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = cursor.fetchone()[0]
                    print(f"  📊 {table}: {count} registros")
                    
                    # Mostrar muestra de datos importantes
                    if table == 'user':
                        cursor.execute("SELECT username FROM user LIMIT 5;")
                        users = cursor.fetchall()
                        print(f"    👥 Usuarios: {[u[0] for u in users]}")
                    
                    elif table == 'point':
                        cursor.execute("SELECT name, city FROM point LIMIT 3;")
                        points = cursor.fetchall()
                        print(f"    📍 Puntos ejemplo: {points}")
                    
                    elif table == 'image':
                        cursor.execute("SELECT filename FROM image LIMIT 3;")
                        images = cursor.fetchall()
                        print(f"    🖼️  Imágenes ejemplo: {[i[0] for i in images]}")
                        
                except Exception as e:
                    print(f"    ❌ Error leyendo {table}: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error analizando {backup_file}: {e}")
        return False

def analyze_json_backup(backup_file):
    """Analizar el contenido de un backup JSON."""
    print(f"\n🔍 Analizando: {backup_file}")
    
    try:
        with open(backup_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📅 Fecha: {data.get('timestamp', 'No disponible')}")
        
        if 'tables' in data:
            print("📋 Tablas en backup:")
            for table, info in data['tables'].items():
                count = info.get('count', 0)
                print(f"  📊 {table}: {count} registros")
                
                # Mostrar muestra de datos
                if count > 0 and 'data' in info:
                    sample = info['data'][0] if info['data'] else {}
                    if table == 'user':
                        usernames = [item.get('username') for item in info['data'][:5]]
                        print(f"    👥 Usuarios: {usernames}")
                    elif table == 'point':
                        points = [(item.get('name'), item.get('city')) for item in info['data'][:3]]
                        print(f"    📍 Puntos ejemplo: {points}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analizando {backup_file}: {e}")
        return False

def choose_best_backup(db_backups, json_backups):
    """Ayudar al usuario a elegir el mejor backup."""
    print("\n🎯 Recomendaciones:")
    
    # Buscar el backup con más datos
    best_db = None
    max_size = 0
    
    for backup in db_backups:
        size = os.path.getsize(backup)
        if size > max_size:
            max_size = size
            best_db = backup
    
    if best_db:
        print(f"📁 Backup DB más grande: {best_db} ({max_size:,} bytes)")
    
    # Buscar JSON más reciente
    if json_backups:
        latest_json = max(json_backups, key=os.path.getmtime)
        mtime = datetime.fromtimestamp(os.path.getmtime(latest_json))
        print(f"📄 Backup JSON más reciente: {latest_json} ({mtime})")
    
    print("\n💡 Opciones:")
    print("1. Usar backup DB más grande (probablemente tiene más datos)")
    print("2. Usar backup JSON más reciente (más confiable)")
    print("3. Analizar manualmente cada backup")
    print("4. Cancelar")
    
    choice = input("\n¿Qué opción prefieres? (1-4): ").strip()
    
    if choice == '1' and best_db:
        return 'db', best_db
    elif choice == '2' and json_backups:
        return 'json', latest_json
    elif choice == '3':
        return 'manual', None
    else:
        return 'cancel', None

def restore_from_db_backup(backup_file):
    """Restaurar desde backup de base de datos."""
    print(f"🔄 Restaurando desde {backup_file}...")
    
    try:
        # Hacer backup de la DB actual
        current_backup = f"current_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        if os.path.exists('instance/app.db'):
            import shutil
            shutil.copy2('instance/app.db', current_backup)
            print(f"✅ Backup actual guardado como: {current_backup}")
        
        # Restaurar desde backup
        import shutil
        shutil.copy2(backup_file, 'instance/app.db')
        
        print("✅ Base de datos restaurada")
        
        # Verificar restauración
        conn = sqlite3.connect('instance/app.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas restauradas: {tables}")
        
        for table in ['user', 'point', 'image']:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                print(f"  📊 {table}: {count} registros")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error restaurando: {e}")
        return False

def restore_from_json_backup(backup_file):
    """Restaurar desde backup JSON."""
    print(f"🔄 Restaurando desde {backup_file}...")
    
    try:
        # Cargar datos JSON
        with open(backup_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Hacer backup de la DB actual
        current_backup = f"current_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        if os.path.exists('instance/app.db'):
            import shutil
            shutil.copy2('instance/app.db', current_backup)
            print(f"✅ Backup actual guardado como: {current_backup}")
        
        # Crear nueva base de datos
        if os.path.exists('instance/app.db'):
            os.remove('instance/app.db')
        
        conn = sqlite3.connect('instance/app.db')
        cursor = conn.cursor()
        
        # Crear tablas y restaurar datos
        tables_created = 0
        records_restored = 0
        
        for table_name, table_info in data['tables'].items():
            if table_name == 'sqlite_sequence':
                continue
            
            # Crear tabla basándose en los datos
            if table_info['data']:
                sample_record = table_info['data'][0]
                columns = list(sample_record.keys())
                
                # Crear tabla básica
                if table_name == 'user':
                    cursor.execute('''
                        CREATE TABLE user (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            username VARCHAR(64) NOT NULL UNIQUE,
                            email VARCHAR(120) UNIQUE,
                            password_hash VARCHAR(256) NOT NULL,
                            role VARCHAR(20) NOT NULL DEFAULT 'administrador',
                            is_active BOOLEAN NOT NULL DEFAULT 1,
                            created_by INTEGER,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        );
                    ''')
                elif table_name == 'point':
                    cursor.execute('''
                        CREATE TABLE point (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            name VARCHAR(100),
                            latitude FLOAT,
                            longitude FLOAT,
                            status VARCHAR(20) DEFAULT 'azul',
                            city VARCHAR(100),
                            source VARCHAR(100),
                            description TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        );
                    ''')
                elif table_name == 'image':
                    cursor.execute('''
                        CREATE TABLE image (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            filename VARCHAR(255) NOT NULL,
                            point_id INTEGER,
                            user_id INTEGER,
                            annotations_json TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (point_id) REFERENCES point (id),
                            FOREIGN KEY (user_id) REFERENCES user (id)
                        );
                    ''')
                
                tables_created += 1
                print(f"✅ Tabla {table_name} creada")
                
                # Insertar datos
                for record in table_info['data']:
                    if table_name == 'user':
                        cursor.execute('''
                            INSERT INTO user (id, username, email, password_hash, role, is_active, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            record.get('id'),
                            record.get('username'),
                            record.get('email'),
                            record.get('password_hash'),
                            record.get('role', 'administrador'),
                            record.get('is_active', True),
                            record.get('created_at'),
                            record.get('updated_at')
                        ))
                    elif table_name == 'point':
                        cursor.execute('''
                            INSERT INTO point (id, name, latitude, longitude, status, city, source, description, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            record.get('id'),
                            record.get('name'),
                            record.get('latitude'),
                            record.get('longitude'),
                            record.get('status', 'azul'),
                            record.get('city'),
                            record.get('source'),
                            record.get('description'),
                            record.get('created_at'),
                            record.get('updated_at')
                        ))
                    # Agregar más tablas según sea necesario
                    
                    records_restored += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ Restauración completada: {tables_created} tablas, {records_restored} registros")
        return True
        
    except Exception as e:
        print(f"❌ Error restaurando desde JSON: {e}")
        return False

def main():
    """Función principal."""
    print("🔄 Restaurador de Base de Datos Original")
    print("=" * 50)
    
    # Listar backups disponibles
    db_backups, json_backups = list_available_backups()
    
    if not db_backups and not json_backups:
        print("❌ No se encontraron backups")
        sys.exit(1)
    
    # Analizar backups
    print("\n🔍 Analizando backups...")
    
    for backup in db_backups[:3]:  # Solo los primeros 3
        analyze_db_backup(backup)
    
    for backup in json_backups[:2]:  # Solo los primeros 2
        analyze_json_backup(backup)
    
    # Ayudar a elegir
    backup_type, backup_file = choose_best_backup(db_backups, json_backups)
    
    if backup_type == 'cancel':
        print("❌ Operación cancelada")
        sys.exit(0)
    
    elif backup_type == 'manual':
        print("\n📋 Backups disponibles:")
        all_backups = db_backups + json_backups
        for i, backup in enumerate(all_backups, 1):
            print(f"  {i}. {backup}")
        
        choice = input(f"\n¿Cuál quieres usar? (1-{len(all_backups)}): ").strip()
        try:
            idx = int(choice) - 1
            backup_file = all_backups[idx]
            backup_type = 'db' if backup_file.endswith('.db') else 'json'
        except:
            print("❌ Selección inválida")
            sys.exit(1)
    
    # Restaurar
    print(f"\n🔄 Restaurando desde: {backup_file}")
    
    if backup_type == 'db':
        success = restore_from_db_backup(backup_file)
    else:
        success = restore_from_json_backup(backup_file)
    
    if success:
        print("\n🎉 ¡Base de datos restaurada exitosamente!")
        print("\n🚀 Próximos pasos:")
        print("   1. Reiniciar aplicación: systemctl restart relevamiento")
        print("   2. Verificar datos: python3 check_db.py")
        print("   3. Probar login en la web")
    else:
        print("\n❌ Error en la restauración")

if __name__ == "__main__":
    main()
