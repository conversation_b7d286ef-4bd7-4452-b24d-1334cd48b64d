# app/models_audit.py
"""
Modelos para el sistema de auditoría y historial de cambios.
"""

from datetime import datetime
from app import db
import json

class AuditLog(db.Model):
    """
    Modelo para registrar todos los cambios en el sistema.
    """
    __tablename__ = 'audit_log'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Información del usuario que realizó la acción
    user_id = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=True)
    username = db.Column(db.String(64), nullable=False)  # Guardamos el username por si el usuario se elimina
    user_ip = db.Column(db.String(45), nullable=True)  # IP del usuario
    
    # Información de la acción
    action = db.Column(db.String(50), nullable=False)  # CREATE, UPDATE, DELETE, LOGIN, LOGOUT
    table_name = db.Column(db.String(50), nullable=False)  # Tabla afectada
    record_id = db.Column(db.Integer, nullable=True)  # ID del registro afectado
    
    # Detalles del cambio
    old_values = db.Column(db.Text, nullable=True)  # Valores anteriores (JSON)
    new_values = db.Column(db.Text, nullable=True)  # Valores nuevos (JSON)
    changed_fields = db.Column(db.Text, nullable=True)  # Campos que cambiaron
    
    # Metadatos
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    description = db.Column(db.String(255), nullable=True)  # Descripción legible
    module = db.Column(db.String(50), nullable=True)  # Módulo de la aplicación
    
    # Relación con usuario
    user = db.relationship('User', backref='audit_logs', lazy=True)
    
    def __repr__(self):
        return f'<AuditLog {self.action} on {self.table_name} by {self.username}>'
    
    @staticmethod
    def log_action(user, action, table_name, record_id=None, old_values=None, 
                   new_values=None, description=None, module=None, user_ip=None):
        """
        Método estático para registrar una acción en el log de auditoría.
        """
        try:
            # Determinar campos que cambiaron
            changed_fields = []
            if old_values and new_values:
                old_dict = old_values if isinstance(old_values, dict) else {}
                new_dict = new_values if isinstance(new_values, dict) else {}
                
                for key in set(old_dict.keys()) | set(new_dict.keys()):
                    if old_dict.get(key) != new_dict.get(key):
                        changed_fields.append(key)
            
            # Crear registro de auditoría
            audit_entry = AuditLog(
                user_id=user.id if user else None,
                username=user.username if user else 'Sistema',
                user_ip=user_ip,
                action=action,
                table_name=table_name,
                record_id=record_id,
                old_values=json.dumps(old_values, default=str) if old_values else None,
                new_values=json.dumps(new_values, default=str) if new_values else None,
                changed_fields=json.dumps(changed_fields) if changed_fields else None,
                description=description,
                module=module
            )
            
            db.session.add(audit_entry)
            db.session.commit()
            
        except Exception as e:
            # En caso de error, no queremos que falle la operación principal
            print(f"Error registrando auditoría: {e}")
            db.session.rollback()
    
    def get_old_values_dict(self):
        """Retorna los valores anteriores como diccionario."""
        if self.old_values:
            try:
                return json.loads(self.old_values)
            except:
                return {}
        return {}
    
    def get_new_values_dict(self):
        """Retorna los valores nuevos como diccionario."""
        if self.new_values:
            try:
                return json.loads(self.new_values)
            except:
                return {}
        return {}
    
    def get_changed_fields_list(self):
        """Retorna la lista de campos que cambiaron."""
        if self.changed_fields:
            try:
                return json.loads(self.changed_fields)
            except:
                return []
        return []
    
    def get_action_icon(self):
        """Retorna el icono apropiado para la acción."""
        icons = {
            'CREATE': 'fas fa-plus-circle text-success',
            'UPDATE': 'fas fa-edit text-warning',
            'DELETE': 'fas fa-trash text-danger',
            'LOGIN': 'fas fa-sign-in-alt text-info',
            'LOGOUT': 'fas fa-sign-out-alt text-secondary',
            'VIEW': 'fas fa-eye text-info',
            'EXPORT': 'fas fa-download text-primary'
        }
        return icons.get(self.action, 'fas fa-question-circle text-muted')
    
    def get_action_color(self):
        """Retorna el color apropiado para la acción."""
        colors = {
            'CREATE': 'success',
            'UPDATE': 'warning',
            'DELETE': 'danger',
            'LOGIN': 'info',
            'LOGOUT': 'secondary',
            'VIEW': 'info',
            'EXPORT': 'primary'
        }
        return colors.get(self.action, 'muted')
    
    def get_readable_description(self):
        """Retorna una descripción legible de la acción."""
        if self.description:
            return self.description
        
        # Generar descripción automática
        action_names = {
            'CREATE': 'creó',
            'UPDATE': 'modificó',
            'DELETE': 'eliminó',
            'LOGIN': 'inició sesión',
            'LOGOUT': 'cerró sesión',
            'VIEW': 'consultó',
            'EXPORT': 'exportó'
        }
        
        action_name = action_names.get(self.action, 'realizó acción en')
        
        if self.action in ['LOGIN', 'LOGOUT']:
            return f"{action_name}"
        
        table_names = {
            'user': 'usuario',
            'point': 'punto',
            'camera': 'cámara',
            'image': 'imagen'
        }
        
        table_name = table_names.get(self.table_name, self.table_name)
        
        if self.record_id:
            return f"{action_name} {table_name} ID {self.record_id}"
        else:
            return f"{action_name} {table_name}"

class UserSession(db.Model):
    """
    Modelo para registrar sesiones de usuario.
    """
    __tablename__ = 'user_session'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    session_id = db.Column(db.String(255), nullable=False)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    login_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    logout_time = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Relación con usuario
    user = db.relationship('User', backref='sessions', lazy=True)
    
    def __repr__(self):
        return f'<UserSession {self.user.username} from {self.ip_address}>'
    
    def duration(self):
        """Calcula la duración de la sesión."""
        end_time = self.logout_time or datetime.utcnow()
        return end_time - self.login_time
    
    def duration_formatted(self):
        """Retorna la duración formateada."""
        duration = self.duration()
        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{int(hours)}h {int(minutes)}m"
        elif minutes > 0:
            return f"{int(minutes)}m {int(seconds)}s"
        else:
            return f"{int(seconds)}s"
