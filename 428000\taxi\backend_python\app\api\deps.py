from typing import Generator, Annotated, Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from jose import jwt, JW<PERSON>rror
from sqlalchemy.orm import Session
from sqlalchemy import text
from pydantic import ValidationError

from app.db.database import <PERSON><PERSON>ocal
from app.core.config import settings
from app.models.user import User, RoleEnum
from app.schemas.token_schema import TokenData
from app.services import user_service

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token", auto_error=False)

def get_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

async def get_token_from_cookie_or_header(request: Request = None) -> Optional[str]:
    """
    Intenta obtener el token de autenticación de la cookie o del header Authorization.
    """
    token = None

    # Primero intentar obtener el token del header Authorization
    if request and hasattr(request, "headers"):
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.replace("Bearer ", "")

    # Si no hay token en el header, intentar obtenerlo de la cookie
    if not token and request and hasattr(request, "cookies"):
        token = request.cookies.get("admin_access_token")

    return token

async def get_current_user(
    request: Request = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Si no hay token del esquema OAuth2, intentar obtenerlo de la cookie
    if not token and request:
        token = await get_token_from_cookie_or_header(request)

    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception

        # Guardar roles del token para uso posterior
        roles_from_token = payload.get("roles", [])

        token_data = TokenData(email=email)

        # Si hay request, guardar información en request.state
        if request:
            request.state.user_email = email
            request.state.user_roles_from_token = roles_from_token

    except (JWTError, ValidationError):
        raise credentials_exception

    user = user_service.get_user_by_email(db, email=token_data.email)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(
    request: Request = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    # Obtener el usuario actual
    current_user = await get_current_user(request=request, db=db, token=token)

    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Funciones de dependencia para roles especificos
def require_role(required_role_enum: RoleEnum):
    async def role_checker(
        request: Request = None,
        db: Session = Depends(get_db),
        token: str = Depends(oauth2_scheme)
    ) -> User:
        # Obtener el usuario actual
        current_user = await get_current_active_user(request=request, db=db, token=token)

        # Verificar si el usuario tiene el rol requerido
        has_required_role = False

        # Primero verificar en request.state si hay roles del token
        if request and hasattr(request.state, "user_roles_from_token"):
            user_roles = request.state.user_roles_from_token
            has_required_role = any(role.lower() == required_role_enum.value.lower() for role in user_roles)

        # Si no se encontró en request.state, verificar en la base de datos
        if not has_required_role:
            try:
                # Consultar directamente a la base de datos para verificar el rol
                sql_query = text("""
                    SELECT r.name
                    FROM roles r
                    JOIN user_roles_association ura ON r.id = ura.role_id
                    WHERE ura.user_id = :user_id AND LOWER(r.name::text) = :role_name
                    LIMIT 1
                """)
                role_query = db.execute(
                    sql_query,
                    {"user_id": current_user.id, "role_name": required_role_enum.value.lower()}
                )

                has_required_role = role_query.first() is not None
            except Exception as e:
                print(f"Error al verificar rol: {e}")

        if not has_required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"User does not have the required role: {required_role_enum.value}"
            )

        return current_user
    return role_checker

# Ejemplos de dependencias de roles
get_current_admin_user = require_role(RoleEnum.ADMINISTRADOR)
get_current_operator_user = require_role(RoleEnum.OPERADOR)
get_current_driver_user = require_role(RoleEnum.TAXI)
get_current_passenger_user = require_role(RoleEnum.USUARIO)

# Función para verificar si un usuario es administrador u operador
def get_current_admin_or_operator_user(
    request: Request = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> User:
    # Verificar si el usuario tiene rol de administrador u operador
    is_admin_or_operator = False
    try:
        # Consultar directamente a la base de datos para verificar el rol
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) IN ('administrador', 'operador')
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})

        is_admin_or_operator = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol: {e}")

    if not is_admin_or_operator:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Se requiere rol de administrador u operador"
        )

    return current_user

