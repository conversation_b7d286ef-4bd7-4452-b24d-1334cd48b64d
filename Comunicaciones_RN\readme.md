 # Comunicaciones RN                                                                                                                                               
                                                                                                                                                                   
 ## Descripción                                                                                                                                                    
                                                                                                                                                                   
 Este proyecto es una aplicación web desarrollada en Python utilizando Flask. Permite la administración de nodos de comunicación y el acceso controlado basado en  
 permisos de usuario. La aplicación soporta la visualización de nodos activos, el conteo de oyentes y la transmisión de audio en tiempo real. Los usuarios pueden  
 enviar mensajes de texto en tiempo real y gestionar los accesos a diferentes nodos de comunicación.                                                               
                                                                                                                                                                   
 ## Estructura del Proyecto                                                                                                                                        
                                                                                                                                                                   
 ```plaintext                                                                                                                                                      
 comunicaciones_rn/                                                                                                                                                
 │                                                                                                                                                                 
 ├── app.py                                                                                                                                                        
 ├── auth.py                                                                                                                                                       
 ├── admin.py                                                                                                                                                      
 ├── database_setup.py                                                                                                                                             
 ├── extensions.py                                                                                                                                                 
 ├── globals.py                                                                                                                                                    
 ├── models.py                                                                                                                                                     
 ├── views.py                                                                                                                                                      
 ├── instance/                                                                                                                                                     
 │   └── vhf.db                                                                                                                                                    
 ├── static/                                                                                                                                                       
 │   ├── audio-icon.png                                                                                                                                            
 │   ├── RNCom.ico                                                                                                                                                 
 │   ├── RNCom.png                                                                                                                                                 
 │   ├── RNCom1.ico
 │   ├── RNCom2.png                                                                                                                                                
 │   ├── rojo.png                                                                                                                                                  
 │   ├── verde.png                                                                                                                                                 
 │   ├── styles.css                                                                                                                                                
 │   └── webrtc.js                                                                                                                                                 
 ├── templates/                                                                                                                                                    
 │   ├── admin/                                                                                                                                                    
 │   │   ├── create_user.html                                                                                                                                      
 │   │   ├── edit_user.html                                                                                                                                        
 │   │   └── users.html                                                                                                                                            
 │   ├── dashboard.html                                                                                                                                            
 │   ├── login.html                                                                                                                                                
 │   └── node.html
 └── README.md
                                                                                                                                                                   

                                                                      Archivos y Directorios

 • app.py: Archivo principal de la aplicación Flask. Configura la aplicación, las rutas y eventos de WebSocket.                                                    
 • auth.py: Maneja la autenticación de usuarios, incluyendo login y logout.                                                                                        
 • admin.py: Contiene rutas y vistas para la administración de usuarios y nodos.                                                                                   
 • database_setup.py: Script para configurar la base de datos inicial y agregar datos predeterminados.                                                             
 • extensions.py: Define extensiones Flask utilizadas en la aplicación.                                                                                            
 • globals.py: Contiene variables globales utilizadas en la aplicación.                                                                                            
 • models.py: Define los modelos de base de datos para usuarios y nodos de comunicación.                                                                           
 • views.py: Define las vistas principales de la aplicación, incluyendo el dashboard y la visualización de nodos.                                                  
 • instance/vhf.db: Base de datos SQLite de la aplicación.                                                                                                         
 • static/: Directorio que contiene archivos estáticos como CSS, JavaScript e imágenes.                                                                            
    • audio-icon.png, RNCom.ico, RNCom.png, RNCom1.ico, RNCom2.png, rojo.png, verde.png: Iconos y logos utilizados en la aplicación.                               
    • styles.css: Archivo de estilos CSS.                                                                                                                          
    • webrtc.js: Script JavaScript para manejar la comunicación en tiempo real a través de WebSockets.                                                             
 • templates/: Directorio que contiene plantillas HTML para las vistas de la aplicación.                                                                           
    • admin/: Contiene plantillas HTML para la administración de usuarios y nodos.                                                                                 
       • create_user.html: Formulario para crear un nuevo usuario.                                                                                                 
       • edit_user.html: Formulario para editar un usuario existente.                                                                                              
       • users.html: Lista de usuarios.                                                                                                                            
    • dashboard.html: Página principal que muestra los nodos disponibles.                                                                                          
    • login.html: Página de inicio de sesión.                                                                                                                      
    • node.html: Vista de un nodo específico, permitiendo la interacción en tiempo real.                                                                           


                                                                         Funcionalidades

 • Autenticación de Usuarios: Login y logout con gestión de sesiones.                                                                                              
 • Gestión de Usuarios: Crear, editar y listar usuarios con diferentes roles y permisos.                                                                           
 • Visualización de Nodos: Mostrar nodos de comunicación y subnodos en una jerarquía.                                                                              
 • Mensajería en Tiempo Real: Enviar y recibir mensajes de texto en tiempo real dentro de los nodos.                                                               
 • Transmisión de Audio: Funcionalidad para transmitir y recibir audio en tiempo real.                                                                             
 • Acceso Controlado: Control de acceso a nodos basado en permisos de usuario.                                                                                     


                                                                            Requisitos

 • Python 3.8+                                                                                                                                                     
 • Flask                                                                                                                                                           
 • Flask-SQLAlchemy                                                                                                                                                
 • Flask-Login                                                                                                                                                     
 • Flask-Migrate                                                                                                                                                   
 • Flask-SocketIO                                                                                                                                                  
 • Werkzeug                                                                                                                                                        
 • eventlet                                                                                                                                                        
 • Flask-CORS                                                                                                                                                      
 • pyserial                                                                                                                                                        


                                                                           Instalación

 1 Clonar el repositorio:                                                                                                                                          
                                                                                                                                                                   
    git clone https://github.com/joacoabe/comunicaciones_rn.git                                                                                                    
    cd comunicaciones_rn                                                                                                                                           
                                                                                                                                                                   
 2 Crear y activar un entorno virtual:                                                                                                                             
                                                                                                                                                                   
    python -m venv venv                                                                                                                                            
    venv\Scripts\activate                                                                                                                                          
                                                                                                                                                                   
 3 Instalar las dependencias:                                                                                                                                      
                                                                                                                                                                   
    pip install -r requirements.txt                                                                                                                                
                                                                                                                                                                   
 4 Configurar la base de datos:                                                                                                                                    
                                                                                                                                                                   
    python database_setup.py                                                                                                                                       
                                                                                                                                                                   
 5 Ejecutar la aplicación:                                                                                                                                         
                                                                                                                                                                   
    flask run                                                                                                                                                      
                                                                                                                                                                   


                                                                               Uso

 • Acceder a la aplicación en http://127.0.0.1:5000.                                                                                                               
 • Iniciar sesión con el usuario y contraseña predeterminados (admin/admin).                                                                                       
 • Navegar por las diferentes vistas para gestionar usuarios y nodos de comunicación.                                                                              


                                                                           Contribución

Para contribuir a este proyecto, por favor sigue estos pasos:

 1 Haz un fork del proyecto.                                                                                                                                       
 2 Crea una nueva rama (git checkout -b feature/nueva-funcionalidad).                                                                                              
 3 Realiza tus cambios y haz commit (git commit -am 'Añadir nueva funcionalidad').                                                                                 
 4 Sube tus cambios (git push origin feature/nueva-funcionalidad).                                                                                                 
 5 Abre un Pull Request.                                                                                                                                           


                                                                             Licencia

Este proyecto está licenciado bajo la Licencia MIT. Consulta el archivo LICENSE para más detalles.