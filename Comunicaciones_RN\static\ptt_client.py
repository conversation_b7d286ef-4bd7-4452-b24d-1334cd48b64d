# -*- coding: utf-8 -*-

import socketio
import json
import requests
from bs4 import BeautifulSoup

# Cargar configuración del usuario
with open('ptt_client_config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

username = config['username']
password = config['password']

# Iniciar sesión y mantener la sesión
login_url = 'https://rncom.patagoniaservers.com.ar/login'
session = requests.Session()

# Obtener la página de inicio de sesión para obtener cookies y tokens
response = session.get(login_url)
soup = BeautifulSoup(response.text, 'html.parser')

# Asumimos que no hay tokens CSRF por simplicidad
login_data = {
    'username': username,
    'password': password,
}

# Enviar el formulario de inicio de sesión
response = session.post(login_url, data=login_data)

# Verificar si el inicio de sesión fue exitoso
if response.status_code == 200 and 'Bienvenido' in response.text:
    print('Inicio de sesión exitoso')
else:
    print('Error al iniciar sesión: No se pudo autenticar')
    print('Contenido de la respuesta:', response.text)
    exit(1)

# Redirigir al nodo correspondiente
node_url = 'https://rncom.patagoniaservers.com.ar/node/41'
response = session.get(node_url)
if response.status_code == 200:
    print(f'Conectado al nodo: {node_url}')
else:
    print(f'Error al conectarse al nodo: {node_url}')
    exit(1)

# Crear cliente de socket
sio = socketio.Client(logger=True, engineio_logger=True)

# Conectar al servidor Flask
@sio.event
def connect():
    print('Conectado al servidor')
    sio.emit('user_connected', {'user': username, 'node_id': 41})
    print('Evento de conexión de usuario emitido')

@sio.event
def disconnect():
    print('Desconectado del servidor')

@sio.event
def connect_error(data):
    print(f'Error al conectar: {data}')

@sio.on('update_users')
def on_update_users(data):
    print(f'Usuarios conectados actualizados: {data}')

if __name__ == '__main__':
    # Extraer las cookies de la sesión para pasarlas a socketio
    cookies = session.cookies.get_dict()
    cookie_header = '; '.join([f'{name}={value}' for name, value in cookies.items()])
    
    try:
        print('Intentando conectar con el servidor...')
        sio.connect('https://rncom.patagoniaservers.com.ar', headers={'Cookie': cookie_header})
        sio.wait()
    except Exception as e:
        print(f'Error al conectar con el servidor: {e}')
