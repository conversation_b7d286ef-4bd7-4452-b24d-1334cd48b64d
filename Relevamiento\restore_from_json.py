#!/usr/bin/env python3
# --- Archivo: restore_from_json.py ---
# Script para restaurar desde el backup JSON

import os
import sys
import json
import sqlite3

def find_json_backup():
    """Encontrar el backup JSON más reciente."""
    import glob
    
    backups = glob.glob('backup_completo_*.json')
    if not backups:
        print("❌ No se encontraron backups JSON")
        return None
    
    latest_backup = max(backups, key=os.path.getctime)
    print(f"📁 Usando backup JSON: {latest_backup}")
    return latest_backup

def load_backup_data(backup_file):
    """Cargar datos del backup JSON."""
    try:
        with open(backup_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📅 Backup del: {data['timestamp']}")
        print("📋 Tablas en backup:")
        for table, info in data['tables'].items():
            print(f"  - {table}: {info['count']} registros")
        
        return data
        
    except Exception as e:
        print(f"❌ Error cargando backup: {e}")
        return None

def create_fresh_database():
    """Crear base de datos completamente nueva."""
    print("🗑️  Eliminando base de datos actual...")
    
    if os.path.exists('app.db'):
        os.remove('app.db')
    
    print("🚀 Creando base de datos nueva...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Crear tabla user con estructura completa
        cursor.execute('''
            CREATE TABLE user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(64) NOT NULL UNIQUE,
                email VARCHAR(120) UNIQUE,
                password_hash VARCHAR(256) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'operador',
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES user (id)
            );
        ''')
        print("✅ Tabla user creada")
        
        # Crear tablas de permisos
        cursor.execute('''
            CREATE TABLE user_city_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                city VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                UNIQUE(user_id, city)
            );
        ''')
        print("✅ Tabla user_city_permissions creada")
        
        cursor.execute('''
            CREATE TABLE user_source_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                source VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                UNIQUE(user_id, source)
            );
        ''')
        print("✅ Tabla user_source_permissions creada")
        
        cursor.execute('''
            CREATE TABLE user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission_type VARCHAR(50) NOT NULL,
                permission_value BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                UNIQUE(user_id, permission_type)
            );
        ''')
        print("✅ Tabla user_permissions creada")
        
        # Crear otras tablas que puedan existir
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS point (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100),
                latitude FLOAT,
                longitude FLOAT,
                status VARCHAR(20) DEFAULT 'azul',
                city VARCHAR(100),
                source VARCHAR(100),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("✅ Tabla point creada")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS image (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                point_id INTEGER,
                user_id INTEGER,
                annotations_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (point_id) REFERENCES point (id),
                FOREIGN KEY (user_id) REFERENCES user (id)
            );
        ''')
        print("✅ Tabla image creada")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS camera (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                point_id INTEGER,
                type VARCHAR(20) DEFAULT 'otra',
                direction FLOAT,
                photo_filename VARCHAR(255),
                latitude FLOAT,
                longitude FLOAT,
                location_source VARCHAR(50),
                location_accuracy FLOAT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (point_id) REFERENCES point (id)
            );
        ''')
        print("✅ Tabla camera creada")
        
        # Crear índices
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_user_city_permissions_user_id ON user_city_permissions(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_city_permissions_city ON user_city_permissions(city);",
            "CREATE INDEX IF NOT EXISTS idx_user_source_permissions_user_id ON user_source_permissions(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_source_permissions_source ON user_source_permissions(source);",
            "CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_permissions_type ON user_permissions(permission_type);"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✅ Índices creados")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creando base de datos: {e}")
        return False

def restore_data_from_backup(backup_data):
    """Restaurar datos desde el backup."""
    print("📥 Restaurando datos desde backup...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Restaurar usuarios
        if 'user' in backup_data['tables'] and backup_data['tables']['user']['data']:
            print("👥 Restaurando usuarios...")
            
            for user_data in backup_data['tables']['user']['data']:
                cursor.execute('''
                    INSERT INTO user (id, username, email, password_hash, role, is_active, created_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    user_data.get('id'),
                    user_data.get('username'),
                    user_data.get('email'),
                    user_data.get('password_hash'),
                    user_data.get('role', 'administrador'),
                    user_data.get('is_active', True),
                    user_data.get('created_by'),
                    user_data.get('created_at'),
                    user_data.get('updated_at')
                ))
            
            print(f"✅ {len(backup_data['tables']['user']['data'])} usuarios restaurados")
        
        # Actualizar secuencia de user
        cursor.execute("UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM user) WHERE name = 'user';")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error restaurando datos: {e}")
        return False

def test_restored_database():
    """Probar que la base de datos restaurada funcione."""
    print("🧪 Probando base de datos restaurada...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Verificar estructura
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = ['id', 'username', 'email', 'password_hash', 'role', 'is_active', 'created_by', 'created_at', 'updated_at']
        missing_columns = set(required_columns) - set(column_names)
        
        if missing_columns:
            print(f"❌ Columnas faltantes: {missing_columns}")
            return False
        
        print("✅ Estructura de tabla user correcta")
        
        # Verificar datos
        cursor.execute("SELECT username, role, is_active FROM user;")
        users = cursor.fetchall()
        
        print(f"👥 Usuarios encontrados: {len(users)}")
        for user in users:
            print(f"  - {user[0]} ({user[1]}) - Activo: {user[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error probando base de datos: {e}")
        return False

def main():
    """Función principal."""
    print("📥 Restaurador desde Backup JSON")
    print("=" * 40)
    
    # Encontrar backup JSON
    backup_file = find_json_backup()
    if not backup_file:
        sys.exit(1)
    
    # Cargar datos del backup
    backup_data = load_backup_data(backup_file)
    if not backup_data:
        sys.exit(1)
    
    # Crear base de datos nueva
    if not create_fresh_database():
        sys.exit(1)
    
    # Restaurar datos
    if not restore_data_from_backup(backup_data):
        sys.exit(1)
    
    # Probar base de datos
    if test_restored_database():
        print("\n🎉 ¡Base de datos restaurada exitosamente!")
        print("\n🚀 Próximos pasos:")
        print("   1. Limpiar cache: find . -name '*.pyc' -delete")
        print("   2. Reiniciar app: systemctl restart relevamiento")
        print("   3. Verificar: python3 verify_final.py")
        print("\n🔑 Credenciales:")
        print("   Usuario: admin")
        print("   Contraseña: isaias52")
    else:
        print("\n❌ Error en la restauración")
        sys.exit(1)

if __name__ == "__main__":
    main()
