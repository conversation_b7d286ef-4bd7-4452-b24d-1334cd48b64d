#!/usr/bin/env python3
# test_audio_simulado.py - Prueba de transmisión de audio simulado

import json
import socketio
import base64
import wave
import io
import numpy as np
import time
import threading
from datetime import datetime

def log_test(mensaje):
    """Log de pruebas"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    with open("test_audio_log.txt", "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {mensaje}\n")
    print(f"[{timestamp}] {mensaje}")

def cargar_config():
    """Carga configuración"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        log_test(f"Error cargando config: {e}")
        return None

def generar_audio_fake():
    """Genera audio de prueba (tono de 440Hz)"""
    sample_rate = 8000
    duration = 2.0  # 2 segundos
    frequency = 440  # La nota A4
    
    # Generar onda senoidal
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    wave_data = np.sin(frequency * 2 * np.pi * t)
    
    # Convertir a int16
    audio_data = (wave_data * 32767).astype(np.int16)
    
    # Crear archivo WAV en memoria
    wav_buffer = io.BytesIO()
    with wave.open(wav_buffer, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)  # 16 bits = 2 bytes
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data.tobytes())
    
    wav_buffer.seek(0)
    audio_base64 = base64.b64encode(wav_buffer.read()).decode('utf-8')
    
    log_test(f"Audio fake generado: {len(audio_base64)} caracteres base64")
    return audio_base64

def test_envio_audio_servidor(config):
    """Test de envío de audio al servidor"""
    log_test("=== TEST ENVIO AUDIO AL SERVIDOR ===")
    
    if not config:
        log_test("Error: No hay configuración")
        return False
    
    try:
        sio = socketio.Client()
        conectado = False
        audio_enviado = False
        
        @sio.event
        def connect():
            nonlocal conectado
            conectado = True
            log_test("✓ Conectado al servidor para envío")
        
        @sio.event
        def disconnect():
            log_test("Desconectado del servidor")
        
        @sio.event
        def connect_error(data):
            log_test(f"Error de conexión: {data}")
        
        # Conectar al servidor
        node_url = config['node_url']
        log_test(f"Conectando a: wss://{node_url}")
        
        sio.connect(f"wss://{node_url}",
                   headers={'node_id': config['node_id'], 'username': config['username']},
                   namespaces=['/node'])
        
        # Esperar conexión
        time.sleep(2)
        
        if conectado:
            # Generar y enviar audio fake
            audio_fake = generar_audio_fake()
            
            log_test("Enviando audio fake al servidor...")
            sio.emit('transmit_audio', {
                'audio': audio_fake,
                'user': config['username'],
                'node_id': config['node_id']
            }, namespace='/node')
            
            audio_enviado = True
            log_test("✓ Audio enviado al servidor")
            
            # Esperar un poco para ver respuesta
            time.sleep(3)
        
        sio.disconnect()
        return conectado and audio_enviado
        
    except Exception as e:
        log_test(f"Error en test de envío: {e}")
        return False

def test_recepcion_audio_local():
    """Test de recepción de audio desde servidor local"""
    log_test("=== TEST RECEPCION DESDE SERVIDOR LOCAL ===")
    
    try:
        sio = socketio.Client()
        conectado = False
        audio_recibido = False
        
        @sio.event
        def connect():
            nonlocal conectado
            conectado = True
            log_test("✓ Conectado al servidor local para recepción")
        
        @sio.on('receive_audio')
        def on_receive_audio(data):
            nonlocal audio_recibido
            log_test(f"✓ Audio recibido de: {data.get('user', 'desconocido')}")
            audio_recibido = True
        
        @sio.on('audio_received')
        def on_audio_received(data):
            nonlocal audio_recibido
            log_test(f"✓ Evento audio_received de: {data.get('user', 'desconocido')}")
            audio_recibido = True
        
        # Conectar al servidor local
        log_test("Conectando al servidor local...")
        sio.connect('http://127.0.0.1:5000')
        
        time.sleep(2)
        
        if conectado:
            log_test("Esperando audio por 10 segundos...")
            time.sleep(10)
        
        sio.disconnect()
        return conectado and audio_recibido
        
    except Exception as e:
        log_test(f"Error en test de recepción local: {e}")
        return False

def test_ptt_directo(config):
    """Test directo del PTT"""
    log_test("=== TEST PTT DIRECTO ===")
    
    if not config:
        log_test("Error: No hay configuración")
        return False
    
    try:
        import requests
        
        # Test activar PTT
        log_test("Activando PTT...")
        response = requests.post('http://127.0.0.1:5000/ptt_event',
                               json={'ptt_state': True},
                               timeout=5)
        
        if response.status_code == 200:
            log_test("✓ PTT activado correctamente")
            time.sleep(2)
            
            # Test desactivar PTT
            log_test("Desactivando PTT...")
            response = requests.post('http://127.0.0.1:5000/ptt_event',
                                   json={'ptt_state': False},
                                   timeout=5)
            
            if response.status_code == 200:
                log_test("✓ PTT desactivado correctamente")
                return True
            else:
                log_test(f"Error desactivando PTT: {response.status_code}")
        else:
            log_test(f"Error activando PTT: {response.status_code}")
        
        return False
        
    except Exception as e:
        log_test(f"Error en test PTT: {e}")
        return False

def test_simulacion_completa(config):
    """Simulación completa de transmisión"""
    log_test("=== SIMULACION COMPLETA ===")
    
    if not config:
        log_test("Error: No hay configuración")
        return False
    
    try:
        # Simular que otro usuario envía audio
        sio = socketio.Client()
        
        @sio.event
        def connect():
            log_test("✓ Conectado para simulación completa")
            
            # Simular audio de otro usuario
            audio_fake = generar_audio_fake()
            
            # Enviar como si fuera otro usuario
            fake_user = "test_user_fake"
            log_test(f"Simulando audio de usuario: {fake_user}")
            
            sio.emit('transmit_audio', {
                'audio': audio_fake,
                'user': fake_user,
                'node_id': config['node_id']
            }, namespace='/node')
            
            log_test("Audio fake enviado como otro usuario")
        
        node_url = config['node_url']
        sio.connect(f"wss://{node_url}",
                   headers={'node_id': config['node_id'], 'username': 'test_fake'},
                   namespaces=['/node'])
        
        time.sleep(5)
        sio.disconnect()
        return True
        
    except Exception as e:
        log_test(f"Error en simulación completa: {e}")
        return False

def main():
    """Función principal de test"""
    # Limpiar log anterior
    import os
    if os.path.exists("test_audio_log.txt"):
        os.remove("test_audio_log.txt")
    
    log_test("INICIANDO TESTS DE AUDIO SIMULADO")
    log_test("=" * 40)
    
    config = cargar_config()
    if not config:
        log_test("No se puede continuar sin configuración")
        return
    
    log_test(f"Usuario: {config['username']}")
    log_test(f"Nodo: {config['node_id']}")
    log_test(f"Servidor: {config['node_url']}")
    
    # Ejecutar tests
    test1 = test_ptt_directo(config)
    test2 = test_envio_audio_servidor(config)
    test3 = test_recepcion_audio_local()
    test4 = test_simulacion_completa(config)
    
    # Resumen
    log_test("=" * 40)
    log_test("RESUMEN DE TESTS:")
    log_test(f"PTT Directo: {'✓' if test1 else '✗'}")
    log_test(f"Envío Audio: {'✓' if test2 else '✗'}")
    log_test(f"Recepción Local: {'✓' if test3 else '✗'}")
    log_test(f"Simulación Completa: {'✓' if test4 else '✗'}")
    
    log_test("Tests completados. Revisa 'test_audio_log.txt'")

if __name__ == '__main__':
    main()
