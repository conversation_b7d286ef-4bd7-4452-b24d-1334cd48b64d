<!-- /app/templates/admin/inventory.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}Inventario - {{ church.name }}{% endblock %}

{% block content %}
  <h1>Inventario de {{ church.name }}</h1>
  <a href="{{ url_for('routes.add_inventory_item', church_id=church.id) }}" class="btn btn-success mb-3">Agregar <PERSON></a>
  
  <table class="table table-striped">
    <thead>
      {% include '_search_form.html' %}
      {% include '_pagination.html' %}
      <tr>
        <th>Nombre</th>
        <th>Tipo</th>
        <th>Canti<PERSON></th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% for item in inventory_items %}
      <tr>
        <td>{{ item.item_name }}</td>
        <td>{{ item.item_type }}</td>
        <td>{{ item.quantity }}</td>
        <td>
          <a href="{{ url_for('routes.edit_inventory_item', item_id=item.id) }}" class="btn btn-primary btn-sm">Editar</a>
          <form action="{{ url_for('routes.delete_inventory_item', item_id=item.id) }}" method="POST" style="display:inline-block;">
            <button type="submit" class="btn btn-danger btn-sm">Eliminar</button>
          </form>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
{% endblock %}
