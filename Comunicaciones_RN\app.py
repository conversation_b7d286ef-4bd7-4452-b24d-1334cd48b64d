#!/usr/bin/env python3
# app.py

from flask import Flask, request, redirect, url_for, current_app, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
import os
from extensions import db, login_manager, migrate, socketio
from flask_socketio import SocketIO, emit, join_room, leave_room, Namespace, disconnect
from flask_login import current_user, login_required, UserMixin
import logging
import eventlet
import eventlet.wsgi
import ssl
from flask_cors import CORS
from datetime import datetime
from models import User, Node
# Importar las variables globales
from globals import connected_users, audio_transmissions
import base64
from threading import Lock

# Configuración del logger
logging.basicConfig(level=logging.DEBUG)

# Diccionario para estados de grabación
recording_states = {}
recording_lock = Lock()


def create_app():
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'isaias52'
    db_path = os.path.join(os.getcwd(), 'instance', 'vhf.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'

    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    socketio.init_app(app,
                      async_mode='eventlet',
                      cors_allowed_origins="*",
                      ping_timeout=600,
                      ping_interval=25)

    login_manager.login_view = 'auth.login'
    CORS(app, resources={r"/*": {"origins": "*"}})

    with app.app_context():
        db.create_all()

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    from auth import auth_bp
    from views import views_bp
    from admin import admin_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(views_bp)
    app.register_blueprint(admin_bp, url_prefix='/admin')

    @app.route('/')
    def index():
        return redirect(url_for('auth.login'))

    @app.route('/get_messages/<node_name>')
    @login_required
    def get_messages(node_name):
        log_file = os.path.join(current_app.root_path, 'logs', 'Mensajes', f'{node_name}.log')
        if os.path.exists(log_file):
            with open(log_file, 'r') as file:
                messages = file.readlines()
            return jsonify({'messages': messages})
        else:
            return jsonify({'messages': []})

    @app.route('/get_connected_users/<node_id>')
    @login_required
    def get_connected_users(node_id):
        print("connected_users:", connected_users)
        users_in_node = list(set([user['username'] for user in connected_users.values() if user['node_id'] == str(node_id)]))
        print("users_in_node:", users_in_node)
        return jsonify({'users': users_in_node})

    @app.route('/get_private_messages/<chat_room>')
    @login_required
    def get_private_messages(chat_room):
        log_file = os.path.join(current_app.root_path, 'logs', 'Mensajes', 'Privados', f'{chat_room}.log')
        if os.path.exists(log_file):
            with open(log_file, 'r') as file:
                messages = file.readlines()
            return jsonify({'messages': messages})
        else:
            return jsonify({'messages': []})

    return app


app = create_app()

@app.route('/log', methods=['POST'])
def log_client_message():
    data = request.get_json()
    message = data.get('message', '')
    log_dir = os.path.join(current_app.root_path, 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'console-log.txt')
    try:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(message)
        return jsonify({'status': 'success'}), 200
    except Exception as e:
        print(f"Error writing to client log file: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/files/<path:filename>')
@login_required  # Opcional, si solo usuarios logueados pueden descargar.
def serve_uploaded_file(filename):
    # Ajusta este directorio a donde guardas tus archivos.
    base_dir = os.path.join(os.getcwd(), 'Registro_de_Archivos')
    return send_from_directory(base_dir, filename)

def get_full_node_hierarchy(node, levels=3):
    hierarchy = []
    current_node = node
    while current_node is not None:
        hierarchy.append(current_node.name)
        current_node = current_node.parent
    hierarchy = hierarchy[::-1]
    limited_hierarchy = hierarchy[-levels:]
    return '.'.join(limited_hierarchy)


def generate_filename(hierarchy, timestamp, extension):
    formatted_time = timestamp.strftime("%d-%m-%Y.%H%M")
    filename = f"{hierarchy}({formatted_time}){extension}"
    return filename


def save_audio(node_id, audio_base64, username):
    with recording_lock:
        node = Node.query.get(node_id)
        if not node:
            logging.error(f"Node with ID {node_id} not found.")
            return
        hierarchy = get_full_node_hierarchy(node)
        now = datetime.now()
        hierarchy_parts = hierarchy.split('.')
        current_date = now.strftime("%d-%m-%Y")
        recordings_dir = os.path.join(os.getcwd(), 'Grabaciones', *hierarchy_parts, current_date)
        os.makedirs(recordings_dir, exist_ok=True)
        audio_file_name = now.strftime("%H%M%S") + ".mp3"
        audio_file_path = os.path.join(recordings_dir, audio_file_name)
        log_file_name = current_date + ".txt"
        log_file_path = os.path.join(recordings_dir, log_file_name)
        try:
            audio_data = base64.b64decode(audio_base64)
        except base64.binascii.Error as e:
            logging.error(f"Error decoding base64 audio: {e}")
            return
        try:
            save_start_time = datetime.now()
            save_started_data = {'node_id': node_id, 'user': username}
            socketio.emit('save_started', save_started_data, namespace='/audio_events')
            with open(audio_file_path, 'wb') as audio_file:
                audio_file.write(audio_data)
            start_time = recording_states[node_id]['start_time']
            end_time = now
            duration = end_time - start_time
            with open(log_file_path, 'a') as log_file:
                log_file.write(f"Usuario: {username}\n")
                log_file.write(f"Inicio de la modulación: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                log_file.write(f"Fin de la modulación: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                log_file.write(f"Duración de la modulación: {duration}\n\n")
            save_end_time = datetime.now()
            save_duration = (save_end_time - save_start_time).total_seconds()
            save_ended_data = {'node_id': node_id, 'user': username, 'duration': save_duration}
            socketio.emit('save_ended', save_ended_data, namespace='/audio_events')
            audio_save_data = {
                'node_id': node_id,
                'user': username,
                'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': now.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': str(duration)
            }
            socketio.emit('audio_transmission_saved', audio_save_data, namespace='/audio_events')
        except IOError as e:
            logging.error(f"Error writing audio or log file: {e}")


def save_file(identifier, file_base64, filename, username):
    MAX_SIZE = 10 * 1024 * 1024
    with recording_lock:
        # Decodificar el archivo
        try:
            file_data = base64.b64decode(file_base64)
        except Exception as e:
            logging.error(f"Error al decodificar el archivo base64: {e}")
            return False, "Error al decodificar el archivo."

        if len(file_data) > MAX_SIZE:
            logging.error(f"El archivo excede el tamaño máximo permitido: {len(file_data)} bytes.")
            return False, "El archivo excede el tamaño máximo permitido de 10 MB."

        now = datetime.now()
        current_date = now.strftime("%d-%m-%Y")
        base_dir = os.path.join(os.getcwd(), 'Registro_de_Archivos')
        
        # Intentamos obtener el nodo. Si no se encuentra (por ej. en chat privado),
        # asumimos que 'identifier' es el nombre del chat (por ejemplo "joacoabe-viedma")
        node = None
        try:
            node = Node.query.get(int(identifier))
        except Exception as e:
            node = None

        if node is not None:
            # Caso de chat público: se guarda según el nombre del nodo.
            hierarchy = node.name
            file_dir = os.path.join(base_dir, hierarchy, current_date)
        else:
            # Caso de chat privado: se guarda en la carpeta Privados/<chat_room>/<fecha>
            file_dir = os.path.join(base_dir, 'Privados', identifier, current_date)
        
        os.makedirs(file_dir, exist_ok=True)

        timestamp_str = now.strftime("%H%M%S")
        file_extension = os.path.splitext(filename)[1]
        safe_filename = f"{os.path.splitext(filename)[0]}_{timestamp_str}{file_extension}"
        file_path = os.path.join(file_dir, safe_filename)

        try:
            with open(file_path, 'wb') as f:
                f.write(file_data)
        except IOError as e:
            logging.error(f"Error al guardar el archivo: {e}")
            return False, "Error al guardar el archivo en el servidor."

        relative_path = os.path.relpath(file_path, base_dir).replace('\\', '/')
        url_path = f"/files/{relative_path}"

        file_info = {
            'filename': safe_filename,
            'filepath': url_path,
            'size': len(file_data)
        }
        return True, file_info

@socketio.on('transmit_file', namespace='/node')
def handle_transmit_file(data):
    file_base64 = data.get('fileData')
    filename = data.get('filename')
    username = data.get('user')
    node_id = data.get('node_id')
    if not file_base64 or not filename or not username or not node_id:
        emit('file_upload_error', {'message': 'Datos incompletos para la carga de archivo.', 'filename': filename}, to=request.sid)
        return
    success, result = save_file(node_id, file_base64, filename, username)
    if success:
        file_info = result
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        emit('receive_file', {'fileinfo': file_info, 'user': username, 'timestamp': timestamp}, room=node_id)
        # Registrar el envío del archivo en el log del nodo, incluyendo el enlace HTML
        node = Node.query.get(node_id)
        if node:
            log_message(
                f"{username}: [FILE] envió el archivo <a href=\"{file_info['filepath']}\" download=\"{file_info['filename']}\">{file_info['filename']}</a> {timestamp}\n",
                node.name
            )
    else:
        emit('file_upload_error', {'message': result, 'filename': filename}, to=request.sid)

@socketio.on('transmit_private_file', namespace='/private')
def handle_transmit_private_file(data):
    file_base64 = data.get('fileData')
    filename = data.get('filename')
    username = data.get('user')
    chat_room = data.get('chat_room')
    if not file_base64 or not filename or not username or not chat_room:
        emit('private_file_upload_error',
             {'message': 'Datos incompletos para la carga de archivo.', 'filename': filename},
             to=request.sid)
        print("Faltan datos en transmit_private_file:", data)
        return
    success, result = save_file(chat_room, file_base64, filename, username)
    if success:
        file_info = result
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        emit('receive_private_file',
             {'fileinfo': file_info, 'user': username, 'timestamp': timestamp},
             room=chat_room, skip_sid=request.sid)
        # Registrar el envío en el log de mensajes privados
        log_dir = os.path.join(current_app.root_path, 'logs', 'Mensajes', 'Privados')
        try:
            os.makedirs(log_dir, exist_ok=True)
            print("Directorio de log verificado:", log_dir)
        except Exception as e:
            print("Error creando el directorio de log:", e)
        # Sanitizamos el valor de chat_room para evitar caracteres problemáticos
        sanitized_chat_room = "".join(c for c in chat_room if c.isalnum() or c in ('-', '_'))
        log_file_path = os.path.join(log_dir, f'{sanitized_chat_room}.log')
        try:
            with open(log_file_path, 'a', encoding='utf-8') as log_file:
                log_entry = (f"{username}: [FILE] envió el archivo "
                             f"<a href=\"{file_info['filepath']}\" download=\"{file_info['filename']}\">"
                             f"{file_info['filename']}</a> {timestamp}\n")
                log_file.write(log_entry)
                log_file.flush()
                print("Archivo logueado:", log_entry)
        except Exception as e:
            print("Error escribiendo en el archivo de log:", e)
    else:
        emit('private_file_upload_error',
             {'message': result, 'filename': filename},
             to=request.sid)
        
@socketio.on('connect', namespace='/node')
def handle_connect():
    node_id = request.args.get('node_id')
    username = request.args.get('username')
    if not node_id or not username:
        return False
    join_room(node_id)
    connected_users[request.sid] = {'username': username, 'node_id': str(node_id)}
    users_in_node = list(set([user['username'] for user in connected_users.values() if user['node_id'] == str(node_id)]))
    emit('update_users', users_in_node, room=node_id)
    print(f"{username} connected to node {node_id}")


@socketio.on('disconnect', namespace='/node')
def handle_disconnect():
    if request.sid in connected_users:
        node_id = connected_users[request.sid]['node_id']
        username = connected_users[request.sid]['username']
        leave_room(node_id)
        del connected_users[request.sid]
        users_in_node = [user['username'] for user in connected_users.values() if user['node_id'] == str(node_id)]
        emit('update_users', users_in_node, room=node_id)
        print(f"{username} disconnected from node {node_id}")
        if not audio_transmissions.get(node_id, False):
            with recording_lock:
                if node_id in recording_states:
                    del recording_states[node_id]


@socketio.on('user_connected', namespace='/')
def handle_user_connected(data):
    username = data['user']
    node_id = data['node_id']
    join_room(node_id)
    connected_users[request.sid] = {'username': username, 'node_id': str(node_id)}
    users_in_node = list(set([user['username'] for user in connected_users.values() if user['node_id'] == str(node_id)]))
    emit('update_users', users_in_node, room=node_id)
    print(f"Usuario {username} conectado al nodo {node_id}")


@socketio.on('send_message', namespace='/node')
def handle_message(data):
    timestamp = datetime.now().strftime('%d-%m-%Y %H:%M')
    message = f"{data['user']}: {data['data']} {timestamp}\n"
    node_id = data['node_id']
    node = Node.query.get(node_id)
    if node:
        log_message(message, node.name)
        emit('broadcast_message', {'data': data['data'], 'user': data['user'], 'timestamp': timestamp}, room=node_id)
        print(f"Broadcast message: {data['data']} from {data['user']} in node {node_id} at {timestamp}")
    else:
        print(f"Node with ID {node_id} not found.")


def log_message(message, node_name):
    log_dir = os.path.join(current_app.root_path, 'logs', 'Mensajes')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'{node_name}.log')
    with open(log_file, 'a') as file:
        file.write(message)


@socketio.on('transmit_audio', namespace='/node')
def handle_transmit_audio(data):
    node_id = data['node_id']
    audio = data['audio']
    user = data['user']
    mime_type = data.get('mimeType', 'audio/wav')  # Fallback a 'audio/wav' si no se envía
    emit('receive_audio',
         {'audio': audio, 'user': user, 'mimeType': mime_type},
         room=node_id,
         skip_sid=request.sid)
    print(f"[SERVER] Broadcast audio from {user} in node {node_id}. Audio data length: {len(audio)} bytes. MIME: {mime_type}")
    logging.info(f"Audio transmitted by {user} in node {node_id}.")
    audio_transmission_received_data = {
        'node_id': node_id,
        'user': user,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    socketio.emit('audio_transmission_received', audio_transmission_received_data, namespace='/audio_events')
    save_audio(node_id, audio, user)


@socketio.on('audio_start', namespace='/node')
def handle_audio_start(data):
    node_id = data['node_id']
    user = data['user']
    if node_id not in audio_transmissions or not audio_transmissions[node_id]:
        audio_transmissions[node_id] = True
        emit('audio_start', {'user': user}, room=node_id)
        logging.info(f"Audio transmission started by {user} in node {node_id}.")
        print(f"Emit event: global_audio_start | User: {user} | Node ID: {node_id} | Event: start")
        emit('global_audio_start', {'user': user, 'node_id': str(node_id), 'event': 'start'}, broadcast=True)
        with recording_lock:
            recording_states[node_id] = {
                'current_audio_file': None,
                'current_log_file': None,
                'start_time': datetime.now()
            }
        transmission_started_data = {'node_id': node_id, 'user': user}
        socketio.emit('transmission_started', transmission_started_data, namespace='/audio_events')
    else:
        emit('audio_busy', {'user': user}, to=request.sid)
        print(f"User {user} attempted to start audio transmission in node {node_id}, but it's already in use.")
        logging.info(f"User {user} attempted to start audio transmission in node {node_id}, but it's already in use.")


@socketio.on('audio_end', namespace='/node')
def handle_audio_end(data):
    node_id = data['node_id']
    user = data['user']
    if node_id in audio_transmissions and audio_transmissions[node_id]:
        audio_transmissions[node_id] = False
        emit('audio_end', {'user': user}, room=node_id)
        logging.info(f"Audio transmission ended by {user} in node {node_id}.")
        print(f"Emit event: global_audio_end | User: {user} | Node ID: {node_id} | Event: end")
        emit('global_audio_end', {'user': user, 'node_id': str(node_id), 'event': 'end'}, broadcast=True)
        with recording_lock:
            if node_id in recording_states:
                recording_states[node_id]['end_time'] = datetime.now()
                duration = (recording_states[node_id]['end_time'] - recording_states[node_id]['start_time']).total_seconds()
                transmission_ended_data = {
                    'node_id': node_id,
                    'user': user,
                    'duration': duration
                }
                socketio.emit('transmission_ended', transmission_ended_data, namespace='/audio_events')
    else:
        print(f"User {user} attempted to end audio transmission in node {node_id}, but no active transmission found.")
        logging.info(f"User {user} attempted to end audio transmission in node {node_id}, but no active transmission found.")


@socketio.on('connect', namespace='/private')
def handle_private_connect():
    chat_room = request.args.get('chat_room')
    username = request.args.get('username')
    if not chat_room or not username:
        return False
    join_room(chat_room)
    print(f"{username} connected to private chat room {chat_room}")


@socketio.on('disconnect', namespace='/private')
def handle_private_disconnect():
    chat_room = request.args.get('chat_room')
    username = request.args.get('username')
    if not chat_room or not username:
        return False
    leave_room(chat_room)
    print(f"{username} disconnected from private chat room {chat_room}")


@socketio.on('send_message', namespace='/node')
def handle_message(data):
    timestamp = datetime.now().strftime('%d-%m-%Y %H:%M')
    message = f"{data['user']}: {data['data']} {timestamp}\n"
    node_id = data['node_id']
    node = Node.query.get(node_id)
    if node:
        log_message(message, node.name)
        emit('broadcast_message', {'data': data['data'], 'user': data['user'], 'timestamp': timestamp}, room=node_id)
        print(f"Broadcast message: {data['data']} from {data['user']} in node {node_id} at {timestamp}")
    else:
        print(f"Node with ID {node_id} not found.")


def log_message(message, node_name):
    log_dir = os.path.join(current_app.root_path, 'logs', 'Mensajes')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'{node_name}.log')
    with open(log_file, 'a') as file:
        file.write(message)

@socketio.on('send_private_message', namespace='/private')
def handle_private_message(data):
    timestamp = datetime.now().strftime('%d-%m-%Y %H:%M')
    message = f"{data['user']}: {data['data']} {timestamp}\n"
    chat_room = data['chat_room']
    # Guardar en el log de mensajes privados
    log_dir = os.path.join(current_app.root_path, 'logs', 'Mensajes', 'Privados')
    os.makedirs(log_dir, exist_ok=True)
    log_file_path = os.path.join(log_dir, f'{chat_room}.log')
    with open(log_file_path, 'a', encoding='utf-8') as log_file:
        log_file.write(message)
    # Emitir el mensaje a la sala
    emit('broadcast_private_message', {'data': data['data'], 'user': data['user'], 'timestamp': timestamp}, room=chat_room)
    
@socketio.on('transmit_private_file', namespace='/private')
def handle_transmit_private_file(data):
    file_base64 = data.get('fileData')
    filename = data.get('filename')
    username = data.get('user')
    chat_room = data.get('chat_room')
    if not file_base64 or not filename or not username or not chat_room:
        emit('private_file_upload_error', {'message': 'Datos incompletos para la carga de archivo.', 'filename': filename}, to=request.sid)
        return
    success, result = save_file(chat_room, file_base64, filename, username)
    if success:
        file_info = result
        emit('receive_private_file', {'fileinfo': file_info, 'user': username}, room=chat_room, skip_sid=request.sid)
    else:
        emit('private_file_upload_error', {'message': result, 'filename': filename}, to=request.sid)


@socketio.on('transmit_private_audio', namespace='/private')
def handle_private_audio(data):
    chat_room = data['chat_room']
    audio = data['audio']
    emit('receive_private_audio', {'audio': audio, 'user': data['user']}, room=chat_room)
    print(f"Broadcast private audio from {data['user']} in chat room {chat_room}")


@socketio.on('get_all_connected_users')
def handle_get_all_connected_users():
    emit_connected_users_update_all_nodes()


def emit_connected_users_update_all_nodes():
    # Recolectar los usuarios conectados agrupados por node_id (manteniendo el ID original)
    connected_users_by_node = {}
    for sid, user_info in connected_users.items():
        node_id = user_info['node_id']
        if node_id not in connected_users_by_node:
            connected_users_by_node[node_id] = set()
        connected_users_by_node[node_id].add(user_info['username'])
    
    # Construir el diccionario final: la clave es el node_id y el valor es un objeto con
    # el nombre del nodo y la lista de usuarios (sin duplicados)
    final = {}
    for node_id, users_set in connected_users_by_node.items():
        try:
            # Convertir el node_id a entero para la consulta, ya que normalmente es numérico
            node_obj = Node.query.get(int(node_id))
        except Exception as e:
            node_obj = None
        if node_obj:
            final[node_id] = {'node_name': node_obj.name, 'users': list(users_set)}
        else:
            final[node_id] = {'node_name': node_id, 'users': list(users_set)}
    
    socketio.emit('update_all_users', final)

# --- AÑADIDO:  Handler para el evento 'audio_received' ---
@socketio.on('audio_received', namespace='/node')
def handle_audio_received(data):
    emit('audio_received', data, room=request.args.get('node_id'), skip_sid=request.sid)

class MonitorNamespace(Namespace):
    def on_connect(self):
        print('Cliente conectado al namespace /monitor')
        emit('monitor_response', {'data': 'Conectado al namespace /monitor'})
    def on_disconnect(self):
        print('Cliente desconectado del namespace /monitor')
    def on_monitor_metrics(self, data):
        print(f"Métricas recibidas: {data}")
        emit('monitor_response', {'message': 'Métricas recibidas correctamente'})


socketio.on_namespace(MonitorNamespace('/monitor'))


class AudioEventsNamespace(Namespace):
    def on_connect(self):
        print('Cliente conectado al namespace /audio_events')
    def on_disconnect(self):
        print('Cliente desconectado del namespace /audio_events')


socketio.on_namespace(AudioEventsNamespace('/audio_events'))


if __name__ == '__main__':
    cert_path = '/etc/letsencrypt/live/patagoniaservers.com.ar/fullchain.pem'
    key_path = '/etc/letsencrypt/live/patagoniaservers.com.ar/privkey.pem'
    ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    ssl_context.load_cert_chain(certfile=cert_path, keyfile=key_path)
    eventlet.wsgi.server(
        eventlet.wrap_ssl(eventlet.listen(('0.0.0.0', 5000)),
                          certfile=cert_path,
                          keyfile=key_path,
                          server_side=True),
        app
    )