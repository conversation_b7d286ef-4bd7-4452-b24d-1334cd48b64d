<!-- app/templates/audit/log_detail.html -->
{% extends "base.html" %}

{% block title %}Detalle de Log #{{ log.id if log else 'Error' }}{% endblock %}

{% block extra_css %}
<style>
    .log-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .info-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .action-icon {
        font-size: 3rem;
        margin-bottom: 15px;
    }
    
    .action-CREATE { color: #28a745; }
    .action-UPDATE { color: #ffc107; }
    .action-DELETE { color: #dc3545; }
    .action-LOGIN { color: #17a2b8; }
    .action-LOGOUT { color: #6c757d; }
    .action-VIEW { color: #007bff; }
    .action-EXPORT { color: #6f42c1; }
    
    .field-comparison {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;
    }
    
    .field-name {
        font-weight: bold;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .value-old {
        background: #f8d7da;
        color: #721c24;
        padding: 5px 10px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.9rem;
    }
    
    .value-new {
        background: #d4edda;
        color: #155724;
        padding: 5px 10px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.9rem;
    }
    
    .value-unchanged {
        background: #e2e3e5;
        color: #383d41;
        padding: 5px 10px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.9rem;
    }
    
    .json-viewer {
        background: #2d3748;
        color: #e2e8f0;
        padding: 15px;
        border-radius: 6px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        overflow-x: auto;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .metadata-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .metadata-item {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #007bff;
    }
    
    .metadata-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .metadata-value {
        font-size: 1rem;
        color: #495057;
        font-weight: 500;
    }
    
    .timeline-item {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 5px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #007bff;
    }
    
    .timeline-item::after {
        content: '';
        position: absolute;
        left: 14px;
        top: 15px;
        width: 2px;
        height: calc(100% + 5px);
        background: #e9ecef;
    }
    
    .timeline-item:last-child::after {
        display: none;
    }
    
    .copy-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }
    
    .copy-btn:hover {
        opacity: 1;
    }
    
    @media (max-width: 768px) {
        .log-header {
            padding: 20px;
        }
        
        .metadata-grid {
            grid-template-columns: 1fr;
        }
        
        .json-viewer {
            font-size: 0.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> {{ error }}
    </div>
    {% elif log %}
    
    <!-- Header del Log -->
    <div class="log-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <i class="{{ log.get_action_icon() }} action-icon action-{{ log.action }}"></i>
            </div>
            <div class="col-md-8">
                <h2>Log de Auditoría #{{ log.id }}</h2>
                <h4>{{ log.get_readable_description() }}</h4>
                <p class="mb-0">
                    <i class="fas fa-user"></i> {{ log.username }} • 
                    <i class="fas fa-clock"></i> {{ log.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}
                    {% if log.user_ip %}
                    • <i class="fas fa-map-marker-alt"></i> {{ log.user_ip }}
                    {% endif %}
                </p>
            </div>
            <div class="col-md-2 text-center">
                <span class="badge badge-{{ log.get_action_color() }} badge-lg p-3">
                    {{ log.action }}
                </span>
            </div>
        </div>
    </div>
    
    <!-- Navegación -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('audit.logs') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Logs
            </a>
            <a href="{{ url_for('audit.dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-line"></i> Dashboard
            </a>
            <button onclick="copyLogData()" class="btn btn-outline-info">
                <i class="fas fa-copy"></i> Copiar Datos
            </button>
        </div>
    </div>
    
    <div class="row">
        <!-- Información Principal -->
        <div class="col-md-8">
            <!-- Metadatos -->
            <div class="info-card">
                <h5><i class="fas fa-info-circle"></i> Información General</h5>
                <div class="metadata-grid">
                    <div class="metadata-item">
                        <div class="metadata-label">ID del Log</div>
                        <div class="metadata-value">#{{ log.id }}</div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-label">Usuario</div>
                        <div class="metadata-value">
                            <i class="fas fa-user"></i> {{ log.username }}
                            {% if log.user_id %}
                            <small class="text-muted">(ID: {{ log.user_id }})</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-label">Acción</div>
                        <div class="metadata-value">
                            <span class="badge badge-{{ log.get_action_color() }}">{{ log.action }}</span>
                        </div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-label">Tabla Afectada</div>
                        <div class="metadata-value">
                            <code>{{ log.table_name }}</code>
                            {% if log.record_id %}
                            <small class="text-muted">(Registro ID: {{ log.record_id }})</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-label">Módulo</div>
                        <div class="metadata-value">
                            {% if log.module %}
                            <span class="badge badge-secondary">{{ log.module }}</span>
                            {% else %}
                            <span class="text-muted">No especificado</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="metadata-item">
                        <div class="metadata-label">Dirección IP</div>
                        <div class="metadata-value">
                            {% if log.user_ip %}
                            <code>{{ log.user_ip }}</code>
                            {% else %}
                            <span class="text-muted">No registrada</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cambios en Campos -->
            {% if log.action == 'UPDATE' and log.get_changed_fields_list() %}
            <div class="info-card">
                <h5><i class="fas fa-edit"></i> Campos Modificados</h5>
                {% set old_values = log.get_old_values_dict() %}
                {% set new_values = log.get_new_values_dict() %}
                
                {% for field in log.get_changed_fields_list() %}
                <div class="field-comparison">
                    <div class="field-name">{{ field }}</div>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">Valor anterior:</small>
                            <div class="value-old">
                                {{ old_values.get(field, 'N/A') }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">Valor nuevo:</small>
                            <div class="value-new">
                                {{ new_values.get(field, 'N/A') }}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Datos Completos -->
            {% if log.old_values or log.new_values %}
            <div class="info-card">
                <h5><i class="fas fa-database"></i> Datos Completos</h5>
                
                {% if log.old_values %}
                <h6 class="mt-3">Valores Anteriores:</h6>
                <div class="position-relative">
                    <button class="btn btn-sm btn-outline-secondary copy-btn" onclick="copyToClipboard('old-values')">
                        <i class="fas fa-copy"></i>
                    </button>
                    <div class="json-viewer" id="old-values">{{ log.old_values }}</div>
                </div>
                {% endif %}
                
                {% if log.new_values %}
                <h6 class="mt-3">Valores Nuevos:</h6>
                <div class="position-relative">
                    <button class="btn btn-sm btn-outline-secondary copy-btn" onclick="copyToClipboard('new-values')">
                        <i class="fas fa-copy"></i>
                    </button>
                    <div class="json-viewer" id="new-values">{{ log.new_values }}</div>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
        
        <!-- Panel Lateral -->
        <div class="col-md-4">
            <!-- Contexto Temporal -->
            <div class="info-card">
                <h6><i class="fas fa-clock"></i> Contexto Temporal</h6>
                <div class="timeline-item">
                    <strong>{{ log.timestamp.strftime('%H:%M:%S') }}</strong>
                    <br><small class="text-muted">{{ log.get_readable_description() }}</small>
                </div>
                
                <!-- Logs relacionados (mismo usuario, misma hora) -->
                {% if related_logs %}
                {% for related in related_logs %}
                <div class="timeline-item">
                    <strong>{{ related.timestamp.strftime('%H:%M:%S') }}</strong>
                    <br><small class="text-muted">{{ related.get_readable_description() }}</small>
                    <br><a href="{{ url_for('audit.log_detail', log_id=related.id) }}" class="btn btn-sm btn-outline-primary">Ver</a>
                </div>
                {% endfor %}
                {% endif %}
            </div>
            
            <!-- Información del Usuario -->
            {% if log.user %}
            <div class="info-card">
                <h6><i class="fas fa-user"></i> Información del Usuario</h6>
                <p><strong>Username:</strong> {{ log.user.username }}</p>
                <p><strong>Email:</strong> {{ log.user.email or 'No especificado' }}</p>
                <p><strong>Rol:</strong> 
                    <span class="badge badge-info">{{ log.user.role }}</span>
                </p>
                <p><strong>Estado:</strong> 
                    <span class="badge badge-{{ 'success' if log.user.is_active else 'danger' }}">
                        {{ 'Activo' if log.user.is_active else 'Inactivo' }}
                    </span>
                </p>
                <a href="{{ url_for('users.edit_user', user_id=log.user.id) }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit"></i> Ver Usuario
                </a>
            </div>
            {% endif %}
            
            <!-- Estadísticas Relacionadas -->
            <div class="info-card">
                <h6><i class="fas fa-chart-bar"></i> Estadísticas</h6>
                <p><strong>Acciones del usuario hoy:</strong> 
                    <span class="badge badge-primary">{{ user_actions_today or 0 }}</span>
                </p>
                <p><strong>Acciones en esta tabla:</strong> 
                    <span class="badge badge-info">{{ table_actions_count or 0 }}</span>
                </p>
                {% if log.record_id %}
                <p><strong>Cambios en este registro:</strong> 
                    <span class="badge badge-warning">{{ record_changes_count or 0 }}</span>
                </p>
                {% endif %}
            </div>
            
            <!-- Acciones -->
            <div class="info-card">
                <h6><i class="fas fa-tools"></i> Acciones</h6>
                <div class="d-grid gap-2">
                    <button onclick="copyLogData()" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-copy"></i> Copiar Datos
                    </button>
                    <a href="{{ url_for('audit.logs', user=log.username) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-filter"></i> Ver Logs del Usuario
                    </a>
                    <a href="{{ url_for('audit.logs', table=log.table_name) }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-table"></i> Ver Logs de la Tabla
                    </a>
                    {% if log.record_id %}
                    <a href="{{ url_for('audit.logs', table=log.table_name, record_id=log.record_id) }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-history"></i> Historial del Registro
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">Log no encontrado</h4>
        <p class="text-muted">El log solicitado no existe o no tienes permisos para verlo</p>
        <a href="{{ url_for('audit.logs') }}" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Volver a Logs
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Función para copiar contenido al portapapeles
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        // Mostrar feedback visual
        const btn = element.parentElement.querySelector('.copy-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');
        
        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}

// Función para copiar todos los datos del log
function copyLogData() {
    const logData = {
        id: {{ log.id }},
        timestamp: '{{ log.timestamp.isoformat() }}',
        user: '{{ log.username }}',
        action: '{{ log.action }}',
        table: '{{ log.table_name }}',
        record_id: {{ log.record_id or 'null' }},
        module: '{{ log.module or '' }}',
        description: '{{ log.get_readable_description() }}',
        ip: '{{ log.user_ip or '' }}',
        {% if log.old_values %}
        old_values: {{ log.old_values|safe }},
        {% endif %}
        {% if log.new_values %}
        new_values: {{ log.new_values|safe }},
        {% endif %}
        changed_fields: {{ log.changed_fields or '[]'|safe }}
    };
    
    const jsonString = JSON.stringify(logData, null, 2);
    
    navigator.clipboard.writeText(jsonString).then(function() {
        alert('Datos del log copiados al portapapeles');
    });
}

// Formatear JSON en los viewers
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.json-viewer').forEach(function(element) {
        try {
            const jsonData = JSON.parse(element.textContent);
            element.textContent = JSON.stringify(jsonData, null, 2);
        } catch (e) {
            // Si no es JSON válido, mantener el texto original
        }
    });
});
</script>
{% endblock %}
