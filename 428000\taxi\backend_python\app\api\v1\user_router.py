from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List

from app.api import deps
from app.schemas import user_schema
from app.services import user_service
from app.models.user import User, RoleEnum

router = APIRouter()

@router.post("/", status_code=status.HTTP_201_CREATED)
def create_user_endpoint(
    user_in: user_schema.UserCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    # Verificar si el usuario actual es administrador
    is_admin = False
    try:
        # Verificar si el usuario tiene rol de administrador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'administrador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})

        is_admin = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de administrador: {e}")

    if not is_admin:
        raise HTTPException(
            status_code=403,
            detail="Solo los administradores pueden crear usuarios"
        )

    db_user = user_service.get_user_by_email(db, email=user_in.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Por defecto, si no se especifica un rol al crear, asignarle 'USUARIO' (pasajero)
    roles_to_assign = user_in.roles if user_in.roles else ["USUARIO"]

    # Crear una copia del objeto sin los roles para evitar problemas con el enum
    user_in_dict = user_in.dict(exclude={"roles"})
    # Asegurarse de que el objeto UserCreate no requiera roles
    user_in_clean = user_schema.UserCreate(**user_in_dict)

    # Crear el usuario
    user = user_service.create_user(db=db, user_in=user_in_clean)

    # Asignar roles manualmente
    try:
        for role_name in roles_to_assign:
            # Obtener el ID del rol
            sql_query = text("""
                SELECT id FROM roles WHERE UPPER(name::text) = :role_name
            """)
            role_query = db.execute(sql_query, {"role_name": role_name})

            role_result = role_query.first()
            if role_result:
                role_id = role_result[0]
                # Asociar el rol al usuario
                sql_query = text("""
                    INSERT INTO user_roles_association (user_id, role_id)
                    VALUES (:user_id, :role_id)
                """)
                db.execute(sql_query, {"user_id": user.id, "role_id": role_id})

        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error al asignar roles: {e}")

    return user

@router.get("/me", response_model=user_schema.User)
async def read_users_me(
    current_user: User = Depends(deps.get_current_active_user)
):
    return current_user

# Ejemplo de ruta protegida por rol especifico
@router.get("/admin-only", response_model=user_schema.User)
async def admin_only_route(
    current_user: User = Depends(deps.get_current_admin_user)
):
    return {"message": f"Hello admin {current_user.email}"}

@router.get("/")
def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    # Verificar si el usuario actual es administrador
    is_admin = False
    try:
        # Verificar si el usuario tiene rol de administrador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'administrador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})

        is_admin = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de administrador: {e}")

    if not is_admin:
        raise HTTPException(
            status_code=403,
            detail="Solo los administradores pueden listar todos los usuarios"
        )

    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.get("/{user_id}")
def read_user(
    user_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    # Verificar si el usuario actual es administrador o es el mismo usuario
    is_admin = False
    try:
        # Verificar si el usuario tiene rol de administrador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'administrador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})

        is_admin = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de administrador: {e}")

    is_same_user = current_user.id == user_id

    if not (is_admin or is_same_user):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos suficientes para acceder a esta información"
        )

    user = user_service.get_user(db, user_id=user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="Usuario no encontrado")
    return user

@router.put("/{user_id}")
def update_user(
    user_id: int,
    user_in: user_schema.UserUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    # Verificar si el usuario actual es administrador o es el mismo usuario
    is_admin = False
    try:
        # Verificar si el usuario tiene rol de administrador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'administrador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})

        is_admin = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de administrador: {e}")

    is_same_user = current_user.id == user_id

    if not (is_admin or is_same_user):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos suficientes para modificar este usuario"
        )

    # Si no es administrador, no puede cambiar roles
    if not is_admin and user_in.roles is not None:
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos suficientes para cambiar roles"
        )

    user = user_service.get_user(db, user_id=user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="Usuario no encontrado")

    # Si se están actualizando los roles, manejarlos manualmente
    roles_to_update = None
    if user_in.roles is not None and is_admin:
        roles_to_update = user_in.roles
        # Crear una copia del objeto sin los roles para evitar problemas con el enum
        user_in_dict = user_in.dict(exclude={"roles"})
        # Asegurarse de que el objeto UserUpdate no requiera roles
        user_in = user_schema.UserUpdate(**user_in_dict)

    # Actualizar el usuario
    updated_user = user_service.update_user(db, db_user=user, user_in=user_in)

    # Si hay roles para actualizar, hacerlo manualmente
    if roles_to_update and is_admin:
        try:
            # Primero eliminar todos los roles actuales
            sql_query = text("""
                DELETE FROM user_roles_association
                WHERE user_id = :user_id
            """)
            db.execute(sql_query, {"user_id": user.id})

            # Luego agregar los nuevos roles
            for role_name in roles_to_update:
                # Obtener el ID del rol
                sql_query = text("""
                    SELECT id FROM roles WHERE UPPER(name::text) = :role_name
                """)
                role_query = db.execute(sql_query, {"role_name": role_name})

                role_result = role_query.first()
                if role_result:
                    role_id = role_result[0]
                    # Asociar el rol al usuario
                    sql_query = text("""
                        INSERT INTO user_roles_association (user_id, role_id)
                        VALUES (:user_id, :role_id)
                    """)
                    db.execute(sql_query, {"user_id": user.id, "role_id": role_id})

            db.commit()
        except Exception as e:
            db.rollback()
            print(f"Error al actualizar roles: {e}")

    return updated_user

@router.delete("/{user_id}")
def delete_user(
    user_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    # Verificar si el usuario actual es administrador
    is_admin = False
    try:
        # Verificar si el usuario tiene rol de administrador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'administrador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})

        is_admin = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de administrador: {e}")

    if not is_admin:
        raise HTTPException(
            status_code=403,
            detail="Solo los administradores pueden eliminar usuarios"
        )

    user = user_service.get_user(db, user_id=user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="Usuario no encontrado")

    # No permitir eliminar al usuario administrador principal
    if user.id == 1 or user.is_superuser:
        raise HTTPException(
            status_code=400,
            detail="No se puede eliminar al usuario administrador principal"
        )

    # Eliminar manualmente las asociaciones de roles
    try:
        sql_query = text("""
            DELETE FROM user_roles_association
            WHERE user_id = :user_id
        """)
        db.execute(sql_query, {"user_id": user.id})
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error al eliminar roles del usuario: {e}")

    deleted_user = user_service.delete_user(db, user_id=user_id)
    return deleted_user
