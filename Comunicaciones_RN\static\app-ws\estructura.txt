﻿cliente/
├── cliente.py                # Archivo principal que inicia el servidor Flask y las funcionalidades
├── config.py                 # Gestión de la configuración desde el archivo JSON
├── icon_tray.py              # Minimiza la aplicación
├── monitor.py                # Monitoriza el origen de la comunicación y decide cómo comportarse
├── requirements.txt          # Dependencias del proyecto
├── ptt_control.py            # Controla el estado del PTT
├── vhf_dig.py                # Captura y envía audio del micrófono (VHF a digital)
├── dig_vhf.py                # Escucha eventos del servidor y activa/desactiva PTT (digital a VHF)
├── static/
│   └── js/
│       ├── vhf-dig.js        # Script JS para VHF a digital (envío de audio)
│       ├── dig-vhf.js        # Script JS para digital a VHF (recepción de audio y control de PTT)
│       └── index.js          # Script JS para mostrar usuarios conectados e informacion de nodo
├── templates/
│   └── index.html            # Página web principal para interacción con el servidor Flask
│   └── config_form.html      # Página web principal para crear el archivo config.json si no existe
└── config.json               # Archivo de configuración del cliente
