<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Usuarios</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
  <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
  <style>
    .body-background::before {
      background: url('{{ url_for('static', filename='RNCom.png') }}') no-repeat center center;
    }
  </style>
</head>
<body class="body-background">
  <header>
    <h1>Administracion de Usuarios</h1>
    <a href="{{ url_for('admin.create_user') }}">Crear Nuevo Usuario</a>
  </header>
  <main>
    <ul>
      {% for user in users %}
        <li>
          {{ user.username }} ({{ user.role }}) - <a href="{{ url_for('admin.edit_user', user_id=user.id) }}">Edit</a>
        </li>
      {% endfor %}
    </ul>
    <a href="{{ url_for('views.dashboard') }}">Volver al Dashboard</a>
  </main>
  <footer>
    <p>© 2024 Tu Sitio Web</p>
  </footer>
</body>
</html>
