# /home/<USER>/rn_rural/backend/apps/core/views_map.py
# ----------------------------------------
from django.http import JsonResponse
# from django.contrib.gis.geos import Point # No se usa directamente aquí
# from django.contrib.gis.measure import D # No se usa directamente aquí
from django.contrib.auth.decorators import login_required
# Cambia la importación al nombre correcto del modelo
from apps.locations.models import BrigadaMovilLocation, UbicacionUsuario # <--- CORREGIDO AQUÍ
from apps.incidents.models import Incidencia
from .route_service import calculate_route_with_openrouteservice
import logging
import math

logger = logging.getLogger(__name__)

@login_required
def operador_map_data(request):
    # Incidencias abiertas
    incs = Incidencia.objects.filter(
        estado__in=['NUEVA','DERIVADA_BRIGADA']
    ).values('id', 'ubicacion_incidencia', 'estado')

    # Brigadas móviles - Usa el nombre correcto del modelo
    brigs = BrigadaMovilLocation.objects.all().values( # <--- CORREGIDO AQUÍ
        'brigada_usuario__username',
        'ubicacion_actual',
        'ultima_actualizacion'
    )

    return JsonResponse({
        "incidencias": [
            {"id": i["id"],
             "lat": i["ubicacion_incidencia"].y if i["ubicacion_incidencia"] else None, # Añadir chequeo por si es None
             "lng": i["ubicacion_incidencia"].x if i["ubicacion_incidencia"] else None, # Añadir chequeo por si es None
             "estado": i["estado"]}
            for i in incs
        ],
        "brigadas": [
            {"nombre": b["brigada_usuario__username"],
             "lat": b["ubicacion_actual"].y if b["ubicacion_actual"] else None, # Añadir chequeo
             "lng": b["ubicacion_actual"].x if b["ubicacion_actual"] else None, # Añadir chequeo
             "ts": b["ultima_actualizacion"].isoformat() if b["ultima_actualizacion"] else None} # Añadir chequeo
            for b in brigs
        ]
    })

@login_required
def brigada_map_data(request):
    # Obtener todas las incidencias asignadas a la brigada
    incidencias = Incidencia.objects.filter(
        brigada_asignada=request.user,
        estado__in=['DERIVADA_BRIGADA', 'EN_PROCESO_BRIGADA']
    ).order_by('fecha_creacion')  # Ordenar por fecha de creación (las más antiguas primero)

    if not incidencias.exists():
        return JsonResponse({"detail": "sin_incidencia"}, status=200)

    # Obtener la ubicación de la brigada (si está disponible)
    ubicacion_brigada = None
    try:
        ubicacion_obj = UbicacionUsuario.objects.get(usuario=request.user)
        if ubicacion_obj.posicion_actual:
            ubicacion_brigada = {
                "lat": ubicacion_obj.posicion_actual.y,
                "lng": ubicacion_obj.posicion_actual.x
            }
    except UbicacionUsuario.DoesNotExist:
        pass

    # Procesar todas las incidencias
    incidencias_data = []
    for inc in incidencias:
        # Añadir chequeo por si ubicacion_incidencia es None
        lat = inc.ubicacion_incidencia.y if inc.ubicacion_incidencia else None
        lng = inc.ubicacion_incidencia.x if inc.ubicacion_incidencia else None

        # Obtener la ubicación del usuario que reportó la incidencia (si está disponible)
        ubicacion_usuario = None
        try:
            ubicacion_obj = UbicacionUsuario.objects.get(usuario=inc.usuario_reporta)
            if ubicacion_obj.posicion_actual:
                ubicacion_usuario = {
                    "lat": ubicacion_obj.posicion_actual.y,
                    "lng": ubicacion_obj.posicion_actual.x
                }
        except UbicacionUsuario.DoesNotExist:
            pass

        # Calcular la ruta si tenemos ubicación de la brigada y de la incidencia
        ruta = None
        distancia = None
        duracion = None

        if ubicacion_brigada and lat and lng:
            from .route_service import calculate_route_with_openrouteservice

            # Coordenadas en formato (longitud, latitud) para OpenRouteService
            start_coords = (ubicacion_brigada["lng"], ubicacion_brigada["lat"])
            end_coords = (lng, lat)

            route_data = calculate_route_with_openrouteservice(start_coords, end_coords)

            if route_data:
                ruta = route_data['coordinates']
                distancia = route_data['distance']
                duracion = route_data['duration']

        # Añadir la incidencia a la lista
        incidencias_data.append({
            "id": inc.id,
            "lat": lat,
            "lng": lng,
            "estado": inc.estado,
            "descripcion": inc.descripcion_texto or "Sin descripción disponible",
            "fecha_creacion": inc.fecha_creacion.strftime("%d/%m/%Y %H:%M"),
            "usuario_reporta": inc.usuario_reporta.username,
            "ubicacion_usuario": ubicacion_usuario,
            "ruta": {
                "coordenadas": ruta,
                "distancia": distancia,  # en metros
                "duracion": duracion     # en segundos
            }
        })

    # Devolver todas las incidencias y la ubicación de la brigada
    return JsonResponse({
        "incidencias": incidencias_data,
        "brigada": {
            "ubicacion": ubicacion_brigada
        },
        "incidencia_activa": incidencias_data[0] if incidencias_data else None  # La primera incidencia como activa por defecto
    })

@login_required
def actualizar_estado_incidencia(request):
    """
    Vista para actualizar el estado de una incidencia.
    Espera una solicitud POST con los siguientes parámetros:
    - incidencia_id: ID de la incidencia a actualizar
    - nuevo_estado: Nuevo estado de la incidencia
    """
    if request.method != 'POST':
        return JsonResponse({"error": "Método no permitido"}, status=405)

    # Verificar que el usuario sea una brigada
    if request.user.role != 'BRIGADA':
        return JsonResponse({"error": "No autorizado"}, status=403)

    # Obtener parámetros
    incidencia_id = request.POST.get('incidencia_id')
    nuevo_estado = request.POST.get('nuevo_estado')

    if not incidencia_id or not nuevo_estado:
        return JsonResponse({"error": "Parámetros incompletos"}, status=400)

    # Verificar que el nuevo estado sea válido
    estados_validos = [
        'EN_PROCESO_BRIGADA',
        'CERRADA_RESUELTA',
        'CERRADA_NO_RESUELTA'
    ]

    if nuevo_estado not in estados_validos:
        return JsonResponse({"error": "Estado no válido"}, status=400)

    try:
        # Obtener la incidencia
        incidencia = Incidencia.objects.get(id=incidencia_id, brigada_asignada=request.user)

        # Verificar que la transición de estado sea válida
        if incidencia.estado == 'DERIVADA_BRIGADA' and nuevo_estado == 'EN_PROCESO_BRIGADA':
            # Transición válida: DERIVADA_BRIGADA -> EN_PROCESO_BRIGADA
            pass
        elif incidencia.estado == 'EN_PROCESO_BRIGADA' and nuevo_estado in [
            'CERRADA_RESUELTA',
            'CERRADA_NO_RESUELTA'
        ]:
            # Transición válida: EN_PROCESO_BRIGADA -> CERRADA_RESUELTA o CERRADA_NO_RESUELTA
            pass
        else:
            return JsonResponse({"error": "Transición de estado no válida"}, status=400)

        # Actualizar el estado
        incidencia.estado = nuevo_estado
        incidencia.save()

        return JsonResponse({
            "success": True,
            "mensaje": f"Estado actualizado a {incidencia.get_estado_display()}",
            "incidencia_id": incidencia.id,
            "nuevo_estado": nuevo_estado,
            "nuevo_estado_display": incidencia.get_estado_display()
        })

    except Incidencia.DoesNotExist:
        return JsonResponse({"error": "Incidencia no encontrada o no asignada a esta brigada"}, status=404)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)

@login_required
def calcular_ruta(request):
    """
    Vista para calcular la ruta entre dos puntos.
    Espera una solicitud GET con los siguientes parámetros:
    - start_lat: Latitud del punto de inicio
    - start_lng: Longitud del punto de inicio
    - end_lat: Latitud del punto de destino
    - end_lng: Longitud del punto de destino
    """
    if request.method != 'GET':
        return JsonResponse({"error": "Método no permitido"}, status=405)

    # Obtener parámetros
    try:
        start_lat = float(request.GET.get('start_lat'))
        start_lng = float(request.GET.get('start_lng'))
        end_lat = float(request.GET.get('end_lat'))
        end_lng = float(request.GET.get('end_lng'))
    except (TypeError, ValueError):
        return JsonResponse({"error": "Parámetros inválidos"}, status=400)

    # Coordenadas en formato (longitud, latitud) para OpenRouteService
    start_coords = (start_lng, start_lat)
    end_coords = (end_lng, end_lat)

    logger.info(f"Calculando ruta desde {start_coords} hasta {end_coords}")

    try:
        # Intentar calcular la ruta usando OpenRouteService
        route_data = calculate_route_with_openrouteservice(start_coords, end_coords)

        if route_data:
            logger.info(f"Ruta calculada con éxito: {len(route_data['coordinates'])} puntos, {route_data['distance']} metros, {route_data['duration']} segundos")
            return JsonResponse({
                "success": True,
                "route": {
                    "coordinates": route_data['coordinates'],
                    "distance": route_data['distance'],
                    "duration": route_data['duration']
                }
            })
        else:
            logger.warning("No se pudo calcular la ruta con OpenRouteService")

            # Calcular distancia en línea recta como fallback
            # Convertir coordenadas a radianes
            lat1 = math.radians(start_lat)
            lon1 = math.radians(start_lng)
            lat2 = math.radians(end_lat)
            lon2 = math.radians(end_lng)

            # Fórmula de Haversine
            dlon = lon2 - lon1
            dlat = lat2 - lat1
            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            r = 6371000  # Radio de la Tierra en metros

            # Calcular distancia en metros
            distancia = c * r

            # Estimar duración (asumiendo velocidad promedio de 40 km/h)
            duracion = (distancia / 1000) / 40 * 3600  # Convertir a segundos

            # Crear coordenadas para una línea recta
            coordenadas = [
                (start_lat, start_lng),
                (end_lat, end_lng)
            ]

            logger.info(f"Calculada distancia en línea recta: {distancia} metros, {duracion} segundos")

            return JsonResponse({
                "success": True,
                "route": {
                    "coordinates": coordenadas,
                    "distance": distancia,
                    "duration": duracion,
                    "es_linea_recta": True
                }
            })
    except Exception as e:
        logger.error(f"Error al calcular la ruta: {str(e)}", exc_info=True)
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)