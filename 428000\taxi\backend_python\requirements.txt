fastapi
uvicorn[standard]
sqlalchemy
psycopg2-binary
alembic
pydantic[email]
python-jose[cryptography]
passlib[bcrypt]
python-dotenv
jinja2
python-multipart # Para formularios
# geoalchemy2 # Para tipos de datos espaciales con SQLAlchemy si los usas directamente
pydantic_settings
bcrypt
websockets
redis>=4.2.0
requests
polyline # Para decodificar/codificar rutas de Google Maps/OSRM
setuptools # Necesario para Python 3.12 (reemplaza distutils)