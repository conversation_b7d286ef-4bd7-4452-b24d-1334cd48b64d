from flask import Blueprint, render_template, redirect, url_for, request, flash
from flask_login import login_required, current_user
from extensions import db
from models import User, Node
from werkzeug.security import generate_password_hash
from flask_socketio import emit
from globals import connected_users

admin_bp = Blueprint('admin', __name__)

# Opciones predefinidas
ROLES = ['admin', 'user_manager', 'password_manager', 'node_manager', 'standard_user']
ORGANISMOS = ['Policia', 'Agencia', 'Salud', 'Educacion', 'Ipross']

@admin_bp.route('/users')
@login_required
def list_users():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    users = User.query.all()
    return render_template('admin/users.html', users=users)

@admin_bp.route('/users/create', methods=['GET', 'POST'])
@login_required
def create_user():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    
    nodes = Node.query.all()
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        badge_number = request.form.get('badge_number')
        organization = request.form.get('organization')
        role = request.form.get('role')
        node_access = request.form.getlist('node_access')
        regional_units = request.form.getlist('regional_units')
        has_police_access = 'police_access' in request.form
        
        hashed_password = generate_password_hash(password, method='pbkdf2:sha256')
        new_user = User(
            username=username,
            password=hashed_password,
            first_name=first_name,
            last_name=last_name,
            badge_number=badge_number,
            organization=organization,
            role=role,
            node_access=",".join(node_access),
            regional_units=",".join(regional_units),
            has_police_access=has_police_access
        )
        db.session.add(new_user)
        db.session.commit()
        flash('User created successfully.')
        return redirect(url_for('admin.list_users'))
    return render_template('admin/create_user.html', roles=ROLES, organismos=ORGANISMOS, nodes=nodes)

@admin_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    
    user = User.query.get_or_404(user_id)
    nodes = Node.query.all()
    if request.method == 'POST':
        user.username = request.form.get('username')
        user.first_name = request.form.get('first_name')
        user.last_name = request.form.get('last_name')
        user.badge_number = request.form.get('badge_number')
        user.organization = request.form.get('organization')
        user.role = request.form.get('role')
        user.node_access = ",".join(request.form.getlist('node_access'))
        user.regional_units = ",".join(request.form.getlist('regional_units'))
        user.has_police_access = 'police_access' in request.form
        
        password = request.form.get('password')
        if password:
            user.password = generate_password_hash(password, method='pbkdf2:sha256')
        
        db.session.commit()
        flash('User updated successfully.')
        return redirect(url_for('admin.list_users'))
    return render_template('admin/edit_user.html', user=user, roles=ROLES, organismos=ORGANISMOS, nodes=nodes)

# editar y crear nodos
@admin_bp.route('/nodes')
@login_required
def list_nodes():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    nodes = Node.query.all()  # Get all nodes
    return render_template('admin/nodes.html', nodes=nodes)

@admin_bp.route('/nodes/create', methods=['GET', 'POST'])
@login_required
def create_node():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    
    if request.method == 'POST':
        name = request.form.get('name')
        parent_id = request.form.get('parent_id')
        new_node = Node(name=name, parent_id=parent_id)
        db.session.add(new_node)
        db.session.commit()
        flash('Node created successfully.')
        return redirect(url_for('admin.list_nodes'))
    
    parent_nodes = Node.query.all()
    return render_template('admin/create_node.html', parent_nodes=parent_nodes)

@admin_bp.route('/nodes/<int:node_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_node(node_id):
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))
    
    node = Node.query.get_or_404(node_id)
    if request.method == 'POST':
        node.name = request.form.get('name')
        node.parent_id = request.form.get('parent_id')
        db.session.commit()
        flash('Node updated successfully.')
        return redirect(url_for('admin.list_nodes'))
    
    parent_nodes = Node.query.all()
    return render_template('admin/edit_node.html', node=node, parent_nodes=parent_nodes)

@admin_bp.route('/connected_users')
@login_required
def connected_users():
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))

    # Crear un diccionario para almacenar usuarios conectados por nodo
    users_by_node = {}

    for user_info in connected_users.values():
        node_id = user_info['node_id']
        username = user_info['username']

        if node_id not in users_by_node:
            users_by_node[node_id] = []
        
        # Agregar el usuario al nodo correspondiente
        users_by_node[node_id].append({'username': username})

    # Depuración: Imprime los datos que se van a renderizar
    print("Datos para renderizar:", users_by_node)

    return render_template('admin/connected_users.html', connected_users_by_node=users_by_node)

@admin_bp.route('/disconnect_user/<sid>', methods=['POST'])
@login_required
def disconnect_user(sid):
    if current_user.role != 'admin':
        flash('Access denied.')
        return redirect(url_for('views.dashboard'))

    if sid in connected_users:
        node_id = connected_users[sid]['node_id']
        username = connected_users[sid]['username']
        del connected_users[sid]  # Elimina al usuario de la lista de conectados
        emit('update_users', [user['username'] for user in connected_users.values() if user['node_id'] == node_id], room=node_id)
        flash(f"User {username} has been disconnected.")
    else:
        flash(f"No user found with session ID {sid}.")
    
    return redirect(url_for('admin.connected_users'))
