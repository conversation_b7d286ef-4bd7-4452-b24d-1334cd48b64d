# ui_improvements.py
# Mejoras de UI/UX para Intranet IMPA

from flask import render_template, jsonify, request
from datetime import datetime, timedelta
import json

# 1. DASHBOARD MEJORADO CON ESTADÍSTICAS
class DashboardService:
    """Servicio para generar datos del dashboard mejorado"""
    
    @staticmethod
    def get_admin_dashboard_data():
        """Obtener datos para dashboard de administrador"""
        from app.models import User, Church, Member, Transaction, CalendarEvent
        from app import db
        from sqlalchemy import func, text
        
        # Estadísticas generales
        stats = {
            'total_users': User.query.count(),
            'total_churches': Church.query.count(),
            'total_members': Member.query.count(),
            'active_members': Member.query.filter_by(is_active=True).count(),
        }
        
        # Estadísticas financieras del último mes
        last_month = datetime.now() - timedelta(days=30)
        financial_stats = db.session.execute(text("""
            SELECT 
                COALESCE(SUM(CASE WHEN transaction_type = 'ingreso' THEN amount ELSE 0 END), 0) as income,
                COALESCE(SUM(CASE WHEN transaction_type = 'egreso' THEN amount ELSE 0 END), 0) as expenses,
                COUNT(*) as total_transactions
            FROM transactions 
            WHERE transaction_date >= :last_month
        """), {'last_month': last_month}).fetchone()
        
        stats.update({
            'monthly_income': float(financial_stats[0] or 0),
            'monthly_expenses': float(financial_stats[1] or 0),
            'monthly_transactions': financial_stats[2] or 0,
            'monthly_balance': float(financial_stats[0] or 0) - float(financial_stats[1] or 0)
        })
        
        # Eventos próximos
        upcoming_events = CalendarEvent.query.filter(
            CalendarEvent.event_date >= datetime.now().date()
        ).order_by(CalendarEvent.event_date).limit(5).all()
        
        # Iglesias con más miembros
        top_churches = db.session.execute(text("""
            SELECT c.name, COUNT(m.id) as member_count
            FROM churches c
            LEFT JOIN members m ON c.id = m.church_id AND m.is_active = 1
            GROUP BY c.id, c.name
            ORDER BY member_count DESC
            LIMIT 5
        """)).fetchall()
        
        # Crecimiento mensual de miembros
        growth_data = db.session.execute(text("""
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as new_members
            FROM members 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month
        """)).fetchall()
        
        return {
            'stats': stats,
            'upcoming_events': upcoming_events,
            'top_churches': [{'name': row[0], 'members': row[1]} for row in top_churches],
            'growth_data': [{'month': row[0], 'members': row[1]} for row in growth_data]
        }
    
    @staticmethod
    def get_pastor_dashboard_data(church_id):
        """Obtener datos para dashboard de pastor"""
        from app.models import Member, Transaction, CalendarEvent, Account
        from app import db
        from sqlalchemy import text
        
        # Estadísticas de la iglesia
        church_stats = db.session.execute(text("""
            SELECT 
                COUNT(DISTINCT m.id) as total_members,
                COUNT(DISTINCT CASE WHEN m.is_active = 1 THEN m.id END) as active_members,
                COUNT(DISTINCT CASE WHEN YEAR(u.date_of_birth) = YEAR(CURDATE()) 
                                   AND MONTH(u.date_of_birth) = MONTH(CURDATE()) 
                                   THEN m.id END) as birthday_this_month
            FROM members m
            JOIN users u ON m.user_id = u.id
            WHERE m.church_id = :church_id
        """), {'church_id': church_id}).fetchone()
        
        # Finanzas de la iglesia
        financial_data = db.session.execute(text("""
            SELECT 
                COALESCE(SUM(CASE WHEN t.transaction_type = 'ingreso' THEN t.amount ELSE 0 END), 0) as income,
                COALESCE(SUM(CASE WHEN t.transaction_type = 'egreso' THEN t.amount ELSE 0 END), 0) as expenses
            FROM accounts a
            LEFT JOIN transactions t ON a.id = t.account_id
            WHERE a.church_id = :church_id
        """), {'church_id': church_id}).fetchone()
        
        # Eventos próximos de la iglesia
        upcoming_events = CalendarEvent.query.join(User).filter(
            User.church_id == church_id,
            CalendarEvent.event_date >= datetime.now().date()
        ).order_by(CalendarEvent.event_date).limit(5).all()
        
        return {
            'total_members': church_stats[0] or 0,
            'active_members': church_stats[1] or 0,
            'birthday_this_month': church_stats[2] or 0,
            'total_income': float(financial_data[0] or 0),
            'total_expenses': float(financial_data[1] or 0),
            'balance': float(financial_data[0] or 0) - float(financial_data[1] or 0),
            'upcoming_events': upcoming_events
        }

# 2. COMPONENTES DE UI REUTILIZABLES
class UIComponents:
    """Componentes de UI reutilizables"""
    
    @staticmethod
    def render_stats_card(title, value, icon, color="primary", subtitle=None):
        """Renderizar tarjeta de estadística"""
        return {
            'title': title,
            'value': value,
            'icon': icon,
            'color': color,
            'subtitle': subtitle
        }
    
    @staticmethod
    def render_chart_data(labels, datasets):
        """Preparar datos para gráficos Chart.js"""
        return {
            'labels': labels,
            'datasets': datasets
        }
    
    @staticmethod
    def render_notification(message, type="info", dismissible=True):
        """Renderizar notificación"""
        return {
            'message': message,
            'type': type,  # success, info, warning, danger
            'dismissible': dismissible,
            'timestamp': datetime.now().isoformat()
        }

# 3. SISTEMA DE NOTIFICACIONES
class NotificationSystem:
    """Sistema de notificaciones en tiempo real"""
    
    @staticmethod
    def create_notification(user_id, title, message, type="info", action_url=None):
        """Crear nueva notificación"""
        from app.models import Notification
        from app import db
        
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            type=type,
            action_url=action_url,
            created_at=datetime.now(),
            is_read=False
        )
        
        db.session.add(notification)
        db.session.commit()
        
        return notification
    
    @staticmethod
    def get_user_notifications(user_id, unread_only=False):
        """Obtener notificaciones del usuario"""
        from app.models import Notification
        
        query = Notification.query.filter_by(user_id=user_id)
        
        if unread_only:
            query = query.filter_by(is_read=False)
        
        return query.order_by(Notification.created_at.desc()).all()
    
    @staticmethod
    def mark_as_read(notification_id, user_id):
        """Marcar notificación como leída"""
        from app.models import Notification
        from app import db
        
        notification = Notification.query.filter_by(
            id=notification_id, 
            user_id=user_id
        ).first()
        
        if notification:
            notification.is_read = True
            db.session.commit()
            return True
        
        return False

# 4. BÚSQUEDA AVANZADA
class AdvancedSearch:
    """Sistema de búsqueda avanzada"""
    
    @staticmethod
    def search_global(query, user_role, church_id=None):
        """Búsqueda global en el sistema"""
        from app.models import User, Church, Member, Document
        from sqlalchemy import or_
        
        results = {
            'users': [],
            'churches': [],
            'members': [],
            'documents': []
        }
        
        if len(query) < 3:
            return results
        
        search_term = f"%{query}%"
        
        # Buscar usuarios
        if user_role in ['administrador', 'secretaria']:
            users = User.query.filter(
                or_(
                    User.first_name.ilike(search_term),
                    User.last_name.ilike(search_term),
                    User.email.ilike(search_term),
                    User.username.ilike(search_term)
                )
            ).limit(10).all()
            results['users'] = users
        
        # Buscar iglesias
        if user_role in ['administrador', 'secretaria']:
            churches = Church.query.filter(
                or_(
                    Church.name.ilike(search_term),
                    Church.address.ilike(search_term),
                    Church.district.ilike(search_term)
                )
            ).limit(10).all()
            results['churches'] = churches
        
        # Buscar miembros
        member_query = Member.query.join(User).filter(
            or_(
                User.first_name.ilike(search_term),
                User.last_name.ilike(search_term),
                User.email.ilike(search_term)
            )
        )
        
        if user_role == 'pastorado' and church_id:
            member_query = member_query.filter(Member.church_id == church_id)
        
        results['members'] = member_query.limit(10).all()
        
        # Buscar documentos
        documents = Document.query.filter(
            or_(
                Document.title.ilike(search_term),
                Document.description.ilike(search_term),
                Document.topic.ilike(search_term)
            )
        ).limit(10).all()
        results['documents'] = documents
        
        return results

# 5. FILTROS AVANZADOS
class AdvancedFilters:
    """Sistema de filtros avanzados"""
    
    @staticmethod
    def filter_members(church_id=None, is_active=None, age_range=None, 
                      functions=None, date_range=None):
        """Filtrar miembros con criterios avanzados"""
        from app.models import Member, User, MemberFunction
        from sqlalchemy import and_, or_
        
        query = Member.query.join(User)
        
        # Filtro por iglesia
        if church_id:
            query = query.filter(Member.church_id == church_id)
        
        # Filtro por estado activo
        if is_active is not None:
            query = query.filter(Member.is_active == is_active)
        
        # Filtro por rango de edad
        if age_range:
            min_age, max_age = age_range
            today = datetime.now().date()
            min_birth = today.replace(year=today.year - max_age)
            max_birth = today.replace(year=today.year - min_age)
            
            query = query.filter(
                and_(
                    User.date_of_birth >= min_birth,
                    User.date_of_birth <= max_birth
                )
            )
        
        # Filtro por funciones
        if functions:
            query = query.join(Member.functions).filter(
                MemberFunction.id.in_(functions)
            )
        
        # Filtro por rango de fechas de registro
        if date_range:
            start_date, end_date = date_range
            query = query.filter(
                and_(
                    Member.created_at >= start_date,
                    Member.created_at <= end_date
                )
            )
        
        return query

# 6. EXPORTACIÓN DE DATOS
class DataExporter:
    """Exportador de datos a diferentes formatos"""
    
    @staticmethod
    def export_to_excel(data, filename, sheet_name="Datos"):
        """Exportar datos a Excel"""
        import pandas as pd
        from io import BytesIO
        
        df = pd.DataFrame(data)
        
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        output.seek(0)
        return output
    
    @staticmethod
    def export_to_csv(data, filename):
        """Exportar datos a CSV"""
        import pandas as pd
        from io import StringIO
        
        df = pd.DataFrame(data)
        output = StringIO()
        df.to_csv(output, index=False)
        output.seek(0)
        return output.getvalue()

# 7. WIDGETS DEL DASHBOARD
class DashboardWidgets:
    """Widgets para el dashboard"""
    
    @staticmethod
    def member_growth_chart(church_id=None):
        """Widget de crecimiento de miembros"""
        from app import db
        from sqlalchemy import text
        
        query = """
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as count
            FROM members 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        """
        
        params = {}
        if church_id:
            query += " AND church_id = :church_id"
            params['church_id'] = church_id
        
        query += " GROUP BY DATE_FORMAT(created_at, '%Y-%m') ORDER BY month"
        
        result = db.session.execute(text(query), params).fetchall()
        
        return {
            'labels': [row[0] for row in result],
            'data': [row[1] for row in result]
        }
    
    @staticmethod
    def financial_summary_chart(church_id=None):
        """Widget de resumen financiero"""
        from app import db
        from sqlalchemy import text
        
        query = """
            SELECT 
                DATE_FORMAT(t.transaction_date, '%Y-%m') as month,
                SUM(CASE WHEN t.transaction_type = 'ingreso' THEN t.amount ELSE 0 END) as income,
                SUM(CASE WHEN t.transaction_type = 'egreso' THEN t.amount ELSE 0 END) as expenses
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            WHERE t.transaction_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        """
        
        params = {}
        if church_id:
            query += " AND a.church_id = :church_id"
            params['church_id'] = church_id
        
        query += " GROUP BY DATE_FORMAT(t.transaction_date, '%Y-%m') ORDER BY month"
        
        result = db.session.execute(text(query), params).fetchall()
        
        return {
            'labels': [row[0] for row in result],
            'income': [float(row[1]) for row in result],
            'expenses': [float(row[2]) for row in result]
        }

# 8. EJEMPLO DE TEMPLATE MEJORADO
IMPROVED_DASHBOARD_TEMPLATE = """
<!-- dashboard_improved.html -->
{% extends "base.html" %}

{% block title %}Dashboard - Intranet IMPA{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<style>
.stats-card {
    transition: transform 0.2s;
}
.stats-card:hover {
    transform: translateY(-2px);
}
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Estadísticas principales -->
    <div class="row mb-4">
        {% for stat in stats_cards %}
        <div class="col-md-3 mb-3">
            <div class="card stats-card border-left-{{ stat.color }}">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-{{ stat.color }} text-uppercase mb-1">
                                {{ stat.title }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stat.value }}
                            </div>
                            {% if stat.subtitle %}
                            <div class="text-xs text-muted">{{ stat.subtitle }}</div>
                            {% endif %}
                        </div>
                        <div class="col-auto">
                            <i class="fas {{ stat.icon }} fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Gráficos -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Crecimiento de Miembros</h6>
                </div>
                <div class="card-body">
                    <canvas id="memberGrowthChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Eventos Próximos</h6>
                </div>
                <div class="card-body">
                    {% for event in upcoming_events %}
                    <div class="d-flex align-items-center mb-2">
                        <div class="mr-3">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-calendar text-white"></i>
                            </div>
                        </div>
                        <div>
                            <div class="small text-gray-500">{{ event.event_date.strftime('%d/%m/%Y') }}</div>
                            <div class="font-weight-bold">{{ event.title }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
// Gráfico de crecimiento de miembros
const ctx = document.getElementById('memberGrowthChart').getContext('2d');
const memberGrowthChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {{ growth_data.labels | tojson }},
        datasets: [{
            label: 'Nuevos Miembros',
            data: {{ growth_data.data | tojson }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
"""
