#!/usr/bin/env python3
"""
Script para limpiar la caché de SQLAlchemy y reiniciar la aplicación.
"""

from sqlalchemy import create_engine, text
from app.core.config import settings
from app.db.database import SessionLocal, engine
import gc

def clear_sqlalchemy_cache():
    print("Intentando limpiar la caché de SQLAlchemy y refrescar la conexión...")

    # Forzar la recolección de basura (su efectividad puede variar)
    # gc.collect() # Opcional, a menudo no es necesario para este propósito específico.

    # Disponer del engine actual para cerrar todas las conexiones en el pool.
    # Esto es útil si quieres asegurar que las nuevas conexiones no usen estados cacheados a nivel de conexión.
    print("Disponiendo del engine de SQLAlchemy...")
    engine.dispose()
    print("Engine dispuesto. Las nuevas conexiones serán establecidas.")

    # Verificar la conexión y los valores del enum con una nueva sesión
    print("Verificando la conexión y los valores del enum con una nueva sesión...")
    with SessionLocal() as db:
        try:
            # Ejecutar una consulta simple para verificar la conexión
            result = db.execute(text("SELECT 1"))
            print("Nueva conexión exitosa:", result.scalar_one_or_none())
            
            # Verificar los valores del enum
            result = db.execute(text("""
            SELECT typname, enumlabel
            FROM pg_enum e
            JOIN pg_type t ON e.enumtypid = t.oid
            WHERE typname = 'roleenum'
            ORDER BY enumsortorder;
            """))
            
            print("\nValores del enum roleenum en la base de datos (post-refresh):")
            for row in result:
                print(f"  - '{row[1]}'")
            
            print("Verificación completada.")
        except Exception as e:
            print(f"Error durante la verificación post-refresh: {e}")
        
if __name__ == "__main__":
    clear_sqlalchemy_cache()