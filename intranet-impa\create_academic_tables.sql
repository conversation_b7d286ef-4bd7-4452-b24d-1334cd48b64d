-- create_academic_tables.sql
-- Script para crear todas las tablas del sistema académico

-- ============================================================================
-- TABLAS PARA ESCUELAS BÍBLICAS (Nivel Básico)
-- ============================================================================

-- Tabla de escuelas bíblicas
CREATE TABLE IF NOT EXISTS academic_schools (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    church_id INT NOT NULL,
    director_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    start_date DATE NOT NULL,
    end_date DATE NULL,
    max_students INT DEFAULT 50,
    auto_enrollment BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (church_id) REFERENCES churches(id) ON DELETE CASCADE,
    FOREIGN KEY (director_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_church_id (church_id),
    INDEX idx_director_id (director_id),
    INDEX idx_is_active (is_active)
);

-- Tabla de pensums académicos
CREATE TABLE IF NOT EXISTS academic_curriculums (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    school_id INT NOT NULL,
    duration_months INT NOT NULL,
    total_credits INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (school_id) REFERENCES academic_schools(id) ON DELETE CASCADE,
    
    INDEX idx_school_id (school_id),
    INDEX idx_is_active (is_active)
);

-- Tabla de materias
CREATE TABLE IF NOT EXISTS academic_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) NOT NULL,
    description TEXT,
    curriculum_id INT NOT NULL,
    credits INT DEFAULT 1,
    level INT DEFAULT 1,
    prerequisites TEXT,
    is_mandatory BOOLEAN DEFAULT TRUE,
    teacher_id INT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (curriculum_id) REFERENCES academic_curriculums(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_curriculum_id (curriculum_id),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_code (code),
    INDEX idx_level (level)
);

-- Tabla de matrículas
CREATE TABLE IF NOT EXISTS academic_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    school_id INT NOT NULL,
    curriculum_id INT NOT NULL,
    enrollment_date DATE DEFAULT (CURRENT_DATE),
    status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
    completion_date DATE NULL,
    final_grade FLOAT NULL,
    certificate_issued BOOLEAN DEFAULT FALSE,
    certificate_date DATE NULL,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (school_id) REFERENCES academic_schools(id) ON DELETE CASCADE,
    FOREIGN KEY (curriculum_id) REFERENCES academic_curriculums(id) ON DELETE CASCADE,
    
    INDEX idx_student_id (student_id),
    INDEX idx_school_id (school_id),
    INDEX idx_curriculum_id (curriculum_id),
    INDEX idx_status (status),
    INDEX idx_enrollment_date (enrollment_date),
    
    UNIQUE KEY unique_active_enrollment (student_id, school_id, status)
);

-- Tabla de calificaciones
CREATE TABLE IF NOT EXISTS academic_grades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    enrollment_id INT NOT NULL,
    subject_id INT NOT NULL,
    grade_value FLOAT NOT NULL,
    grade_type ENUM('partial', 'final', 'makeup') DEFAULT 'partial',
    evaluation_date DATE DEFAULT (CURRENT_DATE),
    teacher_id INT NOT NULL,
    comments TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (enrollment_id) REFERENCES academic_enrollments(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES academic_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_enrollment_id (enrollment_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_grade_type (grade_type),
    INDEX idx_evaluation_date (evaluation_date)
);

-- ============================================================================
-- TABLAS PARA INSTITUTO PASTORAL (Nivel Corporativo)
-- ============================================================================

-- Tabla de institutos pastorales
CREATE TABLE IF NOT EXISTS pastoral_institutes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rector_id INT NULL,
    academic_coordinator_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    accreditation_info TEXT,
    academic_calendar JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (rector_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (academic_coordinator_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_rector_id (rector_id),
    INDEX idx_coordinator_id (academic_coordinator_id),
    INDEX idx_is_active (is_active)
);

-- Tabla de programas pastorales
CREATE TABLE IF NOT EXISTS pastoral_programs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    institute_id INT NOT NULL,
    program_type ENUM('diploma', 'certificate', 'specialization', 'masters') DEFAULT 'certificate',
    duration_months INT NOT NULL,
    total_credits INT DEFAULT 0,
    min_grade_to_pass FLOAT DEFAULT 3.0,
    entry_requirements JSON,
    target_roles JSON,
    min_ministry_years INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    enrollment_start DATE,
    enrollment_end DATE,
    program_start DATE,
    program_end DATE,
    max_students INT DEFAULT 30,
    requires_approval BOOLEAN DEFAULT TRUE,
    created_by_id INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (institute_id) REFERENCES pastoral_institutes(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_institute_id (institute_id),
    INDEX idx_program_type (program_type),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by_id (created_by_id)
);

-- Tabla de materias pastorales
CREATE TABLE IF NOT EXISTS pastoral_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) NOT NULL,
    description TEXT,
    program_id INT NOT NULL,
    credits INT DEFAULT 3,
    semester INT DEFAULT 1,
    prerequisites JSON,
    is_mandatory BOOLEAN DEFAULT TRUE,
    professor_id INT NULL,
    teaching_hours INT DEFAULT 40,
    practical_hours INT DEFAULT 0,
    syllabus TEXT,
    learning_objectives JSON,
    evaluation_methods JSON,
    bibliography TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (program_id) REFERENCES pastoral_programs(id) ON DELETE CASCADE,
    FOREIGN KEY (professor_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_program_id (program_id),
    INDEX idx_professor_id (professor_id),
    INDEX idx_code (code),
    INDEX idx_semester (semester),
    INDEX idx_is_active (is_active)
);

-- Tabla de matrículas pastorales
CREATE TABLE IF NOT EXISTS pastoral_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    program_id INT NOT NULL,
    enrollment_date DATE DEFAULT (CURRENT_DATE),
    status ENUM('pending', 'approved', 'active', 'completed', 'dropped', 'suspended') DEFAULT 'pending',
    approved_by_id INT NULL,
    approval_date DATE NULL,
    approval_notes TEXT,
    current_semester INT DEFAULT 1,
    credits_completed INT DEFAULT 0,
    cumulative_gpa FLOAT DEFAULT 0.0,
    completion_date DATE NULL,
    final_gpa FLOAT NULL,
    graduation_date DATE NULL,
    certificate_type VARCHAR(100),
    certificate_issued BOOLEAN DEFAULT FALSE,
    certificate_number VARCHAR(50) UNIQUE,
    certificate_date DATE NULL,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (program_id) REFERENCES pastoral_programs(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_student_id (student_id),
    INDEX idx_program_id (program_id),
    INDEX idx_approved_by_id (approved_by_id),
    INDEX idx_status (status),
    INDEX idx_enrollment_date (enrollment_date),
    INDEX idx_certificate_number (certificate_number),
    
    UNIQUE KEY unique_active_pastoral_enrollment (student_id, program_id, status)
);

-- Tabla de calificaciones pastorales
CREATE TABLE IF NOT EXISTS pastoral_grades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    enrollment_id INT NOT NULL,
    subject_id INT NOT NULL,
    grade_value FLOAT NOT NULL,
    grade_type ENUM('assignment', 'exam', 'project', 'final') DEFAULT 'assignment',
    weight FLOAT DEFAULT 1.0,
    evaluation_date DATE DEFAULT (CURRENT_DATE),
    professor_id INT NOT NULL,
    evaluation_method VARCHAR(100),
    comments TEXT,
    feedback TEXT,
    improvement_areas TEXT,
    is_final BOOLEAN DEFAULT FALSE,
    requires_remediation BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (enrollment_id) REFERENCES pastoral_enrollments(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES pastoral_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (professor_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_enrollment_id (enrollment_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_professor_id (professor_id),
    INDEX idx_grade_type (grade_type),
    INDEX idx_evaluation_date (evaluation_date),
    INDEX idx_is_final (is_final)
);

-- Tabla de certificados pastorales
CREATE TABLE IF NOT EXISTS pastoral_certificates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    enrollment_id INT NOT NULL,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    certificate_type VARCHAR(100) NOT NULL,
    title_conferred VARCHAR(255),
    specialization VARCHAR(255),
    issue_date DATE DEFAULT (CURRENT_DATE),
    valid_from DATE DEFAULT (CURRENT_DATE),
    valid_until DATE NULL,
    issued_by_id INT NOT NULL,
    signed_by_rector BOOLEAN DEFAULT FALSE,
    signed_by_coordinator BOOLEAN DEFAULT FALSE,
    certificate_file_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    revocation_reason TEXT NULL,
    revoked_date DATE NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (enrollment_id) REFERENCES pastoral_enrollments(id) ON DELETE CASCADE,
    FOREIGN KEY (issued_by_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_enrollment_id (enrollment_id),
    INDEX idx_certificate_number (certificate_number),
    INDEX idx_issued_by_id (issued_by_id),
    INDEX idx_issue_date (issue_date),
    INDEX idx_is_active (is_active)
);

-- ============================================================================
-- INSERTAR DATOS INICIALES
-- ============================================================================

-- Crear Instituto Principal
INSERT INTO pastoral_institutes (name, description, is_active) VALUES 
('Instituto de Formación Pastoral IMPA', 'Instituto corporativo para la formación continua de pastores y líderes de la Corporación IMPA', TRUE);

-- Verificar que las tablas se crearon correctamente
SHOW TABLES LIKE '%academic%';
SHOW TABLES LIKE '%pastoral%';

-- Mostrar estructura de las tablas principales
DESCRIBE academic_schools;
DESCRIBE pastoral_programs;
