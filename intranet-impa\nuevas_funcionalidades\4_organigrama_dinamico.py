# 4_organigrama_dinamico.py
# Organigrama Dinámico para Intranet IMPA

"""
ORGANIGRAMA DINÁMICO
===================

Funcionalidades:
- Visualización jerárquica interactiva de la organización
- Organigramas por iglesia y corporativo
- Drag & drop para reorganizar estructura
- Diferentes vistas (árbol, red, circular)
- Exportación a PDF/imagen
- Historial de cambios organizacionales
- Integración con roles y permisos

Inspirado en Software Redil pero adaptado para corporaciones de iglesias.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, JSON
from sqlalchemy.orm import relationship
from app import db
import json

# ============================================================================
# 1. MODELOS DE BASE DE DATOS
# ============================================================================

class OrganizationalStructure(db.Model):
    """Estructura organizacional"""
    __tablename__ = 'organizational_structures'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    structure_type = db.Column(db.String(50), default='corporate')  # corporate, church, ministry
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_modified = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Configuración de visualización
    layout_config = db.Column(db.JSON)  # Configuración del layout (posiciones, colores, etc.)
    
    # Relaciones
    church = db.relationship('Church', backref='organizational_structures')
    created_by = db.relationship('User', backref='created_structures')
    positions = db.relationship('OrganizationalPosition', backref='structure', cascade='all, delete-orphan')
    relationships = db.relationship('OrganizationalRelationship', backref='structure', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<OrganizationalStructure(name='{self.name}')>"

class OrganizationalPosition(db.Model):
    """Posiciones en el organigrama"""
    __tablename__ = 'organizational_positions'
    
    id = db.Column(db.Integer, primary_key=True)
    structure_id = db.Column(db.Integer, db.ForeignKey('organizational_structures.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    level = db.Column(db.Integer, default=1)  # Nivel jerárquico
    department = db.Column(db.String(255))  # Departamento o área
    
    # Usuario asignado a la posición
    assigned_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # Configuración visual
    position_config = db.Column(db.JSON)  # Color, icono, posición en el gráfico
    
    # Responsabilidades y autoridad
    responsibilities = db.Column(db.Text)  # JSON con lista de responsabilidades
    authority_level = db.Column(db.Integer, default=1)  # Nivel de autoridad (1-10)
    can_approve_budget = db.Column(db.Boolean, default=False)
    max_budget_amount = db.Column(db.Float, default=0)
    
    # Metadatos
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    assigned_user = db.relationship('User', backref='organizational_positions')
    parent_relationships = db.relationship('OrganizationalRelationship', 
                                         foreign_keys='OrganizationalRelationship.child_position_id',
                                         backref='child_position')
    child_relationships = db.relationship('OrganizationalRelationship',
                                        foreign_keys='OrganizationalRelationship.parent_position_id',
                                        backref='parent_position')
    
    def __repr__(self):
        return f"<OrganizationalPosition(title='{self.title}')>"

class OrganizationalRelationship(db.Model):
    """Relaciones jerárquicas entre posiciones"""
    __tablename__ = 'organizational_relationships'
    
    id = db.Column(db.Integer, primary_key=True)
    structure_id = db.Column(db.Integer, db.ForeignKey('organizational_structures.id'), nullable=False)
    parent_position_id = db.Column(db.Integer, db.ForeignKey('organizational_positions.id'), nullable=False)
    child_position_id = db.Column(db.Integer, db.ForeignKey('organizational_positions.id'), nullable=False)
    
    relationship_type = db.Column(db.String(50), default='reports_to')  # reports_to, coordinates_with, supports
    strength = db.Column(db.Integer, default=1)  # Fuerza de la relación (1-5)
    
    # Configuración visual de la conexión
    connection_config = db.Column(db.JSON)  # Estilo de línea, color, etc.
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<OrganizationalRelationship(parent={self.parent_position_id}, child={self.child_position_id})>"

class OrganizationalHistory(db.Model):
    """Historial de cambios organizacionales"""
    __tablename__ = 'organizational_history'
    
    id = db.Column(db.Integer, primary_key=True)
    structure_id = db.Column(db.Integer, db.ForeignKey('organizational_structures.id'), nullable=False)
    change_type = db.Column(db.String(50), nullable=False)  # position_added, position_removed, relationship_changed, etc.
    description = db.Column(db.Text, nullable=False)
    
    # Datos del cambio
    old_data = db.Column(db.JSON)  # Estado anterior
    new_data = db.Column(db.JSON)  # Estado nuevo
    
    # Metadatos
    changed_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    changed_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    structure = db.relationship('OrganizationalStructure', backref='history')
    changed_by = db.relationship('User', backref='organizational_changes')

# ============================================================================
# 2. SERVICIOS DE ORGANIGRAMA
# ============================================================================

class OrganizationalService:
    """Servicio para gestión de organigramas"""
    
    @staticmethod
    def create_structure(name, structure_type='corporate', church_id=None, created_by_id=None):
        """Crear nueva estructura organizacional"""
        structure = OrganizationalStructure(
            name=name,
            structure_type=structure_type,
            church_id=church_id,
            created_by_id=created_by_id
        )
        db.session.add(structure)
        db.session.commit()
        return structure
    
    @staticmethod
    def add_position(structure_id, title, level=1, assigned_user_id=None, **kwargs):
        """Añadir posición al organigrama"""
        position = OrganizationalPosition(
            structure_id=structure_id,
            title=title,
            level=level,
            assigned_user_id=assigned_user_id,
            **kwargs
        )
        db.session.add(position)
        db.session.commit()
        
        # Registrar en historial
        OrganizationalService._log_change(
            structure_id, 'position_added', 
            f"Posición '{title}' añadida",
            None, position.id
        )
        
        return position
    
    @staticmethod
    def create_relationship(structure_id, parent_position_id, child_position_id, 
                          relationship_type='reports_to', strength=1):
        """Crear relación jerárquica"""
        # Verificar que no exista ya la relación
        existing = OrganizationalRelationship.query.filter_by(
            structure_id=structure_id,
            parent_position_id=parent_position_id,
            child_position_id=child_position_id
        ).first()
        
        if existing:
            return existing
        
        relationship = OrganizationalRelationship(
            structure_id=structure_id,
            parent_position_id=parent_position_id,
            child_position_id=child_position_id,
            relationship_type=relationship_type,
            strength=strength
        )
        db.session.add(relationship)
        db.session.commit()
        
        # Registrar en historial
        OrganizationalService._log_change(
            structure_id, 'relationship_added',
            f"Relación creada entre posiciones {parent_position_id} y {child_position_id}",
            None, relationship.id
        )
        
        return relationship
    
    @staticmethod
    def get_structure_data(structure_id, format='hierarchical'):
        """Obtener datos de estructura en formato específico"""
        structure = OrganizationalStructure.query.get(structure_id)
        if not structure:
            return None
        
        positions = OrganizationalPosition.query.filter_by(
            structure_id=structure_id, is_active=True
        ).all()
        
        relationships = OrganizationalRelationship.query.filter_by(
            structure_id=structure_id, is_active=True
        ).all()
        
        if format == 'hierarchical':
            return OrganizationalService._format_hierarchical(positions, relationships)
        elif format == 'network':
            return OrganizationalService._format_network(positions, relationships)
        elif format == 'd3_tree':
            return OrganizationalService._format_d3_tree(positions, relationships)
        else:
            return {
                'positions': [OrganizationalService._position_to_dict(p) for p in positions],
                'relationships': [OrganizationalService._relationship_to_dict(r) for r in relationships]
            }
    
    @staticmethod
    def _format_hierarchical(positions, relationships):
        """Formatear datos para vista jerárquica"""
        # Crear diccionario de posiciones
        pos_dict = {p.id: OrganizationalService._position_to_dict(p) for p in positions}
        
        # Crear mapa de relaciones padre-hijo
        children_map = {}
        for rel in relationships:
            if rel.parent_position_id not in children_map:
                children_map[rel.parent_position_id] = []
            children_map[rel.parent_position_id].append(rel.child_position_id)
        
        # Encontrar posiciones raíz (sin padre)
        all_children = set()
        for children in children_map.values():
            all_children.update(children)
        
        root_positions = [p for p in positions if p.id not in all_children]
        
        # Construir árbol jerárquico
        def build_tree(position_id):
            position = pos_dict[position_id].copy()
            position['children'] = []
            
            if position_id in children_map:
                for child_id in children_map[position_id]:
                    position['children'].append(build_tree(child_id))
            
            return position
        
        return [build_tree(p.id) for p in root_positions]
    
    @staticmethod
    def _format_d3_tree(positions, relationships):
        """Formatear datos para D3.js tree"""
        hierarchical = OrganizationalService._format_hierarchical(positions, relationships)
        
        if not hierarchical:
            return None
        
        # Convertir a formato D3
        def convert_to_d3(node):
            d3_node = {
                'name': node['title'],
                'id': node['id'],
                'data': node
            }
            
            if node['children']:
                d3_node['children'] = [convert_to_d3(child) for child in node['children']]
            
            return d3_node
        
        # Tomar el primer nodo raíz como principal
        return convert_to_d3(hierarchical[0]) if hierarchical else None
    
    @staticmethod
    def _position_to_dict(position):
        """Convertir posición a diccionario"""
        return {
            'id': position.id,
            'title': position.title,
            'description': position.description,
            'level': position.level,
            'department': position.department,
            'assigned_user': {
                'id': position.assigned_user.id,
                'name': f"{position.assigned_user.first_name} {position.assigned_user.last_name}",
                'email': position.assigned_user.email
            } if position.assigned_user else None,
            'responsibilities': json.loads(position.responsibilities) if position.responsibilities else [],
            'authority_level': position.authority_level,
            'position_config': position.position_config or {}
        }
    
    @staticmethod
    def _relationship_to_dict(relationship):
        """Convertir relación a diccionario"""
        return {
            'id': relationship.id,
            'parent_id': relationship.parent_position_id,
            'child_id': relationship.child_position_id,
            'type': relationship.relationship_type,
            'strength': relationship.strength,
            'config': relationship.connection_config or {}
        }
    
    @staticmethod
    def update_position_assignment(position_id, new_user_id, changed_by_id):
        """Actualizar asignación de usuario a posición"""
        position = OrganizationalPosition.query.get(position_id)
        if not position:
            return None
        
        old_user_id = position.assigned_user_id
        position.assigned_user_id = new_user_id
        
        db.session.commit()
        
        # Registrar cambio
        OrganizationalService._log_change(
            position.structure_id, 'assignment_changed',
            f"Asignación de posición '{position.title}' cambiada",
            old_user_id, new_user_id, changed_by_id
        )
        
        return position
    
    @staticmethod
    def _log_change(structure_id, change_type, description, old_data, new_data, changed_by_id=None):
        """Registrar cambio en historial"""
        history = OrganizationalHistory(
            structure_id=structure_id,
            change_type=change_type,
            description=description,
            old_data=old_data,
            new_data=new_data,
            changed_by_id=changed_by_id
        )
        db.session.add(history)
        db.session.commit()

# ============================================================================
# 3. FORMULARIOS DE ORGANIGRAMA
# ============================================================================

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, BooleanField, FloatField
from wtforms.validators import DataRequired, Optional, NumberRange

class OrganizationalStructureForm(FlaskForm):
    """Formulario para crear/editar estructura organizacional"""
    name = StringField('Nombre de la Estructura', validators=[DataRequired()])
    description = TextAreaField('Descripción', validators=[Optional()])
    structure_type = SelectField('Tipo', choices=[
        ('corporate', 'Corporativo'),
        ('church', 'Iglesia'),
        ('ministry', 'Ministerio')
    ], default='corporate')
    church_id = SelectField('Iglesia', coerce=int, validators=[Optional()])
    is_default = BooleanField('Estructura por Defecto')
    submit = SubmitField('Guardar Estructura')

class OrganizationalPositionForm(FlaskForm):
    """Formulario para posiciones organizacionales"""
    title = StringField('Título del Cargo', validators=[DataRequired()])
    description = TextAreaField('Descripción', validators=[Optional()])
    level = IntegerField('Nivel Jerárquico', validators=[NumberRange(min=1, max=20)], default=1)
    department = StringField('Departamento/Área', validators=[Optional()])
    assigned_user_id = SelectField('Usuario Asignado', coerce=int, validators=[Optional()])
    responsibilities = TextAreaField('Responsabilidades', validators=[Optional()])
    authority_level = IntegerField('Nivel de Autoridad', validators=[NumberRange(min=1, max=10)], default=1)
    can_approve_budget = BooleanField('Puede Aprobar Presupuesto')
    max_budget_amount = FloatField('Monto Máximo de Presupuesto', validators=[Optional()], default=0)
    submit = SubmitField('Guardar Posición')

# ============================================================================
# 4. GENERADOR DE VISUALIZACIONES
# ============================================================================

class OrganizationalVisualization:
    """Generador de visualizaciones de organigrama"""
    
    @staticmethod
    def generate_html_tree(structure_id):
        """Generar HTML para árbol organizacional"""
        data = OrganizationalService.get_structure_data(structure_id, 'hierarchical')
        
        def render_node(node, level=0):
            html = f"""
            <div class="org-node level-{level}" data-id="{node['id']}">
                <div class="node-content">
                    <h4>{node['title']}</h4>
                    {f"<p>{node['assigned_user']['name']}</p>" if node.get('assigned_user') else "<p>Sin asignar</p>"}
                    {f"<small>{node['department']}</small>" if node.get('department') else ""}
                </div>
            """
            
            if node.get('children'):
                html += '<div class="node-children">'
                for child in node['children']:
                    html += render_node(child, level + 1)
                html += '</div>'
            
            html += '</div>'
            return html
        
        if not data:
            return "<p>No hay datos de organigrama</p>"
        
        html = '<div class="organizational-chart">'
        for root in data:
            html += render_node(root)
        html += '</div>'
        
        return html
    
    @staticmethod
    def generate_d3_config(structure_id):
        """Generar configuración para D3.js"""
        data = OrganizationalService.get_structure_data(structure_id, 'd3_tree')
        
        config = {
            'data': data,
            'width': 800,
            'height': 600,
            'nodeSize': [120, 80],
            'linkColor': '#ccc',
            'nodeColor': '#4CAF50',
            'textColor': '#333'
        }
        
        return config
    
    @staticmethod
    def export_to_pdf(structure_id):
        """Exportar organigrama a PDF"""
        # Implementar exportación a PDF usando reportlab o similar
        pass

# ============================================================================
# 5. RUTAS DE ORGANIGRAMA (para routes.py)
# ============================================================================

ORGANIGRAMA_ROUTES_EXAMPLE = """
# Añadir estas rutas a routes.py

from nuevas_funcionalidades.organigrama_dinamico import (
    OrganizationalService, OrganizationalStructureForm, OrganizationalPositionForm,
    OrganizationalStructure, OrganizationalVisualization
)

@routes_bp.route('/organigrama')
@login_required
def organigrama():
    structures = OrganizationalStructure.query.filter_by(is_active=True).all()
    return render_template('organigrama/index.html', structures=structures)

@routes_bp.route('/organigrama/<int:structure_id>')
@login_required
def view_organigrama(structure_id):
    structure = OrganizationalStructure.query.get_or_404(structure_id)
    
    # Verificar permisos
    if current_user.role not in ['administrador', 'secretaria']:
        if structure.church_id and structure.church_id != current_user.church_id:
            flash('No tienes permiso para ver este organigrama', 'danger')
            return redirect(url_for('routes.organigrama'))
    
    # Obtener datos para visualización
    tree_data = OrganizationalService.get_structure_data(structure_id, 'd3_tree')
    
    return render_template('organigrama/view.html', 
                         structure=structure, 
                         tree_data=tree_data)

@routes_bp.route('/organigrama/create', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def create_organigrama():
    form = OrganizationalStructureForm()
    form.church_id.choices = [(0, 'Corporativo')] + [
        (c.id, c.name) for c in Church.query.all()
    ]
    
    if form.validate_on_submit():
        structure = OrganizationalService.create_structure(
            name=form.name.data,
            structure_type=form.structure_type.data,
            church_id=form.church_id.data if form.church_id.data != 0 else None,
            created_by_id=current_user.id
        )
        
        flash('Estructura organizacional creada exitosamente', 'success')
        return redirect(url_for('routes.view_organigrama', structure_id=structure.id))
    
    return render_template('organigrama/create.html', form=form)

@routes_bp.route('/api/organigrama/<int:structure_id>/data')
@login_required
def api_organigrama_data(structure_id):
    format_type = request.args.get('format', 'hierarchical')
    data = OrganizationalService.get_structure_data(structure_id, format_type)
    return jsonify(data)

@routes_bp.route('/api/organigrama/position/<int:position_id>/assign', methods=['POST'])
@login_required
@admin_or_secretary_required
def api_assign_position(position_id):
    user_id = request.json.get('user_id')
    
    position = OrganizationalService.update_position_assignment(
        position_id, user_id, current_user.id
    )
    
    if position:
        return jsonify({'success': True, 'message': 'Asignación actualizada'})
    else:
        return jsonify({'success': False, 'message': 'Error al actualizar'}), 400
"""

print("Organigrama Dinámico - Listo para implementar")
print("Dependencias adicionales:")
print("- D3.js (para visualizaciones interactivas)")
print("- Drag & Drop libraries")
print("- PDF export libraries (reportlab)")
