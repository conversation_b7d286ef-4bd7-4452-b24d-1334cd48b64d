# client_ws.py

import socketio
import json
import requests
from bs4 import BeautifulSoup
import tkinter as tk
from tkinter import messagebox
from urllib.parse import urljoin
import base64
import simpleaudio as sa

# Definir variables globales
session = requests.Session()
config = {}
cookie_header = None
root = None

# Función para cargar la configuración del usuario
def load_config():
    try:
        with open('ptt_client_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

# Función para guardar la configuración del usuario
def save_config(config):
    with open('ptt_client_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)

# Función para iniciar sesión y obtener la lista de nodos
def login_and_get_nodes():
    global session, cookie_header
    login_url = 'https://www.patagoniaservers.com.ar:5000/login'
    response = session.get(login_url)
    soup = BeautifulSoup(response.text, 'html.parser')

    login_data = {
        'username': config['username'],
        'password': config['password'],
    }

    response = session.post(login_url, data=login_data)

    if response.status_code == 200 and 'Bienvenido' in response.text:
        print('Inicio de sesión exitoso')
        dashboard_url = 'https://www.patagoniaservers.com.ar:5000/dashboard'
        response = session.get(dashboard_url)
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            nodes = [(node.text, urljoin(dashboard_url, node['href'])) for node in soup.select('ul li a')]
            if nodes:
                print('Nodos encontrados:', nodes)
            else:
                print('No se encontraron nodos en el dashboard')
            # Extraer las cookies de la sesión para pasarlas a socketio
            cookies = session.cookies.get_dict()
            cookie_header = '; '.join([f'{name}={value}' for name, value in cookies.items()])
            return nodes
        else:
            print(f'Error al acceder al dashboard: {response.status_code}')
    else:
        print('Error al iniciar sesión: No se pudo autenticar')
        print('Contenido de la respuesta:', response.text)
        return None

# Función para conectarse al nodo seleccionado
def connect_to_node(node_url, node_id):
    global session, cookie_header, root
    response = session.get(node_url)
    if response.status_code == 200:
        print(f'Conectado al nodo: {node_url}')
        soup = BeautifulSoup(response.text, 'html.parser')
        subnodes = [(subnode.text, urljoin(node_url, subnode['href'])) for subnode in soup.select('ul li a')]
        if subnodes:
            update_listbox(subnodes, is_subnode=True, parent_url=node_url)
        else:
            print(f"No se encontraron subnodos para el nodo {node_url}")
            if cookie_header:
                sio.connect('https://www.patagoniaservers.com.ar:5000/node', headers={'Cookie': cookie_header})
            if root:
                root.after(100, lambda: root.destroy())  # Cierra la ventana de selección de nodo de manera controlada
            try:
                sio.wait()
            except KeyboardInterrupt:
                print("Conexión detenida")
            finally:
                sio.disconnect()
    else:
        print(f'Error al conectarse al nodo: {node_url}')
        exit(1)

# Función para manejar la selección del nodo en la interfaz gráfica
def on_node_select(event):
    try:
        selected_node = node_listbox.get(node_listbox.curselection())
        node_name, node_url = selected_node.split(' - ')
        node_id = node_url.split('/')[-1]
        config['node_id'] = node_id
        config['node_url'] = node_url
        save_config(config)
        connect_to_node(node_url, node_id)
    except Exception as e:
        messagebox.showerror("Error", f"Error al seleccionar nodo: {e}")

# Función para actualizar la lista de nodos en la interfaz gráfica
def update_listbox(nodes, is_subnode=False, parent_url=None):
    node_listbox.delete(0, tk.END)
    for node_name, node_url in nodes:
        node_listbox.insert(tk.END, f"{node_name} - {node_url}")
    back_button.pack_forget()
    save_button.pack_forget()
    if is_subnode:
        back_button.pack(pady=5)
    else:
        save_button.pack(pady=5)  # Mostrar el botón guardar cuando no hay subnodos

# Configurar la interfaz gráfica para seleccionar el nodo
def setup_gui(nodes, is_subnode=False, parent_url=None):
    global node_listbox, back_button, save_button, root

    def on_back():
        nonlocal nodes, is_subnode, parent_url
        if is_subnode and parent_url:
            nodes = login_and_get_nodes()
            is_subnode = False
            parent_url = None
            update_listbox(nodes, is_subnode)

    def on_save():
        if root:
            root.destroy()  # Cierra solo la ventana Tkinter

    root = tk.Tk()
    root.title("Seleccionar Nodo")

    label_text = "Selecciona un nodo para conectarte:"
    label = tk.Label(root, text=label_text)
    label.pack(pady=10)

    node_listbox = tk.Listbox(root)
    node_listbox.pack(pady=10)
    node_listbox.bind('<<ListboxSelect>>', on_node_select)

    back_button = tk.Button(root, text="Atrás", command=on_back)
    save_button = tk.Button(root, text="Guardar", command=on_save)

    update_listbox(nodes, is_subnode)

    root.mainloop()

# Función para mostrar la interfaz gráfica de inicio de sesión
def login_gui():
    def on_login():
        config['username'] = username_entry.get()
        config['password'] = password_entry.get()
        save_config(config)
        root.destroy()

    root = tk.Tk()
    root.title("Inicio de Sesión")

    tk.Label(root, text="Usuario:").pack(pady=5)
    username_entry = tk.Entry(root)
    username_entry.pack(pady=5)

    tk.Label(root, text="Contraseña:").pack(pady=5)
    password_entry = tk.Entry(root, show="*")
    password_entry.pack(pady=5)

    login_button = tk.Button(root, text="Iniciar Sesión", command=on_login)
    login_button.pack(pady=20)

    root.mainloop()

# Crear cliente de socket
sio = socketio.Client()

# Variable para almacenar el estado actual
current_status = None

# Conectar al servidor Flask
@sio.event
def connect():
    global current_status
    status = 'Conectado al servidor'
    if current_status != status:
        print(status)
        current_status = status
    sio.emit('user_connected', {'user': config['username'], 'node_id': config['node_id']})
    print(f'Emit user_connected event for {config["username"]} to node {config["node_id"]}')

@sio.event
def disconnect():
    global current_status
    status = 'Desconectado del servidor'
    if current_status != status:
        print(status)
        current_status = status
    sio.emit('user_disconnected', {'user': config['username'], 'node_id': config['node_id']})
    print(f'Emit user_disconnected event for {config["username"]} to node {config["node_id"]}')

# Manejar errores de conexión
@sio.on('connect_error')
def connect_error(data):
    global current_status
    status = f'Error de conexión: {data}'
    if current_status != status:
        print(status)
        current_status = status

# Manejar reconexión
@sio.on('reconnect')
def on_reconnect():
    global current_status
    status = 'Reconectado al servidor'
    if current_status != status:
        print(status)
        current_status = status
    sio.emit('user_connected', {'user': config['username'], 'node_id': config['node_id']})
    print(f'Reconnect and emit user_connected event for {config["username"]} to node {config["node_id"]}')

# Recibir y manejar lista de usuarios conectados
@sio.on('update_users')
def update_users(users):
    print('Usuarios conectados:', users)

# Manejar el inicio de una transmisión de audio
@sio.on('audio_start')
def on_audio_start(data):
    print(f"Transmisión de audio iniciada por {data['user']} en el nodo.")

# Recibir y manejar la transmisión de audio
@sio.on('receive_audio')
def on_receive_audio(data):
    print(f"Recibiendo audio de {data['user']} en el nodo.")

    # Decodificar el audio base64
    audio_data = base64.b64decode(data['audio'])

    # Reproducir el audio directamente con simpleaudio
    play_obj = sa.play_buffer(audio_data, 1, 2, 44100)
    play_obj.wait_done()

# Manejar el final de una transmisión de audio
@sio.on('audio_end')
def on_audio_end(data):
    print(f"Transmisión de audio finalizada por {data['user']} en el nodo.")

if __name__ == '__main__':
    config = load_config()
    if 'username' not in config or 'password' not in config:
        login_gui()

    if 'node_url' not in config or 'node_id' not in config:
        nodes = login_and_get_nodes()
        if nodes:
            setup_gui(nodes)
        else:
            print("No se encontraron nodos disponibles")
    else:
        # Obtener cookies y conectarse automáticamente
        login_and_get_nodes()
        connect_to_node(config['node_url'], config['node_id'])
