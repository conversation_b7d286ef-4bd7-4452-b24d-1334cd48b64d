{% extends "core/base.html" %}
{% load static %}
{# File: backend/apps/core/templates/core/dashboard_brigada.html #}

{% block title %}Panel de Brigada - RN-Rural{% endblock %}

{% block extra_head %}
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
  <link rel="stylesheet" href="{% static 'css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
  <!-- Token CSRF para las peticiones POST -->
  {% csrf_token %}

  <div class="row mb-3">
    <div class="col-md-12">
      <h2 class="mb-3">Panel de Brigada: {{ user.username }}</h2>
      <hr>
    </div>
  </div>

  <div class="row">
    <!-- <PERSON><PERSON>na <PERSON>: Mapa -->
    <div class="col-lg-8 col-md-7 mb-4">
      <div class="card card-dashboard">
        <div class="card-header card-header-custom bg-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Mapa de Incidencias Asignadas</h4>
          </div>
        </div>
        <div class="card-body p-0 position-relative">
          <div id="map-container" style="position: relative;">
            <div id="mapBrigada"></div>
            <div id="map-loading" class="map-loading">Cargando mapa...</div>
            <div id="map-error" class="map-error">No se pudo cargar el mapa. Por favor, verifica tu conexión.</div>
          </div>

          <div class="map-legend">
            <h6 class="mb-2">Leyenda</h6>
            <div class="legend-item">
              <div class="legend-color legend-brigada"></div>
              <span>Tu ubicación</span>
            </div>
            <div class="legend-item">
              <div class="legend-color legend-incidencia"></div>
              <span>Incidencia asignada</span>
            </div>
            <div class="legend-item">
              <div class="legend-color legend-usuario"></div>
              <span>Ubicación del reportante</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Columna Derecha: Información y Controles -->
    <div class="col-lg-4 col-md-5">
      <div class="card card-dashboard mb-4">
        <div class="card-header card-header-custom bg-info text-white">
          <h4 class="mb-0"><i class="fas fa-clipboard-list"></i> Incidencias Asignadas</h4>
        </div>
        <div class="card-body" id="incidencia-info">
          <div class="text-center py-4" id="no-incidencia">
            <p class="text-muted">No hay incidencias asignadas actualmente.</p>
          </div>
          <div id="incidencias-list" style="display: none; margin-bottom: 20px;">
            <h5>Selecciona una incidencia:</h5>
            <div class="list-group" id="lista-incidencias">
              <!-- Las incidencias se cargarán dinámicamente aquí -->
            </div>
          </div>
          <div id="incidencia-details" style="display: none;">
            <h4 id="incidencia-titulo">Incidencia #<span id="incidencia-id"></span></h4>
            <div class="mb-3">
              <small class="text-muted">Reportada por <span id="usuario-reporta"></span> el <span id="fecha-creacion"></span></small>
            </div>
            <div class="card mb-3">
              <div class="card-header bg-light">Descripción</div>
              <div class="card-body">
                <p id="incidencia-descripcion" class="mb-0"></p>
              </div>
            </div>
            <div class="alert alert-info distance-badge">
              <i class="fas fa-route"></i> Distancia estimada: <strong id="distancia-estimada">Calculando...</strong>
              <div id="tiempo-estimado" style="display: none; margin-top: 5px;"></div>
            </div>
            <div class="status-controls">
              <h5>Actualizar Estado</h5>
              <div class="d-grid gap-2">
                <button id="btn-en-proceso" class="btn btn-warning mb-2">
                  <i class="fas fa-spinner"></i> Marcar En Proceso
                </button>
                <button id="btn-resuelta" class="btn btn-success mb-2">
                  <i class="fas fa-check-circle"></i> Marcar como Resuelta
                </button>
                <button id="btn-no-resuelta" class="btn btn-secondary mb-2">
                  <i class="fas fa-times-circle"></i> No se pudo resolver
                </button>
                <a id="btn-chat" href="#" class="btn btn-info" style="display: none;">
                  <i class="fas fa-comments"></i> Abrir Chat
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card card-dashboard">
        <div class="card-header card-header-custom bg-primary text-white">
          <h4 class="mb-0"><i class="fas fa-bolt"></i> Acciones Rápidas</h4>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button id="btn-actualizar-ubicacion" class="btn btn-primary mb-2">
              <i class="fas fa-location-arrow"></i> Actualizar Mi Ubicación
            </button>
            <a href="{% url 'logout' %}" class="btn btn-outline-danger">
              <i class="fas fa-sign-out-alt"></i> Cerrar Sesión
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
  // Elementos del DOM
  const mapElement = document.getElementById('mapBrigada');
  const mapLoading = document.getElementById('map-loading');
  const mapError = document.getElementById('map-error');
  const noIncidenciaElement = document.getElementById('no-incidencia');
  const incidenciaDetailsElement = document.getElementById('incidencia-details');
  const incidenciaIdElement = document.getElementById('incidencia-id');
  const incidenciaDescripcionElement = document.getElementById('incidencia-descripcion');
  const usuarioReportaElement = document.getElementById('usuario-reporta');
  const fechaCreacionElement = document.getElementById('fecha-creacion');
  const distanciaEstimadaElement = document.getElementById('distancia-estimada');
  const btnEnProceso = document.getElementById('btn-en-proceso');
  const btnResuelta = document.getElementById('btn-resuelta');
  const btnNoResuelta = document.getElementById('btn-no-resuelta');
  const btnActualizarUbicacion = document.getElementById('btn-actualizar-ubicacion');

  if (!mapElement) {
    console.error("Elemento del mapa #mapBrigada no encontrado.");
    return;
  }

  // Función para mostrar un error en el mapa
  function showMapError(message) {
    if (mapLoading) mapLoading.style.display = 'none';
    if (mapError) {
      mapError.textContent = message || "No se pudo cargar el mapa. Por favor, verifica tu conexión.";
      mapError.style.display = 'block';
    }
  }

  // Asegurarse de que Leaflet esté cargado
  if (typeof L === 'undefined') {
    console.error("Leaflet no está cargado. Verifica la conexión a Internet.");
    showMapError("No se pudo cargar la biblioteca de mapas. Verifica tu conexión a Internet.");
    return;
  }

  // --- Configuración de Iconos de Leaflet ---
  try { delete L.Icon.Default.prototype._getIconUrl; } catch(e) { /* Ignorar error si ya fue borrado o no existe */ }
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
    iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
    shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
  });

  // Iconos personalizados
  const brigadaIcon = L.icon({
    iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
    shadowSize: [41, 41],
    className: 'brigada-icon'
  });

  const incidenciaIcon = L.icon({
    iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
    shadowSize: [41, 41],
    className: 'incidencia-icon'
  });

  const usuarioIcon = L.icon({
    iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
    shadowSize: [41, 41],
    className: 'usuario-icon'
  });

  // Variables para el mapa
  let initialLat = -40.8; // Latitud por defecto (centro de Río Negro aprox.)
  let initialLng = -63.0; // Longitud por defecto
  let initialZoom = 6;    // Zoom general para mostrar la provincia
  let map = null;
  let brigadaMarker = null;
  let incidenciaMarker = null;
  let usuarioMarker = null;
  let routeLine = null;
  let currentIncidenciaId = null;

  // Inicializar el mapa
  try {
    map = L.map(mapElement, {
      preferCanvas: true,
      attributionControl: true,
      zoomControl: true,
      minZoom: 3,
      maxZoom: 18
    }).setView([initialLat, initialLng], initialZoom);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 18,
      attribution: 'Map data © <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    console.log("Mapa inicializado correctamente");
  } catch (error) {
    console.error("Error al inicializar el mapa:", error);
    showMapError("Error al inicializar el mapa. Por favor, recarga la página.");
    if (mapLoading) mapLoading.style.display = 'none';
  }

  // Función para calcular la distancia entre dos puntos en km
  function calcularDistancia(lat1, lon1, lat2, lon2) {
    if (!lat1 || !lon1 || !lat2 || !lon2) return null;

    const R = 6371; // Radio de la Tierra en km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distancia en km
    return distance;
  }

  // Función para formatear la distancia
  function formatearDistancia(distancia) {
    if (distancia === null) return "No disponible";
    if (distancia < 1) {
      return Math.round(distancia * 1000) + " metros";
    } else {
      return distancia.toFixed(1) + " km";
    }
  }

  // Función para dibujar la línea entre brigada e incidencia
  function dibujarRuta(latBrigada, lngBrigada, latIncidencia, lngIncidencia, rutaCoords) {
    // Eliminar ruta anterior si existe
    if (routeLine) {
      map.removeLayer(routeLine);
    }

    if (rutaCoords && rutaCoords.length > 0) {
      // Dibujar ruta real usando las coordenadas de la API
      routeLine = L.polyline(rutaCoords, {
        color: '#3388ff',
        weight: 5,
        opacity: 0.8,
        className: 'route-line'
      }).addTo(map);

      // Ajustar el mapa para mostrar toda la ruta
      map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });
    } else {
      // Dibujar nueva ruta en línea recta (fallback)
      routeLine = L.polyline([
        [latBrigada, lngBrigada],
        [latIncidencia, lngIncidencia]
      ], {
        color: '#3388ff',
        weight: 4,
        opacity: 0.7,
        dashArray: '10, 10',
        className: 'route-line'
      }).addTo(map);

      // Ajustar el mapa para mostrar toda la ruta
      map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });

      // Calcular distancia en línea recta
      const distancia = calcularDistancia(latBrigada, lngBrigada, latIncidencia, lngIncidencia);
      distanciaEstimadaElement.textContent = formatearDistancia(distancia);

      // Estimar tiempo basado en velocidad promedio de 40 km/h
      const minutos = Math.round((distancia / 40) * 60); // 40 km/h convertido a minutos
      const tiempoEstimado = document.getElementById('tiempo-estimado');
      if (tiempoEstimado) {
        tiempoEstimado.textContent = `Tiempo estimado aproximado: ${minutos} minutos`;
        tiempoEstimado.style.display = 'block';
      }

      // Solicitar cálculo de ruta al backend
      actualizarRutaDesdeBackend(latBrigada, lngBrigada, latIncidencia, lngIncidencia);
    }
  }

  // Función para solicitar cálculo de ruta al backend
  function actualizarRutaDesdeBackend(latBrigada, lngBrigada, latIncidencia, lngIncidencia) {
    // Construir la URL con los parámetros de coordenadas
    const url = `{% url 'calcular_ruta' %}?start_lat=${latBrigada}&start_lng=${lngBrigada}&end_lat=${latIncidencia}&end_lng=${lngIncidencia}`;

    // Realizar la solicitud al backend
    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Error de red: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.success && data.route && data.route.coordinates && data.route.coordinates.length > 0) {
          // Eliminar ruta anterior
          if (routeLine) {
            map.removeLayer(routeLine);
          }

          // Dibujar la nueva ruta
          routeLine = L.polyline(data.route.coordinates, {
            color: '#3388ff',
            weight: 5,
            opacity: 0.8,
            className: 'route-line'
          }).addTo(map);

          // Ajustar el mapa para mostrar toda la ruta
          map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });

          // Actualizar la distancia y el tiempo estimado
          const distanciaKm = data.route.distance / 1000; // Convertir de metros a km
          distanciaEstimadaElement.textContent = formatearDistancia(distanciaKm);

          // Mostrar tiempo estimado
          const minutos = Math.round(data.route.duration / 60); // Convertir de segundos a minutos
          const tiempoEstimado = document.getElementById('tiempo-estimado');
          if (tiempoEstimado) {
            tiempoEstimado.textContent = `Tiempo estimado: ${minutos} minutos`;
            tiempoEstimado.style.display = 'block';
          }
        }
      })
      .catch(error => {
        console.error("Error al solicitar ruta al backend:", error);
        // Ya tenemos la línea recta como fallback, no es necesario hacer nada más
      });
  }

  // Función para actualizar la ubicación de la brigada
  function actualizarUbicacionBrigada() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function(position) {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;

          // Actualizar marcador de la brigada
          if (brigadaMarker) {
            map.removeLayer(brigadaMarker);
          }

          brigadaMarker = L.marker([lat, lng], {
            icon: brigadaIcon
          }).addTo(map).bindPopup("Tu ubicación actual").openPopup();

          // Si hay una incidencia, actualizar la distancia y la ruta
          if (incidenciaMarker) {
            const incLat = incidenciaMarker.getLatLng().lat;
            const incLng = incidenciaMarker.getLatLng().lng;
            const distancia = calcularDistancia(lat, lng, incLat, incLng);
            distanciaEstimadaElement.textContent = formatearDistancia(distancia);
            dibujarRuta(lat, lng, incLat, incLng);
          }

          // Enviar la ubicación al servidor (implementar más adelante)
          console.log("Ubicación actualizada:", lat, lng);
        },
        function(error) {
          console.error("Error al obtener la ubicación:", error);
          alert("No se pudo obtener tu ubicación. Verifica que el GPS esté activado.");
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        }
      );
    } else {
      alert("Tu navegador no soporta geolocalización.");
    }
  }

  // Función para cargar los datos de las incidencias asignadas
  function cargarIncidenciaAsignada() {
    fetch("{% url 'map_brigada' %}")
      .then(response => {
        if (response.status === 204) {
          return null;
        }
        return response.json();
      })
      .then(data => {
        if (mapLoading) mapLoading.style.display = 'none';

        if (!data || data.detail === "sin_incidencia") {
          // No hay incidencias asignadas
          noIncidenciaElement.style.display = "block";
          document.getElementById('incidencias-list').style.display = "none";
          incidenciaDetailsElement.style.display = "none";

          // Limpiar marcador de incidencia si existe
          if (incidenciaMarker) {
            map.removeLayer(incidenciaMarker);
            incidenciaMarker = null;
          }

          // Limpiar ruta si existe
          if (routeLine) {
            map.removeLayer(routeLine);
            routeLine = null;
          }

          // Centrar en la ubicación de la brigada si está disponible
          if (brigadaMarker) {
            map.setView(brigadaMarker.getLatLng(), 13);
          }

          return;
        }

        // Hay incidencias asignadas
        const incidencias = data.incidencias;
        const incidenciaActiva = data.incidencia_activa;

        if (!incidencias || incidencias.length === 0) {
          noIncidenciaElement.style.display = "block";
          document.getElementById('incidencias-list').style.display = "none";
          incidenciaDetailsElement.style.display = "none";
          return;
        }

        // Mostrar la lista de incidencias
        noIncidenciaElement.style.display = "none";
        const incidenciasListElement = document.getElementById('incidencias-list');
        incidenciasListElement.style.display = "block";

        // Limpiar la lista de incidencias
        const listaIncidenciasElement = document.getElementById('lista-incidencias');
        listaIncidenciasElement.innerHTML = '';

        // Añadir cada incidencia a la lista
        incidencias.forEach(incidencia => {
          const incidenciaItem = document.createElement('a');
          incidenciaItem.href = '#';
          incidenciaItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';
          incidenciaItem.dataset.incidenciaId = incidencia.id;

          // Determinar el color según el estado
          let badgeClass = '';
          if (incidencia.estado === 'DERIVADA_BRIGADA') {
            badgeClass = 'bg-warning text-dark';
          } else if (incidencia.estado === 'EN_PROCESO_BRIGADA') {
            badgeClass = 'bg-primary';
          }

          incidenciaItem.innerHTML = `
            <div>
              <strong>Incidencia #${incidencia.id}</strong>
              <div><small>${incidencia.fecha_creacion}</small></div>
            </div>
            <span class="badge ${badgeClass}">${incidencia.estado === 'DERIVADA_BRIGADA' ? 'Pendiente' : 'En Proceso'}</span>
          `;

          // Añadir evento click para mostrar los detalles de la incidencia
          incidenciaItem.addEventListener('click', function(e) {
            e.preventDefault();
            mostrarDetallesIncidencia(incidencia);

            // Marcar esta incidencia como activa
            document.querySelectorAll('#lista-incidencias a').forEach(item => {
              item.classList.remove('active');
            });
            this.classList.add('active');
          });

          listaIncidenciasElement.appendChild(incidenciaItem);
        });

        // Mostrar los detalles de la incidencia activa (la primera por defecto)
        if (incidenciaActiva) {
          mostrarDetallesIncidencia(incidenciaActiva);

          // Marcar la primera incidencia como activa
          const primerItem = document.querySelector('#lista-incidencias a');
          if (primerItem) {
            primerItem.classList.add('active');
          }
        }
      })
      .catch(error => {
        console.error("Error al cargar datos de incidencia:", error);
        if (mapLoading) mapLoading.style.display = 'none';
      });
  }

  // Función para mostrar los detalles de una incidencia
  function mostrarDetallesIncidencia(incidencia) {
    // Verificar que las coordenadas sean válidas
    if (!incidencia.lat || !incidencia.lng) {
      console.error("Coordenadas de incidencia inválidas:", incidencia);
      return;
    }

    // Actualizar la UI
    incidenciaDetailsElement.style.display = "block";
    incidenciaIdElement.textContent = incidencia.id;
    usuarioReportaElement.textContent = incidencia.usuario_reporta || "Usuario desconocido";
    fechaCreacionElement.textContent = incidencia.fecha_creacion || "Fecha desconocida";

    // Actualizar el ID de la incidencia actual
    currentIncidenciaId = incidencia.id;

    // Actualizar el botón de chat
    const btnChat = document.getElementById('btn-chat');
    if (btnChat) {
      btnChat.href = `/chat/incidencia/${incidencia.id}/`;
      btnChat.style.display = 'block';
    }

    // Actualizar descripción
    incidenciaDescripcionElement.textContent = incidencia.descripcion || "Sin descripción disponible";

    // Actualizar marcador de incidencia
    if (incidenciaMarker) {
      map.removeLayer(incidenciaMarker);
    }

    incidenciaMarker = L.marker([incidencia.lat, incidencia.lng], {
      icon: incidenciaIcon
    }).addTo(map).bindPopup(`<b>Incidencia #${incidencia.id}</b><br>Estado: ${incidencia.estado}`);

    // Si hay ubicación de brigada, actualizar distancia y ruta
    if (brigadaMarker) {
      const brigLat = brigadaMarker.getLatLng().lat;
      const brigLng = brigadaMarker.getLatLng().lng;

      // Usar la distancia calculada por la API si está disponible
      if (incidencia.ruta && incidencia.ruta.distancia) {
        const distanciaKm = incidencia.ruta.distancia / 1000; // Convertir de metros a km
        distanciaEstimadaElement.textContent = formatearDistancia(distanciaKm);

        // Mostrar tiempo estimado si está disponible
        if (incidencia.ruta.duracion) {
          const minutos = Math.round(incidencia.ruta.duracion / 60); // Convertir de segundos a minutos
          const tiempoEstimado = document.getElementById('tiempo-estimado');
          if (tiempoEstimado) {
            tiempoEstimado.textContent = `Tiempo estimado: ${minutos} minutos`;
            tiempoEstimado.style.display = 'block';
          }
        }

        // Dibujar ruta real si hay coordenadas disponibles
        if (incidencia.ruta.coordenadas && incidencia.ruta.coordenadas.length > 0) {
          dibujarRuta(brigLat, brigLng, incidencia.lat, incidencia.lng, incidencia.ruta.coordenadas);
        } else {
          dibujarRuta(brigLat, brigLng, incidencia.lat, incidencia.lng);
        }
      } else {
        // Fallback a cálculo de distancia en línea recta
        const distancia = calcularDistancia(brigLat, brigLng, incidencia.lat, incidencia.lng);
        distanciaEstimadaElement.textContent = formatearDistancia(distancia);
        dibujarRuta(brigLat, brigLng, incidencia.lat, incidencia.lng);
      }
    } else {
      distanciaEstimadaElement.textContent = "Actualiza tu ubicación";
    }

    // Si hay ubicación del usuario que reportó, mostrarla también
    if (incidencia.ubicacion_usuario && incidencia.ubicacion_usuario.lat && incidencia.ubicacion_usuario.lng) {
      // Limpiar marcador anterior si existe
      if (usuarioMarker) {
        map.removeLayer(usuarioMarker);
      }

      // Añadir marcador para la ubicación del usuario
      usuarioMarker = L.marker([incidencia.ubicacion_usuario.lat, incidencia.ubicacion_usuario.lng], {
        icon: usuarioIcon
      }).addTo(map).bindPopup(`<b>Ubicación de ${incidencia.usuario_reporta}</b><br>Reportante de la incidencia`);

      // Ajustar la vista para mostrar todos los marcadores
      const bounds = L.latLngBounds([
        [incidencia.lat, incidencia.lng],
        [incidencia.ubicacion_usuario.lat, incidencia.ubicacion_usuario.lng]
      ]);

      if (brigadaMarker) {
        bounds.extend(brigadaMarker.getLatLng());
      }

      map.fitBounds(bounds, { padding: [50, 50] });
    } else {
      // Si no hay ubicación del usuario, limpiar el marcador si existe
      if (usuarioMarker) {
        map.removeLayer(usuarioMarker);
        usuarioMarker = null;
      }
    }

    // Actualizar estado de los botones según el estado de la incidencia
    actualizarEstadoBotones(incidencia.estado);
  }

  // Función para actualizar el estado de una incidencia
  function actualizarEstadoIncidencia(nuevoEstado) {
    if (!currentIncidenciaId) return;

    // Mostrar indicador de carga
    const btnEnProceso = document.getElementById('btn-en-proceso');
    const btnResuelta = document.getElementById('btn-resuelta');
    const btnNoResuelta = document.getElementById('btn-no-resuelta');

    // Deshabilitar todos los botones durante la operación
    btnEnProceso.disabled = true;
    btnResuelta.disabled = true;
    btnNoResuelta.disabled = true;

    // Mostrar texto de carga en el botón correspondiente
    if (nuevoEstado === 'EN_PROCESO_BRIGADA') {
      btnEnProceso.innerHTML = 'Actualizando...';
    } else if (nuevoEstado === 'CERRADA_RESUELTA') {
      btnResuelta.innerHTML = 'Actualizando...';
    } else if (nuevoEstado === 'CERRADA_NO_RESUELTA') {
      btnNoResuelta.innerHTML = 'Actualizando...';
    }

    // Crear FormData para enviar los datos
    const formData = new FormData();
    formData.append('incidencia_id', currentIncidenciaId);
    formData.append('nuevo_estado', nuevoEstado);

    // Obtener el token CSRF
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Realizar la petición a la API
    fetch("{% url 'actualizar_estado_incidencia' %}", {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRFToken': csrfToken
      }
    })
    .then(response => response.json())
    .then(data => {
      // Restaurar el texto de los botones
      btnEnProceso.innerHTML = 'Marcar En Proceso';
      btnResuelta.innerHTML = 'Marcar como Resuelta';
      btnNoResuelta.innerHTML = 'No se pudo resolver';

      if (data.success) {
        // Mostrar mensaje de éxito
        alert(data.mensaje);

        // Recargar los datos
        cargarIncidenciaAsignada();
      } else {
        // Mostrar mensaje de error
        alert(`Error: ${data.error || 'No se pudo actualizar el estado'}`);

        // Obtener el estado actual de la incidencia y restaurar los botones
        fetch("{% url 'map_brigada' %}")
          .then(response => response.json())
          .then(data => {
            if (data.incidencias && data.incidencias.length > 0) {
              // Buscar la incidencia actual
              const incidenciaActual = data.incidencias.find(inc => inc.id === currentIncidenciaId);
              if (incidenciaActual) {
                actualizarEstadoBotones(incidenciaActual.estado);
              }
            }
          })
          .catch(error => {
            console.error("Error al obtener el estado actual:", error);
            // Habilitar todos los botones como fallback
            btnEnProceso.disabled = false;
            btnResuelta.disabled = false;
            btnNoResuelta.disabled = false;
          });
      }
    })
    .catch(error => {
      console.error('Error al actualizar estado:', error);
      alert('Error al comunicarse con el servidor. Por favor, intenta de nuevo.');

      // Restaurar el texto de los botones
      btnEnProceso.innerHTML = 'Marcar En Proceso';
      btnResuelta.innerHTML = 'Marcar como Resuelta';
      btnNoResuelta.innerHTML = 'No se pudo resolver';

      // Obtener el estado actual de la incidencia y restaurar los botones
      fetch("{% url 'map_brigada' %}")
        .then(response => response.json())
        .then(data => {
          if (data.incidencias && data.incidencias.length > 0) {
            // Buscar la incidencia actual
            const incidenciaActual = data.incidencias.find(inc => inc.id === currentIncidenciaId);
            if (incidenciaActual) {
              actualizarEstadoBotones(incidenciaActual.estado);
            }
          }
        })
        .catch(error => {
          console.error("Error al obtener el estado actual:", error);
          // Habilitar todos los botones como fallback
          btnEnProceso.disabled = false;
          btnResuelta.disabled = false;
          btnNoResuelta.disabled = false;
        });
    });
  }

  // Función para actualizar el estado de los botones según el estado de la incidencia
  function actualizarEstadoBotones(estado) {
    if (estado === "DERIVADA_BRIGADA") {
      btnEnProceso.disabled = false;
      btnResuelta.disabled = true;
      btnNoResuelta.disabled = true;
    } else if (estado === "EN_PROCESO_BRIGADA") {
      btnEnProceso.disabled = true;
      btnResuelta.disabled = false;
      btnNoResuelta.disabled = false;
    } else {
      btnEnProceso.disabled = true;
      btnResuelta.disabled = true;
      btnNoResuelta.disabled = true;
    }
  }

  // Event listeners para los botones
  btnEnProceso.addEventListener('click', function() {
    actualizarEstadoIncidencia('EN_PROCESO_BRIGADA');
  });

  btnResuelta.addEventListener('click', function() {
    actualizarEstadoIncidencia('CERRADA_RESUELTA');
  });

  btnNoResuelta.addEventListener('click', function() {
    actualizarEstadoIncidencia('CERRADA_NO_RESUELTA');
  });

  btnActualizarUbicacion.addEventListener('click', function() {
    actualizarUbicacionBrigada();
  });

  // Cargar datos iniciales
  cargarIncidenciaAsignada();

  // Actualizar ubicación inicial
  actualizarUbicacionBrigada();

  // Configurar actualización periódica
  setInterval(cargarIncidenciaAsignada, 30000); // Cada 30 segundos

  // Evento cuando el mapa termina de cargar
  map.on('load', function() {
    if (mapLoading) mapLoading.style.display = 'none';
  });

  // Si después de 5 segundos sigue cargando, ocultar el indicador de carga
  setTimeout(function() {
    if (mapLoading) mapLoading.style.display = 'none';
  }, 5000);

  // Configurar WebSocket para notificaciones en tiempo real
  setupWebSocket();

  // Función para configurar WebSocket
  function setupWebSocket() {
    // Obtener el ID del usuario actual
    const userId = "{{ request.user.id }}";

    // Crear conexión WebSocket
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${wsProtocol}//${window.location.host}/ws/notifications/${userId}/`;

    console.log("Intentando conectar a WebSocket:", wsUrl);

    const socket = new WebSocket(wsUrl);

    // Evento de conexión establecida
    socket.onopen = function(e) {
      console.log("Conexión WebSocket establecida");
    };

    // Evento de mensaje recibido
    socket.onmessage = function(e) {
      const data = JSON.parse(e.data);
      console.log("Mensaje WebSocket recibido:", data);

      // Procesar según el tipo de notificación
      if (data.notification_type === 'incidencia_asignada') {
        // Mostrar notificación de nueva incidencia asignada
        Swal.fire({
          title: data.title,
          text: data.message,
          icon: 'info',
          confirmButtonText: 'Ver detalles',
          showCancelButton: true,
          cancelButtonText: 'Cerrar'
        }).then((result) => {
          if (result.isConfirmed) {
            // Recargar la página para mostrar la nueva incidencia
            window.location.reload();
          }
        });

        // Reproducir sonido de notificación
        playNotificationSound();

        // Recargar los datos de la incidencia
        cargarIncidenciaAsignada();
      }
    };

    // Evento de error
    socket.onerror = function(e) {
      console.error("Error en la conexión WebSocket:", e);
    };

    // Evento de cierre de conexión
    socket.onclose = function(e) {
      console.log("Conexión WebSocket cerrada. Código:", e.code, "Razón:", e.reason);

      // Intentar reconectar después de 5 segundos
      setTimeout(function() {
        console.log("Intentando reconectar WebSocket...");
        setupWebSocket();
      }, 5000);
    };

    // Enviar ping periódicamente para mantener la conexión viva
    setInterval(function() {
      if (socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({type: 'ping'}));
      }
    }, 30000); // Cada 30 segundos
  }

  // Función para reproducir sonido de notificación
  function playNotificationSound() {
    try {
      const audio = new Audio('/static/sounds/notification.mp3');
      audio.play();
    } catch (e) {
      console.error("Error al reproducir sonido:", e);
    }
  }
});
</script>
{% endblock %}
