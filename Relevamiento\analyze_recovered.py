#!/usr/bin/env python3
# --- Archivo: analyze_recovered.py ---
# Script para analizar archivos SQLite recuperados

import os
import sqlite3
import glob
from datetime import datetime

def analyze_sqlite_file(file_path):
    """Analizar un archivo SQLite específico."""
    print(f"\n🔍 Analizando: {os.path.basename(file_path)}")
    
    # Verificar tamaño
    size = os.path.getsize(file_path)
    print(f"  📊 Tamaño: {size:,} bytes")
    
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        
        # Obtener tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        if not tables:
            print(f"  ❌ No hay tablas (archivo vacío o corrupto)")
            conn.close()
            return None
        
        print(f"  📋 Tablas: {tables}")
        
        # Verificar si tiene las tablas que necesitamos
        important_tables = ['user', 'point', 'image', 'camera']
        found_tables = [table for table in important_tables if table in tables]
        
        if not found_tables:
            print(f"  ⚠️  No contiene tablas importantes")
            conn.close()
            return None
        
        print(f"  ✅ Tablas importantes encontradas: {found_tables}")
        
        # Contar registros en cada tabla
        data_summary = {}
        total_records = 0
        
        for table in found_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                data_summary[table] = count
                total_records += count
                print(f"    📊 {table}: {count} registros")
                
                # Mostrar muestras de datos importantes
                if table == 'point' and count > 0:
                    cursor.execute("SELECT name, city FROM point LIMIT 3;")
                    points = cursor.fetchall()
                    print(f"      📍 Ejemplos: {points}")
                
                elif table == 'user' and count > 0:
                    cursor.execute("SELECT username FROM user LIMIT 5;")
                    users = cursor.fetchall()
                    print(f"      👥 Usuarios: {[u[0] for u in users]}")
                
                elif table == 'image' and count > 0:
                    cursor.execute("SELECT filename FROM image LIMIT 3;")
                    images = cursor.fetchall()
                    print(f"      🖼️  Imágenes: {[i[0] for i in images]}")
                
            except Exception as e:
                print(f"    ❌ Error leyendo {table}: {e}")
                data_summary[table] = 0
        
        conn.close()
        
        # Evaluar calidad del archivo
        score = 0
        if 'point' in data_summary and data_summary['point'] > 1000:
            score += 100  # Muchos puntos = muy bueno
        elif 'point' in data_summary and data_summary['point'] > 100:
            score += 50   # Algunos puntos = bueno
        
        if 'user' in data_summary and data_summary['user'] > 0:
            score += 20   # Tiene usuarios
        
        if 'image' in data_summary and data_summary['image'] > 0:
            score += 10   # Tiene imágenes
        
        if 'camera' in data_summary and data_summary['camera'] > 0:
            score += 10   # Tiene cámaras
        
        if total_records > 1000:
            score += 30   # Muchos datos totales
        
        print(f"  🎯 Puntuación: {score}/170")
        
        if score >= 100:
            print(f"  🎉 ¡EXCELENTE! Posible archivo original")
        elif score >= 50:
            print(f"  ✅ BUENO: Contiene datos útiles")
        elif score >= 20:
            print(f"  ⚠️  REGULAR: Algunos datos")
        else:
            print(f"  ❌ POBRE: Pocos o ningún dato útil")
        
        return {
            'file': file_path,
            'size': size,
            'tables': tables,
            'data': data_summary,
            'total_records': total_records,
            'score': score
        }
        
    except Exception as e:
        print(f"  ❌ Error analizando archivo: {e}")
        return None

def find_all_sqlite_files():
    """Encontrar todos los archivos SQLite en directorios de recuperación."""
    print("🔍 Buscando archivos SQLite recuperados...")
    
    search_patterns = [
        "*.sqlite",
        "*.db",
        "**/recup_dir.*/*.sqlite",
        "**/recup_dir.*/*.db"
    ]
    
    all_files = []
    
    for pattern in search_patterns:
        files = glob.glob(pattern, recursive=True)
        all_files.extend(files)
    
    # Eliminar duplicados y ordenar por tamaño
    unique_files = list(set(all_files))
    unique_files.sort(key=lambda x: os.path.getsize(x), reverse=True)
    
    print(f"📁 Encontrados {len(unique_files)} archivos SQLite")
    
    return unique_files

def main():
    """Función principal."""
    print("🔍 Analizador de Archivos SQLite Recuperados")
    print("=" * 50)
    
    # Buscar archivos
    sqlite_files = find_all_sqlite_files()
    
    if not sqlite_files:
        print("❌ No se encontraron archivos SQLite")
        return
    
    # Analizar cada archivo
    results = []
    
    for file_path in sqlite_files:
        result = analyze_sqlite_file(file_path)
        if result:
            results.append(result)
    
    # Ordenar por puntuación
    results.sort(key=lambda x: x['score'], reverse=True)
    
    print(f"\n📊 RESUMEN DE RESULTADOS")
    print("=" * 30)
    
    if not results:
        print("❌ No se encontraron archivos SQLite válidos")
        return
    
    print(f"🏆 MEJORES CANDIDATOS:")
    
    for i, result in enumerate(results[:5], 1):
        file_name = os.path.basename(result['file'])
        print(f"\n{i}. 📄 {file_name}")
        print(f"   📊 Tamaño: {result['size']:,} bytes")
        print(f"   🎯 Puntuación: {result['score']}/170")
        print(f"   📋 Datos: {result['data']}")
        
        if result['score'] >= 100:
            print(f"   🎉 ¡RECOMENDADO PARA RESTAURAR!")
    
    # Mostrar comandos para restaurar el mejor
    if results and results[0]['score'] >= 50:
        best_file = results[0]['file']
        print(f"\n🚀 COMANDO PARA RESTAURAR EL MEJOR:")
        print(f"cp '{best_file}' /home/<USER>/instance/app.db.recovered")
        print(f"cd /home/<USER>")
        print(f"sqlite3 instance/app.db.recovered '.tables'")
        print(f"sqlite3 instance/app.db.recovered 'SELECT COUNT(*) FROM point;'")
        
        # Crear script de restauración
        script_content = f'''#!/bin/bash
# Script para restaurar la mejor base de datos encontrada

echo "🔄 Restaurando base de datos recuperada..."

BEST_FILE="{best_file}"
TARGET="/home/<USER>/instance/app.db.recovered"

# Hacer backup de la actual si existe
if [ -f "/home/<USER>/instance/app.db" ]; then
    cp /home/<USER>/instance/app.db /home/<USER>/instance/app.db.backup_$(date +%Y%m%d_%H%M%S)
    echo "✅ Backup de la actual creado"
fi

# Copiar archivo recuperado
cp "$BEST_FILE" "$TARGET"
echo "✅ Archivo recuperado copiado a: $TARGET"

# Verificar contenido
echo ""
echo "📊 Verificando contenido:"
sqlite3 "$TARGET" "SELECT COUNT(*) FROM point;" 2>/dev/null && echo "✅ Tabla point accesible"
sqlite3 "$TARGET" "SELECT COUNT(*) FROM user;" 2>/dev/null && echo "✅ Tabla user accesible"
sqlite3 "$TARGET" "SELECT COUNT(*) FROM image;" 2>/dev/null && echo "✅ Tabla image accesible"
sqlite3 "$TARGET" "SELECT COUNT(*) FROM camera;" 2>/dev/null && echo "✅ Tabla camera accesible"

echo ""
echo "🎉 ¡Restauración completada!"
echo "📁 Archivo restaurado: $TARGET"
echo ""
echo "🚀 Próximos pasos:"
echo "   1. Verificar datos: sqlite3 $TARGET '.tables'"
echo "   2. Si está correcto: mv $TARGET /home/<USER>/instance/app.db"
echo "   3. Reiniciar aplicación: systemctl restart relevamiento"
'''
        
        with open('restore_best.sh', 'w') as f:
            f.write(script_content)
        
        os.chmod('restore_best.sh', 0o755)
        print(f"\n📝 Script de restauración creado: restore_best.sh")
        print(f"🚀 Ejecuta: ./restore_best.sh")

if __name__ == "__main__":
    main()
