# File: backend/.env.example
# -----------------------------------------------

DEBUG=True
SECRET_KEY='isaias52'
DATABASE_URL='postgis://rn_rural_user:tu_password@localhost:5432/rn_rural_db'
ALLOWED_HOSTS='localhost,127.0.0.,0.0.0.0'

# Para Channels (si usas Redis como broker)
CHANNEL_LAYER_REDIS_HOST='localhost'
CHANNEL_LAYER_REDIS_PORT='6379'

# Para almacenamiento de medios (ej. S3) - añadir más adelante
# AWS_ACCESS_KEY_ID=''
# AWS_SECRET_ACCESS_KEY=''
# AWS_STORAGE_BUCKET_NAME=''
# AWS_S3_REGION_NAME=''
# AWS_S3_ENDPOINT_URL='' # Para MinIO u otros S3 compatibles
