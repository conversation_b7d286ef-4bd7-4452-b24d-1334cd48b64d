# app/models.py

from datetime import datetime
from decimal import Decimal
from app import db, login_manager # Asegúrate que db y login_manager se inicializan en app/__init__.py
from flask_login import UserMixin
from sqlalchemy import Enum, ForeignKey, CheckConstraint, UniqueConstraint, Index, text# Importar constraints
from sqlalchemy.orm import relationship, validates

@login_manager.user_loader
def load_user(user_id):
    # Usar db.session.get() que es el método preferido en SQLAlchemy 2.0+
    return db.session.get(User, int(user_id))

class User(db.Model, UserMixin):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(255), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False)
    role = db.Column(db.Enum('administrador', 'secretaria', 'pastorado', 'miembro', 'instituto', 'rector', 'profesor_corporativo'), nullable=False)

    first_name = db.Column(db.String(255), nullable=False)
    last_name = db.Column(db.String(255), nullable=False)
    dni = db.Column(db.String(20), unique=True, nullable=True)
    ciudad = db.Column(db.String(255), nullable=True)
    estado_civil = db.Column(db.String(50), nullable=True)
    phone_number = db.Column(db.String(255), nullable=True)
    address = db.Column(db.String(255), nullable=True)
    date_of_birth = db.Column(db.Date, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id', name='fk_user_church_id', ondelete='SET NULL'), nullable=True)

    # Relaciones con roles de pastor
    pastor_roles = db.relationship('PastorRole', secondary='user_pastor_roles',
                                   lazy='dynamic')

    user_documents = db.relationship('Document',
                                     back_populates='uploader',
                                     foreign_keys='Document.uploaded_by',
                                     lazy='dynamic',
                                     cascade='all, delete-orphan')

    messages_sent = db.relationship('Message', foreign_keys='Message.sender_id',
                                    backref='sender', lazy='dynamic', cascade='all, delete-orphan')
    messages_received = db.relationship('Message', foreign_keys='Message.receiver_id',
                                        backref='receiver', lazy='dynamic', cascade='all, delete-orphan')

    maps_created = db.relationship('Map', backref='creator', lazy='dynamic')

    # Relaciones Jerarquía Pastor -> Supervisor
    supervisores = db.relationship('JerarquiaPastores', foreign_keys='JerarquiaPastores.pastor_id',
                                   backref='pastor', lazy='dynamic', cascade='all, delete-orphan')
    pastores_supervisados = db.relationship('JerarquiaPastores', foreign_keys='JerarquiaPastores.supervisor_id',
                                            backref='supervisor', lazy='dynamic', cascade='all, delete-orphan')

    # Relaciones Jerarquía Superintendente <-> Jefe de Sector
    superintendente_de = db.relationship('JerarquiaSuperintendentes',
                                         foreign_keys='JerarquiaSuperintendentes.superintendente_id',
                                         back_populates='superintendente',
                                         lazy='dynamic',
                                         cascade='all, delete-orphan')

    jefes_de = db.relationship('JerarquiaSuperintendentes',
                               foreign_keys='JerarquiaSuperintendentes.jefe_sector_id',
                               back_populates='jefe_sector',
                               lazy='dynamic',
                               cascade='all, delete-orphan')

    # Relaciones Familiares
    relationships1 = db.relationship('UserRelationship', foreign_keys='UserRelationship.user_id_1',
                                     backref='user1', lazy='dynamic', cascade='all, delete-orphan')
    relationships2 = db.relationship('UserRelationship', foreign_keys='UserRelationship.user_id_2',
                                     backref='user2', lazy='dynamic', cascade='all, delete-orphan')

    # Reseñas
    reviews = db.relationship('UserReview', foreign_keys='UserReview.user_id',
                              back_populates='user_reviewed',
                              lazy='dynamic', cascade='all, delete-orphan')
    reviews_written = db.relationship('UserReview', foreign_keys='UserReview.reviewer_id',
                                      back_populates='reviewer',
                                      lazy='dynamic')

    # Otros
    calendar_events = db.relationship('CalendarEvent', backref='user',
                                      lazy='dynamic', cascade='all, delete-orphan')
    transactions = db.relationship('Transaction', foreign_keys='Transaction.user_id',
                                   backref='user_registrar', lazy='dynamic')

    # Relación Uno-a-Uno con Pastor o Miembro (definido en modelos respectivos)
    # Relación con iglesia gobernada (definido en Church con post_update)

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', name='{self.full_name}')>"

# --- Modelo Reseñas de Usuario (Histórico) ---
class UserReview(db.Model):
    __tablename__ = 'user_reviews'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)  # Usuario evaluado
    reviewer_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True)  # Usuario que escribe la reseña
    review_text = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relación con el usuario QUE FUE REVISADO
    user_reviewed = db.relationship('User', foreign_keys=[user_id], back_populates='reviews')
    # Relación con el usuario QUE ESCRIBIÓ la reseña
    reviewer = db.relationship('User', foreign_keys=[reviewer_id], back_populates='reviews_written')

    def __repr__(self):
        return f"<UserReview(id={self.id}, user_id={self.user_id}, reviewer_id={self.reviewer_id})>"

# --- Modelo Pastores ---
class Pastor(db.Model):
    __tablename__ = 'pastores'
    id = db.Column(db.Integer, primary_key=True)
    # Clave foránea única a User, con eliminación en cascada
    user_id = db.Column(db.Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True)
    # Relación Uno-a-Uno con User
    user = db.relationship('User', backref=db.backref('pastor', uselist=False, cascade="all, delete-orphan"))

    # Campos específicos
    latitude = db.Column(db.Float, nullable=True)
    longitude = db.Column(db.Float, nullable=True)
    address = db.Column(db.String(255), nullable=True) # Dirección específica de la Casa Pastoral
    grado = db.Column(db.String(50), nullable=True)
    matricula = db.Column(db.String(50), nullable=True)
    fecha_promocion = db.Column(db.Date, nullable=True)
    fecha_graduacion = db.Column(db.Date, nullable=True)
    foto_pastor = db.Column(db.String(255), nullable=True) # Path a la foto?

    def __repr__(self):
        return f"<Pastor(user_id={self.user_id}, grado='{self.grado}')>"

# --- Modelo Iglesia ---
class Church(db.Model):
    __tablename__ = 'churches'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    address = db.Column(db.String(255), nullable=True)
    city = db.Column(db.String(255), nullable=True)
    province = db.Column(db.String(255), nullable=True)
    district = db.Column(db.String(255), nullable=True)
    latitude = db.Column(db.Float, nullable=True)
    longitude = db.Column(db.Float, nullable=True)
    tipo = db.Column(db.Enum('Principal', 'Anexo'), server_default="Principal", nullable=False)

    # Pastor que gobierna esta iglesia
    pastor_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_church_pastor_id', ondelete='SET NULL'), nullable=True)
    # Relación para obtener el pastor que gobierna
    # *** CORRECCIÓN CLAVE: Añadido post_update=True al backref para romper ciclo ***
    pastor = db.relationship('User',
                             backref=db.backref('church_governed', lazy='dynamic', post_update=True),
                             foreign_keys=[pastor_id])

    # Usuarios que pertenecen a esta iglesia (backref='church' permite user.church)
    users = db.relationship('User', foreign_keys='User.church_id', backref='church', lazy='dynamic')

    # Otras relaciones (cascade en Members, Inventory, Accounts)
    members = db.relationship('Member', backref='church', lazy='dynamic', cascade="all, delete-orphan")
    inventory = db.relationship('ChurchInventory', backref='church', lazy='dynamic', cascade="all, delete-orphan")
    accounts = db.relationship('Account', backref='church', lazy='dynamic', cascade="all, delete-orphan")
    # announcements_targeted definido via backref

    def __repr__(self):
        return f"<Church(id={self.id}, name='{self.name}', city='{self.city}')>"

# --- Modelo Funciones de Miembro ---
class MemberFunction(db.Model):
    __tablename__ = 'member_functions'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), unique=True, nullable=False)
    description = db.Column(db.String(255), nullable=True)
    # members definido via backref

    def __repr__(self):
        return f"<MemberFunction(id={self.id}, name='{self.name}')>"

# Tabla de asociación Miembro <-> Función
member_functions_association = db.Table('member_functions_association', db.metadata,
    db.Column('member_id', db.Integer, db.ForeignKey('members.id', ondelete='CASCADE'), primary_key=True),
    db.Column('function_id', db.Integer, db.ForeignKey('member_functions.id', ondelete='CASCADE'), primary_key=True)
)

# --- Modelo Miembro ---
class Member(db.Model):
    __tablename__ = 'members'
    id = db.Column(db.Integer, primary_key=True)
    # Clave foránea única a User, con eliminación en cascada
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True)
    # Clave foránea a Church, con eliminación en cascada
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id', ondelete='CASCADE'), nullable=False)

    # Campos específicos
    alergies = db.Column(db.String(255), nullable=True)
    emergency_contact = db.Column(db.String(255), nullable=True)

    # Relación uno-a-uno con User
    user = db.relationship('User', backref=db.backref('member', uselist=False, cascade="all, delete-orphan"))

    # Relación many-to-many con funciones
    functions = db.relationship('MemberFunction', secondary=member_functions_association,
                                backref=db.backref('members', lazy='dynamic'), lazy='select')

    # Relación con transacciones donde este miembro es el asociado
    transactions = db.relationship('Transaction', foreign_keys='Transaction.member_id', backref='member', lazy='dynamic')

    # church definida via backref

    def __repr__(self):
        return f"<Member(user_id={self.user_id}, church_id={self.church_id})>"

# --- Modelo Roles de Pastor ---
class PastorRole(db.Model):
    __tablename__ = 'pastor_roles'
    id = db.Column(db.Integer, primary_key=True)
    role_name = db.Column(db.String(255), unique=True, nullable=False)
    description = db.Column(db.String(255), nullable=True)
    # users_with_role definido via backref

    def __repr__(self):
        return f"<PastorRole(id={self.id}, role_name='{self.role_name}')>"

# Tabla de asociación Usuario <-> Rol de Pastor
user_pastor_roles = db.Table('user_pastor_roles', db.metadata,
    db.Column('user_id', db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), primary_key=True),
    db.Column('pastor_role_id', db.Integer, db.ForeignKey('pastor_roles.id', ondelete='CASCADE'), primary_key=True)
)

# --- Modelo Documento ---
class Document(db.Model):
    __tablename__ = 'documents'
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    file_path = db.Column(db.String(255), nullable=False) # Solo el nombre del archivo
    upload_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    category = db.Column(db.Enum('acta', 'informe_financiero', 'circular', 'informe_miembros', 'diploma', 'otro'), nullable=False)
    topic = db.Column(db.String(255), nullable=True)
    version = db.Column(db.Integer, default=1, nullable=False)
    status = db.Column(db.String(50), default='pendiente', nullable=False)

    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True)
    previous_version_id = db.Column(db.Integer, db.ForeignKey('documents.id', ondelete='SET NULL'), nullable=True)

    # Relación con User que subió
    uploader = db.relationship('User', back_populates='user_documents', foreign_keys=[uploaded_by])
    # Relación con versión anterior
    previous_version = db.relationship('Document', remote_side=[id], backref='next_versions')

    def __repr__(self):
        return f"<Document(id={self.id}, title='{self.title}', uploader_id={self.uploaded_by})>"

# --- Modelo Mensaje ---
class Message(db.Model):
    __tablename__ = 'messages'
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    subject = db.Column(db.String(255), nullable=True)
    message_text = db.Column(db.Text, nullable=False)
    sent_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    is_read = db.Column(db.Boolean, default=False, nullable=False)
    forwarded_from = db.Column(db.Integer, db.ForeignKey('messages.id', ondelete='SET NULL'), nullable=True)

    # Relación para obtener el mensaje original
    original_message = db.relationship('Message', remote_side=[id], backref='forwarded_messages')
    # sender definido via backref
    # receiver definido via backref

    def __repr__(self):
        return f"<Message(id={self.id}, from={self.sender_id}, to={self.receiver_id}, subject='{self.subject}')>"

# --- Modelo Mapa ---
class Map(db.Model):
    __tablename__ = 'maps'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    geodata = db.Column(db.JSON, nullable=True) # Para almacenar GeoJSON
    description = db.Column(db.Text, nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True)
    # creator definido via backref

    def __repr__(self):
        return f"<Map(id={self.id}, name='{self.name}')>"

# --- Modelo Jerarquía Pastores ---
class JerarquiaPastores(db.Model):
    __tablename__ = 'jerarquia_pastores'
    id = db.Column(db.Integer, primary_key=True)
    pastor_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=True)
    supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=True)

    # Restricción para evitar pares duplicados
    __table_args__ = (
        UniqueConstraint('pastor_id', 'supervisor_id', name='uq_jerarquia_pair'),
    )
    # pastor definido via backref
    # supervisor definido via backref

    def __repr__(self):
        return f"<Jerarquia(id={self.id}, pastor={self.pastor_id}, supervisor={self.supervisor_id})>"

# --- Modelo Relaciones Familiares ---
class UserRelationship(db.Model):
    __tablename__ = 'user_relationships'
    id = db.Column(db.Integer, primary_key=True)
    user_id_1 = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    user_id_2 = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    tipo_de_relacion = db.Column(db.String(255), nullable=False)

    # Restricción para asegurar que no se relacione un usuario consigo mismo
    __table_args__ = (
        CheckConstraint('user_id_1 != user_id_2', name='ck_different_users'),
    )
    # user1 definido via backref
    # user2 definido via backref

    def __repr__(self):
        return f"<UserRelationship(id={self.id}, user1={self.user_id_1}, user2={self.user_id_2}, type='{self.tipo_de_relacion}')>"

# --- Modelo Inventario Iglesia ---
class ChurchInventory(db.Model):
    __tablename__ = 'church_inventory'
    id = db.Column(db.Integer, primary_key=True)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id', ondelete='CASCADE'), nullable=False)
    item_name = db.Column(db.String(255), nullable=False)
    item_type = db.Column(db.String(100), nullable=True)
    quantity = db.Column(db.Integer, default=1, nullable=False)
    description = db.Column(db.Text, nullable=True)
    purchase_date = db.Column(db.Date, nullable=True)
    purchase_price = db.Column(db.Float, nullable=True) # Considerar Numeric/Decimal
    current_value = db.Column(db.Float, nullable=True)  # Considerar Numeric/Decimal
    serial_number = db.Column(db.String(255), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    # church definido via backref

    def __repr__(self):
        return f"<ChurchInventory(id={self.id}, church_id={self.church_id}, item='{self.item_name}')>"

# --- Modelo Eventos Calendario ---
class CalendarEvent(db.Model):
    __tablename__ = 'calendar_events'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    event_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    # user definido via backref

    def __repr__(self):
        return f"<CalendarEvent(id={self.id}, title='{self.title}', date={self.event_date})>"

# --- Modelos Economía ---
class Account(db.Model):
    __tablename__ = 'accounts'
    id = db.Column(db.Integer, primary_key=True)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id', ondelete='CASCADE'), nullable=False)
    name = db.Column(db.String(255), nullable=False, default="Cuenta Principal")
    balance = db.Column(db.Numeric(10, 2), default=Decimal('0.00'), nullable=False)
    # church definido via backref
    # transactions definido via backref

    def __repr__(self):
        return f"<Account(id={self.id}, name='{self.name}', church_id={self.church_id})>"

class Transaction(db.Model):
    __tablename__ = 'transactions'
    id = db.Column(db.Integer, primary_key=True)
    account_id = db.Column(db.Integer, db.ForeignKey('accounts.id', ondelete='CASCADE'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True) # Usuario que registra
    member_id = db.Column(db.Integer, db.ForeignKey('members.id', ondelete='SET NULL'), nullable=True) # Miembro asociado
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    transaction_type = db.Column(db.Enum('ingreso', 'egreso'), nullable=False)
    category = db.Column(db.Enum('diezmo', 'ofrenda', 'donacion', 'gasto', 'otro'), nullable=False)
    description = db.Column(db.String(255), nullable=True)
    transaction_date = db.Column(db.Date, nullable=False) # Fecha del evento económico
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False) # Fecha registro en sistema
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    notes = db.Column(db.Text, nullable=True)

    # Relaciones
    account = db.relationship('Account', backref=db.backref('transactions', lazy='dynamic', cascade='all, delete-orphan'))
    # user_registrar definido via backref
    # member definido via backref

    def __repr__(self):
        return f"<Transaction(id={self.id}, type='{self.transaction_type}', amount={self.amount})>"

    # Validaciones
    @validates('transaction_type')
    def validate_transaction_type(self, key, transaction_type):
        if transaction_type not in ['ingreso', 'egreso']:
            raise ValueError("Tipo de transacción inválido")
        return transaction_type

    @validates('category')
    def validate_category(self, key, category):
        if category not in ['diezmo', 'ofrenda', 'donacion', 'gasto', 'otro']:
            raise ValueError("Categoría inválida")
        return category

    @validates('amount')
    def validate_amount(self, key, amount):
        # Asegurar que el monto sea positivo
        # Convertir a Decimal antes de comparar si viene como float o string
        try:
            decimal_amount = Decimal(amount)
        except:
             raise ValueError("El monto debe ser un número.")

        if decimal_amount <= 0:
            raise ValueError("El monto debe ser un número positivo.")
        return decimal_amount # Devolver como Decimal

# --- Modelo Anuncios ---
class Announcement(db.Model):
    __tablename__ = 'announcements'
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    author_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True)
    visibility = db.Column(db.Enum('todos', 'administradores', 'secretaria', 'pastorado', 'miembros', 'iglesia_especifica'),
                           nullable=False, default='todos')
    target_church_id = db.Column(db.Integer, db.ForeignKey('churches.id', ondelete='CASCADE'), nullable=True)

    # Relaciones
    author = db.relationship('User', backref='announcements_authored', foreign_keys=[author_id])
    target_church = db.relationship('Church', backref='announcements_targeted', foreign_keys=[target_church_id])

    def __repr__(self):
        return f"<Announcement(id={self.id}, title='{self.title}', visibility='{self.visibility}')>"

class JerarquiaSuperintendentes(db.Model):
    __tablename__ = 'jerarquia_superintendentes'
    id = db.Column(db.Integer, primary_key=True)
    superintendente_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    jefe_sector_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)

    superintendente = db.relationship("User", foreign_keys=[superintendente_id], back_populates="superintendente_de")
    jefe_sector = db.relationship("User", foreign_keys=[jefe_sector_id], back_populates="jefes_de")

    __table_args__ = (
        db.UniqueConstraint('superintendente_id', 'jefe_sector_id', name='uq_superintendente_jefe'),
    )

    def __repr__(self):
        return f"<JerarquiaSuperintendentes(superintendente={self.superintendente_id}, jefe_sector={self.jefe_sector_id})>"

class MemberTransferRequest(db.Model):
    __tablename__ = 'member_transfer_requests'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    origin_church_id = db.Column(db.Integer, db.ForeignKey('churches.id', ondelete='SET NULL'), nullable=True)
    target_church_id = db.Column(db.Integer, db.ForeignKey('churches.id', ondelete='CASCADE'), nullable=False)
    requesting_pastor_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True)
    approving_pastor_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True)
    status = db.Column(db.Enum('pendiente', 'aprobada', 'rechazada', 'cancelada', name='transfer_status_enum'), nullable=False, default='pendiente') # Añadir name al Enum puede ayudar
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    request_notes = db.Column(db.Text, nullable=True)
    approval_notes = db.Column(db.Text, nullable=True)

    # Relaciones
    user = db.relationship('User', foreign_keys=[user_id], backref=db.backref('transfer_requests', lazy='dynamic'))
    origin_church = db.relationship('Church', foreign_keys=[origin_church_id])
    target_church = db.relationship('Church', foreign_keys=[target_church_id])
    requesting_pastor = db.relationship('User', foreign_keys=[requesting_pastor_id])
    approving_pastor = db.relationship('User', foreign_keys=[approving_pastor_id])

    # --- CORRECCIÓN AQUÍ ---
    __table_args__ = (
        # Usar db.Index con unique=True y la condición en postgresql_where
        db.Index(
            'ix_uq_pending_transfer', # Nombre del índice único
            'user_id',
            'target_church_id',
            unique=True,
            postgresql_where=text("status = 'pendiente'") # Condición para PostgreSQL
            # Si soportaras otros dialectos, añadirías condiciones similares:
            # mysql_where="status = 'pendiente'",
            # sqlite_where=text("status = 'pendiente'") # Para SQLite > 3.8.0
        ),
        # Puedes añadir otras restricciones aquí si las necesitas, como CheckConstraints
    )
    # --- FIN CORRECCIÓN ---

    def __repr__(self):
        return f"<MemberTransferRequest id={self.id} user={self.user_id} target={self.target_church_id} status={self.status}>"

# ============================================================================
# SISTEMA ACADÉMICO - MODELOS
# ============================================================================

# ESCUELAS BÍBLICAS (Nivel Básico)
class AcademicSchool(db.Model):
    __tablename__ = 'academic_schools'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=False)
    director_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    max_students = db.Column(db.Integer, default=50)
    auto_enrollment = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relaciones
    church = db.relationship('Church', backref='academic_schools')
    director = db.relationship('User', backref='directed_schools')
    curriculums = db.relationship('AcademicCurriculum', backref='school', cascade='all, delete-orphan')
    enrollments = db.relationship('AcademicEnrollment', backref='school', cascade='all, delete-orphan')

class AcademicCurriculum(db.Model):
    __tablename__ = 'academic_curriculums'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    school_id = db.Column(db.Integer, db.ForeignKey('academic_schools.id'), nullable=False)
    duration_months = db.Column(db.Integer, nullable=False)
    total_credits = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    subjects = db.relationship('AcademicSubject', backref='curriculum', cascade='all, delete-orphan')
    enrollments = db.relationship('AcademicEnrollment', backref='curriculum')

class AcademicSubject(db.Model):
    __tablename__ = 'academic_subjects'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(20), nullable=False)
    description = db.Column(db.Text)
    curriculum_id = db.Column(db.Integer, db.ForeignKey('academic_curriculums.id'), nullable=False)
    credits = db.Column(db.Integer, default=1)
    level = db.Column(db.Integer, default=1)
    prerequisites = db.Column(db.Text)
    is_mandatory = db.Column(db.Boolean, default=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    teacher = db.relationship('User', backref='taught_subjects')
    grades = db.relationship('AcademicGrade', backref='subject', cascade='all, delete-orphan')

class AcademicEnrollment(db.Model):
    __tablename__ = 'academic_enrollments'

    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    school_id = db.Column(db.Integer, db.ForeignKey('academic_schools.id'), nullable=False)
    curriculum_id = db.Column(db.Integer, db.ForeignKey('academic_curriculums.id'), nullable=False)
    enrollment_date = db.Column(db.Date, default=datetime.utcnow().date)
    status = db.Column(db.Enum('active', 'completed', 'dropped', 'suspended'), default='active')
    completion_date = db.Column(db.Date, nullable=True)
    final_grade = db.Column(db.Float, nullable=True)
    certificate_issued = db.Column(db.Boolean, default=False)
    certificate_date = db.Column(db.Date, nullable=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    student = db.relationship('User', backref='academic_enrollments')
    grades = db.relationship('AcademicGrade', backref='enrollment', cascade='all, delete-orphan')

class AcademicGrade(db.Model):
    __tablename__ = 'academic_grades'

    id = db.Column(db.Integer, primary_key=True)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('academic_enrollments.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('academic_subjects.id'), nullable=False)
    grade_value = db.Column(db.Float, nullable=False)
    grade_type = db.Column(db.Enum('partial', 'final', 'makeup'), default='partial')
    evaluation_date = db.Column(db.Date, default=datetime.utcnow().date)
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    comments = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    teacher = db.relationship('User', backref='assigned_grades')

# INSTITUTO PASTORAL (Nivel Corporativo)
class PastoralInstitute(db.Model):
    __tablename__ = 'pastoral_institutes'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    rector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    academic_coordinator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    rector = db.relationship('User', foreign_keys=[rector_id], backref='rectored_institutes')
    coordinator = db.relationship('User', foreign_keys=[academic_coordinator_id], backref='coordinated_institutes')
    programs = db.relationship('PastoralProgram', backref='institute', cascade='all, delete-orphan')

class PastoralProgram(db.Model):
    __tablename__ = 'pastoral_programs'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    institute_id = db.Column(db.Integer, db.ForeignKey('pastoral_institutes.id'), nullable=False)
    program_type = db.Column(db.Enum('diploma', 'certificate', 'specialization', 'masters'), default='certificate')
    duration_months = db.Column(db.Integer, nullable=False)
    total_credits = db.Column(db.Integer, default=0)
    target_roles = db.Column(db.JSON)
    min_ministry_years = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    max_students = db.Column(db.Integer, default=30)
    requires_approval = db.Column(db.Boolean, default=True)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    created_by = db.relationship('User', backref='created_pastoral_programs')
    subjects = db.relationship('PastoralSubject', backref='program', cascade='all, delete-orphan')
    enrollments = db.relationship('PastoralEnrollment', backref='program', cascade='all, delete-orphan')

class PastoralSubject(db.Model):
    __tablename__ = 'pastoral_subjects'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(20), nullable=False)
    description = db.Column(db.Text)
    program_id = db.Column(db.Integer, db.ForeignKey('pastoral_programs.id'), nullable=False)
    credits = db.Column(db.Integer, default=3)
    semester = db.Column(db.Integer, default=1)
    prerequisites = db.Column(db.JSON)
    is_mandatory = db.Column(db.Boolean, default=True)
    professor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    professor = db.relationship('User', backref='taught_pastoral_subjects')
    grades = db.relationship('PastoralGrade', backref='subject', cascade='all, delete-orphan')

class PastoralEnrollment(db.Model):
    __tablename__ = 'pastoral_enrollments'

    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    program_id = db.Column(db.Integer, db.ForeignKey('pastoral_programs.id'), nullable=False)
    enrollment_date = db.Column(db.Date, default=datetime.utcnow().date)
    status = db.Column(db.Enum('pending', 'approved', 'active', 'completed', 'dropped'), default='pending')
    approved_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    completion_date = db.Column(db.Date, nullable=True)
    final_gpa = db.Column(db.Float, nullable=True)
    certificate_issued = db.Column(db.Boolean, default=False)
    certificate_number = db.Column(db.String(50), unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    student = db.relationship('User', foreign_keys=[student_id], backref='pastoral_enrollments')
    approved_by = db.relationship('User', foreign_keys=[approved_by_id])
    grades = db.relationship('PastoralGrade', backref='enrollment', cascade='all, delete-orphan')

class PastoralGrade(db.Model):
    __tablename__ = 'pastoral_grades'

    id = db.Column(db.Integer, primary_key=True)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('pastoral_enrollments.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('pastoral_subjects.id'), nullable=False)
    grade_value = db.Column(db.Float, nullable=False)
    grade_type = db.Column(db.Enum('assignment', 'exam', 'project', 'final'), default='assignment')
    evaluation_date = db.Column(db.Date, default=datetime.utcnow().date)
    professor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    comments = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    professor = db.relationship('User', backref='assigned_pastoral_grades')