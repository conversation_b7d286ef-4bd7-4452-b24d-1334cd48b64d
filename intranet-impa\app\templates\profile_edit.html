<!-- /app/templates/profile_edit.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %} {# Asegúrate que _formhelpers.html exista #}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<h1>{{ title }}</h1>
<p>Aqu<PERSON> puedes actualizar tu información personal.</p>

<form method="POST" action="">
    {{ form.hidden_tag() }}

    <fieldset>
        <legend>Información Básica</legend>
        {# Mostrar username, email, DNI como solo lectura si se desea #}
        <div class="form-group">
            <label>Usuario:</label>
            <input type="text" class="form-control" value="{{ current_user.username }}" readonly>
        </div>
         <div class="form-group">
            <label>Email:</label>
            <input type="text" class="form-control" value="{{ current_user.email }}" readonly>
        </div>
        <div class="form-group">
            <label>DNI:</label>
            <input type="text" class="form-control" value="{{ current_user.dni or 'No especificado' }}" readonly>
        </div>

        {# Campos editables del User #}
        <div class="form-group">
            {{ render_field(form.first_name, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.last_name, class="form-control") }}
        </div>
        <div class="form-group">
            {{ render_field(form.phone_number, class="form-control", placeholder="Ej: +54 9 2920 123456") }}
        </div>
         <div class="form-group">
            {{ render_field(form.address, class="form-control", placeholder="Calle y número") }}
        </div>
        <div class="form-group">
            {{ render_field(form.ciudad, class="form-control", placeholder="Ciudad de residencia") }}
        </div>
         <div class="form-group">
            {{ render_field(form.estado_civil, class="form-control") }}
        </div>
         <div class="form-group">
            {{ render_field(form.date_of_birth, class="form-control", type="date") }} {# Usar type="date" para mejor UX #}
        </div>
    </fieldset>

    {# Mostrar campos de Miembro solo si el usuario tiene un registro Member asociado #}
    {% if user_is_member %}
    <hr>
    <fieldset>
         <legend>Información Adicional (Miembro)</legend>
         <div class="form-group">
            {{ render_field(form.alergies, class="form-control", placeholder="Indicar alergias conocidas") }}
        </div>
        <div class="form-group">
            {{ render_field(form.emergency_contact, class="form-control", placeholder="Nombre y teléfono de contacto") }}
        </div>
    </fieldset>
    {% endif %}

    <hr>
    <fieldset>
         <legend>Cambiar Contraseña (Opcional)</legend>
         <div class="form-group">
            {{ render_field(form.password, class="form-control") }}
        </div>
         <div class="form-group">
            {{ render_field(form.confirm_password, class="form-control") }}
        </div>
    </fieldset>

    <button type="submit" class="btn btn-primary mt-3">{{ form.submit.label }}</button>
    <a href="{{ url_for('routes.dashboard') }}" class="btn btn-secondary mt-3">Cancelar</a>

</form>
{% endblock %}