<!-- /app/templates/pastorado/pending_transfers.html -->
{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container my-4">
    <h1>{{ title }}</h1>

    <!-- Solicitudes para Aprobar -->
    <h2 class="mt-4">Solicitudes Pendientes de mi Aprobación</h2>
    {% if requests_to_approve %}
        <div class="list-group">
            {% for req in requests_to_approve %}
                <a href="{{ url_for('routes.review_transfer_request', request_id=req.id) }}" class="list-group-item list-group-item-action flex-column align-items-start">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">Transferir a: {{ req.user.full_name if req.user else 'Usuario Eliminado' }}</h5>
                        <small class="text-muted">{{ req.created_at | to_local }}</small>
                    </div>
                    <p class="mb-1">
                        Desde: <strong>{{ req.origin_church.name if req.origin_church else 'Iglesia Origen Desconocida' }}</strong><br>
                        Solicitado por: Pastor <strong>{{ req.requesting_pastor.full_name if req.requesting_pastor else 'N/A' }}</strong>
                    </p>
                    {% if req.request_notes %}
                        <small class="text-muted"><i>Notas: {{ req.request_notes|truncate(100) }}</i></small>
                    {% endif %}
                </a>
            {% endfor %}
        </div>
    {% else %}
        <p class="text-muted">No tienes solicitudes de transferencia pendientes de aprobar.</p>
    {% endif %}

    <!-- Solicitudes Iniciadas -->
    <h2 class="mt-5">Solicitudes Iniciadas por Mí (Pendientes)</h2>
    {% if requests_initiated %}
        <div class="list-group">
            {% for req in requests_initiated %}
                <div class="list-group-item flex-column align-items-start"> {# No es un enlace aquí #}
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">Miembro: {{ req.user.full_name if req.user else 'Usuario Eliminado' }}</h5>
                        <small class="text-muted">{{ req.created_at | to_local }}</small>
                    </div>
                    <p class="mb-1">
                        Hacia: <strong>{{ req.target_church.name if req.target_church else 'Iglesia Destino Desconocida' }}</strong><br>
                        Esperando aprobación de: Pastor <strong>{{ req.approving_pastor.full_name if req.approving_pastor else 'N/A' }}</strong>
                    </p>
                     {% if req.request_notes %}
                        <small class="text-muted"><i>Mis Notas: {{ req.request_notes|truncate(100) }}</i></small>
                    {% endif %}
                    {# Botón para Cancelar #}
                    <form action="{{ url_for('routes.cancel_transfer_request', request_id=req.id) }}" method="POST" class="float-right" onsubmit="return confirm('¿Estás seguro de cancelar esta solicitud?');">
                         <button type="submit" class="btn btn-sm btn-outline-danger">Cancelar Solicitud</button>
                    </form>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <p class="text-muted">No tienes solicitudes de transferencia iniciadas pendientes.</p>
    {% endif %}

     <a href="{{ url_for('routes.dashboard') }}" class="btn btn-secondary mt-4">Volver al Escritorio</a>

</div>
{% endblock %}