 # VHF Digital Communicator                                                                                                                                        
                                                                                                                                                                   
 Este proyecto implementa un sistema de comunicación digital para VHF, permitiendo la transmisión y recepción de audio a través de una red. Utiliza WebSockets pa  
 la comunicación en tiempo real y un transceptor VHF para la transmisión de radiofrecuencia.                                                                       
                                                                                                                                                                   
 ## Características                                                                                                                                                
                                                                                                                                                                   
 -   Transmisión de audio en tiempo real.                                                                                                                          
 -   Control de PTT (Push-To-Talk) para la gestión de la transmisión.                                                                                              
 -   Interfaz web para la configuración y el control del sistema.                                                                                                  
 -   Monitoreo y recarga automática de la configuración.                                                                                                           
 -   Icono en la bandeja del sistema para un acceso rápido a las funciones.                                                                                        
                                                                                                                                                                   
 ## Instalación                                                                                                                                                    
                                                                                                                                                                   
 Para instalar las dependencias del proyecto, ejecuta el siguiente comando:                                                                                        
                                                                                                                                                                   
 ```bash                                                                                                                                                           
 pip install -r requirements.txt                                                                                                                                   
                                                                                                                                                                   


                                                                          Configuración

La configuración del sistema se realiza a través del archivo config.json. Este archivo contiene parámetros como el nombre de usuario, la contraseña, la URL del    
nodo, el ID del nodo, el puerto COM para el control del PTT, el índice del dispositivo de entrada de audio y el nivel de volumen para la activación de la 
transmisión.

A continuación, se muestra un ejemplo de la estructura del archivo config.json:

                                                                                                                                                                   
 {                                                                                                                                                                 
     "username": "tu_usuario",                                                                                                                                     
     "password": "tu_contraseña",                                                                                                                                  
     "node_url": "tu_url_de_nodo",                                                                                                                                 
     "node_id": "tu_id_de_nodo",                                                                                                                                   
     "input_device_index": 0,                                                                                                                                      
     "volume_level": 5,                                                                                                                                            
     "port_number": "1"                                                                                                                                            
 }                                                                                                                                                                 
                                                                                                                                                                   


                                                                               Uso

                                                                           Cliente Web

Para iniciar el cliente web, ejecuta el script cliente.py:

                                                                                                                                                                   
 python cliente.py                                                                                                                                                 
                                                                                                                                                                   

Esto iniciará una aplicación Flask que sirve la interfaz de usuario web. Puedes acceder a la interfaz a través de tu navegador web en la dirección 
http://localhost:5000.

                                                                     Captura y Envío de Audio

Para iniciar la captura y el envío de audio, ejecuta el script vhf_dig.py:

                                                                                                                                                                   
 python vhf_dig.py                                                                                                                                                 
                                                                                                                                                                   

Este script se conecta al servidor WebSocket y comienza a monitorear el audio del micrófono. Cuando el volumen del audio supera el umbral configurado, el audio se 
transmite al servidor.

                                                                        Recepción de Audio

Para iniciar la recepción de audio, ejecuta el script dig_vhf.py:

                                                                                                                                                                   
 python dig_vhf.py                                                                                                                                                 
                                                                                                                                                                   

Este script se conecta al servidor WebSocket y escucha los eventos de audio. Cuando se recibe audio, se activa el PTT y se reproduce el audio.

                                                                 Icono de la Bandeja del Sistema

Para iniciar el icono de la bandeja del sistema, ejecuta el script icon_tray.py:

                                                                                                                                                                   
 python icon_tray.py                                                                                                                                               
                                                                                                                                                                   

Esto creará un icono en la bandeja del sistema que te permitirá abrir la interfaz web y salir de la aplicación.

                                                                          Pruebas de PTT

Para probar la funcionalidad del PTT, puedes usar el script ptt_test.py:

                                                                                                                                                                   
 python ptt_test.py                                                                                                                                                
                                                                                                                                                                   

Este script te permitirá activar y desactivar manualmente el PTT.


                                                                          Contribuciones

Las contribuciones a este proyecto son bienvenidas. Si deseas contribuir, por favor, crea un fork del repositorio y envía un pull request con tus cambios.


                                                                             Licencia

Este proyecto está licenciado bajo la Licencia MIT. Consulta el archivo LICENSE para más detalles.