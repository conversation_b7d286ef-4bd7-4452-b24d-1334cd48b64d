<!-- /app/templates/pastorado/economia.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}Economía - {{ current_user.church.name }}{% endblock %}

{% block content %}
  <h1>Economía de {{ current_user.church.name }}</h1>

  <p><strong>Saldo Actual:</strong> ${{ account.balance }}</p>

  <h2>Registrar Transacción</h2>
  <form method="POST" action="">
    {{ form.hidden_tag() }}
    <div class="form-group">
      {{ render_field(form.amount, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.transaction_type, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.category, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.member_id, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.description, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.transaction_date, class="form-control") }}
    </div>
    <div class="form-group">
      {{ render_field(form.notes, class="form-control", rows=3) }}
    </div>

    {{ form.submit(class="btn btn-primary") }}
  </form>

  <hr>

  <h2>Historial de Transacciones</h2>
  <table class="table table-striped">
    <thead>
      <tr>
        <th>Fecha</th>
        <th>Tipo</th>
        <th>Categoría</th>
        <th>Miembro</th>
        <th>Descripción</th>
        <th>Monto</th>
        <th>Notas</th>
      </tr>
    </thead>
    <tbody>
      {% for transaction in transactions %}
      <tr>
        <td>{{ transaction.transaction_date }}</td>
        <td>{{ transaction.transaction_type }}</td>
        <td>{{ transaction.category }}</td>
        <td>
          {% if transaction.member %}
            {{ transaction.member.user.first_name }} {{ transaction.member.user.last_name }}
          {% else %}
            -
          {% endif %}
        </td>
        <td>{{ transaction.description or '-' }}</td>
        <td>
          {% if transaction.transaction_type == 'ingreso' %}
            <span style="color: green;">+${{ transaction.amount }}</span>
          {% else %}
            <span style="color: red;">-${{ transaction.amount }}</span>
          {% endif %}
        </td>
        <td>
          {% if transaction.notes %}
            {{ transaction.notes }}
          {% else %}
            -
          {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
{% endblock %}
