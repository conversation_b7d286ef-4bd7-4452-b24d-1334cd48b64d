# File: backend/apps/chat/urls.py
# -----------------------------------------------

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'chat'

urlpatterns = [
    # Vista para el chat de una incidencia
    path('incidencia/<int:incidencia_id>/', views.chat_incidencia_view, name='chat_incidencia'),

    # APIs para guardar mensajes multimedia
    path('api/guardar-audio/', views.guardar_audio_view, name='guardar_audio'),
    path('api/guardar-imagen/', views.guardar_imagen_view, name='guardar_imagen'),
    path('api/guardar-ubicacion/', views.guardar_ubicacion_view, name='guardar_ubicacion'),

    # API para exportar chat a brigada
    path('api/exportar-brigada/<int:incidencia_id>/', views.exportar_chat_brigada_view, name='exportar_chat_brigada'),

    # API para reparar archivos de audio
    path('api/reparar-archivos-audio/', views.reparar_archivos_audio_view, name='reparar_archivos_audio'),

    # API para reparar archivos de imagen
    path('api/reparar-archivos-imagen/', views.reparar_archivos_imagen_view, name='reparar_archivos_imagen'),
]
