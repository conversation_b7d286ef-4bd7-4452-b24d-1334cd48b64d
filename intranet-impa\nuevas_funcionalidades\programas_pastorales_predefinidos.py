# programas_pastorales_predefinidos.py
# Programas de formación pastoral predefinidos

"""
PROGRAMAS PASTORALES PREDEFINIDOS
=================================

Programas de formación continua para pastores y líderes de la corporación IMPA.
Estos programas están diseñados específicamente para el desarrollo ministerial
y la capacitación pastoral a nivel corporativo.
"""

PROGRAMAS_PASTORALES = {
    'diploma_ministerial': {
        'name': 'Diploma en Ministerio Pastoral',
        'description': 'Formación integral para pastores en ejercicio',
        'program_type': 'diploma',
        'duration_months': 18,
        'target_roles': ['pastorado', 'superintendente'],
        'min_ministry_years': 2,
        'total_credits': 45,
        'entry_requirements': [
            'Mínimo 2 años en ministerio pastoral',
            'Recomendación del superintendente',
            'Escuela bíblica básica completada',
            'Evaluación de desempeño pastoral satisfactoria'
        ],
        'subjects': [
            # PRIMER SEMESTRE
            {
                'name': 'Teología Sistemática I',
                'code': 'TSI-001',
                'description': 'Fundamentos doctrinales del cristianismo',
                'credits': 4,
                'semester': 1,
                'is_mandatory': True,
                'teaching_hours': 60,
                'practical_hours': 20,
                'syllabus': '''
                1. Introducción a la Teología Sistemática
                2. Doctrina de las Escrituras
                3. Doctrina de Dios (Teología Propia)
                4. Doctrina de Cristo (Cristología)
                5. Doctrina del Espíritu Santo (Pneumatología)
                ''',
                'learning_objectives': [
                    'Comprender los fundamentos doctrinales del cristianismo',
                    'Desarrollar habilidades de análisis teológico',
                    'Aplicar principios doctrinales en el ministerio pastoral'
                ],
                'evaluation_methods': [
                    {'type': 'examen_parcial', 'weight': 30},
                    {'type': 'ensayo_teologico', 'weight': 40},
                    {'type': 'examen_final', 'weight': 30}
                ]
            },
            {
                'name': 'Homilética Avanzada',
                'code': 'HOM-002',
                'description': 'Técnicas avanzadas de predicación expositiva',
                'credits': 3,
                'semester': 1,
                'is_mandatory': True,
                'teaching_hours': 45,
                'practical_hours': 30,
                'syllabus': '''
                1. Principios de predicación expositiva
                2. Estructura del sermón expositivo
                3. Hermenéutica aplicada a la predicación
                4. Técnicas de comunicación efectiva
                5. Predicación temática vs expositiva
                6. Uso de ilustraciones y aplicaciones
                ''',
                'learning_objectives': [
                    'Dominar técnicas de predicación expositiva',
                    'Desarrollar sermones estructurados y efectivos',
                    'Mejorar habilidades de comunicación púlpito'
                ]
            },
            {
                'name': 'Liderazgo Pastoral',
                'code': 'LID-002',
                'description': 'Principios de liderazgo en el contexto pastoral',
                'credits': 3,
                'semester': 1,
                'is_mandatory': True,
                'teaching_hours': 45,
                'practical_hours': 15,
                'syllabus': '''
                1. Fundamentos bíblicos del liderazgo
                2. Estilos de liderazgo pastoral
                3. Desarrollo de equipos ministeriales
                4. Gestión de conflictos en la iglesia
                5. Liderazgo transformacional
                6. Sucesión y desarrollo de líderes
                '''
            },
            
            # SEGUNDO SEMESTRE
            {
                'name': 'Consejería Pastoral Avanzada',
                'code': 'CON-002',
                'description': 'Técnicas avanzadas de consejería bíblica',
                'credits': 4,
                'semester': 2,
                'is_mandatory': True,
                'teaching_hours': 50,
                'practical_hours': 40,
                'prerequisites': ['CON-001'],  # De la escuela básica
                'syllabus': '''
                1. Fundamentos de la consejería bíblica
                2. Consejería matrimonial y familiar
                3. Consejería en crisis y trauma
                4. Consejería a jóvenes y adolescentes
                5. Consejería en adicciones
                6. Límites éticos en la consejería
                7. Referencia a profesionales
                '''
            },
            {
                'name': 'Administración Eclesiástica Avanzada',
                'code': 'ADM-002',
                'description': 'Gestión estratégica de la iglesia local',
                'credits': 3,
                'semester': 2,
                'is_mandatory': True,
                'teaching_hours': 45,
                'practical_hours': 20,
                'syllabus': '''
                1. Planificación estratégica pastoral
                2. Gestión financiera de la iglesia
                3. Desarrollo de ministerios
                4. Gestión de recursos humanos
                5. Marketing y comunicación eclesiástica
                6. Evaluación y crecimiento institucional
                '''
            },
            {
                'name': 'Evangelismo y Misiones',
                'code': 'EVA-002',
                'description': 'Estrategias de evangelización y misiones',
                'credits': 3,
                'semester': 2,
                'is_mandatory': True,
                'teaching_hours': 40,
                'practical_hours': 35,
                'syllabus': '''
                1. Teología de la evangelización
                2. Métodos contemporáneos de evangelismo
                3. Plantación de iglesias
                4. Misiones transculturales
                5. Evangelismo digital
                6. Seguimiento de nuevos convertidos
                '''
            },
            
            # TERCER SEMESTRE
            {
                'name': 'Teología Sistemática II',
                'code': 'TSI-002',
                'description': 'Antropología, soteriología y eclesiología',
                'credits': 4,
                'semester': 3,
                'is_mandatory': True,
                'teaching_hours': 60,
                'practical_hours': 15,
                'prerequisites': ['TSI-001'],
                'syllabus': '''
                1. Doctrina del Hombre (Antropología)
                2. Doctrina de la Salvación (Soteriología)
                3. Doctrina de la Iglesia (Eclesiología)
                4. Doctrina de los Últimos Tiempos (Escatología)
                5. Aplicaciones pastorales de la teología
                '''
            },
            {
                'name': 'Pastoral Familiar',
                'code': 'FAM-001',
                'description': 'Ministerio pastoral enfocado en la familia',
                'credits': 3,
                'semester': 3,
                'is_mandatory': True,
                'teaching_hours': 45,
                'practical_hours': 25,
                'syllabus': '''
                1. Teología bíblica de la familia
                2. Pastoral matrimonial
                3. Crianza cristiana de los hijos
                4. Familias en crisis
                5. Ministerio con solteros
                6. Familias reconstituidas
                '''
            },
            {
                'name': 'Proyecto Ministerial',
                'code': 'PRO-001',
                'description': 'Desarrollo de proyecto aplicado al ministerio',
                'credits': 3,
                'semester': 3,
                'is_mandatory': True,
                'teaching_hours': 20,
                'practical_hours': 60,
                'syllabus': '''
                1. Identificación de necesidades ministeriales
                2. Diseño de proyecto de intervención
                3. Implementación supervisada
                4. Evaluación de resultados
                5. Presentación de resultados
                '''
            }
        ]
    },
    
    'especializacion_liderazgo': {
        'name': 'Especialización en Liderazgo Eclesiástico',
        'description': 'Programa avanzado para superintendentes y líderes corporativos',
        'program_type': 'specialization',
        'duration_months': 12,
        'target_roles': ['superintendente', 'jefe_sector'],
        'min_ministry_years': 5,
        'total_credits': 30,
        'entry_requirements': [
            'Mínimo 5 años en liderazgo pastoral',
            'Diploma ministerial o equivalente',
            'Recomendación del consejo directivo',
            'Evaluación de liderazgo satisfactoria'
        ],
        'subjects': [
            {
                'name': 'Liderazgo Estratégico',
                'code': 'LES-001',
                'description': 'Liderazgo a nivel organizacional',
                'credits': 4,
                'semester': 1,
                'is_mandatory': True,
                'teaching_hours': 50,
                'practical_hours': 30
            },
            {
                'name': 'Gestión de Cambio Organizacional',
                'code': 'GCO-001',
                'description': 'Manejo del cambio en organizaciones eclesiásticas',
                'credits': 3,
                'semester': 1,
                'is_mandatory': True,
                'teaching_hours': 45,
                'practical_hours': 25
            },
            {
                'name': 'Desarrollo de Líderes',
                'code': 'DLI-001',
                'description': 'Formación y mentoría de nuevos líderes',
                'credits': 3,
                'semester': 2,
                'is_mandatory': True,
                'teaching_hours': 40,
                'practical_hours': 35
            },
            {
                'name': 'Comunicación Organizacional',
                'code': 'COM-001',
                'description': 'Comunicación efectiva en organizaciones',
                'credits': 3,
                'semester': 2,
                'is_mandatory': True,
                'teaching_hours': 45,
                'practical_hours': 20
            }
        ]
    },
    
    'certificado_consejeria': {
        'name': 'Certificado en Consejería Pastoral',
        'description': 'Especialización en consejería bíblica y pastoral',
        'program_type': 'certificate',
        'duration_months': 9,
        'target_roles': ['pastorado', 'lider_ministerio'],
        'min_ministry_years': 1,
        'total_credits': 24,
        'entry_requirements': [
            'Mínimo 1 año en ministerio',
            'Curso básico de consejería completado',
            'Recomendación pastoral',
            'Evaluación psicológica básica'
        ],
        'subjects': [
            {
                'name': 'Fundamentos de Consejería Bíblica',
                'code': 'FCB-001',
                'description': 'Principios bíblicos de la consejería',
                'credits': 3,
                'semester': 1,
                'is_mandatory': True
            },
            {
                'name': 'Consejería Matrimonial',
                'code': 'CMA-001',
                'description': 'Consejería especializada en matrimonios',
                'credits': 3,
                'semester': 1,
                'is_mandatory': True
            },
            {
                'name': 'Consejería Familiar',
                'code': 'CFA-001',
                'description': 'Consejería para familias en crisis',
                'credits': 3,
                'semester': 2,
                'is_mandatory': True
            },
            {
                'name': 'Consejería en Crisis',
                'code': 'CCR-001',
                'description': 'Intervención en situaciones de crisis',
                'credits': 3,
                'semester': 2,
                'is_mandatory': True
            },
            {
                'name': 'Práctica Supervisada',
                'code': 'PRS-001',
                'description': 'Práctica de consejería bajo supervisión',
                'credits': 4,
                'semester': 3,
                'is_mandatory': True,
                'practical_hours': 80
            }
        ]
    },
    
    'maestria_teologia_pastoral': {
        'name': 'Maestría en Teología Pastoral',
        'description': 'Programa de maestría para formación pastoral avanzada',
        'program_type': 'masters',
        'duration_months': 24,
        'target_roles': ['pastorado', 'superintendente', 'rector'],
        'min_ministry_years': 7,
        'total_credits': 60,
        'entry_requirements': [
            'Licenciatura en Teología o equivalente',
            'Mínimo 7 años en ministerio pastoral',
            'Diploma ministerial completado',
            'Tesis de investigación aprobada',
            'Examen de admisión',
            'Entrevista con comité académico'
        ],
        'subjects': [
            # Primer año
            {
                'name': 'Metodología de Investigación Teológica',
                'code': 'MIT-001',
                'description': 'Métodos de investigación en teología',
                'credits': 3,
                'semester': 1,
                'is_mandatory': True
            },
            {
                'name': 'Exégesis Avanzada del Antiguo Testamento',
                'code': 'EAT-001',
                'description': 'Exégesis profunda de textos del AT',
                'credits': 4,
                'semester': 1,
                'is_mandatory': True
            },
            {
                'name': 'Exégesis Avanzada del Nuevo Testamento',
                'code': 'ENT-001',
                'description': 'Exégesis profunda de textos del NT',
                'credits': 4,
                'semester': 2,
                'is_mandatory': True
            },
            {
                'name': 'Historia de la Iglesia',
                'code': 'HIG-001',
                'description': 'Historia del cristianismo y sus desarrollos',
                'credits': 3,
                'semester': 2,
                'is_mandatory': True
            },
            
            # Segundo año
            {
                'name': 'Teología Pastoral Contemporánea',
                'code': 'TPC-001',
                'description': 'Desafíos pastorales del siglo XXI',
                'credits': 4,
                'semester': 3,
                'is_mandatory': True
            },
            {
                'name': 'Seminario de Tesis',
                'code': 'STE-001',
                'description': 'Desarrollo de proyecto de tesis',
                'credits': 6,
                'semester': 4,
                'is_mandatory': True
            }
        ]
    }
}

# ============================================================================
# FUNCIONES DE INICIALIZACIÓN
# ============================================================================

def create_pastoral_institute_structure():
    """Crear estructura completa del instituto pastoral"""
    from nuevas_funcionalidades.sistema_academico_pastoral import (
        PastoralAcademicService, PastoralInstitute, PastoralProgram, PastoralSubject
    )
    
    # 1. Crear Instituto Principal
    institute = PastoralAcademicService.create_institute(
        name="Instituto de Formación Pastoral IMPA",
        description="Instituto corporativo para la formación continua de pastores y líderes",
        # rector_id y coordinator_id se asignarán después
    )
    
    programs_created = []
    
    # 2. Crear todos los programas predefinidos
    for program_key, program_data in PROGRAMAS_PASTORALES.items():
        # Crear programa
        program = PastoralAcademicService.create_pastoral_program(
            institute_id=institute.id,
            name=program_data['name'],
            program_type=program_data['program_type'],
            duration_months=program_data['duration_months'],
            target_roles=program_data['target_roles'],
            min_ministry_years=program_data['min_ministry_years'],
            total_credits=program_data['total_credits'],
            entry_requirements=program_data['entry_requirements'],
            requires_approval=True,
            max_students=20  # Límite por programa
        )
        
        # 3. Crear materias del programa
        for subject_data in program_data['subjects']:
            subject = PastoralSubject(
                program_id=program.id,
                name=subject_data['name'],
                code=subject_data['code'],
                description=subject_data['description'],
                credits=subject_data['credits'],
                semester=subject_data['semester'],
                is_mandatory=subject_data['is_mandatory'],
                teaching_hours=subject_data.get('teaching_hours', 40),
                practical_hours=subject_data.get('practical_hours', 0),
                syllabus=subject_data.get('syllabus', ''),
                learning_objectives=subject_data.get('learning_objectives', []),
                evaluation_methods=subject_data.get('evaluation_methods', []),
                prerequisites=subject_data.get('prerequisites', [])
            )
            
            db.session.add(subject)
        
        programs_created.append(program)
        print(f"✅ Programa creado: {program.name} ({program.program_type})")
    
    db.session.commit()
    
    print(f"\n🎓 Instituto Pastoral IMPA configurado:")
    print(f"   - 1 Instituto principal")
    print(f"   - {len(programs_created)} programas de formación")
    print(f"   - {sum(len(p['subjects']) for p in PROGRAMAS_PASTORALES.values())} materias totales")
    
    return {
        'institute': institute,
        'programs': programs_created
    }

def assign_academic_roles():
    """Asignar roles académicos a usuarios existentes"""
    from app.models import User
    
    # Buscar usuarios apropiados para roles académicos
    superintendents = User.query.filter_by(role='superintendente').all()
    secretaries = User.query.filter_by(role='secretaria').all()
    
    assignments = []
    
    # Asignar rector (primer superintendente)
    if superintendents:
        rector = superintendents[0]
        # Aquí se actualizaría el instituto con el rector
        assignments.append(f"Rector asignado: {rector.first_name} {rector.last_name}")
    
    # Asignar coordinador académico (primera secretaria)
    if secretaries:
        coordinator = secretaries[0]
        assignments.append(f"Coordinador académico asignado: {coordinator.first_name} {coordinator.last_name}")
    
    # Crear usuarios con rol 'instituto' si no existen
    instituto_users = User.query.filter_by(role='instituto').all()
    if not instituto_users:
        assignments.append("⚠️ Se recomienda crear usuarios con rol 'instituto' para gestión académica")
    
    return assignments

def generate_enrollment_report():
    """Generar reporte de matrículas pastorales"""
    from nuevas_funcionalidades.sistema_academico_pastoral import PastoralEnrollment, PastoralProgram
    from app.models import User
    
    # Estadísticas generales
    total_pastors = User.query.filter_by(role='pastorado').count()
    total_enrollments = PastoralEnrollment.query.count()
    active_enrollments = PastoralEnrollment.query.filter_by(status='active').count()
    completed_enrollments = PastoralEnrollment.query.filter_by(status='completed').count()
    
    # Por programa
    programs = PastoralProgram.query.all()
    program_stats = []
    
    for program in programs:
        enrollments = PastoralEnrollment.query.filter_by(program_id=program.id).count()
        active = PastoralEnrollment.query.filter_by(program_id=program.id, status='active').count()
        completed = PastoralEnrollment.query.filter_by(program_id=program.id, status='completed').count()
        
        program_stats.append({
            'program': program.name,
            'type': program.program_type,
            'total_enrollments': enrollments,
            'active': active,
            'completed': completed,
            'capacity_usage': f"{(active/program.max_students)*100:.1f}%" if program.max_students > 0 else "N/A"
        })
    
    report = {
        'general_stats': {
            'total_pastors': total_pastors,
            'total_enrollments': total_enrollments,
            'active_enrollments': active_enrollments,
            'completed_enrollments': completed_enrollments,
            'enrollment_rate': f"{(total_enrollments/total_pastors)*100:.1f}%" if total_pastors > 0 else "0%"
        },
        'program_stats': program_stats,
        'generated_at': datetime.now()
    }
    
    return report

print("Programas Pastorales Predefinidos - Listo para implementar")
print("Programas incluidos:")
print("1. Diploma en Ministerio Pastoral (18 meses)")
print("2. Especialización en Liderazgo Eclesiástico (12 meses)")
print("3. Certificado en Consejería Pastoral (9 meses)")
print("4. Maestría en Teología Pastoral (24 meses)")
