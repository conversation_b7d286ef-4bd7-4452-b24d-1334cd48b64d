<!-- /app/templates/miembros/dashboard.html -->
{% extends "base.html" %}
{% block title %}Dashboard Miembro{% endblock %}

{% block content %}
<div class="container my-4">
    <h1><i class="fas fa-user mr-2"></i>Dashboard de Miembro</h1> {# Icono Miembro #}
    <p>Bienvenido, {{ view_user.first_name }} {{ view_user.last_name }}!</p>
    <p>Desde aquí puedes acceder a las funcionalidades disponibles para ti.</p>

    <!-- ############################################## -->
    <!-- ## SECCIÓN DE WIDGETS (Miembro)             ## -->
    <!-- ############################################## -->
    <div class="row mb-4">
        <!-- Widget Mensaje<PERSON> -->
        <div class="col-md-4"> {# Ajustar ancho si hay menos widgets #}
            <div class="card text-white bg-info mb-3 shadow">
                 <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-envelope-open-text mr-2"></i>Mensajes Nuevos</h5>
                    <p class="card-text display-4">{{ unread_count if unread_count is defined else 'N/A' }}</p>
                    <small>Sin leer</small>
                </div>
                <a href="{{ url_for('routes.inbox') }}" class="card-footer text-white">Ir al Buzón <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
        <!-- Widget Cumpleaños (Solo muestra próximos, no necesariamente el propio) -->
         <div class="col-md-4"> {# Ajustar ancho #}
            <div class="card bg-light mb-3 shadow">
                <div class="card-header"><i class="fas fa-birthday-cake mr-2"></i>Próximos Cumpleaños</div>
                <div class="card-body" style="max-height: 150px; overflow-y: auto;">
                    {# Nota: La ruta de dashboard para miembro actualmente pasa una lista vacía. #}
                    {# Si se cambiara para mostrar cumpleaños de la iglesia, se verían aquí. #}
                    {% if upcoming_birthdays %}
                         <ul class="list-unstyled mb-0">
                            {% for user_bday in upcoming_birthdays %}
                                <li class="mb-1">
                                     {# Sin enlace para miembros para evitar exponer otros perfiles? O enlazar? Decisión de diseño. #}
                                    {{ user_bday.full_name }}
                                    <span class="badge badge-pill badge-secondary float-right">{{ user_bday.date_of_birth.strftime('%d/%m') }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted small">No hay cumpleaños próximos para mostrar.</p>
                    {% endif %}
                </div>
                 <a href="{{ url_for('routes.calendar_view') }}" class="card-footer text-muted">Ver Calendario Completo <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
         <!-- Widget Mi Perfil -->
        <div class="col-md-4"> {# Ajustar ancho #}
            <div class="card text-white bg-secondary mb-3 shadow">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-id-card mr-2"></i>Mi Perfil</h5>
                    <p class="card-text">Actualiza tu información personal y contraseña.</p>
                </div>
                 <a href="{{ url_for('routes.edit_own_profile') }}" class="card-footer text-white">Editar mi Perfil <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>
    <!-- ############################################## -->
    <!-- ## FIN SECCIÓN DE WIDGETS                     ## -->
    <!-- ############################################## -->

    <hr>

    <!-- ############################################## -->
    <!-- ## SECCIÓN ACCIONES RÁPIDAS Y ANUNCIOS      ## -->
    <!-- ############################################## -->
     <div class="row mb-4">
        <!-- Columna Acciones Rápidas (Miembro) -->
        <div class="col-lg-4">
             <div class="card shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <i class="fas fa-bolt mr-2"></i>Acciones Rápidas
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('routes.edit_own_profile') }}" class="list-group-item list-group-item-action"><i class="fas fa-user-edit fa-fw mr-2 text-primary"></i>Editar Mi Perfil</a>
                    <a href="{{ url_for('routes.list_documents') }}" class="list-group-item list-group-item-action"><i class="fas fa-folder-open fa-fw mr-2 text-info"></i>Ver Documentos</a>
                    <a href="{{ url_for('routes.calendar_view') }}" class="list-group-item list-group-item-action"><i class="far fa-calendar-alt fa-fw mr-2 text-warning"></i>Ver Calendario</a>
                    <a href="{{ url_for('routes.send_message') }}" class="list-group-item list-group-item-action"><i class="fas fa-paper-plane fa-fw mr-2 text-success"></i>Enviar Mensaje (Pastor)</a>
                    <a href="{{ url_for('routes.inbox') }}" class="list-group-item list-group-item-action"><i class="fas fa-inbox fa-fw mr-2 text-secondary"></i>Consultar Mensajes</a>
                </div>
            </div>
        </div>

         <!-- Columna Anuncios Recientes -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                 <div class="card-header bg-light">
                    <i class="fas fa-bullhorn mr-2"></i>Anuncios Recientes
                 </div>
                 <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% if announcements %}
                        {% for announcement in announcements %}
                            <div class="alert alert-{{ 'secondary' if loop.index % 2 == 0 else 'light' }} border mb-2" role="alert">
                                <h5 class="alert-heading">{{ announcement.title }}</h5>
                                <p class="mb-1 small">{{ announcement.content | nl2br | safe }}</p>
                                <hr class="my-1">
                                <p class="mb-0 small text-muted">
                                    <i class="fas fa-user fa-fw"></i> {{ announcement.author.full_name if announcement.author else 'Sistema' }}
                                    <i class="far fa-clock fa-fw ml-2"></i> {{ announcement.created_at | to_local }}
                                </p>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No hay anuncios para mostrar.</p>
                    {% endif %}
                 </div>
             </div>
        </div>
    </div>
    <!-- ############################################## -->
    <!-- ## FIN SECCIÓN ACCIONES/ANUNCIOS            ## -->
    <!-- ############################################## -->

    {# No se necesitan enlaces detallados adicionales para miembro por ahora #}

</div> {# Fin container #}
{% endblock %}