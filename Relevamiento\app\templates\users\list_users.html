<!-- app/templates/users/list_users.html -->
{% extends "users/base_users.html" %}

{% block title %}Gestión de Usuarios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-users"></i>
                    Gestión de Usuarios
                </h2>
                {% if current_user.can_manage_users() %}
                <a href="{{ url_for('users.create_user') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Nuevo Usuario
                </a>
                {% endif %}
            </div>

            <!-- Filtros -->
            <div class="filter-section">
                <h5><i class="fas fa-filter"></i> Filtros</h5>
                <form method="GET" id="filter-form">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="search" class="form-label">Buscar</label>
                            <input type="text"
                                   class="form-control"
                                   id="search"
                                   name="search"
                                   value="{{ request.args.get('search', '') }}"
                                   placeholder="Nombre de usuario o email">
                        </div>

                        <div class="filter-group">
                            <label for="role" class="form-label">Rol</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">Todos los roles</option>
                                <option value="administrador" {{ 'selected' if request.args.get('role') == 'administrador' }}>Administrador</option>
                                <option value="supervisor" {{ 'selected' if request.args.get('role') == 'supervisor' }}>Supervisor</option>
                                <option value="operador" {{ 'selected' if request.args.get('role') == 'operador' }}>Operador</option>
                                <option value="visualizador" {{ 'selected' if request.args.get('role') == 'visualizador' }}>Visualizador</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="status" class="form-label">Estado</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Todos</option>
                                <option value="active" {{ 'selected' if request.args.get('status') == 'active' }}>Activos</option>
                                <option value="inactive" {{ 'selected' if request.args.get('status') == 'inactive' }}>Inactivos</option>
                            </select>
                        </div>

                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                Filtrar
                            </button>
                            <button type="button" class="btn btn-secondary" id="clear-filters">
                                <i class="fas fa-times"></i>
                                Limpiar
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Resultados -->
            <div class="results-section">
                {% if users_data %}
                <div class="results-info mb-3">
                    <span class="badge bg-info">{{ users_data|length }} usuario(s) encontrado(s)</span>
                </div>

                <!-- Vista de tarjetas para móvil -->
                <div class="d-md-none">
                    {% for user in users_data %}
                    <div class="user-card">
                        <div class="user-header">
                            <div class="user-info">
                                <h6 class="mb-1">
                                    <i class="fas fa-user"></i>
                                    {{ user.username }}
                                    <span class="role-badge role-{{ user.role }}">{{ user.role }}</span>
                                </h6>
                                {% if user.email %}
                                <small class="text-muted">
                                    <i class="fas fa-envelope"></i>
                                    {{ user.email }}
                                </small>
                                {% endif %}
                                <div class="permissions-summary">
                                    <span class="{{ 'status-active' if user.is_active else 'status-inactive' }}">
                                        <i class="fas fa-{{ 'check-circle' if user.is_active else 'times-circle' }}"></i>
                                        {{ 'Activo' if user.is_active else 'Inactivo' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        {% if current_user.can_manage_users() %}
                        <div class="user-actions">
                            <a href="{{ url_for('users.edit_user', user_id=user.id) }}"
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i>
                                Editar
                            </a>

                            <a href="{{ url_for('users.user_permissions', user_id=user.id) }}"
                               class="btn btn-sm btn-outline-info">
                                <i class="fas fa-key"></i>
                                Permisos
                            </a>

                            {% if user.id != current_user.id %}
                            <form method="POST"
                                  action="{{ url_for('users.toggle_user_status', user_id=user.id) }}"
                                  style="display: inline;">
                                <!-- CSRF token será agregado automáticamente por Flask-WTF -->
                                <button type="submit"
                                        class="btn btn-sm btn-outline-{{ 'warning' if user.is_active else 'success' }} btn-toggle-status"
                                        data-username="{{ user.username }}"
                                        data-action="{{ 'deactivate' if user.is_active else 'activate' }}">
                                    <i class="fas fa-{{ 'pause' if user.is_active else 'play' }}"></i>
                                    {{ 'Desactivar' if user.is_active else 'Activar' }}
                                </button>
                            </form>

                            <form method="POST"
                                  action="{{ url_for('users.delete_user', user_id=user.id) }}"
                                  style="display: inline;">
                                <!-- CSRF token será agregado automáticamente por Flask-WTF -->
                                <button type="submit"
                                        class="btn btn-sm btn-outline-danger btn-delete-user"
                                        data-username="{{ user.username }}">
                                    <i class="fas fa-trash"></i>
                                    Eliminar
                                </button>
                            </form>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>

                <!-- Vista de tabla para desktop -->
                <div class="d-none d-md-block">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Usuario</th>
                                    <th>Email</th>
                                    <th>Rol</th>
                                    <th>Estado</th>
                                    <th>Creado</th>
                                    {% if current_user.can_manage_users() %}
                                    <th>Acciones</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users_data %}
                                <tr>
                                    <td>
                                        <i class="fas fa-user"></i>
                                        {{ user.username }}
                                    </td>
                                    <td>
                                        {% if user.email %}
                                        <i class="fas fa-envelope"></i>
                                        {{ user.email }}
                                        {% else %}
                                        <span class="text-muted">Sin email</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="role-badge role-{{ user.role }}">
                                            {{ user.role }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="{{ 'status-active' if user.is_active else 'status-inactive' }}">
                                            <i class="fas fa-{{ 'check-circle' if user.is_active else 'times-circle' }}"></i>
                                            {{ 'Activo' if user.is_active else 'Inactivo' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.created_at %}
                                        <small>{{ user.created_at.strftime('%d/%m/%Y') }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    {% if current_user.can_manage_users() %}
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('users.edit_user', user_id=user.id) }}"
                                               class="btn btn-outline-primary"
                                               title="Editar usuario">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <a href="{{ url_for('users.user_permissions', user_id=user.id) }}"
                                               class="btn btn-outline-info"
                                               title="Ver permisos">
                                                <i class="fas fa-key"></i>
                                            </a>

                                            {% if user.id != current_user.id %}
                                            <form method="POST"
                                                  action="{{ url_for('users.toggle_user_status', user_id=user.id) }}"
                                                  style="display: inline;">
                                                <!-- CSRF token será agregado automáticamente por Flask-WTF -->
                                                <button type="submit"
                                                        class="btn btn-outline-{{ 'warning' if user.is_active else 'success' }} btn-toggle-status"
                                                        data-username="{{ user.username }}"
                                                        data-action="{{ 'deactivate' if user.is_active else 'activate' }}"
                                                        title="{{ 'Desactivar' if user.is_active else 'Activar' }} usuario">
                                                    <i class="fas fa-{{ 'pause' if user.is_active else 'play' }}"></i>
                                                </button>
                                            </form>

                                            <form method="POST"
                                                  action="{{ url_for('users.delete_user', user_id=user.id) }}"
                                                  style="display: inline;">
                                                <!-- CSRF token será agregado automáticamente por Flask-WTF -->
                                                <button type="submit"
                                                        class="btn btn-outline-danger btn-delete-user"
                                                        data-username="{{ user.username }}"
                                                        title="Eliminar usuario">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No se encontraron usuarios</h5>
                    <p class="text-muted">
                        {% if request.args.get('search') or request.args.get('role') or request.args.get('status') %}
                        Intenta ajustar los filtros de búsqueda.
                        {% else %}
                        {% if current_user.can_manage_users() %}
                        <a href="{{ url_for('users.create_user') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Crear primer usuario
                        </a>
                        {% endif %}
                        {% endif %}
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
