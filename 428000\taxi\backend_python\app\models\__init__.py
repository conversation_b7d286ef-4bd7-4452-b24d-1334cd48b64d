# app/models/__init__.py

# Importa tus modelos con los nombres correctos
from .user import User, UserRoleModel, UserRolesAssociation, RoleEnum
from .vehicle import Vehicle, VehicleStatus
from .trip import Trip, TripStatusEnum
from .base_station import BaseStation
from .trip_stop import TripStop, TripStopStatusEnum

# Exporta todos los modelos para que sean accesibles desde app.models
__all__ = [
    'User', 'UserRoleModel', 'UserRolesAssociation', 'RoleEnum',
    'Vehicle', 'VehicleStatus',
    'Trip', 'TripStatusEnum',
    'BaseStation',
    'TripStop', 'TripStopStatusEnum'
]

# Agrega aquí todos tus modelos para que Alembic los vea y los use
# al generar las migraciones.
# La clase Base de SQLAlchemy ya debería estar configurada en alembic/env.py
# para que Alembic use el target_metadata de esa Base.