<!-- /app/templates/calendar.html -->
{% extends "base.html" %}
{% block title %}Calendario de Anotaciones y Cumpleaños{% endblock %}

{% block content %}
<div class="container my-4">
  <h1 class="mb-4">Calendario de Anotaciones y Cumpleaños</h1>
  <!-- Botón para agregar un evento -->
  <div class="mb-3">
    <a href="{{ url_for('routes.add_calendar_event') }}" class="btn btn-primary">Agregar Evento</a>
  </div>
  <div id="calendar"></div>
</div>
{% endblock %}

{% block scripts %}
  <!-- FullCalendar JS -->
  <script src='https://unpkg.com/fullcalendar@6.1.8/index.global.min.js'></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var calendarEl = document.getElementById('calendar');
      // Genera el prefijo para editar y eliminar (usando dummy "0" y luego quitándolo)
      var editUrlPrefix = "{{ url_for('routes.edit_calendar_event', event_id=0) }}".replace('0', '');
      var deleteUrlPrefix = "{{ url_for('routes.delete_calendar_event', event_id=0) }}".replace('0', '');
      
      var calendar = new FullCalendar.Calendar(calendarEl, {
        locale: 'es',
        initialView: 'dayGridMonth',
        headerToolbar: {
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        events: {{ events_json|safe }},
        eventClick: function(info) {
          // Ignorar eventos de cumpleaños (IDs que empiezan con "birthday-")
          if(String(info.event.id).startsWith("birthday-")){
            return;
          }
          var accion = prompt("Digite 'e' para editar, 'd' para eliminar o deje en blanco para cancelar:");
          if(accion === 'e'){
            window.location.href = editUrlPrefix + info.event.id;
          } else if(accion === 'd'){
            if(confirm("¿Está seguro de eliminar este evento?")){
              fetch(deleteUrlPrefix + info.event.id, {
                method: "POST",
                headers: {
                  'Content-Type': 'application/json'
                  {# Si necesitas CSRF, inclúyelo usando comentarios de Jinja para que no se evalúe: 
                  {# 'X-CSRFToken': "{{ csrf_token() }}" #} 
                }
              })
              .then(response => {
                if(response.ok){
                  alert("Evento eliminado correctamente.");
                  calendar.refetchEvents();
                } else {
                  alert("Error al eliminar el evento.");
                }
              })
              .catch(error => {
                console.error("Error:", error);
                alert("Error al eliminar el evento.");
              });
            }
          }
        }
      });
      calendar.render();
    });
  </script>
{% endblock %}
