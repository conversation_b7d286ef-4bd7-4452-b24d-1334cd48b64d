"""Initial schema

Revision ID: c9236737a1de
Revises: 
Create Date: 2025-05-07 00:04:45.679843

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c9236737a1de'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('ptt_config')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ptt_config',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('activation_type', sa.VARCHAR(length=50), nullable=False),
    sa.Column('serial_port', sa.VARCHAR(length=10), nullable=False),
    sa.Column('bps', sa.BOOLEAN(), nullable=True),
    sa.Column('key_ptt', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
