# File: backend/apps/core/forms.py
# -----------------------------------------------

from django import forms
from django.contrib.auth.forms import AuthenticationForm, UserCreationForm, UserChangeForm # Asegúrate de importar AuthenticationForm
from apps.users.models import User, UserProfile # importa tu modelo

# === INICIO DE LA CLASE CustomLoginForm AÑADIDA ===
class CustomLoginForm(AuthenticationForm):
    # Puedes personalizar campos aquí si lo deseas, por ejemplo, añadiendo clases CSS a los widgets
    # Ejemplo:
    # username = forms.CharField(widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Usuario'}))
    # password = forms.CharField(widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Contraseña'}))
    pass # Si heredas de AuthenticationForm y no añades/modificas campos, 'pass' está bien.
# === FIN DE LA CLASE CustomLoginForm AÑADIDA ===

class CustomRegisterForm(UserCreationForm):
    # Campos extra que irán al modelo User directamente si existen allí,
    # o que puedes manejar para UserProfile.
    # UserCreationForm por defecto no maneja first_name/last_name, hay que añadirlos si se quieren.
    first_name = forms.CharField(max_length=150, required=False, label="Nombre(s)")
    last_name = forms.CharField(max_length=150, required=False, label="Apellido(s)")
    
    # Campos extra para el perfil
    telefono = forms.CharField(max_length=20, required=False, label="Teléfono")
    # edad = forms.IntegerField(min_value=0, required=False, label="Edad") # Quitado temporalmente para simplificar, ya que tu UserProfile lo tiene
                                                                        # y UserCreationForm no lo tiene por defecto.
                                                                        # Si lo quieres en el form de registro, defínelo aquí.

    class Meta(UserCreationForm.Meta): # Hereda de UserCreationForm.Meta para mantener su comportamiento
        model = User
        # Campos que se mostrarán en el formulario de registro.
        # UserCreationForm maneja username, password1, password2 internamente.
        # Aquí lista los campos del modelo User que quieres que se muestren en el form,
        # además de los campos personalizados que has definido en esta clase (telefono, edad).
        fields = ("username", "first_name", "last_name", "telefono") # 'edad' se puede añadir si también lo defines como campo de clase

    def save(self, commit=True):
        user = super().save(commit=False) # Llama al save de UserCreationForm
        user.role = User.Role.CIUDADANO
        
        # Asignar first_name y last_name al objeto user
        user.first_name = self.cleaned_data.get("first_name")
        user.last_name = self.cleaned_data.get("last_name")
        
        if commit:
            user.save()
            # Crear/actualizar UserProfile
            telefono = self.cleaned_data.get("telefono")
            # edad = self.cleaned_data.get("edad") # Si añades 'edad' como campo de clase arriba, obténlo aquí

            # UserProfile ya tiene un campo 'user' OneToOne.
            # Si ya existe un perfil, podrías querer actualizarlo en lugar de fallar o crear duplicados (aunque OneToOne lo previene).
            # Usar update_or_create es más robusto si el perfil pudiera crearse por otras vías.
            UserProfile.objects.update_or_create(
                user=user,
                defaults={
                    'telefono': telefono,
                    # 'edad': edad # Si lo incluyes
                }
            )
        return user
    
class UserManageCreateForm(UserCreationForm):
    class Meta:
        model  = User
        fields = ("username", "first_name", "last_name", "role", "password1", "password2")

class UserManageUpdateForm(UserChangeForm):
    password = None                           # ocultamos el hash
    class Meta:
        model  = User
        fields = ("username", "first_name", "last_name", "role", "is_active")