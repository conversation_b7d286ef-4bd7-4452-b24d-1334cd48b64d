#!/bin/bash

# Activar el entorno virtual
source /home/<USER>/taxi/428000/bin/activate

# Instalar setuptools (necesario para Python 3.12)
pip install setuptools

# Desinstalar aioredis si está instalado
pip uninstall -y aioredis

# Instalar redis-py con soporte para asyncio
pip install "redis>=4.2.0"

# Instalar otras dependencias
pip install websockets requests polyline

# Mostrar las dependencias instaladas
pip list

echo "Dependencias instaladas correctamente."
