// /static/js/index.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;

    console.log(`Conectado como ${username} al nodo ${nodeId}`);  // Log inicial

    const socket = io.connect('https://www.patagoniaservers.com.ar:5000/node', {
        query: {
            node_id: nodeId,
            username: username
        }
    });

    // Referencia al botón de transmisión de audio
    const transmitAudioButton = document.getElementById('transmitAudio');

    socket.on('connect', () => {
        console.log(`Conexión exitosa como ${username} al nodo ${nodeId}`);
        // Mostrar en la interfaz a qué nodo está conectado el usuario
        const nodeInfo = document.getElementById('node-info');
        if (nodeInfo) {
            nodeInfo.textContent = `Conectado al nodo ${nodeId}`;
        }
    });

    // Evento para recibir lista de usuarios conectados
    socket.on('update_users', (users) => {
        console.log('Usuarios conectados:', users);
        
        // Usamos un Set para eliminar duplicados
        const uniqueUsers = [...new Set(users)];

        // Lógica para mostrar usuarios conectados en la interfaz
        const usersList = document.getElementById('users_list');
        if (usersList) {
            usersList.innerHTML = ''; // Limpiar la lista
            uniqueUsers.forEach(user => {
                const userItem = document.createElement('li');
                userItem.textContent = user;
                usersList.appendChild(userItem);
            });
        }
    });

    // Evento para mostrar niveles de audio en la pantalla
    const volumeMeter = document.getElementById('volume_meter');
    socket.on('audio_level', (level) => {
        if (volumeMeter) {
            volumeMeter.style.width = `${level}%`;
            volumeMeter.style.backgroundColor = level >= 50 ? 'red' : 'green';
        }
        console.log('Nivel de audio:', level);
    });

    // Mostrar eventos de audio
    socket.on('audio_event', (data) => {
        console.log('Evento de audio recibido:', data);
        // Mostrar el evento de audio en la interfaz
        const eventsList = document.getElementById('events_list');
        if (eventsList) {
            const newEvent = document.createElement('li');
            newEvent.textContent = `Audio recibido de ${data.user}`;
            eventsList.appendChild(newEvent);
        }
    });

    // Evento global de inicio de audio
    socket.on('global_audio_start', (data) => {
        if (data.node_id === nodeId) {
            console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio iniciado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }

            // Cambiar imagen a "rojo.png" cuando el audio comienza
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/rojo.png';
            }
        }
    });

    // Evento global de finalización de audio
    socket.on('global_audio_end', (data) => {
        if (data.node_id === nodeId) {
            console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
            const eventsList = document.getElementById('events_list');
            if (eventsList) {
                const newEvent = document.createElement('li');
                newEvent.textContent = `Audio finalizado por ${data.user}`;
                eventsList.appendChild(newEvent);
            }

            // Cambiar imagen a "verde.png" cuando el audio termina
            if (transmitAudioButton) {
                transmitAudioButton.src = '/static/verde.png';
            }
        }
    });

    // Solicitar usuarios conectados al nodo al iniciar
    socket.emit('get_connected_users', { node_id: nodeId });
});
