// fileTransfer.js

import { socket, safeEmit } from './socketConnection.js';

// Obtener elementos del DOM
const fileInput = document.getElementById('fileInput');
const uploadFileButton = document.getElementById('uploadFileButton');
const dragDropArea = document.getElementById('dragDropArea');
const username = document.body.dataset.username;
const nodeId = parseInt(document.body.dataset.nodeId);

// --- Carga de archivos (drag & drop y botón) ---

if (uploadFileButton) { //Verifico que exista el boton antes de añadir el event listener
    uploadFileButton.addEventListener('click', function() {
        fileInput.click();
    });
}

if (fileInput) { //Verifico que exista el input antes de añadir el event listener
    fileInput.addEventListener('change', handleFileUpload);
}

if(dragDropArea){ //Verifico que exista el area antes de añadir los event listeners
    dragDropArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        dragDropArea.style.borderColor = "#000";
    });
    dragDropArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dragDropArea.style.borderColor = "#ccc";
    });
    dragDropArea.addEventListener('drop', function(e) {
        e.preventDefault();
        dragDropArea.style.borderColor = "#ccc";
        handleFileDrop(e);
    });
}

function handleFileUpload(event) {
    if (event.target.files[0]) { uploadFile(event.target.files[0]); }
}

function handleFileDrop(event) {
    if (event.dataTransfer.files[0]) { uploadFile(event.dataTransfer.files[0]); }
}

function uploadFile(file) {
    const MAX_SIZE = 10 * 1024 * 1024; // 10 MB
    if (file.size > MAX_SIZE) {
        alert("El archivo excede el tamaño máximo permitido de 10 MB.");
        return;
    }
    const reader = new FileReader();
    reader.onload = function(e) {
        const base64Data = e.target.result.split(',')[1];
        let eventName = 'transmit_file';
        let payload = { fileData: base64Data, filename: file.name, user: username };
         if (typeof chat_room !== 'undefined') {
                eventName = 'transmit_private_file';
                payload.chat_room = chat_room;
            } else {
                payload.node_id = String(nodeId);
            }
        safeEmit(eventName, payload);
    };
    reader.readAsDataURL(file);
}

// --- Listeners de Socket.IO ---

//Centralizo la logica para determinar si es un chat privado o no.
function handleFileTransferError(data, isPrivate) {
    const errorEvent = isPrivate ? 'private_file_upload_error' : 'file_upload_error';
    alert(`Error al cargar archivo ${data.filename}: ${data.message}`);
}

function handleReceiveFile(data, isPrivate) {
    const newMessage = document.createElement('p');
    newMessage.className = 'message';
    const prefix = isPrivate? '' : 'envió el archivo ';
    newMessage.innerHTML = `<strong>${data.user}</strong> ${prefix}<a href="${data.fileinfo.filepath}" download="${data.fileinfo.filename}">${data.fileinfo.filename}</a> <span class="timestamp">${data.timestamp || new Date().toLocaleTimeString()}</span>`;
     // messagesDiv debería estar definido o importado si es necesario
    if(typeof messagesDiv !== "undefined") {
      messagesDiv.appendChild(newMessage);
      messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

}

socket.on('receive_file', (data) => handleReceiveFile(data, false));
socket.on('file_upload_error', (data) => handleFileTransferError(data, false));
socket.on('transmit_file', (data) => { /*...*/ }); // No es necesario un listener como tal, la emision ya esta

socket.on('receive_private_file',  (data) => handleReceiveFile(data, true));
socket.on('private_file_upload_error', (data) => handleFileTransferError(data, true));
socket.on('transmit_private_file', (data) => { /*...*/ });// No es necesario un listener como tal, la emision ya esta


// Este módulo no exporta nada.