# /app/forms.py
import datetime
from flask_wtf import FlaskForm
from wtforms import (
    StringField, PasswordField, SubmitField, BooleanField, ValidationError,
    SelectField, SelectMultipleField, widgets, FloatField, DateField,
    TextAreaField, DecimalField, IntegerField, FileField
)
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional, NumberRange
# Asegurar que todos los modelos usados en validaciones o choices estén importados
from app.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Pastor, Member, MemberFunction
from app import db # Necesario para validaciones y __init__

# --- Helpers ---
class MultiCheckboxField(SelectMultipleField):
    """Renderiza un SelectMultipleField como checkboxes."""
    widget = widgets.ListWidget(prefix_label=False)
    option_widget = widgets.CheckboxInput()

# --- Formulario de Registro de Usuario ---
class RegistrationForm(FlaskForm):
    username = <PERSON><PERSON>ield('Usuario', validators=[DataRequired(), Length(min=2, max=20)])
    email = StringField('Correo Electrónico', validators=[DataRequired(), Email()])
    password = PasswordField('Contraseña', validators=[DataRequired()])
    confirm_password = PasswordField('Confirmar Contraseña', validators=[DataRequired(), EqualTo('password', message='Las contraseñas deben coincidir.')])
    first_name = StringField('Nombre', validators=[DataRequired()])
    last_name = StringField('Apellido', validators=[DataRequired()])
    dni = StringField('DNI', validators=[Optional(), Length(max=20)]) # Añadido Length
    address = StringField('Dirección', validators=[Optional()])
    ciudad = StringField('Ciudad Personal', validators=[Optional(), Length(max=255)]) # Añadido Length
    estado_civil = StringField('Estado Civil', validators=[Optional(), Length(max=50)]) # Añadido Length
    phone_number = StringField('Teléfono', validators=[Optional(), Length(max=255)]) # Añadido Length

    role = SelectField('Rol Principal', choices=[
        ('administrador', 'Administrador'),
        ('secretaria', 'Secretaria'),
        ('pastorado', 'Pastorado'),
        ('miembro', 'Miembro')
    ], validators=[DataRequired()])
    # Iglesia a la que pertenece (se popula en la ruta)
    church = SelectField('Iglesia (pertenece)', coerce=int, validators=[Optional()])

    # Campos específicos para rol 'pastorado' (se muestran/ocultan con JS en plantilla)
    grado = SelectField('Grado Pastoral (si es Pastor)', choices=[ # Choices corregidas para coincidir con BD/importación
        ('', '-- Seleccionar Grado --'), # Opción por defecto
        ('Probando', 'Probando'),
        ('Diacono', 'Diacono'),
        ('Presbistero', 'Presbistero')
    ], validators=[Optional()])
    address_pastoral = StringField('Dirección Casa Pastoral (si es Pastor)', validators=[Optional(), Length(max=255)]) # Campo renombrado para claridad
    latitude = FloatField('Latitud (Casa Pastoral)', validators=[Optional(), NumberRange(min=-90, max=90)])
    longitude = FloatField('Longitud (Casa Pastoral)', validators=[Optional(), NumberRange(min=-180, max=180)])
    pastor_roles = MultiCheckboxField('Roles Adicionales (si es Pastor)', coerce=int) # Se popula en la ruta

    submit = SubmitField('Registrar Usuario')

    # Validaciones de unicidad
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('Ese nombre de usuario ya está en uso. Por favor, elige otro.')

    def validate_email(self, email):
        # Permitir email vacío si no es requerido, pero si se provee, debe ser único
        if email.data:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('Ese correo electrónico ya está en uso. Por favor, elige otro.')

    def validate_dni(self, dni):
        # Permitir DNI vacío, pero si se provee, debe ser único
        if dni.data:
            user = User.query.filter_by(dni=dni.data).first()
            if user:
                raise ValidationError('Ese DNI ya está registrado.')

# --- Formulario de Edición de Usuario ---
class EditUserForm(FlaskForm):
    username = StringField('Usuario', validators=[DataRequired(), Length(min=2, max=20)])
    email = StringField('Correo Electrónico', validators=[DataRequired(), Email()])
    # Opcional: permitir cambiar contraseña
    password = PasswordField('Nueva Contraseña (dejar vacío para no cambiar)')
    confirm_password = PasswordField('Confirmar Nueva Contraseña', validators=[EqualTo('password', message='Las contraseñas deben coincidir.')])
    first_name = StringField('Nombre', validators=[DataRequired()])
    last_name = StringField('Apellido', validators=[DataRequired()])
    dni = StringField('DNI', validators=[Optional(), Length(max=20)])
    ciudad = StringField('Ciudad Personal', validators=[Optional(), Length(max=255)])
    estado_civil = StringField('Estado Civil', validators=[Optional(), Length(max=50)])
    phone_number = StringField('Teléfono', validators=[Optional(), Length(max=255)])
    address = StringField('Dirección Personal', validators=[Optional(), Length(max=255)]) # Dirección del User
    date_of_birth = DateField('Fecha de Nacimiento (YYYY-MM-DD)', format='%Y-%m-%d', validators=[Optional()])

    role = SelectField('Rol Principal', choices=[
        ('administrador', 'Administrador'),
        ('secretaria', 'Secretaria'),
        ('pastorado', 'Pastorado'),
        ('miembro', 'Miembro')
    ], validators=[DataRequired()])
    church = SelectField('Iglesia (pertenece)', coerce=int, validators=[Optional()]) # Se popula en la ruta
    pastor_roles = MultiCheckboxField('Roles Adicionales (si es Pastor)', coerce=int) # Se popula en la ruta

    submit = SubmitField('Guardar Cambios')

    # Guardamos el ID del usuario que se está editando para validaciones
    def __init__(self, original_username, original_email, original_dni, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.original_username = original_username
        self.original_email = original_email
        self.original_dni = original_dni

    def validate_username(self, username):
        if username.data != self.original_username:
            user = User.query.filter_by(username=username.data).first()
            if user:
                raise ValidationError('Ese nombre de usuario ya está en uso.')

    def validate_email(self, email):
        if email.data and email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('Ese correo electrónico ya está en uso.')

    def validate_dni(self, dni):
         if dni.data and dni.data != self.original_dni:
            user = User.query.filter_by(dni=dni.data).first()
            if user:
                raise ValidationError('Ese DNI ya está registrado.')

# --- Formulario de Registro de Miembro (por Pastor) ---
class MemberRegistrationForm(FlaskForm):
    username = StringField('Usuario', validators=[DataRequired(), Length(min=2, max=20)])
    email = StringField('Correo Electrónico', validators=[Optional(), Email()]) # Email opcional para miembros?
    password = PasswordField('Contraseña', validators=[DataRequired()])
    confirm_password = PasswordField('Confirmar Contraseña', validators=[DataRequired(), EqualTo('password')])
    first_name = StringField('Nombre', validators=[DataRequired()])
    last_name = StringField('Apellido', validators=[DataRequired()])
    # Campos opcionales que puede llenar el pastor al registrar
    dni = StringField('DNI', validators=[Optional(), Length(max=20)])
    ciudad = StringField('Ciudad Personal', validators=[Optional(), Length(max=255)])
    estado_civil = StringField('Estado Civil', validators=[Optional(), Length(max=50)])
    phone_number = StringField('Teléfono', validators=[Optional(), Length(max=255)])
    address = StringField('Dirección Personal', validators=[Optional(), Length(max=255)])
    date_of_birth = DateField('Fecha de Nacimiento (YYYY-MM-DD)', format='%Y-%m-%d', validators=[Optional()])
    alergies = StringField('Alergias', validators=[Optional(), Length(max=255)])
    emergency_contact = StringField('Contacto de Emergencia', validators=[Optional(), Length(max=255)])
    member_functions = MultiCheckboxField('Funciones Iniciales', coerce=int) # Se popula en la ruta

    submit = SubmitField('Registrar Miembro')

    # Validaciones de unicidad (igual que en RegistrationForm)
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('Ese nombre de usuario ya está en uso.')

    def validate_email(self, email):
        if email.data:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('Ese correo electrónico ya está en uso.')

    def validate_dni(self, dni):
        if dni.data:
            user = User.query.filter_by(dni=dni.data).first()
            if user:
                raise ValidationError('Ese DNI ya está registrado.')


# --- Formulario de Edición de Miembro ---
# Usado para que Admin/Secretaria/Pastor editen datos específicos del Miembro
class EditMemberForm(FlaskForm):
    # Campos del User que el Miembro (o su pastor) puede editar
    first_name = StringField('Nombre', validators=[DataRequired()])
    last_name = StringField('Apellido', validators=[DataRequired()])
    email = StringField('Correo Electrónico', validators=[Optional(), Email()]) # Opcional?
    phone_number = StringField('Teléfono', validators=[Optional(), Length(max=255)])
    address = StringField('Dirección Personal', validators=[Optional(), Length(max=255)])
    date_of_birth = DateField('Fecha de Nacimiento', format='%Y-%m-%d', validators=[Optional()])
    ciudad = StringField('Ciudad Personal', validators=[Optional(), Length(max=255)])
    estado_civil = StringField('Estado Civil', validators=[Optional(), Length(max=50)])
    dni = StringField('DNI', validators=[Optional(), Length(max=20)]) # Puede editar su DNI?

    # Campos específicos del Member
    alergies = StringField('Alergias', validators=[Optional(), Length(max=255)])
    emergency_contact = StringField('Contacto de Emergencia', validators=[Optional(), Length(max=255)])
    member_functions = MultiCheckboxField('Funciones', coerce=int) # Se popula en la ruta

    submit = SubmitField('Guardar Cambios Miembro')

    # Guardamos datos originales para validación de unicidad
    def __init__(self, original_email, original_dni, *args, **kwargs):
        super(EditMemberForm, self).__init__(*args, **kwargs)
        self.original_email = original_email
        self.original_dni = original_dni

    def validate_email(self, email):
        if email.data and email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('Ese correo electrónico ya está en uso.')

    def validate_dni(self, dni):
         if dni.data and dni.data != self.original_dni:
            user = User.query.filter_by(dni=dni.data).first()
            if user:
                raise ValidationError('Ese DNI ya está registrado.')

# --- Formulario Iglesia ---
class ChurchForm(FlaskForm):
    name = StringField('Nombre de la Iglesia', validators=[DataRequired(), Length(max=255)])
    address = StringField('Dirección', validators=[Optional(), Length(max=255)]) # Dirección puede ser opcional?
    city = StringField('Ciudad', validators=[Optional(), Length(max=255)])        # <-- AÑADIDO
    province = StringField('Provincia', validators=[Optional(), Length(max=255)])  # <-- AÑADIDO
    district = StringField('Distrito', validators=[Optional(), Length(max=255)])
    pastor = SelectField('Pastor Gobernante', coerce=int, validators=[Optional()]) # Se popula en la ruta
    latitude = FloatField('Latitud', validators=[Optional(), NumberRange(min=-90, max=90)])
    longitude = FloatField('Longitud', validators=[Optional(), NumberRange(min=-180, max=180)])
    # tipo no se edita por formulario usualmente
    submit = SubmitField('Guardar Iglesia')

# --- Formulario Pastor (para datos específicos en tabla 'pastores') ---
# Este formulario NO edita los datos del User (nombre, email, etc.)
# Solo los datos de la tabla Pastor (grado, matricula, dirección casa pastoral si es diferente)
class PastorForm(FlaskForm):
    # Choices actualizadas para consistencia
    grado = SelectField('Grado Pastoral', choices=[
        ('', '-- Seleccionar Grado --'),
        ('Probando', 'Probando'),
        ('Diacono', 'Diacono'),
        ('Presbistero', 'Presbistero')
    ], validators=[Optional()]) # Optional si puede no tener grado aún
    matricula = StringField('Matrícula', validators=[Optional(), Length(max=50)])
    # Dirección específica de la casa pastoral (si es distinta a la personal del User)
    address = StringField('Dirección Casa Pastoral (opcional, si difiere)', validators=[Optional(), Length(max=255)])
    latitude = FloatField('Latitud (Casa Pastoral)', validators=[Optional(), NumberRange(min=-90, max=90)])
    longitude = FloatField('Longitud (Casa Pastoral)', validators=[Optional(), NumberRange(min=-180, max=180)])
    # Otros campos de Pastor como fechas, foto, etc., podrían añadirse aquí si se editan vía form
    # fecha_promocion = DateField(...)
    # fecha_graduacion = DateField(...)
    # foto_pastor = FileField(...) # O StringField para el path si se gestiona aparte

    submit = SubmitField('Guardar Datos Pastor')

# --- Formulario Login ---
class LoginForm(FlaskForm):
    username = StringField('Usuario', validators=[DataRequired(), Length(min=2, max=20)])
    password = PasswordField('Contraseña', validators=[DataRequired()])
    remember = BooleanField('Recordarme')
    submit = SubmitField('Iniciar Sesión')

# --- Formulario Relaciones Familiares ---
class FamilyRelationshipForm(FlaskForm):
    user1 = SelectField('Usuario 1', coerce=int, validators=[DataRequired()]) # Se popula en la ruta
    user2 = SelectField('Usuario 2', coerce=int, validators=[DataRequired()]) # Se popula en la ruta
    relationship_type = SelectField('Tipo de Relación (Usuario 1 es ... de Usuario 2)', choices=[
        ('Esposo', 'Esposo'), ('Esposa', 'Esposa'),
        ('Padre', 'Padre'), ('Madre', 'Madre'),
        ('Hijo', 'Hijo'), ('Hija', 'Hija'),
        ('Hermano', 'Hermano'), ('Hermana', 'Hermana'),
        ('Abuelo', 'Abuelo'), ('Abuela', 'Abuela'),
        ('Nieto', 'Nieto'), ('Nieta', 'Nieta'),
        ('Tio', 'Tio'), ('Tia', 'Tia'),
        ('Sobrino', 'Sobrino'), ('Sobrina', 'Sobrina'),
        ('Cuñado', 'Cuñado'), ('Cuñada', 'Cuñada'),
        ('Primo', 'Primo'), ('Prima', 'Prima'),
        ('Yerno', 'Yerno'), ('Nuera', 'Nuera'),
        ('Suegro', 'Suegro'), ('Suegra', 'Suegra'), # Añadido
        ('Concuñado', 'Concuñado'), ('Concuñada', 'Concuñada')
    ], validators=[DataRequired()])
    submit = SubmitField('Agregar/Actualizar Relación')

    def validate(self, extra_validators=None):
        # Llama a la validación base primero
        if not super(FamilyRelationshipForm, self).validate(extra_validators):
            return False
        # Validación personalizada
        if self.user1.data == self.user2.data:
            # Adjuntar error al campo específico si es posible
            self.user2.errors.append('No puedes relacionar un usuario consigo mismo.')
            # O usar un error general si no se puede asociar a un campo
            # raise ValidationError('No puedes relacionar un usuario consigo mismo.')
            return False
        return True

# --- Formulario Inventario Iglesia ---
class InventoryItemForm(FlaskForm):
    item_name = StringField('Nombre del Objeto', validators=[DataRequired(), Length(max=255)])
    item_type = StringField('Tipo de Objeto', validators=[Optional(), Length(max=100)])
    quantity = IntegerField('Cantidad', validators=[DataRequired(), NumberRange(min=0)], default=1) # Asegurar no negativo
    description = TextAreaField('Descripción', validators=[Optional()])
    purchase_date = DateField('Fecha de Compra (YYYY-MM-DD)', format='%Y-%m-%d', validators=[Optional()])
    purchase_price = DecimalField('Precio de Compra', places=2, validators=[Optional(), NumberRange(min=0)]) # Usar DecimalField para dinero
    current_value = DecimalField('Valor Actual', places=2, validators=[Optional(), NumberRange(min=0)]) # Usar DecimalField
    serial_number = StringField('Número de Serie', validators=[Optional(), Length(max=255)])
    notes = TextAreaField('Notas', validators=[Optional()])
    submit = SubmitField('Guardar Elemento')

# --- Formulario Mensajes ---
class MessageForm(FlaskForm):
    receiver = SelectField('Enviar a', coerce=int, validators=[DataRequired()]) # Se popula en la ruta
    subject = StringField('Asunto', validators=[Optional(), Length(max=255)])
    message_text = TextAreaField('Mensaje', validators=[DataRequired(), Length(min=1)])
    submit = SubmitField('Enviar Mensaje')

# --- Formulario Reseñas ---
class UserReviewForm(FlaskForm):
    review_text = TextAreaField('Texto de la Reseña/Histórico', validators=[DataRequired(), Length(min=1)])
    submit = SubmitField('Guardar Reseña')

# --- Formulario Selección Usuario para Reseña ---
class SelectReviewUserForm(FlaskForm):
    user = SelectField('Selecciona el usuario a revisar', coerce=int, validators=[DataRequired()]) # Se popula en la ruta
    submit = SubmitField('Continuar')

# --- Formulario Eventos Calendario ---
class CalendarEventForm(FlaskForm):
    title = StringField('Título', validators=[DataRequired(), Length(max=255)])
    description = TextAreaField('Descripción', validators=[Optional()])
    event_date = DateField('Fecha del Evento (YYYY-MM-DD)', format='%Y-%m-%d', validators=[DataRequired()])
    submit = SubmitField('Guardar Evento')

# --- Formulario Documentos ---
class DocumentForm(FlaskForm):
    title = StringField('Título', validators=[DataRequired(), Length(max=255)])
    description = TextAreaField('Descripción', validators=[Optional()])
    file = FileField('Archivo', validators=[DataRequired()]) # Validar extensión/tamaño en la ruta
    category = SelectField('Categoría', choices=[
        ('', '-- Seleccionar --'),
        ('acta', 'Acta'),
        ('informe_financiero', 'Informe Financiero'),
        ('circular', 'Circular'),
        ('informe_miembros', 'Informe de Miembros'),
        ('diploma', 'Diploma'),
        ('otro', 'Otro') # Añadido por si acaso
    ], validators=[DataRequired()])
    topic = StringField('Tópico/Referencia', validators=[Optional(), Length(max=255)])
    submit = SubmitField('Subir Documento')

# --- Formulario Acta ---
class ActaForm(FlaskForm):
    acta_number = StringField('Acta Nº', validators=[DataRequired(), Length(max=50)])
    city = StringField('Ciudad donde se realiza', validators=[DataRequired(), Length(max=100)])
    province = StringField('Provincia donde se realiza', validators=[DataRequired(), Length(max=100)])
    day = IntegerField('Día', validators=[DataRequired(), NumberRange(min=1, max=31)]) # Usar IntegerField
    month = SelectField('Mes', choices=[ # Usar SelectField para meses
        ('Enero','Enero'), ('Febrero','Febrero'), ('Marzo','Marzo'), ('Abril','Abril'),
        ('Mayo','Mayo'), ('Junio','Junio'), ('Julio','Julio'), ('Agosto','Agosto'),
        ('Septiembre','Septiembre'), ('Octubre','Octubre'), ('Noviembre','Noviembre'), ('Diciembre','Diciembre')
    ], validators=[DataRequired()])
    year = IntegerField('Año', validators=[DataRequired(), NumberRange(min=1900, max=datetime.date.today().year + 5)]) # Usar IntegerField
    time = StringField('Hora (HH:MM)', validators=[DataRequired(), Length(max=5)]) # Validar formato HH:MM si es necesario
    participants = MultiCheckboxField('Participantes', coerce=int) # Se popula en la ruta
    act_description = TextAreaField('Descripción del Acta/Resolución', validators=[DataRequired()])
    submit = SubmitField('Generar Vista Previa Acta')

# --- Formulario Transacciones Económicas ---
class TransactionForm(FlaskForm):
    amount = DecimalField('Monto', places=2, validators=[DataRequired(), NumberRange(min=0, message="El monto debe ser positivo.")]) # Asegurar positivo
    transaction_type = SelectField('Tipo', choices=[('ingreso', 'Ingreso'), ('egreso', 'Egreso')], validators=[DataRequired()])
    category = SelectField('Categoría', choices=[
         ('', '-- Seleccionar --'),
        ('diezmo', 'Diezmo'), ('ofrenda', 'Ofrenda'), ('donacion', 'Donación'),
        ('gasto', 'Gasto'), ('otro', 'Otro')
    ], validators=[DataRequired()])
    member_id = SelectField('Miembro asociado (opcional)', coerce=int, validators=[Optional()]) # Se popula en __init__
    description = StringField('Descripción breve', validators=[Optional(), Length(max=255)])
    transaction_date = DateField('Fecha de la Transacción (YYYY-MM-DD)', format='%Y-%m-%d', validators=[DataRequired()], default=datetime.date.today) # Default a hoy
    notes = TextAreaField('Notas Adicionales', validators=[Optional()])
    submit = SubmitField('Registrar Transacción')

    def __init__(self, *args, church_id=None, **kwargs):
        super().__init__(*args, **kwargs)
        # Poblar miembros de la iglesia específica
        member_choices = [(0, 'Ninguno/Anónimo')] # Opción por defecto/no aplicable
        if church_id:
            from sqlalchemy import func # Importar func aquí
            members = db.session.query(
                Member.id,
                # Usar User.first_name y User.last_name
                func.concat(User.first_name, ' ', User.last_name).label("full_name")
            ).join(User, Member.user_id == User.id)\
             .filter(Member.church_id == church_id)\
             .order_by(User.last_name, User.first_name).all() # Ordenar por apellido, nombre
            member_choices.extend([(m.id, m.full_name) for m in members])
        self.member_id.choices = member_choices

# --- Formulario para seleccionar Pastor para Credencial ---
class SelectPastorCredencialForm(FlaskForm):
    pastor_user = SelectField('Selecciona el Pastor', coerce=int, validators=[DataRequired()]) # Se popula en __init__
    submit = SubmitField('Generar Credencial')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Poblar el select solo con usuarios que tengan rol 'pastorado' y un registro Pastor asociado
        pastores_users = db.session.query(User).join(Pastor, User.id == Pastor.user_id)\
                         .filter(User.role == 'pastorado')\
                         .order_by(User.last_name, User.first_name).all()
        self.pastor_user.choices = [(u.id, f"{u.last_name}, {u.first_name}") for u in pastores_users]

        # --- Formulario para que el usuario edite su PROPIO perfil ---
class ProfileEditForm(FlaskForm):
    # Campos del User
    first_name = StringField('Nombre', validators=[DataRequired(), Length(max=255)])
    last_name = StringField('Apellido', validators=[DataRequired(), Length(max=255)])
    # email = StringField('Correo Electrónico', render_kw={'readonly': True}) # Mostrar pero no editar
    # dni = StringField('DNI', render_kw={'readonly': True}) # Mostrar pero no editar
    phone_number = StringField('Teléfono', validators=[Optional(), Length(max=255)])
    address = StringField('Dirección Personal', validators=[Optional(), Length(max=255)])
    ciudad = StringField('Ciudad Personal', validators=[Optional(), Length(max=255)])
    estado_civil = StringField('Estado Civil', validators=[Optional(), Length(max=50)])
    date_of_birth = DateField('Fecha de Nacimiento (YYYY-MM-DD)', format='%Y-%m-%d', validators=[Optional()])

    # Campos del Member (solo se mostrarán/procesarán si el usuario es miembro)
    alergies = StringField('Alergias', validators=[Optional(), Length(max=255)])
    emergency_contact = StringField('Contacto de Emergencia', validators=[Optional(), Length(max=255)])

    # Opcional: Cambio de contraseña
    password = PasswordField('Nueva Contraseña (dejar vacío para no cambiar)')
    confirm_password = PasswordField('Confirmar Nueva Contraseña', validators=[EqualTo('password', message='Las contraseñas deben coincidir.')])

    submit = SubmitField('Actualizar Mi Perfil')

    # No necesitamos __init__ complejo si no validamos unicidad de email/dni aquí
    # def __init__(self, original_email, original_dni, *args, **kwargs):
    #    super().__init__(*args, **kwargs)
    #    self.original_email = original_email
    #    self.original_dni = original_dni
    # Podríamos añadir validaciones específicas si fueran necesarias
    
    
    # --- Formulario para Crear/Editar Anuncios ---
class AnnouncementForm(FlaskForm):
    title = StringField('Título del Anuncio', validators=[DataRequired(), Length(max=255)])
    content = TextAreaField('Contenido del Anuncio', validators=[DataRequired()])
    visibility = SelectField('Visible para:', choices=[
        ('todos', 'Todos los Usuarios'),
        ('administradores', 'Solo Administradores'),
        ('secretaria', 'Solo Secretaria'), # Podrías combinar Admin/Sec
        ('pastorado', 'Solo Pastorado (Todos)'),
        ('miembros', 'Solo Miembros (Todos)'),
        ('iglesia_especifica', 'Solo una Iglesia Específica')
    ], validators=[DataRequired()])
    # Campo para seleccionar iglesia, se muestra/oculta con JS
    target_church = SelectField('Iglesia Específica', coerce=int, validators=[Optional()])

    submit = SubmitField('Publicar Anuncio')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Poblar iglesias para selección
        self.target_church.choices = [(0, '-- Seleccionar Iglesia --')] + \
                                     [(c.id, c.name) for c in Church.query.order_by('name').all()]

    def validate(self, extra_validators=None):
        # Validación base
        if not super().validate(extra_validators):
            return False
        # Validación personalizada: si visibilidad es 'iglesia_especifica', target_church debe ser > 0
        if self.visibility.data == 'iglesia_especifica' and (not self.target_church.data or self.target_church.data == 0):
            self.target_church.errors.append('Debes seleccionar una iglesia específica.')
            return False
        return True
    
class AsignarSuperintendenteForm(FlaskForm):
    superintendente_id = SelectField('Superintendente (Pastor que supervisa)', coerce=int, validators=[DataRequired()])
    jefe_sector_id = SelectField('Jefe de Sector (Pastor)', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Asignar')

class JerarquiaPastorForm(FlaskForm):
    superintendente_id = SelectField('Pastor Superintendente', coerce=int)
    pastor_comun_id = SelectField('Pastor Asignado', coerce=int)
    submit_pastor = SubmitField('Asignar Pastor a Superintendente')

class JerarquiaSuperintForm(FlaskForm):
    jefe_sector_id = SelectField('Pastor Jefe de Sector', coerce=int)
    superintendente_sec_id = SelectField('Superintendente Asignado', coerce=int)
    submit_superintendente = SubmitField('Asignar Superintendente a Jefe de Sector')

class RequestMemberTransferForm(FlaskForm):
    target_church = SelectField('Transferir a Iglesia', coerce=int, validators=[DataRequired()])
    request_notes = TextAreaField('Notas (Opcional)', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Solicitar Transferencia')

    def __init__(self, current_church_id=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Poblar con TODAS las iglesias EXCEPTO la actual
        churches = Church.query.filter(Church.id != current_church_id).order_by(Church.name).all()
        self.target_church.choices = [(c.id, f"{c.name} (Pastor: {c.pastor.full_name if c.pastor else 'N/A'})") for c in churches]
        self.target_church.choices.insert(0, (0, '-- Selecciona Iglesia Destino --'))

    def validate_target_church(self, field):
        if field.data == 0:
            raise ValidationError('Debes seleccionar una iglesia destino.')
        target = db.session.get(Church, field.data)
        if not target:
             raise ValidationError('La iglesia seleccionada ya no existe.')
        # Validar que la iglesia destino tenga un pastor asignado para aprobar
        if not target.pastor_id:
             raise ValidationError('La iglesia destino seleccionada no tiene un pastor asignado para aprobar la transferencia.')

class ApproveRejectTransferForm(FlaskForm):
    approval_notes = TextAreaField('Notas (Opcional)', validators=[Optional(), Length(max=500)])
    submit_approve = SubmitField('Aprobar Transferencia')
    submit_reject = SubmitField('Rechazar Transferencia')
    # No se necesita campo de selección, la decisión se toma por el botón presionado            