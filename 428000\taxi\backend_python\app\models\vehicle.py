import enum # Asegúrate de importar enum de Python
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Enum as SQLAlchemyEnum
# from geoalchemy2 import Geometry # Para PostGIS
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base
from app.models.user import User # Importar User para relaciones

# Definir el Enum de Python NORMALMENTE
class VehicleStatus(str, enum.Enum): # Estado operativo del vehículo (seleccionado por el taxista)
    LIBRE = "libre"
    OCUPADO = "ocupado"
    ALERTA = "alerta"
    EMERGENCIA = "emergencia"
    FUERA_DE_SERVICIO = "fuera_de_servicio"

# Categoría administrativa del vehículo
class VehicleCategory(str, enum.Enum):
    ACTIVO = "activo"
    EN_MANTENIMIENTO = "en_mantenimiento"
    INACTIVO = "inactivo"

class Vehicle(Base):
    id = Column(Integer, primary_key=True, index=True)
    plate_number = Column(String, unique=True, index=True, nullable=False)
    brand = Column(String)
    model = Column(String)
    year = Column(Integer)
    color = Column(String)
    # is_active = Column(Boolean, default=True) # Eliminamos esta columna ya que no existe en la base de datos

    # Estado operativo del vehículo (seleccionado por el taxista)
    status = Column(SQLAlchemyEnum(VehicleStatus), default=VehicleStatus.FUERA_DE_SERVICIO)

    # Categoría administrativa del vehículo (asignada por el administrador)
    category = Column(SQLAlchemyEnum(VehicleCategory), default=VehicleCategory.ACTIVO)

    # current_location = Column(Geometry('POINT', srid=4326), nullable=True) # Para PostGIS
    last_latitude = Column(String, nullable=True)
    last_longitude = Column(String, nullable=True)
    last_location_update = Column(DateTime(timezone=True), nullable=True)

    owner_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    owner = relationship("User", back_populates="vehicles_owned", foreign_keys=[owner_id])

    driver_id = Column(Integer, ForeignKey('users.id'), nullable=True, unique=True)
    current_driver = relationship("User", back_populates="driven_vehicle", foreign_keys=[driver_id])

    base_station_id = Column(Integer, ForeignKey('basestations.id'), nullable=True)
    base_station = relationship("BaseStation", back_populates="vehicles")

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())