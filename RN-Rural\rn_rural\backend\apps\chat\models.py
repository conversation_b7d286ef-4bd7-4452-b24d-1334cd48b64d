# File: backend/apps/chat/models.py
# -----------------------------------------------

from django.db import models
from django.conf import settings
from apps.incidents.models import Incidencia

class MensajeChat(models.Model):
    """
    Modelo para almacenar mensajes de chat relacionados con una incidencia.
    """
    TIPO_CHOICES = [
        ('TEXTO', 'Mensaje de texto'),
        ('AUDIO', 'Mensaje de audio'),
        ('IMAGEN', 'Mensaje con imagen'),
        ('UBICACION', 'Mensaje con ubicación'),
        ('SISTEMA', 'Mensaje del sistema'),
    ]

    incidencia = models.ForeignKey(Incidencia, on_delete=models.CASCADE, related_name="mensajes_chat")
    remitente = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="mensajes_enviados")
    texto = models.TextField(blank=True, null=True)
    audio_url = models.URLField(max_length=500, blank=True, null=True)
    imagen_url = models.URLField(max_length=500, blank=True, null=True)
    ubicacion_lat = models.FloatField(null=True, blank=True)
    ubicacion_lng = models.FloatField(null=True, blank=True)
    tipo = models.CharField(max_length=10, choices=TIPO_CHOICES, default='TEXTO')
    fecha_envio = models.DateTimeField(auto_now_add=True)
    leido = models.BooleanField(default=False)

    # Campo para indicar si el mensaje ha sido exportado a la brigada
    exportado_brigada = models.BooleanField(default=False)

    def __str__(self):
        if self.tipo == 'TEXTO':
            return f"Mensaje de {self.remitente.username} en Incidencia #{self.incidencia.id}: {self.texto[:30]}..." if self.texto else "Mensaje vacío"
        elif self.tipo == 'AUDIO':
            return f"Audio de {self.remitente.username} en Incidencia #{self.incidencia.id}"
        elif self.tipo == 'IMAGEN':
            return f"Imagen de {self.remitente.username} en Incidencia #{self.incidencia.id}"
        elif self.tipo == 'UBICACION':
            return f"Ubicación de {self.remitente.username} en Incidencia #{self.incidencia.id}"
        else:
            return f"Mensaje del sistema en Incidencia #{self.incidencia.id}: {self.texto[:30]}..." if self.texto else "Mensaje del sistema"

    class Meta:
        ordering = ['fecha_envio']
        verbose_name = "Mensaje de chat"
        verbose_name_plural = "Mensajes de chat"
