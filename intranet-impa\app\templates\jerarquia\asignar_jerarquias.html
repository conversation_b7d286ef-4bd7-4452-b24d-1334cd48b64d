{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON><PERSON><PERSON> entre Pastores{% endblock %}

{% block content %}
<div class="container my-5">
  <h2 class="text-center text-secondary mb-4"><PERSON><PERSON><PERSON><PERSON><PERSON> entre Pastores</h2>

  <!-- Bloque: Designar Jefe de Sector a Superintendentes -->
  <div class="card mb-5 border-info shadow-sm">
    <div class="card-header bg-info text-white">
      <strong>Designar Jefe de Sector → Superintendentes</strong>
    </div>
    <div class="card-body">
      <form method="POST">
        {{ form_superint.hidden_tag() }}
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              {{ form_superint.jefe_sector_id.label(class="form-label") }}
              {{ form_superint.jefe_sector_id(class="form-control") }}
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              {{ form_superint.superintendente_sec_id.label(class="form-label") }}
              {{ form_superint.superintendente_sec_id(class="form-control") }}
            </div>
          </div>
        </div>
        {{ form_superint.submit_superintendente(class="btn btn-info mt-3") }}
      </form>
    </div>
  </div>

  <!-- Bloque: Designar Superintendente a Pastores -->
  <div class="card mb-5 border-primary shadow-sm">
    <div class="card-header bg-primary text-white">
      <strong>Designar Superintendente → Pastores</strong>
    </div>
    <div class="card-body">
      <form method="POST">
        {{ form_pastor.hidden_tag() }}
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              {{ form_pastor.superintendente_id.label(class="form-label") }}
              {{ form_pastor.superintendente_id(class="form-control") }}
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              {{ form_pastor.pastor_comun_id.label(class="form-label") }}
              {{ form_pastor.pastor_comun_id(class="form-control") }}
            </div>
          </div>
        </div>
        {{ form_pastor.submit_pastor(class="btn btn-primary mt-3") }}
      </form>
    </div>
  </div>

  <!-- Relaciones actuales: Jefe de Sector → Superintendentes -->
  <div class="mt-5">
    <h3>Relaciones Actuales: Jefe de Sector → Superintendentes</h3>
    {% if jerarquias_superintendentes %}
      <table class="table table-bordered table-striped">
        <thead class="thead-light">
          <tr>
            <th>Jefe de Sector</th>
            <th>Superintendente Asignado</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          {% for relacion in jerarquias_superintendentes %}
            <tr>
              <td>{{ relacion.jefe_sector.full_name }}</td>
              <td>{{ relacion.superintendente.full_name }}</td>
              <td>
                <form method="POST" action="{{ url_for('routes.eliminar_jerarquia_superintendente', id=relacion.id) }}">
                  <button type="submit" class="btn btn-sm btn-danger"
                          onclick="return confirm('¿Eliminar esta relación?')">Eliminar</button>
                </form>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <p class="text-muted">No hay relaciones registradas aún.</p>
    {% endif %}
  </div>

  <!-- Relaciones actuales: Superintendente → Pastores -->
  <div class="mt-5">
    <h3>Relaciones Actuales: Superintendente → Pastores</h3>
    {% if jerarquias_pastores %}
      <table class="table table-bordered table-striped">
        <thead class="thead-light">
          <tr>
            <th>Superintendente</th>
            <th>Pastor Asignado</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          {% for relacion in jerarquias_pastores %}
            <tr>
              <td>{{ relacion.supervisor.full_name }}</td>
              <td>{{ relacion.pastor.full_name }}</td>
              <td>
                <form method="POST" action="{{ url_for('routes.eliminar_jerarquia_pastor', id=relacion.id) }}">
                  <button type="submit" class="btn btn-sm btn-danger"
                          onclick="return confirm('¿Eliminar esta relación?')">Eliminar</button>
                </form>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <p class="text-muted">No hay relaciones registradas aún.</p>
    {% endif %}
  </div>
</div>
{% endblock %}
