{% extends "base_layout_passenger.html" %}

{% block title %}Panel de Pasajero - Taxis{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .trip-card {
        border-left: 4px solid #6f42c1;
        margin-bottom: 10px;
        transition: all 0.3s;
    }
    .trip-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .trip-card.active {
        border-left-color: #0d6efd;
    }
    .trip-card.completed {
        border-left-color: #198754;
    }
    .trip-card.cancelled {
        border-left-color: #dc3545;
    }
    .search-box {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Panel de Pasajero</h1>
        <p class="lead">Bienvenido al panel de pasajero del sistema de taxis.</p>
    </div>
</div>

<!-- Solicitar Viaje -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Solicitar un Taxi</h5>
            </div>
            <div class="card-body">
                <div class="search-box">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group mb-3">
                                <label for="origin" class="form-label">Origen</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                    <input type="text" class="form-control" id="origin" placeholder="Dirección de origen">
                                    <button class="btn btn-outline-secondary" type="button" onclick="useCurrentLocation()">
                                        <i class="bi bi-cursor"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-group mb-3">
                                <label for="destination" class="form-label">Destino</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-geo-alt-fill"></i></span>
                                    <input type="text" class="form-control" id="destination" placeholder="Dirección de destino">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-primary w-100" onclick="searchRoute()">
                                <i class="bi bi-search me-2"></i> Buscar
                            </button>
                        </div>
                    </div>
                </div>
                <div id="map"></div>
                <div id="route-info" class="mt-3" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Distancia</h6>
                                    <p class="card-text" id="route-distance">0 km</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Tiempo Estimado</h6>
                                    <p class="card-text" id="route-time">0 min</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Tarifa Estimada</h6>
                                    <p class="card-text" id="route-fare">$0</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-grid gap-2 mt-3">
                        <button class="btn btn-success btn-lg" onclick="requestTrip()">
                            <i class="bi bi-taxi-front me-2"></i> Solicitar Taxi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Viaje Actual y Estadísticas -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Viaje Actual</h5>
            </div>
            <div class="card-body">
                {% if current_trip %}
                <div class="trip-card active">
                    <div class="card-body">
                        <h5 class="card-title">Viaje #{{ current_trip.id }}</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p>
                                    <strong>Estado:</strong> <span class="badge bg-primary">{{ current_trip.status }}</span><br>
                                    <strong>Conductor:</strong> {{ current_trip.driver_name|default('Buscando conductor...') }}<br>
                                    <strong>Vehículo:</strong> {{ current_trip.vehicle_info|default('') }}<br>
                                    <strong>Origen:</strong> {{ current_trip.origin_address }}<br>
                                    <strong>Destino:</strong> {{ current_trip.destination_address }}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p>
                                    <strong>Tarifa Estimada:</strong> ${{ current_trip.estimated_fare }}<br>
                                    <strong>Distancia Estimada:</strong> {{ current_trip.estimated_distance_km|default('0') }} km<br>
                                    <strong>Tiempo Estimado:</strong> {{ current_trip.estimated_duration_min|default('0') }} min<br>
                                    <strong>Solicitado:</strong> {{ current_trip.requested_at|default('') }}
                                </p>
                                <div class="d-grid gap-2">
                                    <a href="{{ url_for('passenger_trip_details_route', trip_id=current_trip.id) }}" class="btn btn-primary">
                                        <i class="bi bi-eye me-2"></i> Ver Detalles
                                    </a>
                                    <button class="btn btn-danger" onclick="cancelTrip('{{ current_trip.id }}')">
                                        <i class="bi bi-x-circle me-2"></i> Cancelar Viaje
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-car-front display-1 text-muted"></i>
                    <p class="mt-3">No tienes un viaje activo actualmente.</p>
                    <p class="text-muted">Solicita un taxi usando el formulario de arriba.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mis Estadísticas</h5>
            </div>
            <div class="card-body text-center">
                <div class="row">
                    <div class="col-6">
                        <i class="bi bi-geo-alt card-icon text-primary"></i>
                        <div class="stats-value">{{ stats.total_trips|default(0) }}</div>
                        <div class="stats-label">Viajes Totales</div>
                    </div>
                    <div class="col-6">
                        <i class="bi bi-cash-coin card-icon text-success"></i>
                        <div class="stats-value">${{ stats.total_spent|default(0) }}</div>
                        <div class="stats-label">Total Gastado</div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-6">
                        <i class="bi bi-star card-icon text-warning"></i>
                        <div class="stats-value">{{ stats.avg_rating|default('0.0') }}</div>
                        <div class="stats-label">Calificación</div>
                    </div>
                    <div class="col-6">
                        <i class="bi bi-clock card-icon text-info"></i>
                        <div class="stats-value">{{ stats.avg_wait_time|default('0') }}</div>
                        <div class="stats-label">Tiempo de Espera</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Últimos Viajes -->
<div class="row">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mis Últimos Viajes</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Fecha</th>
                                <th>Origen</th>
                                <th>Destino</th>
                                <th>Conductor</th>
                                <th>Estado</th>
                                <th>Tarifa</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for trip in recent_trips|default([]) %}
                            <tr>
                                <td>{{ trip.id }}</td>
                                <td>{{ trip.requested_at|default('') }}</td>
                                <td>{{ trip.origin_address|truncate(15) }}</td>
                                <td>{{ trip.destination_address|truncate(15) }}</td>
                                <td>{{ trip.driver_name|default('Sin asignar') }}</td>
                                <td>
                                    <span class="badge {% if trip.status == 'COMPLETADO' %}bg-success{% elif trip.status == 'CANCELADO' %}bg-danger{% else %}bg-primary{% endif %}">
                                        {{ trip.status }}
                                    </span>
                                </td>
                                <td>${{ trip.actual_fare|default(trip.estimated_fare) }}</td>
                                <td>
                                    <a href="{{ url_for('passenger_trip_details_route', trip_id=trip.id) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center">No hay viajes recientes</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 13); // Buenos Aires como ejemplo
    let originMarker = null;
    let destinationMarker = null;
    let routeLine = null;
    let originCoords = null;
    let destinationCoords = null;

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Función para usar la ubicación actual
    function useCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(position => {
                const latitude = position.coords.latitude;
                const longitude = position.coords.longitude;
                
                // Actualizar el campo de origen
                document.getElementById('origin').value = `${latitude}, ${longitude}`;
                
                // Actualizar el mapa
                if (originMarker) {
                    map.removeLayer(originMarker);
                }
                
                originMarker = L.marker([latitude, longitude]).addTo(map);
                originMarker.bindPopup("Tu ubicación actual").openPopup();
                
                map.setView([latitude, longitude], 15);
                
                // Guardar coordenadas
                originCoords = { lat: latitude, lng: longitude };
                
                // Geocodificar para obtener la dirección
                fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.display_name) {
                            document.getElementById('origin').value = data.display_name;
                        }
                    })
                    .catch(error => console.error('Error al geocodificar:', error));
            }, error => {
                console.error('Error al obtener ubicación:', error);
                alert('No se pudo obtener tu ubicación actual.');
            });
        } else {
            alert('Tu navegador no soporta geolocalización.');
        }
    }

    // Función para buscar ruta
    function searchRoute() {
        const origin = document.getElementById('origin').value;
        const destination = document.getElementById('destination').value;
        
        if (!origin || !destination) {
            alert('Por favor ingresa origen y destino.');
            return;
        }
        
        // Geocodificar origen si no tenemos coordenadas
        if (!originCoords) {
            fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(origin)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.length > 0) {
                        originCoords = { lat: parseFloat(data[0].lat), lng: parseFloat(data[0].lon) };
                        geocodeDestination();
                    } else {
                        alert('No se pudo encontrar la dirección de origen.');
                    }
                })
                .catch(error => {
                    console.error('Error al geocodificar origen:', error);
                    alert('Error al buscar la dirección de origen.');
                });
        } else {
            geocodeDestination();
        }
        
        function geocodeDestination() {
            fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(destination)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.length > 0) {
                        destinationCoords = { lat: parseFloat(data[0].lat), lng: parseFloat(data[0].lon) };
                        showRoute();
                    } else {
                        alert('No se pudo encontrar la dirección de destino.');
                    }
                })
                .catch(error => {
                    console.error('Error al geocodificar destino:', error);
                    alert('Error al buscar la dirección de destino.');
                });
        }
        
        function showRoute() {
            // Limpiar marcadores y ruta anterior
            if (originMarker) map.removeLayer(originMarker);
            if (destinationMarker) map.removeLayer(destinationMarker);
            if (routeLine) map.removeLayer(routeLine);
            
            // Añadir marcadores
            originMarker = L.marker([originCoords.lat, originCoords.lng]).addTo(map);
            originMarker.bindPopup("Origen: " + origin).openPopup();
            
            destinationMarker = L.marker([destinationCoords.lat, destinationCoords.lng]).addTo(map);
            destinationMarker.bindPopup("Destino: " + destination);
            
            // Ajustar vista para mostrar ambos marcadores
            const bounds = L.latLngBounds([
                [originCoords.lat, originCoords.lng],
                [destinationCoords.lat, destinationCoords.lng]
            ]);
            map.fitBounds(bounds, { padding: [50, 50] });
            
            // Dibujar línea recta (simplificado)
            routeLine = L.polyline([
                [originCoords.lat, originCoords.lng],
                [destinationCoords.lat, destinationCoords.lng]
            ], { color: 'blue' }).addTo(map);
            
            // Calcular distancia (en línea recta)
            const distance = map.distance(
                [originCoords.lat, originCoords.lng],
                [destinationCoords.lat, destinationCoords.lng]
            ) / 1000; // convertir a km
            
            // Estimar tiempo (asumiendo 30 km/h promedio)
            const timeMinutes = Math.round((distance / 30) * 60);
            
            // Estimar tarifa (base + por km)
            const baseFare = 300;
            const perKmFare = 50;
            const fare = Math.round(baseFare + (distance * perKmFare));
            
            // Mostrar información
            document.getElementById('route-distance').textContent = distance.toFixed(1) + ' km';
            document.getElementById('route-time').textContent = timeMinutes + ' min';
            document.getElementById('route-fare').textContent = '$' + fare;
            
            // Mostrar sección de información
            document.getElementById('route-info').style.display = 'block';
        }
    }

    // Función para solicitar un viaje
    async function requestTrip() {
        if (!originCoords || !destinationCoords) {
            alert('Por favor busca una ruta primero.');
            return;
        }
        
        try {
            const response = await fetchWithAuth('/api/v1/trips', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    origin_latitude: originCoords.lat,
                    origin_longitude: originCoords.lng,
                    origin_address: document.getElementById('origin').value,
                    destination_latitude: destinationCoords.lat,
                    destination_longitude: destinationCoords.lng,
                    destination_address: document.getElementById('destination').value
                })
            });
            
            if (response.ok) {
                const data = await response.json();
                alert('¡Viaje solicitado con éxito! Buscando conductor...');
                window.location.reload();
            } else {
                const error = await response.json();
                alert('Error al solicitar viaje: ' + (error.detail || 'Error desconocido'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error de red al solicitar viaje');
        }
    }

    // Función para cancelar un viaje
    async function cancelTrip(tripId) {
        if (confirm('¿Estás seguro de que deseas cancelar este viaje?')) {
            try {
                const response = await fetchWithAuth(`/api/v1/trips/${tripId}/cancel`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    alert('Viaje cancelado con éxito.');
                    window.location.reload();
                } else {
                    const error = await response.json();
                    alert('Error al cancelar viaje: ' + (error.detail || 'Error desconocido'));
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error de red al cancelar viaje');
            }
        }
    }

    // Cargar mapa al iniciar
    document.addEventListener('DOMContentLoaded', function() {
        // Si hay un viaje actual, mostrar en el mapa
        {% if current_trip and current_trip.origin_latitude and current_trip.origin_longitude %}
        const originLatLng = [{{ current_trip.origin_latitude }}, {{ current_trip.origin_longitude }}];
        const destLatLng = [{{ current_trip.destination_latitude }}, {{ current_trip.destination_longitude }}];
        
        originMarker = L.marker(originLatLng).addTo(map);
        originMarker.bindPopup("Origen: {{ current_trip.origin_address }}").openPopup();
        
        destinationMarker = L.marker(destLatLng).addTo(map);
        destinationMarker.bindPopup("Destino: {{ current_trip.destination_address }}");
        
        routeLine = L.polyline([originLatLng, destLatLng], { color: 'blue' }).addTo(map);
        
        const bounds = L.latLngBounds([originLatLng, destLatLng]);
        map.fitBounds(bounds, { padding: [50, 50] });
        
        // Si hay un conductor asignado, mostrar su ubicación
        {% if current_trip.driver_latitude and current_trip.driver_longitude %}
        const driverLatLng = [{{ current_trip.driver_latitude }}, {{ current_trip.driver_longitude }}];
        const driverMarker = L.marker(driverLatLng, {
            icon: L.icon({
                iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            })
        }).addTo(map);
        driverMarker.bindPopup("Conductor: {{ current_trip.driver_name }}");
        {% endif %}
        {% else %}
        // Usar ubicación actual si está disponible
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(position => {
                map.setView([position.coords.latitude, position.coords.longitude], 15);
            }, error => {
                console.error('Error al obtener ubicación:', error);
            });
        }
        {% endif %}
    });
</script>
{% endblock %}
