# app/auth.py
from flask import Blueprint, render_template, flash, redirect, url_for, request, current_app
from flask_login import current_user, login_user, logout_user
# from werkzeug.urls import url_parse  # <--- COMENTA O ELIMINA ESTA LÍNEA
from urllib.parse import urlparse     # <--- AÑADE ESTA LÍNEA
from app import db
from app.forms import LoginForm, RegistrationForm
from app.models import User

bp = Blueprint('auth', __name__)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index')) # Redirige a la ruta principal

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user is None or not user.check_password(form.password.data):
            flash('Usuario o contraseña inválidos', 'danger') # Categoría danger para Bootstrap
            current_app.logger.warning(f"Intento de login fallido para usuario: {form.username.data}")
            return redirect(url_for('auth.login'))

        login_user(user, remember=form.remember_me.data)
        current_app.logger.info(f"Usuario {user.username} inició sesión exitosamente.")

        next_page = request.args.get('next')
        # Seguridad: Validar que next_page sea una ruta relativa
        # --- CORRECCIÓN AQUÍ ---
        if not next_page or urlparse(next_page).netloc != '':
        # --- FIN CORRECCIÓN ---
            next_page = url_for('main.index') # Redirigir al índice si no hay next o es externo

        flash(f'Bienvenido de nuevo, {user.username}!', 'success')
        return redirect(next_page)

    return render_template('auth/login.html', title='Iniciar Sesión', form=form)
    
@bp.route('/logout')
def logout():
    if current_user.is_authenticated:
         current_app.logger.info(f"Usuario {current_user.username} cerró sesión.")
         logout_user()
         flash('Has cerrado sesión.', 'info')
    return redirect(url_for('main.index'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        try:
            user = User(username=form.username.data, email=form.email.data or None) # Guardar None si email está vacío
            user.set_password(form.password.data)
            db.session.add(user)
            db.session.commit()
            current_app.logger.info(f"Nuevo usuario registrado: {user.username} (Email: {user.email})")
            flash('¡Felicidades, te has registrado correctamente! Ahora puedes iniciar sesión.', 'success')
            return redirect(url_for('auth.login'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error al registrar usuario {form.username.data}: {e}")
            flash('Ocurrió un error durante el registro. Inténtalo de nuevo.', 'danger')

    return render_template('auth/register.html', title='Registro', form=form)