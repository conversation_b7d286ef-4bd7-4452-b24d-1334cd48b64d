import json
import serial
import time
import os
import ctypes
import pyaudio
import wave
import io
import base64
import numpy as np
import threading
import hashlib
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

# Función para cargar la configuración del usuario
def load_config():
    try:
        with open('ptt_client_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: No se encontró el archivo de configuración.")
        return {}

# Función para calcular el hash del archivo de configuración
def file_checksum(file_path):
    md5_hash = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
    except FileNotFoundError:
        print("Error: No se encontró el archivo de configuración.")
        return None
    return md5_hash.hexdigest()

# Función para monitorear el archivo de configuración
def monitor_config_file(config, config_lock):
    config_path = 'ptt_client_config.json'
    last_checksum = file_checksum(config_path)
    
    while True:
        time.sleep(2)
        current_checksum = file_checksum(config_path)
        
        if current_checksum != last_checksum:
            print("Cambios detectados en el archivo de configuración. Recargando...")
            with config_lock:
                new_config = load_config()
                config.update(new_config)
            last_checksum = current_checksum

# Función para iniciar sesión y navegar al nodo usando Selenium
def login_and_navigate(config):
    chrome_options = Options()
    chrome_options.add_argument("--use-fake-ui-for-media-stream")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-software-rasterizer")
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--autoplay-policy=no-user-gesture-required")

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

    driver.get(config['node_url'])

    username = driver.find_element(By.NAME, 'username')
    password = driver.find_element(By.NAME, 'password')

    username.send_keys(config['username'])
    password.send_keys(config['password'])

    password.send_keys(Keys.RETURN)
    time.sleep(5)

    driver.get(config['node_url'])
    time.sleep(5)
    
    return driver

# Función para controlar el PTT en el puerto COM (exclusiva de dig-vhf)
def set_ptt(state, config, ser=None):
    com_port = f"COM{config.get('port_number', '1')}"
    try:
        if state:
            if ser is None or not ser.is_open:
                ser = serial.Serial(com_port, baudrate=9600, timeout=1)
                print(f"Puerto {com_port} abierto con éxito.")
            
            ser.setDTR(True)
            print(f"PTT activado en {com_port}. Mide el voltaje en el pin 7.")
            return ser
        else:
            if ser is not None and ser.is_open:
                ser.setDTR(False)
                print(f"PTT desactivado en {com_port}. Verifica si el voltaje vuelve a 0.")
                ser.close()
                print(f"Puerto {com_port} cerrado.")
            return None
    except serial.SerialException as e:
        print(f"Error al intentar abrir el puerto {com_port}: {e}")
        return None

# Función para escuchar eventos de audio desde la página web (exclusiva de dig-vhf)
def listen_to_audio_events(driver, config):
    ser = None

    driver.execute_script("""
        const socket = io.connect('https://' + document.domain + ':' + location.port + '/node', {
            query: {
                node_id: arguments[0],
                username: arguments[1]
            }
        });

        socket.on('global_audio_start', (data) => {
            if (data.node_id === arguments[0]) {
                console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
                localStorage.setItem('audio_state', 'started');
            }
        });

        socket.on('global_audio_end', (data) => {
            if (data.node_id === arguments[0]) {
                console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
                localStorage.setItem('audio_state', 'ended');
            }
        });

        socket.on('receive_audio', (data) => {
            localStorage.setItem('audio_playing', 'pending');
            setTimeout(() => {
                const audioBlob = new Blob([new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))], { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);
                audio.play();

                audio.onplay = () => {
                    localStorage.setItem('audio_playing', 'true');
                };
                
                audio.onended = () => {
                    localStorage.setItem('audio_playing', 'false');
                };
            }, 1000);
        });
    """, config['node_id'], config['username'])

    try:
        while True:
            audio_playing = driver.execute_script("return localStorage.getItem('audio_playing');")
            if audio_playing == 'pending':
                ser = set_ptt(True, config, ser)
                driver.execute_script("localStorage.setItem('audio_playing', 'true');")
            elif audio_playing == 'true':
                pass
            elif audio_playing == 'false':
                ser = set_ptt(False, config, ser)
                driver.execute_script("localStorage.removeItem('audio_playing');")
            time.sleep(0.5)
    except KeyboardInterrupt:
        set_ptt(False, config, ser)
        print("Script detenido manualmente. PTT desactivado.")

# Función para capturar y enviar audio del micrófono al sitio web (exclusiva de vhf-dig)
def capture_and_send_audio(driver, config, config_lock):
    p = pyaudio.PyAudio()

    stream = p.open(format=pyaudio.paInt16,
                    channels=1,
                    rate=16000,
                    input=True,
                    input_device_index=config.get('input_device_index', 0),
                    frames_per_buffer=2048)

    volume_level_threshold = config.get('volume_level', 5)
    is_transmitting = False

    def send_audio(data):
        wav_buffer = io.BytesIO()
        wf = wave.open(wav_buffer, 'wb')
        wf.setnchannels(1)
        wf.setsampwidth(p.get_sample_size(pyaudio.paInt16))
        wf.setframerate(16000)
        wf.writeframes(data)
        wf.close()

        wav_buffer.seek(0)
        audio_base64 = base64.b64encode(wav_buffer.read()).decode('utf-8')

        script = f"""
            const socket = io.connect('https://' + document.domain + ':' + location.port + '/node', {{
                query: {{
                    node_id: '{config['node_id']}',
                    username: '{config['username']}'
                }}
            }});

            socket.emit('transmit_audio', {{
                audio: '{audio_base64}',
                user: '{config['username']}',
                node_id: '{config['node_id']}'
            }});
        """
        driver.execute_script(script)

    print("Monitoreando el audio...")

    try:
        while True:
            data = stream.read(2048)
            audio_data = np.frombuffer(data, dtype=np.int16)
            volume = np.linalg.norm(audio_data) / len(audio_data)

            with config_lock:
                volume_level_threshold = config.get('volume_level', 5)

            if volume > volume_level_threshold and not is_transmitting:
                print(f"Volumen {volume} excede el umbral {volume_level_threshold}. Iniciando transmisión...")
                is_transmitting = True

            if is_transmitting:
                send_audio(data)

            if is_transmitting and volume <= volume_level_threshold:
                print(f"Volumen {volume} por debajo del umbral {volume_level_threshold}. Deteniendo transmisión...")
                is_transmitting = False

            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interrumpido por el usuario, deteniendo el envío de audio.")
    finally:
        stream.stop_stream()
        stream.close()
        p.terminate()
        print("Transmisión de audio detenida.")

if __name__ == '__main__':
    config = load_config()
    config_lock = threading.Lock()

    if 'username' not in config or 'password' not in config or 'node_url' not in config or 'node_id' not in config:
        print("Error: Configuración incompleta. Por favor, asegúrate de tener usuario, contraseña y nodo configurados.")
        exit(1)

    threading.Thread(target=monitor_config_file, args=(config, config_lock), daemon=True).start()

    driver = login_and_navigate(config)

    threading.Thread(target=listen_to_audio_events, args=(driver, config), daemon=True).start()
    capture_and_send_audio(driver, config, config_lock)
