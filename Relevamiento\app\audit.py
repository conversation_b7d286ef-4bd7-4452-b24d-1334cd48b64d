# app/audit.py
"""
Blueprint para el sistema de auditoría y historial de cambios.
"""

from flask import Blueprint, render_template, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from app import db
from app.models_audit import AuditLog, UserSession
from app.audit_utils import get_audit_stats, audit_view_access, audit_export
from sqlalchemy import desc, func, and_, or_
from datetime import datetime, timedelta
import csv
import io
import json

bp = Blueprint('audit', __name__, url_prefix='/audit')

# Decorador para verificar permisos de administrador
def admin_required(f):
    """Decorador para verificar que el usuario sea administrador."""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'administrador':
            return jsonify({'error': 'Acceso denegado'}), 403
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """Dashboard principal de auditoría."""
    try:
        # Registrar acceso a la vista
        audit_view_access('audit_dashboard', description="Acceso al dashboard de auditoría")
        
        # Obtener estadísticas
        stats = get_audit_stats()
        
        # Actividad reciente (últimos 50 registros)
        recent_logs = AuditLog.query.order_by(desc(AuditLog.timestamp)).limit(50).all()
        
        # Actividad por día (últimos 7 días)
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        daily_activity = db.session.query(
            func.date(AuditLog.timestamp).label('date'),
            func.count(AuditLog.id).label('count')
        ).filter(
            AuditLog.timestamp >= seven_days_ago
        ).group_by(
            func.date(AuditLog.timestamp)
        ).order_by('date').all()
        
        # Módulos más utilizados
        module_activity = db.session.query(
            AuditLog.module,
            func.count(AuditLog.id).label('count')
        ).filter(
            AuditLog.module.isnot(None)
        ).group_by(AuditLog.module).order_by(
            func.count(AuditLog.id).desc()
        ).limit(10).all()
        
        return render_template('audit/dashboard.html',
                             title="Dashboard de Auditoría",
                             stats=stats,
                             recent_logs=recent_logs,
                             daily_activity=daily_activity,
                             module_activity=module_activity)
    
    except Exception as e:
        current_app.logger.error(f"Error en dashboard de auditoría: {e}")
        return render_template('audit/dashboard.html',
                             title="Dashboard de Auditoría",
                             error="Error cargando datos de auditoría")

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """Lista completa de logs de auditoría con filtros."""
    try:
        # Registrar acceso a la vista
        audit_view_access('audit_logs', description="Acceso a logs de auditoría")
        
        # Parámetros de filtro
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # Filtros
        user_filter = request.args.get('user', '')
        action_filter = request.args.get('action', '')
        table_filter = request.args.get('table', '')
        module_filter = request.args.get('module', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        
        # Construir query base
        query = AuditLog.query
        
        # Aplicar filtros
        if user_filter:
            query = query.filter(AuditLog.username.ilike(f'%{user_filter}%'))
        
        if action_filter:
            query = query.filter(AuditLog.action == action_filter)
        
        if table_filter:
            query = query.filter(AuditLog.table_name == table_filter)
        
        if module_filter:
            query = query.filter(AuditLog.module == module_filter)
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(AuditLog.timestamp >= date_from_obj)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(AuditLog.timestamp < date_to_obj)
            except ValueError:
                pass
        
        # Ordenar y paginar
        logs = query.order_by(desc(AuditLog.timestamp)).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Obtener opciones para filtros
        users = db.session.query(AuditLog.username).distinct().order_by(AuditLog.username).all()
        actions = db.session.query(AuditLog.action).distinct().order_by(AuditLog.action).all()
        tables = db.session.query(AuditLog.table_name).distinct().order_by(AuditLog.table_name).all()
        modules = db.session.query(AuditLog.module).filter(AuditLog.module.isnot(None)).distinct().order_by(AuditLog.module).all()
        
        return render_template('audit/logs.html',
                             title="Logs de Auditoría",
                             logs=logs,
                             users=[u[0] for u in users],
                             actions=[a[0] for a in actions],
                             tables=[t[0] for t in tables],
                             modules=[m[0] for m in modules],
                             filters={
                                 'user': user_filter,
                                 'action': action_filter,
                                 'table': table_filter,
                                 'module': module_filter,
                                 'date_from': date_from,
                                 'date_to': date_to
                             })
    
    except Exception as e:
        current_app.logger.error(f"Error en logs de auditoría: {e}")
        return render_template('audit/logs.html',
                             title="Logs de Auditoría",
                             error="Error cargando logs de auditoría")

@bp.route('/log/<int:log_id>')
@login_required
@admin_required
def log_detail(log_id):
    """Detalle de un log específico."""
    try:
        log = AuditLog.query.get_or_404(log_id)
        
        # Registrar acceso a la vista
        audit_view_access('audit_log_detail', record_id=log_id, 
                         description=f"Acceso a detalle de log {log_id}")
        
        return render_template('audit/log_detail.html',
                             title=f"Detalle de Log #{log_id}",
                             log=log)
    
    except Exception as e:
        current_app.logger.error(f"Error en detalle de log: {e}")
        return render_template('audit/log_detail.html',
                             title="Error",
                             error="Error cargando detalle del log")

@bp.route('/sessions')
@login_required
@admin_required
def sessions():
    """Lista de sesiones de usuario."""
    try:
        # Registrar acceso a la vista
        audit_view_access('user_sessions', description="Acceso a sesiones de usuario")
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # Filtros
        user_filter = request.args.get('user', '')
        active_only = request.args.get('active_only', False, type=bool)
        
        # Construir query
        query = UserSession.query.join(UserSession.user)
        
        if user_filter:
            query = query.filter(User.username.ilike(f'%{user_filter}%'))
        
        if active_only:
            query = query.filter(UserSession.is_active == True)
        
        sessions = query.order_by(desc(UserSession.login_time)).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return render_template('audit/sessions.html',
                             title="Sesiones de Usuario",
                             sessions=sessions,
                             filters={
                                 'user': user_filter,
                                 'active_only': active_only
                             })
    
    except Exception as e:
        current_app.logger.error(f"Error en sesiones: {e}")
        return render_template('audit/sessions.html',
                             title="Sesiones de Usuario",
                             error="Error cargando sesiones")

@bp.route('/export')
@login_required
@admin_required
def export_logs():
    """Exportar logs de auditoría a CSV."""
    try:
        # Aplicar los mismos filtros que en la vista de logs
        user_filter = request.args.get('user', '')
        action_filter = request.args.get('action', '')
        table_filter = request.args.get('table', '')
        module_filter = request.args.get('module', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        
        # Construir query
        query = AuditLog.query
        
        if user_filter:
            query = query.filter(AuditLog.username.ilike(f'%{user_filter}%'))
        if action_filter:
            query = query.filter(AuditLog.action == action_filter)
        if table_filter:
            query = query.filter(AuditLog.table_name == table_filter)
        if module_filter:
            query = query.filter(AuditLog.module == module_filter)
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(AuditLog.timestamp >= date_from_obj)
            except ValueError:
                pass
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(AuditLog.timestamp < date_to_obj)
            except ValueError:
                pass
        
        logs = query.order_by(desc(AuditLog.timestamp)).all()
        
        # Crear CSV
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Headers
        writer.writerow([
            'ID', 'Fecha/Hora', 'Usuario', 'Acción', 'Tabla', 'Registro ID',
            'Módulo', 'Descripción', 'IP', 'Campos Modificados'
        ])
        
        # Datos
        for log in logs:
            writer.writerow([
                log.id,
                log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                log.username,
                log.action,
                log.table_name,
                log.record_id or '',
                log.module or '',
                log.get_readable_description(),
                log.user_ip or '',
                ', '.join(log.get_changed_fields_list())
            ])
        
        # Registrar exportación
        audit_export('audit_log', 
                    description="Exportación de logs de auditoría",
                    record_count=len(logs))
        
        # Preparar respuesta
        output.seek(0)
        filename = f"audit_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        return send_file(
            io.BytesIO(output.getvalue().encode('utf-8')),
            mimetype='text/csv',
            as_attachment=True,
            download_name=filename
        )
    
    except Exception as e:
        current_app.logger.error(f"Error exportando logs: {e}")
        return jsonify({'error': 'Error exportando logs'}), 500

@bp.route('/api/stats')
@login_required
@admin_required
def api_stats():
    """API para obtener estadísticas de auditoría."""
    try:
        stats = get_audit_stats()
        return jsonify(stats)
    except Exception as e:
        current_app.logger.error(f"Error en API de estadísticas: {e}")
        return jsonify({'error': 'Error obteniendo estadísticas'}), 500
