# /app/__init__.py
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_bcrypt import Bcrypt
from flask_migrate import Migrate
from config import Config
import datetime
import pytz  # Asegúrate de importar pytz si usas to_local
from markupsafe import Markup, escape  # <-- IMPORTACIONES NECESARIAS
import re  # <-- IMPORTACIÓN NECESARIA

# 1. Crear las instancias de las extensiones *fuera* de la función create_app
db = SQLAlchemy()
login_manager = LoginManager()
bcrypt = Bcrypt()
migrate = Migrate()

# --- FUNCIÓN PARA EL FILTRO NL2BR ---
# Definir la función ANTES de create_app o dentro de ella antes de registrarla
def nl2br(value):
    if not isinstance(value, str):
        value = str(value) # Convertir a string si no lo es
    # Escapar HTML primero para seguridad
    escaped_value = escape(value)
    # Reemplazar saltos de línea (\r\n, \r, \n) con <br>
    # Usamos expresión regular para cubrir varios tipos de saltos de línea
    converted = Markup(re.sub(r'(\r\n|\r|\n)', '<br>\n', escaped_value))
    return converted
# --- FIN FUNCIÓN FILTRO ---

def create_app(config_class=Config):
    # 2. Crear la instancia de la aplicación Flask
    app = Flask(__name__)

    # 3. Configurar la aplicación
    app.config.from_object(config_class)

    # 4. Inicializar las extensiones *con la aplicación*
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'routes.login'
    login_manager.login_message_category = 'info'
    bcrypt.init_app(app)
    migrate.init_app(app, db)

    # --- Registrar filtros Jinja ---
    # (Debes hacerlo DESPUÉS de crear 'app' y ANTES de registrar blueprints/rutas que los usen)
    from app.utils import utc_to_buenos_aires, calculate_age # Mover imports aquí si utils importa app
    app.jinja_env.filters['to_local'] = utc_to_buenos_aires
    app.jinja_env.filters['calculate_age'] = calculate_age
    app.jinja_env.filters['nl2br'] = nl2br  # <-- REGISTRO DEL FILTRO NL2BR

    # --- Importar y Registrar Blueprints ---
    # (Hacerlo DESPUÉS de inicializar extensiones y registrar filtros)
    from app.routes import routes_bp
    app.register_blueprint(routes_bp)

    # --- Context Processor ---
    # (Puede ir aquí o antes de registrar blueprints)
    @app.context_processor
    def inject_user_view():
        from app.utils import get_user_for_view # Import local para evitar ciclos
        # Añadir datetime al diccionario que se inyecta
        return dict(
            get_user_for_view=get_user_for_view,
            now=datetime.datetime.utcnow # O datetime.datetime.now()
        )

    return app