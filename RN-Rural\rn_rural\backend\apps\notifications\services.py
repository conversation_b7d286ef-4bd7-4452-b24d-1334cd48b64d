# File: backend/apps/notifications/services.py
# -----------------------------------------------

import json
import logging
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from datetime import datetime  # Añadir esta importación

logger = logging.getLogger(__name__)

def send_notification(user_id, notification_type, title, message, data=None):
    """
    Envía una notificación a un usuario específico a través de WebSockets.
    
    Args:
        user_id (int): ID del usuario destinatario
        notification_type (str): Tipo de notificación (ej: 'incidencia_asignada')
        title (str): Título de la notificación
        message (str): Mensaje de la notificación
        data (dict, optional): Datos adicionales para la notificación
    
    Returns:
        bool: True si la notificación se envió correctamente, False en caso contrario
    """
    try:
        # Obtener la capa de canales
        channel_layer = get_channel_layer()
        
        # Preparar los datos de la notificación
        notification_data = {
            'type': 'notification_message',
            'notification_type': notification_type,
            'title': title,
            'message': message,
            'timestamp': str(datetime.now().isoformat()),
        }
        
        # Añadir datos adicionales si se proporcionan
        if data:
            notification_data['data'] = data
        
        # Enviar la notificación al grupo del usuario
        async_to_sync(channel_layer.group_send)(
            f'notifications_{user_id}',
            notification_data
        )
        
        logger.info(f"Notificación enviada al usuario {user_id}: {title}")
        return True
    except Exception as e:
        logger.error(f"Error al enviar notificación: {str(e)}")
        return False

def send_incidencia_asignada_notification(brigada_user, incidencia):
    """
    Envía una notificación a una brigada cuando se le asigna una incidencia.
    
    Args:
        brigada_user (User): Usuario de la brigada
        incidencia (Incidencia): Incidencia asignada
    
    Returns:
        bool: True si la notificación se envió correctamente, False en caso contrario
    """
    try:
        # Datos adicionales para la notificación
        data = {
            'incidencia_id': incidencia.id,
            'ubicacion': {
                'lat': incidencia.ubicacion_incidencia.y if incidencia.ubicacion_incidencia else None,
                'lng': incidencia.ubicacion_incidencia.x if incidencia.ubicacion_incidencia else None
            },
            'estado': incidencia.estado,
            'descripcion': incidencia.descripcion_texto or "Sin descripción disponible",
            'fecha_creacion': incidencia.fecha_creacion.strftime("%d/%m/%Y %H:%M"),
            'usuario_reporta': incidencia.usuario_reporta.username
        }
        
        # Enviar la notificación
        return send_notification(
            user_id=brigada_user.id,
            notification_type='incidencia_asignada',
            title='Nueva Incidencia Asignada',
            message=f'Se te ha asignado la incidencia #{incidencia.id}',
            data=data
        )
    except Exception as e:
        logger.error(f"Error al enviar notificación de incidencia asignada: {str(e)}")
        return False