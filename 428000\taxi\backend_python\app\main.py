from datetime import timed<PERSON>ta
from fastapi import FastAPI, Depends, HTTPException, status, Request, Form, Response, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
from jose import jwt, JWTError  # Para decodificar el token
import logging

from app.api.v1 import auth_router
from app.api.v1 import user_router
from app.api.v1 import trip_router
from app.api.v1 import vehicle_router
from app.core.config import settings
from app.db.database import engine, get_db, SessionLocal # Importamos SessionLocal para el evento de inicio
from app.models import user as user_model # Para crear tablas si no usas Alembic al inicio
from app.services import user_service # Para crear roles iniciales
from app.api import deps # Para obtener el usuario actual en las plantillas
from app.schemas import user_schema # Importamos user_schema para el endpoint de API
from app.schemas.token_schema import TokenData
from app.websockets.router import websocket_router
from app.websockets.connection import connection_manager, get_redis_connection, close_redis_connection

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Descomentar si quieres que FastAPI cree las tablas al inicio (NO recomendado si usas Alembic)
# user_model.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url="/api/v1/openapi.json" # Ruta para la especificacion OpenAPI
)

# Montar archivos estaticos (CSS, JS para el panel web)
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Configurar plantillas Jinja2
templates = Jinja2Templates(directory="app/templates")

# --- Middleware para el Panel Web (Opcional, para proteger rutas de plantillas) ---
# Este middleware es un ejemplo basico. En produccion, considera soluciones mas robustas.
@app.middleware("http")
async def web_auth_middleware(request: Request, call_next):
    # Rutas que no requieren autenticacion web
    public_web_paths = ["/web/login", "/static/", "/favicon.ico", "/api/v1/auth/web/login", "/web/login_submit"]

    # Permitir acceso a la documentacion de la API sin token web
    if request.url.path.startswith("/docs") or request.url.path.startswith("/redoc") or request.url.path.startswith("/api/v1/openapi.json"):
        response = await call_next(request)
        return response

    if request.url.path.startswith("/web") and not any(request.url.path.startswith(p) for p in public_web_paths):
        token = request.cookies.get("admin_access_token")
        user = None
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email: str = payload.get("sub")
                # roles_from_token = payload.get("roles", []) # Puedes usar esto si es necesario
                if email:
                    # No hacemos una llamada a la BD en el middleware por rendimiento.
                    # Guardamos la info del token en request.state para usarla en la ruta.
                    request.state.user_email = email
                    request.state.user_roles_from_token = payload.get("roles", [])
                    user = True # Marcador de que hay un usuario (simplificado)
            except JWTError:
                pass # Token invalido o expirado

        if not user:
            # Si no esta autenticado y no es la pagina de login, redirigir a login
            return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    response = await call_next(request)
    return response


# --- Rutas de API Móvil ---
app.include_router(auth_router.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(user_router.router, prefix="/api/v1/users", tags=["Users"])
app.include_router(trip_router.router, prefix="/api/v1/trips", tags=["Trips"])
app.include_router(vehicle_router.router, prefix="/api/v1/vehicles", tags=["Vehicles"])

# --- Rutas WebSocket ---
app.include_router(websocket_router, tags=["WebSockets"])


# --- Rutas para el Panel Web (Jinja2) ---
@app.get("/web/login", response_class=HTMLResponse, name="login_page")
async def login_page_route(request: Request, error_message: str | None = None):
    return templates.TemplateResponse("login.html", {"request": request, "error_message": error_message})

@app.post("/web/login_submit", name="web_login_submit") # Nueva ruta para el formulario
async def web_login_submit_route(
    request: Request,
    db: Session = Depends(get_db),
    username: str = Form(...),
    password: str = Form(...)
):
    try:
        # Verificar credenciales
        user = user_service.get_user_by_email(db, email=username)
        if not user or not user_service.verify_password(password, user.hashed_password):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Credenciales incorrectas")

        # Verificar si el usuario es superusuario o tiene ID 3 (joacoabe)
        if user.is_superuser or user.id == 3:
            # Asignar roles manualmente
            user_roles = ["administrador"]
        else:
            # Verificar si el usuario tiene roles asignados (de forma segura)
            user_roles = []
            try:
                # Consultar directamente a la base de datos para obtener los roles
                sql_query = text("""
                    SELECT r.name::text
                    FROM roles r
                    JOIN user_roles_association ura ON r.id = ura.role_id
                    WHERE ura.user_id = :user_id
                """)
                role_query = db.execute(sql_query, {"user_id": user.id})

                for row in role_query:
                    user_roles.append(row[0])
            except Exception as e:
                print(f"Error al consultar roles: {e}")

            # Verificar si el usuario tiene un rol permitido para el panel web
            allowed_web_roles = [
                "administrador",
                "operador",
                "titular",
                "base",
                "taxi",
                "usuario"
            ]

            # Convertir todos los roles a minúsculas para comparación
            user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]

            if not user_roles_lower or not any(role in user_roles_lower for role in allowed_web_roles):
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="No autorizado para el panel web")

        # Crear token para cualquier usuario autorizado
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        from app.services.security import create_access_token
        access_token = create_access_token(
            data={"sub": user.email, "roles": user_roles}, expires_delta=access_token_expires
        )

        # Crear respuesta con redirección al dashboard
        response = RedirectResponse(url="/web/dashboard", status_code=303)

        # Establecer la cookie con el token
        response.set_cookie(
            key="admin_access_token",
            value=access_token,
            httponly=False,  # Cambiar a False para que JavaScript pueda acceder a la cookie
            max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            samesite="lax",
            secure=False  # Cambiar a True en producción con HTTPS
        )

        return response
    except HTTPException as e:
        # Si el login falla, renderiza la pagina de login con mensaje de error
        return templates.TemplateResponse("login.html", {"request": request, "error_message": e.detail})

@app.get("/web/logout", name="web_logout")
async def web_logout_route(response: Response):
    response = RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)
    response.delete_cookie("admin_access_token")
    return response

@app.get("/web/dashboard", response_class=HTMLResponse, name="dashboard_route")
async def dashboard_route(request: Request, db: Session = Depends(get_db)):
    """
    Ruta principal del dashboard que redirige al usuario al dashboard correspondiente según su rol.
    """
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario de request.state (establecido por el middleware) o del token
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    # Si no hay usuario en request.state, intentar obtenerlo del token en la cookie
    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    # Si aún no hay usuario, redirigir al login
    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener roles del usuario desde el token
    user_roles = []
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token

    # Convertir roles a minúsculas para comparación
    user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]

    # Redirigir al dashboard correspondiente según el rol
    if "administrador" in user_roles_lower:
        return RedirectResponse(url=app.url_path_for("admin_dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)
    elif "operador" in user_roles_lower:
        return RedirectResponse(url=app.url_path_for("operator_dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)
    elif "taxi" in user_roles_lower:
        return RedirectResponse(url=app.url_path_for("driver_dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)
    elif "usuario" in user_roles_lower:
        return RedirectResponse(url=app.url_path_for("passenger_dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)
    elif "titular" in user_roles_lower:
        return RedirectResponse(url=app.url_path_for("owner_dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)
    elif "base" in user_roles_lower:
        return RedirectResponse(url=app.url_path_for("base_dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)
    else:
        # Si no tiene un rol específico, redirigir al dashboard de administrador por defecto
        return RedirectResponse(url=app.url_path_for("admin_dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

@app.get("/web/admin/dashboard", response_class=HTMLResponse, name="admin_dashboard_route")
async def admin_dashboard_route(request: Request, db: Session = Depends(get_db)):
    """
    Dashboard para administradores.
    """
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario de request.state (establecido por el middleware) o del token
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    # Si no hay usuario en request.state, intentar obtenerlo del token en la cookie
    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    # Si aún no hay usuario, redirigir al login
    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es administrador
    is_admin = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_admin = "administrador" in user_roles_lower

    if not is_admin and not current_user.is_superuser:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener estadísticas para el dashboard
    try:
        # Contar usuarios
        total_users = db.query(user_model.User).count()

        # Contar taxis (vehículos con conductor asignado)
        # Esto es un ejemplo, ajusta según tu modelo de datos
        total_taxis = 12  # Valor de ejemplo

        # Contar viajes de hoy
        # Esto es un ejemplo, ajusta según tu modelo de datos
        total_trips = 23  # Valor de ejemplo

        # Ingresos totales (ejemplo)
        total_revenue = 15000  # Valor de ejemplo

    except Exception as e:
        print(f"Error al obtener estadísticas: {e}")
        total_users = 0
        total_taxis = 0
        total_trips = 0
        total_revenue = 0

    # Preparar datos para la plantilla
    dashboard_data = {
        "request": request,
        "current_user": current_user,
        "total_users": total_users,
        "total_taxis": total_taxis,
        "total_trips": total_trips,
        "total_revenue": total_revenue
    }

    return templates.TemplateResponse("admin_dashboard.html", dashboard_data)

@app.get("/web/operator/dashboard", response_class=HTMLResponse, name="operator_dashboard_route")
async def operator_dashboard_route(request: Request, db: Session = Depends(get_db)):
    """
    Dashboard para operadores.
    """
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es operador
    is_operator = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_operator = "operador" in user_roles_lower

    if not is_operator:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener estadísticas para el dashboard (ejemplo)
    stats = {
        "pending_trips": 5,
        "in_progress_trips": 8,
        "completed_trips": 15,
        "active_vehicles": 10
    }

    # Obtener viajes pendientes (ejemplo)
    pending_trips = [
        {"id": 1, "origin_address": "Av. Corrientes 1234", "destination_address": "Av. Santa Fe 4321", "requested_at": "10:30 AM"},
        {"id": 2, "origin_address": "Av. Rivadavia 5678", "destination_address": "Av. Cabildo 8765", "requested_at": "11:15 AM"}
    ]

    # Obtener viajes en curso (ejemplo)
    in_progress_trips = [
        {"id": 3, "passenger_name": "Juan Pérez", "driver_name": "Carlos Gómez", "status": "EN CURSO", "origin_address": "Av. Córdoba 1234", "destination_address": "Av. Callao 4321"},
        {"id": 4, "passenger_name": "María López", "driver_name": "Roberto Sánchez", "status": "ACEPTADO", "origin_address": "Av. Pueyrredón 5678", "destination_address": "Av. Las Heras 8765"}
    ]

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "stats": stats,
        "pending_trips": pending_trips,
        "in_progress_trips": in_progress_trips
    }

    return templates.TemplateResponse("operator_dashboard.html", template_data)

@app.get("/web/driver/dashboard", response_class=HTMLResponse, name="driver_dashboard_route")
async def driver_dashboard_route(request: Request, db: Session = Depends(get_db)):
    """
    Dashboard para conductores (taxis).
    """
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es conductor
    is_driver = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_driver = "taxi" in user_roles_lower

    if not is_driver:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener vehículo del conductor (ejemplo)
    vehicle = {
        "id": 1,
        "plate_number": "ABC123",
        "brand": "Toyota",
        "model": "Corolla",
        "year": "2020",
        "color": "Blanco",
        "status": "libre"
    }

    # Obtener viaje actual (ejemplo)
    current_trip = None  # No hay viaje actual

    # Obtener estadísticas (ejemplo)
    stats = {
        "trips_today": 5,
        "earnings_today": 2500,
        "hours_online": 6,
        "rating": 4.8
    }

    # Obtener viajes recientes (ejemplo)
    recent_trips = [
        {"id": 10, "completed_at": "10:30 AM", "origin_address": "Av. Corrientes 1234", "destination_address": "Av. Santa Fe 4321", "fare": 350, "passenger_rating_for_driver": 5},
        {"id": 9, "completed_at": "09:15 AM", "origin_address": "Av. Rivadavia 5678", "destination_address": "Av. Cabildo 8765", "fare": 420, "passenger_rating_for_driver": 4}
    ]

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "vehicle": vehicle,
        "current_trip": current_trip,
        "stats": stats,
        "recent_trips": recent_trips
    }

    return templates.TemplateResponse("driver_dashboard.html", template_data)

@app.get("/web/passenger/dashboard", response_class=HTMLResponse, name="passenger_dashboard_route")
async def passenger_dashboard_route(request: Request, db: Session = Depends(get_db)):
    """
    Dashboard para pasajeros (usuarios).
    """
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es pasajero
    is_passenger = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_passenger = "usuario" in user_roles_lower

    if not is_passenger:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener viaje actual (ejemplo)
    current_trip = None  # No hay viaje actual

    # Obtener estadísticas (ejemplo)
    stats = {
        "total_trips": 15,
        "total_spent": 7500,
        "avg_rating": 4.7,
        "avg_wait_time": "5 min"
    }

    # Obtener viajes recientes (ejemplo)
    recent_trips = [
        {"id": 10, "requested_at": "Hoy, 10:30 AM", "origin_address": "Av. Corrientes 1234", "destination_address": "Av. Santa Fe 4321", "driver_name": "Carlos Gómez", "status": "COMPLETADO", "actual_fare": 350},
        {"id": 9, "requested_at": "Hoy, 09:15 AM", "origin_address": "Av. Rivadavia 5678", "destination_address": "Av. Cabildo 8765", "driver_name": "Roberto Sánchez", "status": "COMPLETADO", "actual_fare": 420}
    ]

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "current_trip": current_trip,
        "stats": stats,
        "recent_trips": recent_trips
    }

    return templates.TemplateResponse("passenger_dashboard.html", template_data)

@app.get("/web/owner/dashboard", response_class=HTMLResponse, name="owner_dashboard_route")
async def owner_dashboard_route(request: Request, db: Session = Depends(get_db)):
    """
    Dashboard para titulares (dueños de vehículos).
    """
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es titular
    is_owner = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_owner = "titular" in user_roles_lower

    if not is_owner:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener estadísticas (ejemplo)
    stats = {
        "total_vehicles": 5,
        "total_drivers": 7,
        "total_trips": 120,
        "total_earnings": 60000
    }

    # Obtener vehículos activos (ejemplo)
    active_vehicles = [
        {"id": 1, "plate_number": "ABC123", "brand": "Toyota", "model": "Corolla", "driver_name": "Carlos Gómez", "status": "libre", "last_update": "Hace 5 min"},
        {"id": 2, "plate_number": "DEF456", "brand": "Honda", "model": "Civic", "driver_name": "Roberto Sánchez", "status": "ocupado", "last_update": "Hace 2 min"}
    ]

    # Obtener conductores destacados (ejemplo)
    top_drivers = [
        {"name": "Carlos Gómez", "vehicle": "Toyota Corolla", "trips": 45, "rating": 4.8, "earnings": 22500},
        {"name": "Roberto Sánchez", "vehicle": "Honda Civic", "trips": 38, "rating": 4.7, "earnings": 19000}
    ]

    # Datos para el gráfico de ganancias (ejemplo)
    earnings_data = {
        "labels": ["Lun", "Mar", "Mié", "Jue", "Vie", "Sáb", "Dom"],
        "values": [8500, 9200, 8700, 9500, 12000, 15000, 10000]
    }

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "stats": stats,
        "active_vehicles": active_vehicles,
        "top_drivers": top_drivers,
        "earnings_data": earnings_data
    }

    return templates.TemplateResponse("owner_dashboard.html", template_data)

@app.get("/web/base/dashboard", response_class=HTMLResponse, name="base_dashboard_route")
async def base_dashboard_route(request: Request, db: Session = Depends(get_db)):
    """
    Dashboard para bases (estaciones).
    """
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es base
    is_base = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_base = "base" in user_roles_lower

    if not is_base:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener estadísticas (ejemplo)
    stats = {
        "active_vehicles": 10,
        "available_drivers": 5,
        "trips_today": 25,
        "avg_response_time": "3m"
    }

    # Obtener estadísticas de vehículos (ejemplo)
    vehicle_stats = {
        "available": 5,
        "busy": 3,
        "alert": 1,
        "offline": 1
    }

    # Obtener vehículos (ejemplo)
    vehicles = [
        {"plate_number": "ABC123", "driver_name": "Carlos Gómez", "status": "libre", "last_update": "Hace 5 min"},
        {"plate_number": "DEF456", "driver_name": "Roberto Sánchez", "status": "ocupado", "last_update": "Hace 2 min"},
        {"plate_number": "GHI789", "driver_name": "Juan Pérez", "status": "alerta", "last_update": "Hace 1 min"}
    ]

    # Obtener viajes recientes (ejemplo)
    recent_trips = [
        {"id": 10, "driver_name": "Carlos Gómez", "origin_address": "Av. Corrientes 1234", "destination_address": "Av. Santa Fe 4321", "status": "COMPLETADO", "requested_at": "10:30 AM"},
        {"id": 9, "driver_name": "Roberto Sánchez", "origin_address": "Av. Rivadavia 5678", "destination_address": "Av. Cabildo 8765", "status": "EN CURSO", "requested_at": "11:15 AM"}
    ]

    # Obtener conductores activos (ejemplo)
    active_drivers = [
        {"name": "Carlos Gómez", "vehicle": "Toyota Corolla (ABC123)", "status": "libre", "hours_active": "5h", "trips_today": 8},
        {"name": "Roberto Sánchez", "vehicle": "Honda Civic (DEF456)", "status": "ocupado", "hours_active": "4h", "trips_today": 6}
    ]

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "stats": stats,
        "vehicle_stats": vehicle_stats,
        "vehicles": vehicles,
        "recent_trips": recent_trips,
        "active_drivers": active_drivers
    }

    return templates.TemplateResponse("base_dashboard.html", template_data)

# Rutas adicionales para cada tipo de usuario

# Rutas para pasajeros
@app.get("/web/passenger/request-trip", response_class=HTMLResponse, name="passenger_request_trip_route")
async def passenger_request_trip_route(request: Request, db: Session = Depends(get_db)):
    """Página para solicitar un viaje."""
    # Importar el servicio de usuario
    from app.services import user_service

    # Verificar autenticación
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es pasajero
    is_passenger = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_passenger = "usuario" in user_roles_lower

    if not is_passenger and not current_user.is_superuser:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    return templates.TemplateResponse("passenger_request_trip.html", {
        "request": request,
        "current_user": current_user
    })

@app.get("/web/passenger/trips", response_class=HTMLResponse, name="passenger_trips_route")
async def passenger_trips_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver historial de viajes del pasajero."""
    # Importar el servicio de usuario
    from app.services import user_service

    # Verificar autenticación
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es pasajero
    is_passenger = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_passenger = "usuario" in user_roles_lower

    if not is_passenger and not current_user.is_superuser:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener viajes del pasajero (ejemplo)
    # En una implementación real, esto se obtendría de la base de datos
    trips = [
        {
            "id": 1,
            "origin_address": "Av. Corrientes 1234, Buenos Aires",
            "destination_address": "Av. Santa Fe 4321, Buenos Aires",
            "driver_name": "Carlos Gómez",
            "status": "completado",
            "requested_at": "2023-05-22 15:30:00",
            "completed_at": "2023-05-22 16:15:00",
            "estimated_fare": 450,
            "actual_fare": 480
        },
        {
            "id": 2,
            "origin_address": "Av. Rivadavia 5678, Buenos Aires",
            "destination_address": "Av. Cabildo 8765, Buenos Aires",
            "driver_name": "Roberto Sánchez",
            "status": "en_viaje",
            "requested_at": "2023-05-23 10:30:00",
            "completed_at": None,
            "estimated_fare": 550,
            "actual_fare": None
        }
    ]

    return templates.TemplateResponse("passenger_trips.html", {
        "request": request,
        "current_user": current_user,
        "trips": trips
    })

@app.get("/web/passenger/profile", response_class=HTMLResponse, name="passenger_profile_route")
async def passenger_profile_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver y editar perfil del pasajero."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("passenger_profile.html", {"request": request})

@app.get("/web/passenger/trip/{trip_id}", response_class=HTMLResponse, name="passenger_trip_details_route")
async def passenger_trip_details_route(trip_id: int, request: Request, db: Session = Depends(get_db)):
    """Página para ver detalles de un viaje específico."""
    # Importar el servicio de usuario y viaje
    from app.services import user_service, trip_service

    # Verificar autenticación
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es pasajero
    is_passenger = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_passenger = "usuario" in user_roles_lower

    if not is_passenger and not current_user.is_superuser:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener detalles del viaje
    try:
        trip = trip_service.get_trip_with_details(db, trip_id)

        # Verificar que el viaje pertenece al pasajero actual
        if trip and trip["passenger_id"] != current_user.id and not current_user.is_superuser:
            return RedirectResponse(url=app.url_path_for("passenger_trips_route"), status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        print(f"Error al obtener detalles del viaje: {e}")
        trip = None

    return templates.TemplateResponse("passenger_trip_details.html", {
        "request": request,
        "trip_id": trip_id,
        "trip": trip,
        "current_user": current_user
    })

# Rutas para conductores
@app.get("/web/driver/trips", response_class=HTMLResponse, name="driver_trips_route")
async def driver_trips_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver historial de viajes del conductor."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("driver_trips.html", {"request": request})

@app.get("/web/driver/vehicle", response_class=HTMLResponse, name="driver_vehicle_route")
async def driver_vehicle_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver información del vehículo asignado."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("driver_vehicle.html", {"request": request})

@app.get("/web/driver/trip/{trip_id}", response_class=HTMLResponse, name="driver_trip_details_route")
async def driver_trip_details_route(trip_id: int, request: Request, db: Session = Depends(get_db)):
    """Página para ver detalles de un viaje específico."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("driver_trip_details.html", {"request": request, "trip_id": trip_id})

# Rutas para operadores
@app.get("/web/operator/trips", response_class=HTMLResponse, name="operator_trips_route")
async def operator_trips_route(request: Request, db: Session = Depends(get_db)):
    """Página para gestionar viajes."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("operator_trips.html", {"request": request})

@app.get("/web/operator/trip/{trip_id}", response_class=HTMLResponse, name="trip_details_route")
async def trip_details_route(trip_id: int, request: Request, db: Session = Depends(get_db)):
    """Página para ver detalles de un viaje específico."""
    # Importar el servicio de usuario
    from app.services import user_service

    # Verificar autenticación
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario es operador o administrador
    is_authorized = False
    if hasattr(request.state, "user_roles_from_token"):
        user_roles = request.state.user_roles_from_token
        user_roles_lower = [role.lower() if isinstance(role, str) else str(role).lower() for role in user_roles]
        is_authorized = "operador" in user_roles_lower or "administrador" in user_roles_lower

    if not is_authorized and not current_user.is_superuser:
        return RedirectResponse(url=app.url_path_for("dashboard_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener datos del viaje (ejemplo)
    # En una implementación real, esto se obtendría de la base de datos
    trip = {
        "id": trip_id,
        "passenger_name": "Juan Pérez",
        "passenger_phone": "+54 9 11 1234-5678",
        "driver_name": "Carlos Gómez",
        "driver_phone": "+54 9 11 8765-4321",
        "origin_address": "Av. Corrientes 1234, Buenos Aires",
        "destination_address": "Av. Santa Fe 4321, Buenos Aires",
        "origin_latitude": -34.6037,
        "origin_longitude": -58.3816,
        "destination_latitude": -34.5924,
        "destination_longitude": -58.3889,
        "estimated_fare": 500,
        "actual_fare": None,
        "status": "aceptado",
        "requested_at": "2023-05-23 10:30:00",
        "accepted_at": "2023-05-23 10:32:00",
        "started_at": None,
        "completed_at": None,
        "cancelled_at": None
    }

    # Obtener conductores disponibles (ejemplo)
    available_drivers = [
        {"id": 1, "name": "Roberto Sánchez", "vehicle": "Toyota Corolla (ABC123)"},
        {"id": 2, "name": "María López", "vehicle": "Honda Civic (DEF456)"}
    ]

    return templates.TemplateResponse("trip_details.html", {
        "request": request,
        "trip": trip,
        "trip_id": trip_id,
        "available_drivers": available_drivers,
        "current_user": current_user
    })

# Rutas para titulares
@app.get("/web/owner/vehicles", response_class=HTMLResponse, name="owner_vehicles_route")
async def owner_vehicles_route(request: Request, db: Session = Depends(get_db)):
    """Página para gestionar vehículos del titular."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("owner_vehicles.html", {"request": request})

@app.get("/web/owner/drivers", response_class=HTMLResponse, name="owner_drivers_route")
async def owner_drivers_route(request: Request, db: Session = Depends(get_db)):
    """Página para gestionar conductores del titular."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("owner_drivers.html", {"request": request})

@app.get("/web/owner/earnings", response_class=HTMLResponse, name="owner_earnings_route")
async def owner_earnings_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver ganancias del titular."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("owner_earnings.html", {"request": request})

# Rutas para bases
@app.get("/web/base/vehicles", response_class=HTMLResponse, name="base_vehicles_route")
async def base_vehicles_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver vehículos de la base."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("base_vehicles.html", {"request": request})

@app.get("/web/base/drivers", response_class=HTMLResponse, name="base_drivers_route")
async def base_drivers_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver conductores de la base."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("base_drivers.html", {"request": request})

@app.get("/web/base/trips", response_class=HTMLResponse, name="base_trips_route")
async def base_trips_route(request: Request, db: Session = Depends(get_db)):
    """Página para ver viajes de la base."""
    # Verificar autenticación y rol (código similar al dashboard)
    # ...
    return templates.TemplateResponse("base_trips.html", {"request": request})

@app.get("/web/users", response_class=HTMLResponse, name="users_management_route")
async def users_management_route(request: Request, db: Session = Depends(get_db)):
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual (similar a admin_dashboard_route)
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener estadísticas y datos para la página
    try:
        # Importar el servicio de usuario
        from app.services import user_service

        # Obtener todos los usuarios
        all_users = user_service.get_users(db)
        total_users = len(all_users)

        # Contar usuarios por rol
        total_drivers = 0
        total_passengers = 0
        total_operators = 0

        # Lista de usuarios con sus roles
        users = []

        # Obtener todos los usuarios con sus roles
        for user in all_users:
            # Obtener el rol principal del usuario
            user_role = None
            try:
                sql_query = text("""
                    SELECT r.name::text
                    FROM roles r
                    JOIN user_roles_association ura ON r.id = ura.role_id
                    WHERE ura.user_id = :user_id
                    LIMIT 1
                """)
                role_query = db.execute(sql_query, {"user_id": user.id})
                role_result = role_query.first()

                if role_result:
                    user_role = role_result[0]

                    # Contar usuarios por rol
                    if user_role.lower() == 'taxi':
                        total_drivers += 1
                    elif user_role.lower() == 'usuario':
                        total_passengers += 1
                    elif user_role.lower() == 'operador':
                        total_operators += 1
            except Exception as e:
                print(f"Error al obtener rol del usuario {user.id}: {e}")
                user_role = "desconocido"

            # Agregar usuario a la lista
            users.append({
                "id": user.id,
                "full_name": user.full_name or "",
                "email": user.email,
                "phone_number": user.phone_number or "",
                "role_name": user_role,
                "is_active": user.is_active
            })

        print(f"Se encontraron {len(users)} usuarios")
        print(f"Conductores: {total_drivers}, Pasajeros: {total_passengers}, Operadores: {total_operators}")

    except Exception as e:
        print(f"Error al obtener datos de usuarios: {e}")
        total_users = 0
        total_drivers = 0
        total_passengers = 0
        total_operators = 0
        users = []

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "total_users": total_users,
        "total_drivers": total_drivers,
        "total_passengers": total_passengers,
        "total_operators": total_operators,
        "users": users
    }

    return templates.TemplateResponse("users_management.html", template_data)

@app.get("/web/users/{user_id}", response_class=HTMLResponse, name="user_details_route")
async def user_details_route(user_id: int, request: Request, db: Session = Depends(get_db)):
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual (similar a admin_dashboard_route)
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Verificar si el usuario actual es administrador o es el mismo usuario
    is_admin = False
    if hasattr(request.state, "user_roles_from_token"):
        is_admin = any(role.lower() == "administrador" for role in request.state.user_roles_from_token)

    is_same_user = current_user.id == user_id

    if not (is_admin or is_same_user):
        return RedirectResponse(url=app.url_path_for("users_management_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener datos del usuario
    try:
        user = user_service.get_user(db, user_id=user_id)
        if not user:
            return RedirectResponse(url=app.url_path_for("users_management_route"), status_code=status.HTTP_303_SEE_OTHER)

        # Estadísticas del usuario (ejemplo)
        user_stats = {
            "total_trips": 25,
            "completed_trips": 20,
            "cancelled_trips": 5,
            "average_rating": 4.8,
            "last_login": "22/05/2023 14:30"
        }

    except Exception as e:
        print(f"Error al obtener datos del usuario: {e}")
        return RedirectResponse(url=app.url_path_for("users_management_route"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener el rol del usuario
    user_role = None
    try:
        # Consultar directamente a la base de datos para obtener el rol principal
        sql_query = text("""
            SELECT r.name::text
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": user.id})

        role_result = role_query.first()
        if role_result:
            user_role = role_result[0]
    except Exception as e:
        print(f"Error al consultar rol del usuario: {e}")

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "user": user,
        "user_stats": user_stats,
        "user_role": user_role
    }

    return templates.TemplateResponse("user_details.html", template_data)

@app.get("/web/vehicles", response_class=HTMLResponse, name="vehicles_management_route")
async def vehicles_management_route(request: Request, db: Session = Depends(get_db)):
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual (similar a admin_dashboard_route)
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener estadísticas y datos para la página
    try:
        # Importar el servicio de vehículos
        from app.services import vehicle_service
        from app.models.vehicle import Vehicle, VehicleStatus, VehicleCategory

        # Obtener todos los vehículos
        all_vehicles = vehicle_service.get_vehicles(db)
        total_vehicles = len(all_vehicles)

        # Contar vehículos por categoría administrativa
        active_vehicles = len([v for v in all_vehicles if v.category == VehicleCategory.ACTIVO])
        maintenance_vehicles = len([v for v in all_vehicles if v.category == VehicleCategory.EN_MANTENIMIENTO])
        inactive_vehicles = len([v for v in all_vehicles if v.category == VehicleCategory.INACTIVO])

        # Preparar lista de vehículos para la plantilla
        vehicles = []
        for v in all_vehicles:
            # Obtener el nombre del conductor
            driver_name = "Sin conductor"
            if v.driver_id:
                try:
                    from app.services import user_service
                    driver = user_service.get_user(db, user_id=v.driver_id)
                    if driver:
                        driver_name = driver.full_name or driver.email
                except Exception as e:
                    print(f"Error al obtener conductor: {e}")

            vehicles.append({
                "id": v.id,
                "brand": v.brand,
                "model": v.model,
                "license_plate": v.plate_number,
                "year": v.year,
                "color": v.color,
                "status": v.status.value,
                "category": v.category.value,
                "driver_name": driver_name,
                "mileage": 0  # No tenemos este campo en el modelo
            })

        # Obtener conductores (usuarios con rol de taxi)
        from app.services import user_service
        all_users = user_service.get_users(db)
        drivers = []

        # Obtener todos los usuarios con rol de taxi directamente con una consulta SQL
        try:
            sql_query = text("""
                SELECT u.id, u.full_name, u.email
                FROM users u
                JOIN user_roles_association ura ON u.id = ura.user_id
                JOIN roles r ON ura.role_id = r.id
                WHERE LOWER(r.name::text) = 'taxi'
            """)
            taxi_users = db.execute(sql_query).fetchall()

            for user in taxi_users:
                drivers.append({
                    "id": user.id,
                    "full_name": user.full_name or user.email
                })

            print(f"Se encontraron {len(drivers)} conductores")
        except Exception as e:
            print(f"Error al obtener conductores: {e}")

    except Exception as e:
        print(f"Error al obtener datos de vehículos: {e}")
        total_vehicles = 0
        active_vehicles = 0
        maintenance_vehicles = 0
        inactive_vehicles = 0
        vehicles = []
        drivers = []

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "total_vehicles": total_vehicles,
        "active_vehicles": active_vehicles,
        "maintenance_vehicles": maintenance_vehicles,
        "inactive_vehicles": inactive_vehicles,
        "vehicles": vehicles,
        "drivers": drivers
    }

    return templates.TemplateResponse("vehicles_management.html", template_data)

# Rutas para las nuevas páginas
@app.get("/web/trips", response_class=HTMLResponse, name="trips_management_route")
async def trips_management_route(request: Request, db: Session = Depends(get_db)):
    # Importar los servicios necesarios
    from app.services import user_service, trip_service
    from app.models.trip import TripStatusEnum

    # Obtener usuario actual (similar a admin_dashboard_route)
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener estadísticas y datos para la página
    try:
        # Obtener todos los viajes
        all_trips = trip_service.get_trips(db)
        total_trips = len(all_trips)

        # Contar viajes por estado
        active_trips = len(trip_service.get_active_trips(db))
        completed_trips = len([t for t in all_trips if t.status == TripStatusEnum.COMPLETADO])
        cancelled_trips = len([t for t in all_trips if t.status in [TripStatusEnum.CANCELADO_PASAJERO, TripStatusEnum.CANCELADO_CONDUCTOR]])

        # Preparar lista de viajes para la plantilla
        trips = []
        for trip in all_trips:
            # Obtener nombres de pasajero y conductor
            passenger_name = "Desconocido"
            driver_name = "Sin asignar"

            if trip.passenger_id:
                try:
                    passenger = user_service.get_user(db, user_id=trip.passenger_id)
                    if passenger:
                        passenger_name = passenger.full_name or passenger.email
                except Exception as e:
                    print(f"Error al obtener pasajero: {e}")

            if trip.driver_id:
                try:
                    driver = user_service.get_user(db, user_id=trip.driver_id)
                    if driver:
                        driver_name = driver.full_name or driver.email
                except Exception as e:
                    print(f"Error al obtener conductor: {e}")

            trips.append({
                "id": trip.id,
                "passenger_id": trip.passenger_id,
                "passenger_name": passenger_name,
                "driver_id": trip.driver_id,
                "driver_name": driver_name,
                "origin_latitude": trip.origin_latitude,
                "origin_longitude": trip.origin_longitude,
                "origin_address": trip.origin_address or f"{trip.origin_latitude}, {trip.origin_longitude}",
                "destination_latitude": trip.destination_latitude,
                "destination_longitude": trip.destination_longitude,
                "destination_address": trip.destination_address or f"{trip.destination_latitude}, {trip.destination_longitude}",
                "status": trip.status.value,
                "requested_at": trip.requested_at,
                "accepted_at": trip.accepted_at,
                "started_at": trip.started_at,
                "completed_at": trip.completed_at,
                "cancelled_at": trip.cancelled_at,
                "estimated_fare": trip.estimated_fare,
                "actual_fare": trip.actual_fare,
                "estimated_distance_meters": trip.estimated_distance_meters,
                "actual_distance_meters": trip.actual_distance_meters,
                "estimated_duration_seconds": trip.estimated_duration_seconds,
                "actual_duration_seconds": trip.actual_duration_seconds,
                "passenger_rating_for_driver": trip.passenger_rating_for_driver,
                "passenger_comment_for_driver": trip.passenger_comment_for_driver,
                "driver_rating_for_passenger": trip.driver_rating_for_passenger,
                "driver_comment_for_passenger": trip.driver_comment_for_passenger
            })

        # Obtener conductores disponibles para asignar a viajes
        drivers = []
        try:
            # Consultar directamente a la base de datos para obtener conductores con vehículos disponibles
            sql_query = text("""
                SELECT u.id, u.full_name, u.email, v.id as vehicle_id, v.plate_number, v.brand, v.model
                FROM users u
                JOIN user_roles_association ura ON u.id = ura.user_id
                JOIN roles r ON ura.role_id = r.id
                JOIN vehicles v ON v.driver_id = u.id
                WHERE LOWER(r.name::text) = 'taxi'
                AND v.status = 'libre'
                AND u.is_active = true
            """)
            available_drivers = db.execute(sql_query).fetchall()

            for driver in available_drivers:
                drivers.append({
                    "id": driver.id,
                    "full_name": driver.full_name or driver.email,
                    "vehicle_info": f"{driver.brand} {driver.model} ({driver.plate_number})"
                })

            print(f"Se encontraron {len(drivers)} conductores disponibles")
        except Exception as e:
            print(f"Error al obtener conductores disponibles: {e}")

    except Exception as e:
        print(f"Error al obtener datos de viajes: {e}")
        total_trips = 0
        active_trips = 0
        completed_trips = 0
        cancelled_trips = 0
        trips = []
        drivers = []

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "total_trips": total_trips,
        "active_trips": active_trips,
        "completed_trips": completed_trips,
        "cancelled_trips": cancelled_trips,
        "trips": trips,
        "drivers": drivers
    }

    return templates.TemplateResponse("trips_management.html", template_data)

@app.get("/web/fares", response_class=HTMLResponse, name="fare_settings_route")
async def fare_settings_route(request: Request, db: Session = Depends(get_db)):
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual (similar a admin_dashboard_route)
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener datos para la página
    try:
        # Estos son valores de ejemplo, ajusta según tu modelo de datos
        base_fare = 300
        per_km_fare = 50
        per_minute_fare = 5
        minimum_fare = 500

    except Exception as e:
        print(f"Error al obtener datos de tarifas: {e}")
        base_fare = 0
        per_km_fare = 0
        per_minute_fare = 0
        minimum_fare = 0

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "base_fare": base_fare,
        "per_km_fare": per_km_fare,
        "per_minute_fare": per_minute_fare,
        "minimum_fare": minimum_fare
    }

    return templates.TemplateResponse("fare_settings.html", template_data)

@app.get("/web/map", response_class=HTMLResponse, name="realtime_map_route")
async def realtime_map_route(request: Request, db: Session = Depends(get_db)):
    # Importar el servicio de usuario
    from app.services import user_service

    # Obtener usuario actual (similar a admin_dashboard_route)
    current_user = None
    if hasattr(request.state, "user_email"):
        current_user = user_service.get_user_by_email(db, email=request.state.user_email)

    if not current_user:
        token = request.cookies.get("admin_access_token")
        if token:
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                email = payload.get("sub")
                if email:
                    current_user = user_service.get_user_by_email(db, email=email)
            except JWTError as e:
                print(f"Error al decodificar token: {e}")

    if not current_user:
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=status.HTTP_303_SEE_OTHER)

    # Obtener datos para la página
    try:
        # Estos son valores de ejemplo, ajusta según tu modelo de datos
        active_vehicles = 12
        available_vehicles = 5
        busy_vehicles = 5
        idle_vehicles = 2

        # Lista de vehículos (ejemplo)
        vehicles = [
            {"id": 1, "license_plate": "ABC123", "driver_name": "Juan Pérez", "brand": "Toyota", "model": "Corolla", "status": "available", "last_update": "2 min"},
            {"id": 2, "license_plate": "DEF456", "driver_name": "María López", "brand": "Honda", "model": "Civic", "status": "busy", "last_update": "1 min"},
            {"id": 3, "license_plate": "GHI789", "driver_name": "Carlos Gómez", "brand": "Ford", "model": "Focus", "status": "idle", "last_update": "5 min"},
        ]

    except Exception as e:
        print(f"Error al obtener datos de vehículos: {e}")
        active_vehicles = 0
        available_vehicles = 0
        busy_vehicles = 0
        idle_vehicles = 0
        vehicles = []

    # Preparar datos para la plantilla
    template_data = {
        "request": request,
        "current_user": current_user,
        "active_vehicles": active_vehicles,
        "available_vehicles": available_vehicles,
        "busy_vehicles": busy_vehicles,
        "idle_vehicles": idle_vehicles,
        "vehicles": vehicles
    }

    return templates.TemplateResponse("realtime_map.html", template_data)

# Endpoint de API para obtener "me" que usa el dashboard (como ejemplo)
# Esto se llama desde JavaScript en admin_dashboard.html
@app.get("/api/v1/web/users/me", name="read_users_me_api", tags=["Web Panel API"])
async def read_users_me_for_web(
    # Para el panel web, podrias tener una dependencia diferente que lea el token de la cookie o de request.state
    # O simplemente reutilizar la dependencia de la API movil si el token se envia como Bearer
    current_user: user_model.User = Depends(deps.get_current_active_user)
):
    # Convertir el objeto User a un diccionario para evitar problemas con el enum
    user_dict = {
        "id": current_user.id,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "phone_number": current_user.phone_number,
        "is_active": current_user.is_active,
        "is_superuser": current_user.is_superuser,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at,
        "roles": []
    }

    # Obtener roles de forma segura
    try:
        for role in current_user.roles:
            user_dict["roles"].append({
                "id": role.id,
                "name": str(role.name),
                "description": role.description
            })
    except Exception as e:
        print(f"Error al obtener roles: {e}")

    return user_dict


@app.on_event("startup")
async def startup_event():
    # Comentamos la creación de roles iniciales para evitar problemas
    # Esto se hará manualmente con el script init_db_manual.py
    # db = SessionLocal()
    # try:
    #     user_service.create_initial_roles(db)
    #     print("Roles iniciales verificados/creados.")
    # finally:
    #     db.close()

    # Inicializar conexión a Redis para WebSockets
    try:
        redis_conn = await get_redis_connection()
        if redis_conn:
            logger.info("Conexión a Redis establecida correctamente")
        else:
            logger.warning("No se pudo establecer conexión a Redis, se usará almacenamiento en memoria")
    except Exception as e:
        logger.error(f"Error al inicializar Redis: {e}")

    logger.info("Aplicación iniciada correctamente.")

@app.on_event("shutdown")
async def shutdown_event():
    # Cerrar conexión a Redis
    try:
        await close_redis_connection()
        logger.info("Conexión a Redis cerrada correctamente")
    except Exception as e:
        logger.error(f"Error al cerrar conexión a Redis: {e}")

    logger.info("Aplicación detenida correctamente.")


@app.get("/", include_in_schema=False) # Ruta raiz, no en la documentacion de la API
async def root():
    return {"message": f"Bienvenido a {settings.PROJECT_NAME} v{settings.PROJECT_VERSION}"}


# Configuración para ejecutar la aplicación directamente
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=5008, reload=True)

