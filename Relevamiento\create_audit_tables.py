#!/usr/bin/env python3
"""
Script para crear las tablas de auditoría en la base de datos.
"""

import sys
import os

# Agregar el directorio actual al path para importar la app
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import AuditLog, UserSession

def create_audit_tables():
    """Crea las tablas de auditoría."""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 Creando tablas de auditoría...")
            
            # Crear las tablas
            db.create_all()
            
            print("✅ Tablas de auditoría creadas exitosamente:")
            print("  - audit_log")
            print("  - user_session")
            
            # Verificar que las tablas existen
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'audit_log' in tables:
                print("✅ Tabla audit_log verificada")
            else:
                print("❌ Tabla audit_log no encontrada")
                
            if 'user_session' in tables:
                print("✅ Tabla user_session verificada")
            else:
                print("❌ Tabla user_session no encontrada")
            
            # Registrar la creación del sistema de auditoría
            AuditLog.log_action(
                user=None,
                action='CREATE',
                table_name='audit_system',
                description='Sistema de auditoría inicializado',
                module='Sistema'
            )
            
            print("✅ Sistema de auditoría inicializado correctamente")
            return True
            
        except Exception as e:
            print(f"❌ Error creando tablas de auditoría: {e}")
            return False

if __name__ == '__main__':
    success = create_audit_tables()
    sys.exit(0 if success else 1)
