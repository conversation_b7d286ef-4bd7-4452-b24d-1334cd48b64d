import os
from werkzeug.security import generate_password_hash
from models import User
from extensions import db
from flask import Flask

# Configura tu aplicación Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'isaias52'
db_path = os.path.join(os.getcwd(), 'instance', 'vhf.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Inicializa la base de datos
db.init_app(app)

# Actualiza las contraseñas de los usuarios
with app.app_context():
    try:
        users = User.query.all()
        for user in users:
            # Genera un hash de contraseña para una contraseña predeterminada (puedes cambiar esto)
            new_password_hash = generate_password_hash('123')  # Cambia '123' por la contraseña deseada
            user.password_hash = new_password_hash
        db.session.commit()
        print("Contraseñas de los usuarios actualizadas correctamente.")
    except Exception as e:
        print(f"Error al actualizar las contraseñas: {e}")
