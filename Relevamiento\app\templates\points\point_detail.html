<!-- app/templates/points/point_detail.html -->
{% extends "base.html" %}

{% block head_extra %}
<style>
    .konva-container-wrapper {
        position: relative;
        line-height: 0;
        margin-bottom: 1rem;
        border: 1px solid #ccc;
        width: 100%;
        max-width: 100%;
        display: inline-block;
    }
    .konva-container-wrapper img.annotated-image {
        max-width: 100%;
        height: auto;
        display: block;
        object-fit: contain;
        border-radius: 4px;
    }
    .konva-container {
        position: absolute;
        top: 0;
        left: 0;
        cursor: crosshair;
    }
    .annotation-toolbar {
        margin-top: 5px;
        margin-bottom: 10px;
        padding: 5px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        justify-content: center;
    }
    .annotation-toolbar button {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .annotation-toolbar button.active {
        background-color: #0d6efd;
        color: white;
        border-color: #0a58ca;
    }
    .image-card .konva-container-wrapper {
        width: 100%;
        max-width: 600px;
        margin: auto;
    }
    @media (max-width: 992px) {
        .image-card .konva-container-wrapper {
            max-width: 100%;
        }
    }
    .card.image-card {
        max-width: 100%;
        margin-bottom: 1rem;
    }
    .point-detail-header > div:last-child {
        text-align: right;
        flex-shrink: 0;
    }
    .point-detail-header > div:last-child a {
        margin-left: 5px;
    }
    @media (max-width: 576px) {
        .point-detail-header {
            flex-direction: column;
            align-items: flex-start;
        }
        .point-detail-header > div:last-child {
            margin-top: 10px;
            width: 100%;
            text-align: left;
        }
        .point-detail-header > div:last-child a {
            margin-left: 0;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-block;
        }
    }
    .camera-marker-icon {
        background: none !important;
        border: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        border-radius: 50%;
    }
    .camera-popup {
        min-width: 200px;
    }
    .camera-popup h6 {
        margin-bottom: 8px;
        color: #1f2937;
    }
    .camera-popup p {
        margin-bottom: 4px;
        font-size: 0.9em;
    }
    .camera-popup .badge {
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.75em;
    }
</style>
{% endblock %}

{% block content %}
<div class="point-detail-header d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>
            Punto: {{ point.name or point.id }}
            <span class="status-indicator status-{{ point.status }}" title="Estado: {{ point.status.capitalize() }}"></span>
        </h1>
        <p class="lead mb-1"><strong>Coordenadas:</strong> <span class="point-coordinates">({{ "%.6f"|format(point.latitude) }}, {{ "%.6f"|format(point.longitude) }})</span></p>
        <p><small class="text-muted">Creado: {{ point.created_at.strftime('%d/%m/%Y %H:%M') if point.created_at else 'N/A' }} | Última actualización: {{ point.updated_at.strftime('%d/%m/%Y %H:%M') if point.updated_at else 'N/A' }}</small></p>
    </div>
    <div> {# Contenedor para los botones de la derecha #}
        <a href="{{ url_for('main.index') }}" class="btn btn-sm btn-outline-secondary mb-1">← Volver al Mapa</a>
        <a href="{{ url_for('points.export_point_pdf', point_id=point.id) }}" class="btn btn-sm btn-danger mb-1" target="_blank"> {# target="_blank" opcional #}
             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-pdf-fill me-1" viewBox="0 0 16 16">
                <path d="M5.523 12.424c.14-.082.293-.162.459-.252.166-.09.337-.187.518-.293.181-.105.37-.218.566-.341l.004-.002.003-.002.002-.001.002-.001a.5.5 0 0 1 .246-.016c.15.02.292.057.425.107.134.05.26.11.377.175.118.065.229.14.33.223.102.083.197.175.285.275.087.099.165.207.234.324a.5.5 0 0 1-.141.677c-.06.05-.126.09-.199.123-.072.034-.15.06-.23.076a.5.5 0 0 1-.254-.015.5.5 0 0 1-.076-.036c-.076-.052-.147-.115-.21-.19a.5.5 0 0 1 .003-.607c.053-.07.117-.138.19-.204.074-.067.158-.132.25-.193a.5.5 0 0 1 .199-.076.5.5 0 0 1 .23.034c.073.033.14.074.199.123.06.05.113.106.152.168.038.06.066.124.083.192a.5.5 0 0 1-.141.678c-.047.053-.102.098-.163.138-.06.04-.128.074-.2.102a.5.5 0 0 1-.211.017.5.5 0 0 1-.096-.026c-.063-.04-.117-.09-.162-.148a.5.5 0 0 1 .078-.682c.053-.06.115-.116.186-.168.07-.052.15-.097.234-.134a.5.5 0 0 1 .206-.041.5.5 0 0 1 .163.028c.055.025.105.058.15.099.044.04.08.087.108.141a.5.5 0 0 1-.121.711c-.035.03-.076.055-.12.076-.045.02-.093.036-.144.046a.5.5 0 0 1-.16.004.5.5 0 0 1-.118-.026c-.04-.018-.076-.04-.108-.067a.5.5 0 0 1 .092-.709c.042-.05.09-.095.144-.134.054-.04.115-.073.182-.098a.5.5 0 0 1 .188-.032.5.5 0 0 1 .21.03c.06.022.114.05.162.084.048.035.088.077.12.126a.5.5 0 0 1-.142.687c-.05.043-.106.078-.168.106-.062.028-.128.048-.197.06a.5.5 0 0 1-.228.004.5.5 0 0 1-.11-.024c-.05-.02-.094-.045-.128-.074a.5.5 0 0 1 .1-.704c.04-.04.084-.075.133-.106.05-.03.104-.053.162-.07a.5.5 0 0 1 .18-.028.5.5 0 0 1 .212.031c.056.022.107.05.152.082.045.032.082.07.11.114a.5.5 0 0 1-.152.694c-.037.03-.078.054-.123.073-.045.02-.093.034-.143.044a.5.5 0 0 1-.171.004.5.5 0 0 1-.118-.025c-.042-.017-.08-.038-.112-.062a.5.5 0 0 1 .08-.708c.037-.037.08-.068.128-.094.048-.025.102-.044.16-.058a.5.5 0 0 1 .188-.025.5.5 0 0 1 .208.03c.058.02.11.045.155.076.044.03.08.065.108.105a.5.5 0 0 1-.158.691c-.042.033-.087.06-.136.08-.05.02-.102.034-.156.043a.5.5 0 0 1-.181.004.5.5 0 0 1-.118-.025c-.04-.017-.077-.037-.108-.06a.5.5 0 0 1 .088-.712c.04-.04.084-.073.132-.1.048-.027.102-.047.16-.06a.5.5 0 0 1 .198-.022.5.5 0 0 1 .212.032c.057.023.109.05.155.08.046.03.084.066.112.106a.5.5 0 0 1-.13.707c-.037.032-.077.057-.12.077-.044.02-.088.035-.134.044a.5.5 0 0 1-.175.004.5.5 0 0 1-.11-.025c-.037-.016-.07-.034-.099-.054a.5.5 0 0 1 .09-.71c.034-.033.07-.06.11-.082.04-.023.083-.04.128-.05a.5.5 0 0 1 .196-.018.5.5 0 0 1 .186.028c.05.02.095.043.134.07s.07.057.094.09c.024.033.04.07.048.11a.5.5 0 0 1-.14.496c-.032.027-.067.048-.104.065-.038.017-.077.03-.117.038a.5.5 0 0 1-.177-.003.5.5 0 0 1-.11-.024c-.03-.015-.058-.03-.08-.045a.5.5 0 0 1 .093-.715zm-1.122-1.582c.2-.117.41-.22.617-.308.207-.088.41-.16.606-.217.195-.057.384-.097.56-.12.176-.023.342-.03.492-.023a1.5 1.5 0 0 1 .49.074c.13.045.25.105.357.175.107.07.2.15.277.235.078.085.14.18.188.283a.5.5 0 0 1-.16.61c-.046.038-.098.07-.154.094-.056.025-.116.044-.178.058a.5.5 0 0 1-.197.004.5.5 0 0 1-.118-.025c-.042-.018-.08-.038-.112-.062a.5.5 0 0 1 .08-.708c.036-.036.078-.066.124-.09.046-.024.098-.04.153-.05a.5.5 0 0 1 .176-.008.5.5 0 0 1 .176.02c.054.014.104.035.147.062.043.027.078.06.104.097a.5.5 0 0 1-.141.684c-.032.028-.068.05-.107.068-.04.018-.08.032-.124.04a.5.5 0 0 1-.177.003.5.5 0 0 1-.11-.024c-.032-.015-.06-.03-.082-.045a.5.5 0 0 1 .092-.715c.03-.03.064-.056.1-.078.037-.022.076-.04.118-.05a.5.5 0 0 1 .16-.015.5.5 0 0 1 .16.02c.048.015.09.035.126.06.035.026.064.057.084.092a.5.5 0 0 1-.142.677c-.03.025-.062.045-.097.06-.035.016-.07.027-.106.034a.5.5 0 0 1-.16-.003.5.5 0 0 1-.108-.024c-.028-.013-.052-.027-.072-.04a.5.5 0 0 1 .085-.714c.026-.026.054-.048.084-.066.03-.018.062-.032.097-.04a.5.5 0 0 1 .138-.015.5.5 0 0 1 .147.02c.04.013.076.03.106.05.03.02.054.043.07.067a.5.5 0 0 1-.158.683c-.027.02-.055.036-.085.048-.03.013-.06.023-.092.03a.5.5 0 0 1-.144.003.5.5 0 0 1-.106-.023c-.028-.013-.053-.027-.073-.04a.5.5 0 0 1 .09-.714c.026-.026.055-.048.086-.066.03-.018.062-.032.096-.04a.5.5 0 0 1 .14-.014.5.5 0 0 1 .146.02c.038.012.073.028.102.046.028.018.05.04.065.064a.5.5 0 0 1-.156.68c-.025.018-.05.033-.076.044-.026.012-.052.02-.078.024a.5.5 0 0 1-.148-.003.5.5 0 0 1-.104-.023c-.023-.01-.044-.02-.062-.033a.5.5 0 0 1 .09-.714z"/>
                <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2zM9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5v2z"/>
            </svg>
            Generar Reporte PDF
        </a>
    </div>
</div>

<!-- *** INICIO ESTRUCTURA DE DOS COLUMNAS *** -->
<div class="row">

    <!-- === COLUMNA IZQUIERDA: Punto e Imágenes del Punto === -->
    <div class="col-lg-5 mb-4 mb-lg-0">

        <!-- 1. Actualizar Punto -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>Actualizar Punto</span>
                <button type="button" class="btn btn-sm btn-outline-primary" id="edit-point-btn" onclick="togglePointEditing()">
                    📍 Editar Ubicación
                </button>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('points.detail', point_id=point.id) }}" novalidate>
                    {{ status_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ status_form.status.label(class="form-label") }}
                        {{ status_form.status(class="form-select" + (" is-invalid" if status_form.status.errors else "")) }}
                        {% for error in status_form.status.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ status_form.description.label(class="form-label") }}
                        {{ status_form.description(class="form-control" + (" is-invalid" if status_form.description.errors else ""), rows=4) }}
                        {% for error in status_form.description.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                    </div>
                    {{ status_form.submit_status(class="btn btn-primary") }}
                </form>
            </div>
        </div>

        <!-- 1.5. Mapa de Edición del Punto -->
        <div class="card mb-4" id="point-edit-map-card" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">📍 Editar Ubicación del Punto</h6>
                <small class="text-muted">Arrastra el marcador para cambiar las coordenadas del punto</small>
            </div>
            <div class="card-body p-0">
                <div id="point-edit-map"
                     data-point-lat="{{ point.latitude }}"
                     data-point-lon="{{ point.longitude }}"
                     data-point-id="{{ point.id }}"
                     data-point-status="{{ point.status }}"
                     style="height: 400px; width: 100%;">
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando mapa...</span>
                        </div>
                        <p class="mt-2">Cargando mapa de edición...</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        💡 <strong>Tip:</strong> Arrastra el marcador del punto para cambiar su ubicación. Los cambios se guardan automáticamente.
                    </small>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="togglePointEditing()">
                        Cerrar Edición
                    </button>
                </div>
            </div>
        </div>

        <!-- 2. Subir Nueva Imagen del Punto -->
        <div class="card mb-4">
            <div class="card-header">Subir Nueva Imagen (Punto)</div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('points.upload_image', point_id=point.id) }}" enctype="multipart/form-data" novalidate>
                    {{ image_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ image_form.image.label(class="form-label") }}
                        {{ image_form.image(class="form-control" + (" is-invalid" if image_form.image.errors else "")) }}
                        {% for error in image_form.image.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ image_form.notes.label(class="form-label") }}
                        {{ image_form.notes(class="form-control" + (" is-invalid" if image_form.notes.errors else ""), rows=3) }}
                        {% for error in image_form.notes.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                    </div>
                    {{ image_form.submit(class="btn btn-success") }}
                </form>
            </div>
        </div>

        <!-- 3. Imágenes Asociadas al Punto (Movido de la derecha original) -->
        <div class="mb-4"> {# No necesita ser una card si no quieres borde extra #}
            <h2>Imágenes Asociadas (Punto) <span class="badge rounded-pill bg-info ms-2">{{ images|length }}</span></h2>
            <hr>
            {% if images %}
                {% for image in images %}
                <div class="card image-card mb-4 shadow-sm"> {# Usamos image-card aquí #}
                    <div class="card-body">
                        <h5 class="card-title">
                            Imagen #{{ image.id }}
                            {% if image.original_filename %}<small class="text-muted">({{ image.original_filename }})</small>{% endif %}
                        </h5>
                        <div class="annotation-toolbar" id="toolbar-{{ image.id }}">
                             {# Botones Konva para imágenes del punto #}
                             <button class="btn btn-outline-secondary btn-sm tool-button active" data-tool="select" data-image-id="{{ image.id }}" title="Seleccionar/Mover">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cursor-fill" viewBox="0 0 16 16"><path d="M14.082 2.182a.5.5 0 0 1 .103.557L8.528 15.467a.5.5 0 0 1-.917-.007L5.57 10.694.803 8.652a.5.5 0 0 1-.006-.916l12.728-5.657a.5.5 0 0 1 .556.103z"/></svg>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm tool-button" data-tool="circle" data-image-id="{{ image.id }}" title="Dibujar Círculo">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-circle" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/></svg>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm tool-button" data-tool="rect" data-image-id="{{ image.id }}" title="Dibujar Rectángulo">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-square" viewBox="0 0 16 16"><path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/></svg>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm tool-button" data-tool="arrow" data-image-id="{{ image.id }}" title="Dibujar Flecha">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-up-right" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z"/></svg>
                            </button>
                            <button class="btn btn-outline-danger btn-sm tool-button" data-tool="delete" data-image-id="{{ image.id }}" title="Eliminar Forma Seleccionada">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash3-fill" viewBox="0 0 16 16"><path d="M11 1.5v1h3.5a.5.5 0 0 1 0 1h-.538l-.853 10.66A2 2 0 0 1 11.115 16h-6.23a2 2 0 0 1-1.994-1.84L2.038 3.5H1.5a.5.5 0 0 1 0-1H5v-1A1.5 1.5 0 0 1 6.5 0h3A1.5 1.5 0 0 1 11 1.5Zm-5 0v1h4v-1a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5ZM4.5 5.029l.5 8.5a.5.5 0 1 0 .998-.06l-.5-8.5a.5.5 0 1 0-.998.06Zm6.53-.528a.5.5 0 0 0-.528.47l-.5 8.5a.5.5 0 0 0 .998.058l.5-8.5a.5.5 0 0 0-.47-.528ZM8 4.5a.5.5 0 0 0-.5.5v8.5a.5.5 0 0 0 1 0V5a.5.5 0 0 0-.5-.5Z"/></svg>
                            </button>
                            <button class="btn btn-success btn-sm save-annotations-button ms-auto" data-image-id="{{ image.id }}" title="Guardar Anotaciones">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save-fill" viewBox="0 0 16 16"><path d="M8.5 1.5A1.5 1.5 0 0 1 10 0h4a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h6c-.314.418-.5.937-.5 1.5v7.793L6 6.646a.5.5 0 0 0-.707.707l3.5 3.5a.5.5 0 0 0 .707 0l3.5-3.5a.5.5 0 0 0-.707-.707L11 9.293V1.5a.5.5 0 0 0-.5-.5h-2a.5.5 0 0 0-.5.5v1.293z"/><path d="M2 2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1H2z"/></svg>
                            </button>
                        </div>
                        <div class="konva-container-wrapper">
                            <img src="{{ image.get_url() }}" alt="Imagen {{ image.id }}" class="img-fluid annotated-image" id="image-{{ image.id }}" data-image-id="{{ image.id }}" loading="lazy">
                            <div id="konva-container-{{ image.id }}" class="konva-container"></div>
                        </div>
                        <p class="card-text mt-2">
                            {% if image.notes %}<strong>Notas:</strong> {{ image.notes|nl2br }}<br>{% endif %}
                            <small class="text-muted">Subida por: {{ image.uploader.username if image.uploader else 'Desconocido' }} el {{ image.upload_timestamp.strftime('%d/%m/%y %H:%M') if image.upload_timestamp else 'N/A' }}</small>
                        </p>
                        <div class="d-flex justify-content-end">
                            <form action="{{ url_for('points.delete_image', image_id=image.id) }}" method="POST" class="d-inline" onsubmit="return confirm('¿Estás seguro de eliminar esta imagen?');">
                                <button type="submit" class="btn btn-danger btn-sm" title="Eliminar imagen">🗑️</button>
                            </form>
                        </div>
                    </div>
                    <script type="application/json" id="annotations-data-{{ image.id }}">
                        {{ image.annotations_json | default('{}') | safe }}
                    </script>
                </div>
                {% endfor %}
            {% else %}
            <div class="alert alert-light text-center" role="alert">
                No hay imágenes asociadas a este punto todavía. ¡Subí la primera!
            </div>
            {% endif %}
        </div>

    </div> <!-- === FIN COLUMNA IZQUIERDA === -->

    <!-- === COLUMNA DERECHA: Cámaras (Listado, Formulario y Anotadores) === -->
    <div class="col-lg-7">

         <!-- 4. Cámaras Asociadas (Listado Resumen) (Movido de la izquierda original) -->
        {% if point.cameras.count() > 0 %}
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>Cámaras Asociadas</span>
                <button type="button" class="btn btn-sm btn-outline-primary" id="edit-cameras-btn" onclick="toggleCameraEditing()">
                    📍 Editar Ubicaciones
                </button>
            </div>
            <div class="card-body">
                {% for camera in point.cameras %}
                    <div class="mb-3 pb-3 {% if not loop.last %} border-bottom {% endif %}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h5>Cámara #{{ camera.id }} <span class="badge bg-secondary">{{ camera.type|capitalize }}</span></h5>
                                <p class="mb-1"><strong>Dirección:</strong> {{ camera.direction }}</p>

                                <!-- Información de coordenadas -->
                                {% if camera.has_own_coordinates() %}
                                    <p class="mb-1">
                                        <strong>📍 Coordenadas:</strong>
                                        {{ "%.6f"|format(camera.latitude) }}, {{ "%.6f"|format(camera.longitude) }}
                                        <span class="badge bg-info text-dark ms-1">{{ camera.location_source|upper }}</span>
                                        {% if camera.location_accuracy %}
                                            <small class="text-muted">(±{{ "%.1f"|format(camera.location_accuracy) }}m)</small>
                                        {% endif %}
                                    </p>
                                {% else %}
                                    <p class="mb-1">
                                        <strong>📍 Coordenadas:</strong>
                                        <span class="text-muted">Usa las del punto ({{ "%.6f"|format(point.latitude) }}, {{ "%.6f"|format(point.longitude) }})</span>
                                    </p>
                                {% endif %}

                                <p><small class="text-muted">Cargada el {{ camera.created_at.strftime('%d/%m/%Y %H:%M') }}</small></p>
                                {% if not camera.photo_filename %}
                                    <p class="text-muted"><small>Sin imagen asociada</small></p>
                                {% endif %}
                                {# La imagen se muestra abajo si existe #}
                            </div>
                            <div>
                                <form method="POST" action="{{ url_for('points.delete_camera', camera_id=camera.id) }}" onsubmit="return confirm('¿Eliminar esta cámara y sus anotaciones?')">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Eliminar Cámara">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                                          <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                          <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                        </svg>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
         <div class="alert alert-light text-center" role="alert">
            No hay cámaras asociadas a este punto todavía.
        </div>
        {% endif %}

        <!-- 4.5. Mapa de Edición de Cámaras -->
        {% if point.cameras.count() > 0 %}
        <div class="card mb-4" id="camera-edit-map-card" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">📍 Editar Ubicaciones de Cámaras</h6>
                <small class="text-muted">Arrastra los marcadores para cambiar las coordenadas de las cámaras</small>
            </div>
            <div class="card-body p-0">
                <div id="camera-edit-map"
                     data-point-lat="{{ point.latitude }}"
                     data-point-lon="{{ point.longitude }}"
                     data-point-id="{{ point.id }}"
                     style="height: 400px; width: 100%;">
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando mapa...</span>
                        </div>
                        <p class="mt-2">Cargando mapa de edición...</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        💡 <strong>Tip:</strong> Arrastra los marcadores de cámaras para cambiar su ubicación. Los cambios se guardan automáticamente.
                    </small>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="toggleCameraEditing()">
                        Cerrar Edición
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 5. Agregar Cámara (Movido de la izquierda original) -->
        <div class="card mb-4">
            <div class="card-header">Agregar Cámara</div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('points.add_camera', point_id=point.id) }}" enctype="multipart/form-data" id="camera-form">
                    {{ camera_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ camera_form.type.label(class="form-label") }}
                        {{ camera_form.type(class="form-select" + (" is-invalid" if camera_form.type.errors else "")) }}
                        {% for error in camera_form.type.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ camera_form.direction.label(class="form-label") }}
                        {{ camera_form.direction(class="form-control" + (" is-invalid" if camera_form.direction.errors else ""), placeholder="Dirección a cubrir") }}
                        {% for error in camera_form.direction.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ camera_form.photo.label(class="form-label") }}
                        {{ camera_form.photo(class="form-control" + (" is-invalid" if camera_form.photo.errors else "")) }}
                        {% for error in camera_form.photo.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                    </div>

                    <!-- Sección de Coordenadas -->
                    <div class="card border-light mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">📍 Coordenadas de la Cámara</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                {{ camera_form.location_source.label(class="form-label") }}
                                {{ camera_form.location_source(class="form-select" + (" is-invalid" if camera_form.location_source.errors else ""), id="location-source") }}
                                {% for error in camera_form.location_source.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                                <div class="form-text">
                                    Si no especifica coordenadas, se usarán las del punto ({{ "%.6f"|format(point.latitude) }}, {{ "%.6f"|format(point.longitude) }})
                                </div>
                            </div>

                            <div id="coordinates-section" style="display: none;">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ camera_form.latitude.label(class="form-label") }}
                                        {{ camera_form.latitude(class="form-control" + (" is-invalid" if camera_form.latitude.errors else ""), type="number", step="any", placeholder="-40.123456", id="camera-latitude") }}
                                        {% for error in camera_form.latitude.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                                    </div>
                                    <div class="col-md-6">
                                        {{ camera_form.longitude.label(class="form-label") }}
                                        {{ camera_form.longitude(class="form-control" + (" is-invalid" if camera_form.longitude.errors else ""), type="number", step="any", placeholder="-67.987654", id="camera-longitude") }}
                                        {% for error in camera_form.longitude.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ camera_form.location_accuracy.label(class="form-label") }}
                                        {{ camera_form.location_accuracy(class="form-control" + (" is-invalid" if camera_form.location_accuracy.errors else ""), type="number", step="0.1", placeholder="5.0") }}
                                        {% for error in camera_form.location_accuracy.errors %}<div class="invalid-feedback">{{ error }}</div>{% endfor %}
                                        <div class="form-text">Precisión en metros (opcional)</div>
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-primary" id="get-location-btn">
                                            📱 Obtener mi ubicación
                                        </button>
                                    </div>
                                </div>

                                <div class="alert alert-info" id="location-status" style="display: none;">
                                    <small id="location-message"></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{ camera_form.submit(class="btn btn-primary") }}
                </form>
            </div>
        </div>

        <!-- 6. Anotaciones de cada Cámara (Movido de la izquierda original) -->
        {% for camera in point.cameras %}
            {% if camera.photo_filename %}
            <div class="card mt-4 shadow-sm mb-4"> {# Le quite image-card para diferenciarla si es necesario #}
                <div class="card-header">
                    Anotaciones Cámara #{{ camera.id }} - {{ camera.type.capitalize() }}
                    <span class="text-muted ms-2">({{ camera.direction }})</span>
                </div>
                <div class="card-body">
                    <div class="annotation-toolbar" id="toolbar-cam-{{ camera.id }}">
                         {# Botones Konva para imágenes de cámara #}
                         <button class="btn btn-outline-secondary btn-sm tool-button active" data-tool="select" data-image-id="cam-{{ camera.id }}" title="Seleccionar/Mover">
                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cursor-fill" viewBox="0 0 16 16"><path d="M14.082 2.182a.5.5 0 0 1 .103.557L8.528 15.467a.5.5 0 0 1-.917-.007L5.57 10.694.803 8.652a.5.5 0 0 1-.006-.916l12.728-5.657a.5.5 0 0 1 .556.103z"/></svg>
                         </button>
                         <button class="btn btn-outline-secondary btn-sm tool-button" data-tool="circle" data-image-id="cam-{{ camera.id }}" title="Dibujar Círculo">
                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-circle" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/></svg>
                         </button>
                         <button class="btn btn-outline-secondary btn-sm tool-button" data-tool="rect" data-image-id="cam-{{ camera.id }}" title="Dibujar Rectángulo">
                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-square" viewBox="0 0 16 16"><path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/></svg>
                         </button>
                         <button class="btn btn-outline-secondary btn-sm tool-button" data-tool="arrow" data-image-id="cam-{{ camera.id }}" title="Dibujar Flecha">
                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-up-right" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z"/></svg>
                         </button>
                         <button class="btn btn-outline-danger btn-sm tool-button" data-tool="delete" data-image-id="cam-{{ camera.id }}" title="Eliminar Forma Seleccionada">
                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash3-fill" viewBox="0 0 16 16"><path d="M11 1.5v1h3.5a.5.5 0 0 1 0 1h-.538l-.853 10.66A2 2 0 0 1 11.115 16h-6.23a2 2 0 0 1-1.994-1.84L2.038 3.5H1.5a.5.5 0 0 1 0-1H5v-1A1.5 1.5 0 0 1 6.5 0h3A1.5 1.5 0 0 1 11 1.5Zm-5 0v1h4v-1a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5ZM4.5 5.029l.5 8.5a.5.5 0 1 0 .998-.06l-.5-8.5a.5.5 0 1 0-.998.06Zm6.53-.528a.5.5 0 0 0-.528.47l-.5 8.5a.5.5 0 0 0 .998.058l.5-8.5a.5.5 0 0 0-.47-.528ZM8 4.5a.5.5 0 0 0-.5.5v8.5a.5.5 0 0 0 1 0V5a.5.5 0 0 0-.5-.5Z"/></svg>
                         </button>
                         <button class="btn btn-success btn-sm save-annotations-button ms-auto" data-camera-id="{{ camera.id }}" title="Guardar Anotaciones Cámara"> {# Ojo: data-camera-id se usa en JS #}
                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save-fill" viewBox="0 0 16 16"><path d="M8.5 1.5A1.5 1.5 0 0 1 10 0h4a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h6c-.314.418-.5.937-.5 1.5v7.793L6 6.646a.5.5 0 0 0-.707.707l3.5 3.5a.5.5 0 0 0 .707 0l3.5-3.5a.5.5 0 0 0-.707-.707L11 9.293V1.5a.5.5 0 0 0-.5-.5h-2a.5.5 0 0 0-.5.5v1.293z"/><path d="M2 2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1H2z"/></svg>
                         </button>
                    </div>
                    <div class="konva-container-wrapper">
                        <img src="{{ camera.get_photo_url() }}" class="img-fluid annotated-image" id="image-cam-{{ camera.id }}" data-image-id="cam-{{ camera.id }}" loading="lazy">
                        <div id="konva-container-cam-{{ camera.id }}" class="konva-container"></div>
                    </div>
                    <script type="application/json" id="annotations-data-cam-{{ camera.id }}">
                        {{ camera.annotations_json | default('{}') | safe }}
                    </script>
                </div>
            </div>
            {% endif %}
        {% endfor %}

    </div> <!-- === FIN COLUMNA DERECHA === -->

</div> <!-- *** FIN ESTRUCTURA DE DOS COLUMNAS *** -->
{% endblock %}


{% block scripts_extra %}
{# Asegúrate que base.html incluya Konva y image_annotator.js en este bloque o en scripts_vendor #}
{# Si no lo hace, necesitas añadirlos aquí explícitamente para esta página #}
{# <script src="https://unpkg.com/konva@9.3.6/konva.min.js"></script> #}
{# <script src="{{ url_for('static', filename='js/image_annotator.js') }}"></script> #}

<!-- Cargar scripts para edición -->
<script src="{{ url_for('static', filename='js/camera-icons.js') }}"></script>
<script src="{{ url_for('static', filename='js/point-editor.js') }}"></script>

{{ super() }} {# Llama al contenido del bloque padre si existe #}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elementos del formulario de cámara
    const locationSourceSelect = document.getElementById('location-source');
    const coordinatesSection = document.getElementById('coordinates-section');
    const getLocationBtn = document.getElementById('get-location-btn');
    const latitudeInput = document.getElementById('camera-latitude');
    const longitudeInput = document.getElementById('camera-longitude');
    const accuracyInput = document.getElementById('camera-latitude').closest('.row').querySelector('#camera-longitude').closest('.row').nextElementSibling.querySelector('input[type="number"]');
    const locationStatus = document.getElementById('location-status');
    const locationMessage = document.getElementById('location-message');

    // Función para mostrar/ocultar sección de coordenadas
    function toggleCoordinatesSection() {
        const selectedValue = locationSourceSelect.value;
        if (selectedValue === 'gps' || selectedValue === 'manual') {
            coordinatesSection.style.display = 'block';
        } else {
            coordinatesSection.style.display = 'none';
            // Limpiar campos cuando se oculta
            latitudeInput.value = '';
            longitudeInput.value = '';
            if (accuracyInput) accuracyInput.value = '';
            hideLocationStatus();
        }
    }

    // Función para mostrar estado de ubicación
    function showLocationStatus(message, type = 'info') {
        locationMessage.textContent = message;
        locationStatus.className = `alert alert-${type}`;
        locationStatus.style.display = 'block';
    }

    // Función para ocultar estado de ubicación
    function hideLocationStatus() {
        locationStatus.style.display = 'none';
    }

    // Event listener para el select de origen de coordenadas
    locationSourceSelect.addEventListener('change', toggleCoordinatesSection);

    // Event listener para el botón de obtener ubicación
    getLocationBtn.addEventListener('click', function() {
        if (!navigator.geolocation) {
            showLocationStatus('La geolocalización no está soportada por este navegador.', 'warning');
            return;
        }

        // Cambiar estado del botón
        const originalText = getLocationBtn.textContent;
        getLocationBtn.disabled = true;
        getLocationBtn.textContent = '📍 Obteniendo ubicación...';

        showLocationStatus('Solicitando ubicación del dispositivo...', 'info');

        const options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
        };

        navigator.geolocation.getCurrentPosition(
            function(position) {
                // Éxito al obtener ubicación
                const lat = position.coords.latitude;
                const lon = position.coords.longitude;
                const accuracy = position.coords.accuracy;

                latitudeInput.value = lat.toFixed(6);
                longitudeInput.value = lon.toFixed(6);

                // Establecer precisión si hay campo para ello
                const accuracyField = document.querySelector('input[placeholder="5.0"]');
                if (accuracyField && accuracy) {
                    accuracyField.value = accuracy.toFixed(1);
                }

                // Cambiar automáticamente a GPS si no estaba seleccionado
                if (locationSourceSelect.value !== 'gps') {
                    locationSourceSelect.value = 'gps';
                }

                showLocationStatus(
                    `Ubicación obtenida: ${lat.toFixed(6)}, ${lon.toFixed(6)} (precisión: ±${accuracy ? accuracy.toFixed(1) : 'N/A'} metros)`,
                    'success'
                );

                // Restaurar botón
                getLocationBtn.disabled = false;
                getLocationBtn.textContent = originalText;
            },
            function(error) {
                // Error al obtener ubicación
                let errorMessage = 'Error al obtener ubicación: ';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Permiso denegado por el usuario.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Información de ubicación no disponible.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Tiempo de espera agotado.';
                        break;
                    default:
                        errorMessage += 'Error desconocido.';
                        break;
                }

                showLocationStatus(errorMessage, 'danger');

                // Restaurar botón
                getLocationBtn.disabled = false;
                getLocationBtn.textContent = originalText;
            },
            options
        );
    });

    // Inicializar estado al cargar la página
    toggleCoordinatesSection();
});

// --- FUNCIONES PARA EDICIÓN DE CÁMARAS ---
let cameraEditMap = null;
let cameraEditMarkers = [];
let isEditingCameras = false;

function toggleCameraEditing() {
    const mapCard = document.getElementById('camera-edit-map-card');
    const editBtn = document.getElementById('edit-cameras-btn');

    if (!isEditingCameras) {
        // Activar modo edición
        mapCard.style.display = 'block';
        editBtn.textContent = '❌ Cancelar Edición';
        editBtn.className = 'btn btn-sm btn-outline-danger';
        isEditingCameras = true;

        // Inicializar mapa si no existe
        if (!cameraEditMap) {
            initializeCameraEditMap();
        }
    } else {
        // Desactivar modo edición
        mapCard.style.display = 'none';
        editBtn.textContent = '📍 Editar Ubicaciones';
        editBtn.className = 'btn btn-sm btn-outline-primary';
        isEditingCameras = false;
    }
}

function initializeCameraEditMap() {
    const mapElement = document.getElementById('camera-edit-map');
    if (!mapElement || typeof L === 'undefined') {
        console.error('Mapa o Leaflet no disponible');
        return;
    }

    const pointLat = parseFloat(mapElement.dataset.pointLat);
    const pointLon = parseFloat(mapElement.dataset.pointLon);
    const pointId = parseInt(mapElement.dataset.pointId);

    // Limpiar contenido de carga
    mapElement.innerHTML = '';

    try {
        // Crear mapa centrado en el punto
        cameraEditMap = L.map(mapElement).setView([pointLat, pointLon], 16);

        // Agregar capa de tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(cameraEditMap);

        // Agregar marcador del punto (no editable)
        const pointIcon = L.icon({
            iconUrl: '/static/img/marker_{{ point.status }}.png',
            iconSize: [30, 40],
            iconAnchor: [15, 40],
            popupAnchor: [0, -35]
        });

        const pointMarker = L.marker([pointLat, pointLon], { icon: pointIcon });
        pointMarker.bindPopup('<b>{{ point.name or "Punto " + point.id|string }}</b><br>Punto base');
        pointMarker.addTo(cameraEditMap);

        // Obtener datos de cámaras directamente del punto actual
        console.log('Inicializando marcadores de cámaras para punto:', pointId);

        // Crear datos de cámaras desde el HTML (más directo)
        const cameras = [
            {% for camera in point.cameras %}
            {
                id: {{ camera.id }},
                type: '{{ camera.type }}',
                direction: '{{ camera.direction }}',
                coordinates: {
                    {% if camera.has_own_coordinates() %}
                    lat: {{ camera.latitude }},
                    lon: {{ camera.longitude }},
                    source: '{{ camera.location_source }}',
                    accuracy: {{ camera.location_accuracy or 'null' }}
                    {% else %}
                    lat: {{ point.latitude }},
                    lon: {{ point.longitude }},
                    source: 'point',
                    accuracy: null
                    {% endif %}
                },
                created_at: '{{ camera.created_at.isoformat() if camera.created_at else "" }}',
                has_own_coordinates: {{ 'true' if camera.has_own_coordinates() else 'false' }}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        console.log('Datos de cámaras:', cameras);

        // Crear marcadores editables para cada cámara
        cameras.forEach(camera => {
            console.log('Creando marcador para cámara:', camera.id);

            if (typeof window.CameraIcons !== 'undefined') {
                const pointData = {
                    id: pointId,
                    name: '{{ point.name or "Punto " + point.id|string }}',
                    latitude: pointLat,
                    longitude: pointLon
                };

                const cameraMarker = window.CameraIcons.createEditableCameraMarker(camera, pointData, cameraEditMap);
                if (cameraMarker) {
                    console.log('Marcador creado, agregando al mapa');
                    cameraMarker.addTo(cameraEditMap);
                    cameraEditMarkers.push(cameraMarker);
                } else {
                    console.error('No se pudo crear marcador para cámara:', camera.id);
                }
            } else {
                console.error('CameraIcons no está disponible');
            }
        });

        console.log('Total de marcadores de cámaras creados:', cameraEditMarkers.length);

        // Ajustar vista para incluir todas las cámaras
        if (cameraEditMarkers.length > 0) {
            const allMarkers = [pointMarker, ...cameraEditMarkers];
            const group = L.featureGroup(allMarkers);
            cameraEditMap.fitBounds(group.getBounds().pad(0.1));
        } else {
            console.warn('No se crearon marcadores de cámaras');
        }

        console.log('Mapa de edición de cámaras inicializado');

    } catch (error) {
        console.error('Error inicializando mapa de edición:', error);
        mapElement.innerHTML = '<p class="text-danger text-center mt-5">Error al cargar el mapa de edición.</p>';
    }
}

// Las funciones de edición del punto están en point-editor.js
</script>
{% endblock %}