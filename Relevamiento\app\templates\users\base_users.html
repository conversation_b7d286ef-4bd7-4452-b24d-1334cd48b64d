<!-- app/templates/users/base_users.html -->
{% extends "base.html" %}

{% block extra_css %}
<style>
    /* Estilos específicos para gestión de usuarios */
    .user-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .user-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .user-info {
        flex-grow: 1;
    }

    .user-actions {
        display: flex;
        gap: 5px;
    }

    .role-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
        text-transform: uppercase;
    }

    .role-administrador {
        background-color: #dc3545;
        color: white;
    }

    .role-supervisor {
        background-color: #fd7e14;
        color: white;
    }

    .role-operador {
        background-color: #198754;
        color: white;
    }

    .role-visualizador {
        background-color: #6c757d;
        color: white;
    }

    .status-active {
        color: #198754;
        font-weight: bold;
    }

    .status-inactive {
        color: #dc3545;
        font-weight: bold;
    }

    .permissions-summary {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
    }

    .filter-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .filter-row {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }

    .filter-group {
        flex: 1;
        min-width: 200px;
    }

    .filter-actions {
        display: flex;
        gap: 10px;
    }

    .form-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #ddd;
        margin-bottom: 20px;
    }

    .form-section h4 {
        margin-top: 0;
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
    }

    .checkbox-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin-top: 10px;
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .multi-select {
        min-height: 120px;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
    }

    .pagination-wrapper {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-top: 20px;
    }

    .results-info {
        color: #666;
        font-size: 0.9em;
    }

    /* Estilos adicionales para templates de edición y permisos */
    .user-info-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
    }

    .user-avatar-large {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #0056b3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 32px;
        margin: 0 auto;
    }

    .user-summary-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .permission-summary-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }

    .permission-summary-card:hover {
        transform: translateY(-2px);
    }

    .permission-icon {
        font-size: 24px;
        color: #007bff;
        margin-bottom: 10px;
    }

    .permissions-section {
        margin-bottom: 30px;
    }

    .permissions-section h4 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .role-permissions-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
    }

    .permission-list {
        list-style: none;
        padding: 0;
    }

    .permission-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .permission-item:last-child {
        border-bottom: none;
    }

    .permission-item i {
        width: 20px;
        margin-right: 10px;
    }

    .permission-item.active {
        color: #28a745;
    }

    .permission-item.inactive {
        color: #6c757d;
    }

    .permission-status {
        margin-left: auto;
        font-weight: bold;
    }

    .geographic-permissions-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        min-height: 150px;
    }

    .permission-chips {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .permission-chip {
        display: inline-block;
        background: #e9ecef;
        color: #495057;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        margin: 2px;
    }

    .city-chip {
        background: #d1ecf1;
        color: #0c5460;
    }

    .source-chip {
        background: #d4edda;
        color: #155724;
    }

    .no-restrictions {
        text-align: center;
        color: #6c757d;
        padding: 40px 20px;
    }

    .no-restrictions i {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .special-permissions-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
    }

    .special-permission-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
    }

    .permission-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }

    .permission-header i {
        margin-right: 8px;
    }

    .permission-value.active {
        color: #28a745;
        font-weight: bold;
    }

    .permission-value.inactive {
        color: #dc3545;
        font-weight: bold;
    }

    .access-stats-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
    }

    .stat-item {
        display: flex;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        text-align: center;
    }

    .stat-icon {
        font-size: 24px;
        color: #007bff;
        margin-right: 15px;
    }

    .stat-info h5 {
        margin: 0 0 5px 0;
        font-size: 14px;
        color: #6c757d;
    }

    .stat-number {
        margin: 0;
        font-size: 24px;
        font-weight: bold;
        color: #495057;
    }

    .permissions-actions {
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
    }

    .current-permissions {
        margin-top: 8px;
    }

    @media (max-width: 768px) {
        .filter-row {
            flex-direction: column;
        }

        .filter-group {
            min-width: 100%;
        }

        .user-header {
            flex-direction: column;
            align-items: start;
            gap: 10px;
        }

        .user-actions {
            width: 100%;
            justify-content: space-between;
        }

        .checkbox-group {
            grid-template-columns: 1fr;
        }

        .permission-summary-card {
            margin-bottom: 15px;
        }

        .stat-item {
            flex-direction: column;
            text-align: center;
        }

        .stat-icon {
            margin-right: 0;
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// JavaScript común para gestión de usuarios
document.addEventListener('DOMContentLoaded', function() {
    // Función para aplicar permisos por defecto según rol
    function applyRoleDefaults(role) {
        const defaults = {
            'administrador': {
                'points_view': true,
                'points_edit': true,
                'points_create': true,
                'points_delete': true,
                'cameras_view': true,
                'cameras_edit': true,
                'cameras_create': true,
                'cameras_delete': true,
                'reports_view': true,
                'reports_export': true,
                'users_manage': true
            },
            'supervisor': {
                'points_view': true,
                'points_edit': true,
                'points_create': true,
                'points_delete': true,
                'cameras_view': true,
                'cameras_edit': true,
                'cameras_create': true,
                'cameras_delete': true,
                'reports_view': true,
                'reports_export': true,
                'users_manage': false
            },
            'operador': {
                'points_view': true,
                'points_edit': true,
                'points_create': true,
                'points_delete': false,
                'cameras_view': true,
                'cameras_edit': true,
                'cameras_create': true,
                'cameras_delete': false,
                'reports_view': true,
                'reports_export': false,
                'users_manage': false
            },
            'visualizador': {
                'points_view': true,
                'points_edit': false,
                'points_create': false,
                'points_delete': false,
                'cameras_view': true,
                'cameras_edit': false,
                'cameras_create': false,
                'cameras_delete': false,
                'reports_view': true,
                'reports_export': false,
                'users_manage': false
            }
        };

        const roleDefaults = defaults[role] || {};

        // Aplicar a checkboxes de permisos
        Object.keys(roleDefaults).forEach(permission => {
            const checkbox = document.querySelector(`input[name="${permission}"]`);
            if (checkbox) {
                checkbox.checked = roleDefaults[permission];
            }
        });
    }

    // Event listener para cambio de rol
    const roleSelect = document.querySelector('select[name="role"]');
    if (roleSelect) {
        roleSelect.addEventListener('change', function() {
            applyRoleDefaults(this.value);
        });
    }

    // Botón para aplicar permisos por defecto
    const applyDefaultsBtn = document.querySelector('#apply-role-defaults');
    if (applyDefaultsBtn) {
        applyDefaultsBtn.addEventListener('click', function() {
            const role = roleSelect ? roleSelect.value : '';
            if (role) {
                applyRoleDefaults(role);
            }
        });
    }

    // Confirmación para eliminar usuarios
    document.querySelectorAll('.btn-delete-user').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const username = this.dataset.username;
            if (!confirm(`¿Estás seguro de que quieres eliminar al usuario "${username}"?`)) {
                e.preventDefault();
            }
        });
    });

    // Toggle para activar/desactivar usuarios
    document.querySelectorAll('.btn-toggle-status').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const username = this.dataset.username;
            const action = this.dataset.action;
            const actionText = action === 'activate' ? 'activar' : 'desactivar';

            if (!confirm(`¿Estás seguro de que quieres ${actionText} al usuario "${username}"?`)) {
                e.preventDefault();
            }
        });
    });

    // Seleccionar/deseleccionar todos los checkboxes en grupos
    document.querySelectorAll('.select-all-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const group = this.dataset.group;
            const checkboxes = document.querySelectorAll(`input[data-group="${group}"]`);
            checkboxes.forEach(cb => {
                cb.checked = this.checked;
            });
        });
    });

    // Limpiar filtros
    const clearFiltersBtn = document.querySelector('#clear-filters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            document.querySelectorAll('.filter-section input, .filter-section select').forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else {
                    input.value = '';
                }
            });
        });
    }
});
</script>
{% endblock %}
