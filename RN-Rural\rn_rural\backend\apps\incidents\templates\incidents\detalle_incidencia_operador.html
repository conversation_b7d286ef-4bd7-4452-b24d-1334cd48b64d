{# File: backend/apps/incidents/templates/incidents/detalle_incidencia_operador.html #}
{% extends "core/base.html" %}

{% block title %}{{ titulo_pagina }}{% endblock %}

{% block extra_head %}
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #mapDetalleIncidencia {
            height: 400px;
            width: 100%;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .card-dashboard {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card-dashboard:hover {
            transform: translateY(-5px);
        }
        .card-header-custom {
            border-radius: 10px 10px 0 0;
            padding: 15px;
            font-weight: bold;
        }
        .bg-nueva { background-color: #0dcaf0; color: white; }
        .bg-proceso { background-color: #ffc107; color: black; }
        .bg-derivada { background-color: #fd7e14; color: white; }
        .bg-resuelta { background-color: #198754; color: white; }
        .bg-cerrada { background-color: #6c757d; color: white; }
        .detail-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .estado-badge {
            font-size: 1rem;
            padding: 8px 12px;
            border-radius: 20px;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0">{{ titulo_pagina }}</h2>
        <a href="{% url 'incidents:lista_incidencias_operador' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Volver a la Lista
        </a>
    </div>

    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle"></i> Para asignar una brigada, primero cambie el estado a "En Proceso por Operador".
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-md-7">
            <!-- Tarjeta de información de la incidencia -->
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-info-circle"></i> Información de la Incidencia</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="detail-section">
                                <div class="detail-label">ID:</div>
                                <div class="detail-value">{{ incidencia.id }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-section">
                                <div class="detail-label">Estado Actual:</div>
                                <div class="detail-value">
                                    {% if incidencia.estado == 'NUEVA' %}
                                        <span class="badge bg-nueva estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% elif incidencia.estado == 'ASIGNADA_OPERADOR' or incidencia.estado == 'EN_PROCESO_OPERADOR' %}
                                        <span class="badge bg-proceso estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% elif incidencia.estado == 'DERIVADA_BRIGADA' %}
                                        <span class="badge bg-derivada estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% elif incidencia.estado == 'CERRADA_RESUELTA' %}
                                        <span class="badge bg-resuelta estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% else %}
                                        <span class="badge bg-cerrada estado-badge">{{ incidencia.get_estado_display }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-section">
                        <div class="detail-label">Reportada por:</div>
                        <div class="detail-value">
                            <i class="fas fa-user"></i> {{ incidencia.usuario_reporta.username }}
                            {% if incidencia.usuario_reporta.profile.telefono %}
                                <br><i class="fas fa-phone"></i> {{ incidencia.usuario_reporta.profile.telefono }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-section">
                        <div class="detail-label">Fecha Creación:</div>
                        <div class="detail-value">
                            <i class="fas fa-calendar-alt"></i> {{ incidencia.fecha_creacion|date:"d/m/Y H:i:s" }}
                        </div>
                    </div>

                    <div class="detail-section">
                        <div class="detail-label">Descripción:</div>
                        <div class="detail-value">
                            <p style="white-space: pre-wrap;">{{ incidencia.descripcion_texto|default:"Sin descripción." }}</p>
                        </div>
                    </div>

                    <div class="detail-section mt-4">
                        <div class="detail-label">Comunicación:</div>
                        <div class="detail-value">
                            <a href="{% url 'chat:chat_incidencia' incidencia.id %}" class="btn btn-primary">
                                <i class="fas fa-comments"></i> Abrir Chat
                            </a>
                            <small class="text-muted d-block mt-1">
                                Comunícate con el ciudadano y la brigada asignada
                            </small>
                        </div>
                    </div>

                    {% if incidencia.audio_url %}
                        <div class="detail-section">
                            <div class="detail-label">Audio:</div>
                            <div class="detail-value">
                                <audio controls src="{{ incidencia.audio_url }}" class="w-100">
                                    Tu navegador no soporta el elemento de audio.
                                </audio>
                            </div>
                        </div>
                    {% endif %}

                    {% if incidencia.foto_url %}
                        <div class="detail-section">
                            <div class="detail-label">Foto:</div>
                            <div class="detail-value">
                                <img src="{{ incidencia.foto_url }}" alt="Foto de la incidencia" class="img-fluid rounded" style="max-height: 300px;">
                            </div>
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-section">
                                <div class="detail-label">Operador Asignado:</div>
                                <div class="detail-value">
                                    {% if incidencia.operador_asignado %}
                                        <i class="fas fa-headset"></i> {{ incidencia.operador_asignado.username }}
                                    {% else %}
                                        <span class="text-muted">Ninguno</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-section">
                                <div class="detail-label">Brigada Asignada:</div>
                                <div class="detail-value">
                                    {% if incidencia.brigada_asignada %}
                                        <i class="fas fa-users"></i> {{ incidencia.brigada_asignada.username }}
                                    {% else %}
                                        <span class="text-muted">Ninguna</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tarjeta de acciones -->
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-cogs"></i> Acciones sobre Incidencia</h4>
                </div>
                <div class="card-body">
                    <!-- Cambiar Estado -->
                    <div class="detail-section">
                        <div class="detail-label mb-2">Cambiar Estado:</div>
                        <form method="post" action="{% url 'incidents:cambiar_estado_incidencia' incidencia.id %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                {{ cambiar_estado_form.nuevo_estado }}
                                {% if cambiar_estado_form.nuevo_estado.errors %}
                                    <div class="text-danger mt-2">
                                        {{ cambiar_estado_form.nuevo_estado.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> Actualizar Estado
                            </button>
                        </form>
                    </div>

                    <!-- Asignar a Brigada (solo visible si el estado es EN_PROCESO_OPERADOR) -->
                    {% if incidencia.estado == 'EN_PROCESO_OPERADOR' %}
                    <div class="detail-section mt-4">
                        <div class="detail-label mb-2">Asignar a Brigada:</div>
                        <form method="post" action="{% url 'incidents:asignar_brigada' incidencia.id %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                {{ asignar_brigada_form.brigada }}
                                {% if asignar_brigada_form.brigada.errors %}
                                    <div class="text-danger mt-2">
                                        {{ asignar_brigada_form.brigada.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Asignar Brigada
                            </button>
                        </form>
                    </div>
                    {% else %}
                    <div class="detail-section mt-4">
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle"></i> Para asignar una brigada, primero cambie el estado a "En Proceso por Operador".
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-5">
            <!-- Tarjeta del mapa -->
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Ubicación de la Incidencia</h4>
                </div>
                <div class="card-body">
                    <div id="mapDetalleIncidencia"></div>
                    {% if incidencia.ubicacion_incidencia %}
                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i> Coordenadas:
                                {{ incidencia.ubicacion_incidencia.y|floatformat:6 }},
                                {{ incidencia.ubicacion_incidencia.x|floatformat:6 }}
                            </small>
                        </div>
                    {% else %}
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i> No hay datos de ubicación precisos para esta incidencia. Se muestra una ubicación aproximada.
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Tarjeta de información adicional -->
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-clipboard-list"></i> Información Adicional</h4>
                </div>
                <div class="card-body">
                    <div class="detail-section">
                        <div class="detail-label">Flujo de Estados:</div>
                        <div class="detail-value">
                            <ol class="mb-0">
                                <li>Nueva</li>
                                <li>Asignada a Operador</li>
                                <li>En Proceso por Operador</li>
                                <li>Derivada a Brigada</li>
                                <li>Cerrada (Resuelta/No Resuelta)</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Determinar las coordenadas a usar
            {% if incidencia.ubicacion_incidencia %}
                const lat = {{ incidencia.ubicacion_incidencia.y|floatformat:6 }};
                const lng = {{ incidencia.ubicacion_incidencia.x|floatformat:6 }};
                const hasExactLocation = true;
                console.log("Coordenadas de la incidencia:", lat, lng);
            {% else %}
                // Coordenadas por defecto (centro de Río Negro, Argentina)
                const lat = -40.8;
                const lng = -63.0;
                const hasExactLocation = false;
                console.log("Usando coordenadas por defecto:", lat, lng);
            {% endif %}

            // Crear mapa con animación de zoom
            const map = L.map('mapDetalleIncidencia', {
                zoomAnimation: true,
                fadeAnimation: true
            }).setView([lat, lng], hasExactLocation ? 15 : 7);

            // Añadir capa de mapa base con estilo más moderno
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // Crear icono personalizado según el estado de la incidencia
            let iconColor = '#0dcaf0'; // Color por defecto (azul)

            {% if incidencia.estado == 'NUEVA' %}
                iconColor = '#0dcaf0'; // Azul
            {% elif incidencia.estado == 'ASIGNADA_OPERADOR' or incidencia.estado == 'EN_PROCESO_OPERADOR' %}
                iconColor = '#ffc107'; // Amarillo
            {% elif incidencia.estado == 'DERIVADA_BRIGADA' %}
                iconColor = '#fd7e14'; // Naranja
            {% elif incidencia.estado == 'CERRADA_RESUELTA' %}
                iconColor = '#198754'; // Verde
            {% else %}
                iconColor = '#6c757d'; // Gris
            {% endif %}

            // Crear icono personalizado
            const customIcon = L.divIcon({
                className: 'custom-div-icon',
                html: `<div style="background-color:${iconColor}; width:30px; height:30px; border-radius:50%; border:3px solid white; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;">{{ incidencia.id }}</div>`,
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            if (hasExactLocation) {
                // Añadir marcador con el icono personalizado
                const marker = L.marker([lat, lng], { icon: customIcon }).addTo(map);

                // Añadir popup con información detallada
                marker.bindPopup(`
                    <div style="text-align:center;">
                        <h5 style="margin-bottom:8px;">Incidencia #{{ incidencia.id }}</h5>
                        <span class="badge" style="background-color:${iconColor}; padding:5px 10px; border-radius:20px;">
                            {{ incidencia.get_estado_display }}
                        </span>
                        <hr style="margin:10px 0;">
                        <p><strong>Reportada por:</strong> {{ incidencia.usuario_reporta.username }}</p>
                        <p><strong>Fecha:</strong> {{ incidencia.fecha_creacion|date:"d/m/Y H:i" }}</p>
                    </div>
                `).openPopup();

                // Añadir círculo para mostrar el área aproximada
                L.circle([lat, lng], {
                    color: iconColor,
                    fillColor: iconColor,
                    fillOpacity: 0.2,
                    radius: 200
                }).addTo(map);
            } else {
                // Si no hay ubicación exacta, mostrar un mensaje en el mapa
                const messageDiv = L.DomUtil.create('div', 'map-message');
                messageDiv.innerHTML = `
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1000; background: rgba(255,255,255,0.8); padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <i class="fas fa-map-marker-alt" style="color: ${iconColor}; font-size: 24px;"></i>
                        <p style="margin: 10px 0 0 0;">No hay datos de ubicación precisos para esta incidencia.</p>
                    </div>
                `;
                document.getElementById('mapDetalleIncidencia').appendChild(messageDiv);
            }

            // Añadir control de escala
            L.control.scale({imperial: false}).addTo(map);

            // Añadir botón para centrar el mapa
            L.Control.CenterButton = L.Control.extend({
                onAdd: function(map) {
                    const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
                    const button = L.DomUtil.create('a', '', container);
                    button.innerHTML = '<i class="fas fa-crosshairs"></i>';
                    button.title = 'Centrar mapa';
                    button.style.display = 'flex';
                    button.style.alignItems = 'center';
                    button.style.justifyContent = 'center';
                    button.style.width = '30px';
                    button.style.height = '30px';
                    button.style.backgroundColor = 'white';
                    button.style.cursor = 'pointer';

                    L.DomEvent.on(button, 'click', function() {
                        map.setView([lat, lng], hasExactLocation ? 15 : 7);
                    });

                    return container;
                }
            });

            new L.Control.CenterButton({ position: 'topleft' }).addTo(map);

            // Intentar cargar ubicaciones de brigadas si la incidencia está derivada
            {% if incidencia.estado == 'DERIVADA_BRIGADA' and incidencia.brigada_asignada %}
                // Aquí podríamos cargar la ubicación de la brigada asignada
                // y mostrarla en el mapa para ver la distancia a la incidencia
                fetch('/api/brigadas/{{ incidencia.brigada_asignada.id }}/ubicacion/')
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.posicion_actual) {
                            const brigadaLat = data.posicion_actual.y;
                            const brigadaLng = data.posicion_actual.x;

                            // Crear icono para la brigada
                            const brigadaIcon = L.divIcon({
                                className: 'custom-div-icon',
                                html: `<div style="background-color:#198754; width:30px; height:30px; border-radius:50%; border:3px solid white; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;"><i class="fas fa-truck"></i></div>`,
                                iconSize: [30, 30],
                                iconAnchor: [15, 15]
                            });

                            // Añadir marcador de la brigada
                            const brigadaMarker = L.marker([brigadaLat, brigadaLng], { icon: brigadaIcon }).addTo(map);

                            // Añadir popup con información de la brigada
                            brigadaMarker.bindPopup(`
                                <div style="text-align:center;">
                                    <h5 style="margin-bottom:8px;">Brigada {{ incidencia.brigada_asignada.username }}</h5>
                                    <hr style="margin:10px 0;">
                                    <p><strong>Última actualización:</strong> ${data.ultima_actualizacion}</p>
                                </div>
                            `);

                            // Si tenemos ubicación exacta de la incidencia, trazar una línea entre la brigada y la incidencia
                            if (hasExactLocation) {
                                const polyline = L.polyline([
                                    [lat, lng],
                                    [brigadaLat, brigadaLng]
                                ], {
                                    color: '#fd7e14',
                                    weight: 3,
                                    opacity: 0.7,
                                    dashArray: '10, 10',
                                    lineJoin: 'round'
                                }).addTo(map);

                                // Ajustar el zoom para que se vean ambos puntos
                                map.fitBounds(polyline.getBounds(), { padding: [50, 50] });
                            }
                        }
                    })
                    .catch(error => console.error('Error al cargar la ubicación de la brigada:', error));
            {% endif %}
        });
    </script>
{% endblock %}