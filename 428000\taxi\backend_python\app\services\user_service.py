from sqlalchemy.orm import Session
from passlib.context import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from typing import List, Optional

from app.models.user import User, UserRoleModel, RoleEnum as PythonRoleEnum, UserRolesAssociation
from app.schemas.user_schema import UserCreate, UserUpdate

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    return db.query(User).filter(User.email == email).first()

def get_user(db: Session, user_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == user_id).first()

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
    return db.query(User).offset(skip).limit(limit).all()

def create_user(db: Session, user_in: UserCreate) -> User:
    hashed_password = get_password_hash(user_in.password)
    db_user = User(
        email=user_in.email,
        full_name=user_in.full_name,
        hashed_password=hashed_password,
        phone_number=user_in.phone_number,
        is_active=user_in.is_active
    )

    # No asignar roles aquí, se hará en el router

    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(db: Session, db_user: User, user_in: UserUpdate) -> User:
    if user_in.password:
        db_user.hashed_password = get_password_hash(user_in.password)
    if user_in.email is not None:
        db_user.email = user_in.email
    if user_in.full_name is not None:
        db_user.full_name = user_in.full_name
    if user_in.phone_number is not None:
        db_user.phone_number = user_in.phone_number
    if user_in.is_active is not None:
        db_user.is_active = user_in.is_active
    if user_in.is_superuser is not None:
        db_user.is_superuser = user_in.is_superuser

    # No actualizar roles aquí, se hará en el router

    db.commit()
    db.refresh(db_user)
    return db_user

def create_initial_roles(db: Session):
    for role_enum_member in PythonRoleEnum: # Iterar sobre el Enum de Python
        role_value = role_enum_member.value  # Obtener el valor del enum (ej: "usuario" en minúsculas)
        existing_role = db.query(UserRoleModel).filter(UserRoleModel.name == role_value).first() # Query sobre UserRoleModel
        if not existing_role:
            db_role = UserRoleModel(name=role_value, description=f"Rol de {role_value.capitalize()}") # Crear instancia de UserRoleModel
            db.add(db_role)
            print(f"Rol {role_enum_member.name} creado.")
    db.commit()

def delete_user(db: Session, user_id: int) -> User:
    user = db.query(User).filter(User.id == user_id).first()
    if user:
        # Guardar una copia de los datos del usuario antes de eliminarlo
        user_copy = User(
            id=user.id,
            email=user.email,
            full_name=user.full_name,
            phone_number=user.phone_number,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            hashed_password=user.hashed_password
        )

        # Eliminar el usuario
        db.delete(user)
        db.commit()

        return user_copy
    return None
