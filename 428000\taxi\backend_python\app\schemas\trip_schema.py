from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from app.models.trip import TripStatusEnum # Importar el Enum de Python
from app.schemas.trip_stop_schema import TripStop, TripStopCreate

class TripBase(BaseModel):
    origin_latitude: str
    origin_longitude: str
    origin_address: Optional[str] = None
    destination_latitude: str
    destination_longitude: str
    destination_address: Optional[str] = None

class TripCreate(TripBase):
    passenger_id: int # Asumimos que el ID del pasajero se conoce al crear el viaje
    scheduled_for: Optional[datetime] = None # Para viajes programados
    stops: Optional[List[TripStopCreate]] = None # Paradas intermedias

class TripUpdate(BaseModel):
    status: Optional[TripStatusEnum] = None # Usar el Enum de Python
    driver_id: Optional[int] = None
    vehicle_id: Optional[int] = None
    actual_fare: Optional[float] = None
    # ... otros campos actualizables

class Trip(TripBase):
    id: int
    passenger_id: int
    driver_id: Optional[int] = None
    vehicle_id: Optional[int] = None
    status: TripStatusEnum # Usar el Enum de Python
    estimated_fare: Optional[float] = None
    estimated_distance_meters: Optional[int] = None
    estimated_duration_seconds: Optional[int] = None
    requested_at: datetime
    scheduled_for: Optional[datetime] = None
    has_stops: Optional[bool] = False
    stops: Optional[List[TripStop]] = None

    class Config:
        from_attributes = True
