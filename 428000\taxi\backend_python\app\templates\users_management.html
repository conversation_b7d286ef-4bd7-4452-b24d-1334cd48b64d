{% extends "base_layout.html" %}

{% block title %}Gestión de Usuarios - Panel de Taxis{% endblock %}

{% block head_extra %}
<style>
    .user-card {
        transition: transform 0.3s;
    }
    .user-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .role-badge {
        font-size: 0.8rem;
    }
    .search-container {
        margin-bottom: 20px;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .action-buttons .btn {
        margin-right: 5px;
    }
    .filter-buttons {
        margin-bottom: 15px;
    }
    .filter-buttons .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Gestión de Usuarios</h2>
                <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="bi bi-plus-circle"></i> Nuevo Usuario
                </button>
            </div>
            <div class="card-body">
                <!-- Estadísticas rápidas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5>Total Usuarios</h5>
                                <h2>{{ total_users }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>Conductores</h5>
                                <h2>{{ total_drivers }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h5>Pasajeros</h5>
                                <h2>{{ total_passengers }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-secondary text-white">
                            <div class="card-body text-center">
                                <h5>Operadores</h5>
                                <h2>{{ total_operators }}</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Búsqueda y filtros -->
                <div class="search-container">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchUser" placeholder="Buscar por nombre, email o teléfono...">
                                <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                    <i class="bi bi-search"></i> Buscar
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="filter-buttons d-flex flex-wrap justify-content-end">
                                <button class="btn btn-outline-primary filter-btn" data-filter="all">Todos</button>
                                <button class="btn btn-outline-success filter-btn" data-filter="taxi">Conductores</button>
                                <button class="btn btn-outline-warning filter-btn" data-filter="usuario">Pasajeros</button>
                                <button class="btn btn-outline-info filter-btn" data-filter="operador">Operadores</button>
                                <button class="btn btn-outline-secondary filter-btn" data-filter="administrador">Administradores</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabla de usuarios -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Nombre</th>
                                <th>Email</th>
                                <th>Teléfono</th>
                                <th>Rol</th>
                                <th>Estado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            {% for user in users %}
                            <tr data-role="{{ user.role_name|lower }}">
                                <td>{{ user.id }}</td>
                                <td>{{ user.full_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.phone_number }}</td>
                                <td>
                                    {% if user.role_name == 'administrador' %}
                                    <span class="badge bg-secondary">Administrador</span>
                                    {% elif user.role_name == 'operador' %}
                                    <span class="badge bg-info">Operador</span>
                                    {% elif user.role_name == 'taxi' %}
                                    <span class="badge bg-success">Conductor</span>
                                    {% elif user.role_name == 'usuario' %}
                                    <span class="badge bg-warning text-dark">Pasajero</span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">{{ user.role_name }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">Activo</span>
                                    {% else %}
                                    <span class="badge bg-danger">Inactivo</span>
                                    {% endif %}
                                </td>
                                <td class="action-buttons">
                                    <a href="{{ url_for('user_details_route', user_id=user.id) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-warning" onclick="editUser({{ user.id }})">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUser({{ user.id }})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginación -->
                <nav aria-label="Paginación de usuarios">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Siguiente</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal para añadir/editar usuario -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addUserModalLabel">Nuevo Usuario</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="fullName" class="form-label">Nombre Completo</label>
                            <input type="text" class="form-control" id="fullName" name="full_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="phoneNumber" class="form-label">Teléfono</label>
                            <input type="tel" class="form-control" id="phoneNumber" name="phone_number">
                        </div>
                        <div class="col-md-6">
                            <label for="userRole" class="form-label">Rol</label>
                            <select class="form-select" id="userRole" name="role" required>
                                <option value="">Seleccionar rol...</option>
                                <option value="usuario">Pasajero</option>
                                <option value="taxi">Conductor</option>
                                <option value="operador">Operador</option>
                                <option value="administrador">Administrador</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">Contraseña</label>
                            <input type="password" class="form-control" id="password" name="password">
                            <small class="text-muted">Dejar en blanco para mantener la contraseña actual (al editar)</small>
                        </div>
                        <div class="col-md-6">
                            <label for="confirmPassword" class="form-label">Confirmar Contraseña</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirm_password">
                        </div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                        <label class="form-check-label" for="isActive">Usuario Activo</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Guardar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
    // Variables globales
    let currentUserId = null;
    let isEditing = false;

    // Función para obtener el token de autenticación
    function getAuthToken() {
        const cookieValue = document.cookie.split('; ')
            .find(row => row.startsWith('admin_access_token='))
            ?.split('=')[1];

        if (cookieValue) {
            // Decodificar el valor de la cookie si está codificado
            try {
                return decodeURIComponent(cookieValue);
            } catch (e) {
                return cookieValue;
            }
        }
        return null;
    }

    // Función para realizar peticiones a la API con autenticación
    async function fetchWithAuth(url, options = {}) {
        const token = getAuthToken();
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
            console.log('Token enviado:', token);
        } else {
            console.error('No se encontró token de autenticación');
        }

        return fetch(url, {
            ...options,
            headers,
            credentials: 'include' // Incluir cookies en la solicitud
        });
    }

    // Cargar usuarios desde la API
    async function loadUsers() {
        try {
            const response = await fetchWithAuth('/api/v1/users/');
            if (!response.ok) {
                throw new Error('Error al cargar usuarios');
            }

            const users = await response.json();
            renderUsers(users);
        } catch (error) {
            console.error('Error:', error);
            alert('Error al cargar usuarios: ' + error.message);
        }
    }

    // Renderizar usuarios en la tabla
    function renderUsers(users) {
        const tbody = document.getElementById('usersTableBody');
        tbody.innerHTML = '';

        users.forEach(user => {
            // Determinar el nombre del rol principal
            let roleName = 'Sin rol';
            if (user.roles && user.roles.length > 0) {
                roleName = user.roles[0].name.toLowerCase();
            }

            const row = document.createElement('tr');
            row.setAttribute('data-role', roleName);

            row.innerHTML = `
                <td>${user.id}</td>
                <td>${user.full_name || '-'}</td>
                <td>${user.email}</td>
                <td>${user.phone_number || '-'}</td>
                <td>
                    ${getRoleBadge(roleName)}
                </td>
                <td>
                    ${user.is_active ?
                        '<span class="badge bg-success">Activo</span>' :
                        '<span class="badge bg-danger">Inactivo</span>'}
                </td>
                <td class="action-buttons">
                    <a href="/web/users/${user.id}" class="btn btn-sm btn-primary">
                        <i class="bi bi-eye"></i>
                    </a>
                    <button class="btn btn-sm btn-warning" onclick="editUser(${user.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });

        // Actualizar contadores
        updateCounters(users);
    }

    // Obtener badge HTML para un rol
    function getRoleBadge(roleName) {
        switch(roleName) {
            case 'administrador':
                return '<span class="badge bg-secondary">Administrador</span>';
            case 'operador':
                return '<span class="badge bg-info">Operador</span>';
            case 'taxi':
                return '<span class="badge bg-success">Conductor</span>';
            case 'usuario':
                return '<span class="badge bg-warning text-dark">Pasajero</span>';
            default:
                return `<span class="badge bg-light text-dark">${roleName}</span>`;
        }
    }

    // Actualizar contadores de usuarios
    function updateCounters(users) {
        let totalUsers = users.length;
        let totalDrivers = users.filter(u => u.roles && u.roles.some(r => r.name.toLowerCase() === 'taxi')).length;
        let totalPassengers = users.filter(u => u.roles && u.roles.some(r => r.name.toLowerCase() === 'usuario')).length;
        let totalOperators = users.filter(u => u.roles && u.roles.some(r => r.name.toLowerCase() === 'operador')).length;

        document.querySelector('.card.bg-info h2').textContent = totalUsers;
        document.querySelector('.card.bg-success h2').textContent = totalDrivers;
        document.querySelector('.card.bg-warning h2').textContent = totalPassengers;
        document.querySelector('.card.bg-secondary h2').textContent = totalOperators;
    }

    // Filtrar usuarios por rol
    document.querySelectorAll('.filter-btn').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            const rows = document.querySelectorAll('#usersTableBody tr');

            // Resaltar botón activo
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');

            // Filtrar filas
            rows.forEach(row => {
                if (filter === 'all' || row.getAttribute('data-role') === filter) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });

    // Búsqueda de usuarios
    document.getElementById('searchButton').addEventListener('click', function() {
        const searchTerm = document.getElementById('searchUser').value.toLowerCase();
        const rows = document.querySelectorAll('#usersTableBody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // También buscar al presionar Enter
    document.getElementById('searchUser').addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            document.getElementById('searchButton').click();
        }
    });

    // Ver detalles del usuario
    async function viewUser(userId) {
        try {
            const response = await fetchWithAuth(`/api/v1/users/${userId}`);
            if (!response.ok) {
                throw new Error('Error al cargar datos del usuario');
            }

            const user = await response.json();

            // Mostrar detalles en un modal o alerta
            alert(`
                ID: ${user.id}
                Nombre: ${user.full_name || '-'}
                Email: ${user.email}
                Teléfono: ${user.phone_number || '-'}
                Rol: ${user.roles && user.roles.length > 0 ? user.roles[0].name : 'Sin rol'}
                Estado: ${user.is_active ? 'Activo' : 'Inactivo'}
            `);
        } catch (error) {
            console.error('Error:', error);
            alert('Error al cargar datos del usuario: ' + error.message);
        }
    }

    // Editar usuario
    async function editUser(userId) {
        try {
            const response = await fetchWithAuth(`/api/v1/users/${userId}`);
            if (!response.ok) {
                throw new Error('Error al cargar datos del usuario');
            }

            const user = await response.json();

            // Establecer modo de edición
            isEditing = true;
            currentUserId = userId;

            // Llenar el formulario con los datos del usuario
            document.getElementById('fullName').value = user.full_name || '';
            document.getElementById('email').value = user.email || '';
            document.getElementById('phoneNumber').value = user.phone_number || '';
            document.getElementById('isActive').checked = user.is_active;

            // Seleccionar el rol principal
            if (user.roles && user.roles.length > 0) {
                const roleName = user.roles[0].name.toLowerCase();
                document.getElementById('userRole').value = roleName;
            } else {
                document.getElementById('userRole').value = '';
            }

            // Limpiar campos de contraseña
            document.getElementById('password').value = '';
            document.getElementById('confirmPassword').value = '';

            // Cambiar título del modal
            document.getElementById('addUserModalLabel').textContent = 'Editar Usuario';

            // Mostrar modal
            new bootstrap.Modal(document.getElementById('addUserModal')).show();
        } catch (error) {
            console.error('Error:', error);
            alert('Error al cargar datos del usuario: ' + error.message);
        }
    }

    // Eliminar usuario
    async function deleteUser(userId) {
        if (!confirm('¿Está seguro de que desea eliminar este usuario?')) {
            return;
        }

        try {
            const response = await fetchWithAuth(`/api/v1/users/${userId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error al eliminar usuario');
            }

            alert('Usuario eliminado correctamente');
            loadUsers(); // Recargar lista de usuarios
        } catch (error) {
            console.error('Error:', error);
            alert('Error al eliminar usuario: ' + error.message);
        }
    }

    // Preparar modal para nuevo usuario
    document.querySelector('button[data-bs-target="#addUserModal"]').addEventListener('click', function() {
        // Establecer modo de creación
        isEditing = false;
        currentUserId = null;

        // Limpiar formulario
        document.getElementById('userForm').reset();

        // Cambiar título del modal
        document.getElementById('addUserModalLabel').textContent = 'Nuevo Usuario';
    });

    // Guardar usuario (nuevo o editado)
    document.getElementById('saveUserBtn').addEventListener('click', async function() {
        // Validar formulario
        const form = document.getElementById('userForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Verificar que las contraseñas coincidan
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (password && password !== confirmPassword) {
            alert('Las contraseñas no coinciden');
            return;
        }

        // Recopilar datos del formulario
        const userData = {
            full_name: document.getElementById('fullName').value,
            email: document.getElementById('email').value,
            phone_number: document.getElementById('phoneNumber').value,
            is_active: document.getElementById('isActive').checked
        };

        // Añadir contraseña si se ha proporcionado
        if (password) {
            userData.password = password;
        }

        // Añadir rol si se ha seleccionado
        const selectedRole = document.getElementById('userRole').value;
        if (selectedRole) {
            userData.roles = [selectedRole.toUpperCase()];
        }

        try {
            let response;

            if (isEditing) {
                // Actualizar usuario existente
                response = await fetchWithAuth(`/api/v1/users/${currentUserId}`, {
                    method: 'PUT',
                    body: JSON.stringify(userData)
                });
            } else {
                // Crear nuevo usuario
                if (!password) {
                    alert('La contraseña es obligatoria para nuevos usuarios');
                    return;
                }

                response = await fetchWithAuth('/api/v1/users/', {
                    method: 'POST',
                    body: JSON.stringify(userData)
                });
            }

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error al guardar usuario');
            }

            alert(isEditing ? 'Usuario actualizado correctamente' : 'Usuario creado correctamente');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            loadUsers(); // Recargar lista de usuarios
        } catch (error) {
            console.error('Error:', error);
            alert('Error al guardar usuario: ' + error.message);
        }
    });

    // Cargar usuarios al iniciar la página
    document.addEventListener('DOMContentLoaded', function() {
        loadUsers();
    });
</script>
{% endblock %}
