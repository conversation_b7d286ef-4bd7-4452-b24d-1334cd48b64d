import websocket
import json

# URL del WebSocket
ws_url = "wss://www.patagoniaservers.com.ar:5000/socket.io/?EIO=4&transport=websocket"

# Header de la cookie (reemplaza con el valor obtenido anteriormente)
cookie_header = "session=.eJwtzjkOwzAMBdG7qE5BUdS35csYNBckrR1XQe4eFakHeJhP2fOM61m293nHo-wvL1vhnunLWJitD6op4jIa0EhziB2QJWAeaFjrgCnXBkVYVNDqwS1nShVSF25xICkpSHqrfToTkKgW4omu2pkDagHqhwPDyhy5rzj_N-X7A4wILww.ZrFrsA.0vyH8L5Q5Rl5y9CktchqM9-NfcA"

# Inicializar WebSocket
ws = websocket.WebSocket()

try:
    # Conectar al WebSocket
    ws.connect(ws_url, header=[f"Cookie: {cookie_header}"])
    print("Conexión WebSocket establecida")
    
    # Enviar un mensaje de conexión de usuario
    message = json.dumps({"type": "user_connected", "user": "viedma", "node_id": 41})
    ws.send(message)
    print("Mensaje enviado:", message)

    # Recibir mensajes del servidor
    while True:
        response = ws.recv()
        print(f"Mensaje recibido: {response}")
except Exception as e:
    print(f"Error en la conexión WebSocket: {e}")
finally:
    ws.close()
