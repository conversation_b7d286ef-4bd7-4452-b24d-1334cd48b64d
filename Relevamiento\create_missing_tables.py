#!/usr/bin/env python3
# --- Archivo: create_missing_tables.py ---
# Script para crear las tablas faltantes en la base de datos

import sqlite3
import os

def create_missing_tables():
    """Crear las tablas que faltan en la base de datos."""
    print("🔧 Creando tablas faltantes...")
    
    try:
        conn = sqlite3.connect('instance/app.db')
        cursor = conn.cursor()
        
        # Verificar qué tablas existen
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas existentes: {existing_tables}")
        
        tables_created = 0
        
        # Crear tabla point si no existe
        if 'point' not in existing_tables:
            cursor.execute('''
                CREATE TABLE point (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100),
                    latitude FLOAT,
                    longitude FLOAT,
                    status VARCHAR(20) DEFAULT 'azul',
                    city VARCHAR(100),
                    source VARCHAR(100),
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            ''')
            print("✅ Tabla point creada")
            tables_created += 1
        else:
            print("ℹ️  Tabla point ya existe")
        
        # Crear tabla image si no existe
        if 'image' not in existing_tables:
            cursor.execute('''
                CREATE TABLE image (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    filename VARCHAR(255) NOT NULL,
                    point_id INTEGER,
                    user_id INTEGER,
                    annotations_json TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (point_id) REFERENCES point (id),
                    FOREIGN KEY (user_id) REFERENCES user (id)
                );
            ''')
            print("✅ Tabla image creada")
            tables_created += 1
        else:
            print("ℹ️  Tabla image ya existe")
        
        # Crear tabla camera si no existe
        if 'camera' not in existing_tables:
            cursor.execute('''
                CREATE TABLE camera (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    point_id INTEGER,
                    type VARCHAR(20) DEFAULT 'otra',
                    direction FLOAT,
                    photo_filename VARCHAR(255),
                    latitude FLOAT,
                    longitude FLOAT,
                    location_source VARCHAR(50),
                    location_accuracy FLOAT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (point_id) REFERENCES point (id)
                );
            ''')
            print("✅ Tabla camera creada")
            tables_created += 1
        else:
            print("ℹ️  Tabla camera ya existe")
        
        # Crear tablas de permisos si no existen
        if 'user_city_permissions' not in existing_tables:
            cursor.execute('''
                CREATE TABLE user_city_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    city VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, city)
                );
            ''')
            print("✅ Tabla user_city_permissions creada")
            tables_created += 1
        else:
            print("ℹ️  Tabla user_city_permissions ya existe")
        
        if 'user_source_permissions' not in existing_tables:
            cursor.execute('''
                CREATE TABLE user_source_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    source VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, source)
                );
            ''')
            print("✅ Tabla user_source_permissions creada")
            tables_created += 1
        else:
            print("ℹ️  Tabla user_source_permissions ya existe")
        
        if 'user_permissions' not in existing_tables:
            cursor.execute('''
                CREATE TABLE user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    permission_type VARCHAR(50) NOT NULL,
                    permission_value BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, permission_type)
                );
            ''')
            print("✅ Tabla user_permissions creada")
            tables_created += 1
        else:
            print("ℹ️  Tabla user_permissions ya existe")
        
        # Crear índices
        indexes = [
            ("idx_point_city", "point", "city"),
            ("idx_point_source", "point", "source"),
            ("idx_point_status", "point", "status"),
            ("idx_image_point_id", "image", "point_id"),
            ("idx_image_user_id", "image", "user_id"),
            ("idx_camera_point_id", "camera", "point_id"),
            ("idx_camera_type", "camera", "type"),
            ("idx_user_city_permissions_user_id", "user_city_permissions", "user_id"),
            ("idx_user_city_permissions_city", "user_city_permissions", "city"),
            ("idx_user_source_permissions_user_id", "user_source_permissions", "user_id"),
            ("idx_user_source_permissions_source", "user_source_permissions", "source"),
            ("idx_user_permissions_user_id", "user_permissions", "user_id"),
            ("idx_user_permissions_type", "user_permissions", "permission_type")
        ]
        
        indexes_created = 0
        for index_name, table_name, column_name in indexes:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name});")
                indexes_created += 1
            except Exception as e:
                print(f"⚠️  Error creando índice {index_name}: {e}")
        
        print(f"✅ {indexes_created} índices creados")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Proceso completado: {tables_created} tablas creadas")
        
        # Verificar resultado
        conn = sqlite3.connect('instance/app.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        final_tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas finales: {final_tables}")
        
        # Contar registros
        for table in ['user', 'point', 'image', 'camera']:
            if table in final_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                print(f"  📊 {table}: {count} registros")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creando tablas: {e}")
        return False

def main():
    """Función principal."""
    print("🔧 Creador de Tablas Faltantes")
    print("=" * 40)
    
    if create_missing_tables():
        print("\n🎉 ¡Tablas creadas exitosamente!")
        print("\n🚀 Próximos pasos:")
        print("   1. Reiniciar aplicación: systemctl restart relevamiento")
        print("   2. Verificar funcionamiento: python3 test_login.py")
        print("   3. Crear templates HTML para gestión de usuarios")
    else:
        print("\n❌ Error creando tablas")

if __name__ == "__main__":
    main()
