import os
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from dotenv import load_dotenv

# Cargar variables de entorno desde .env
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
load_dotenv(os.path.join(BASE_DIR, ".env"))

# Importar tus modelos para que Alembic los conozca
# Aseg�rate de que la ruta de importaci�n sea correcta seg�n tu estructura
from app.db.base_class import Base # Base de SQLAlchemy
import app.models # Importa todos los modelos a través del __init__.py

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interprete el archivo .ini para el logging de Python.
# Esta l�nea b�sicamente configura los loggers solo con la directiva de Alembic.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata
# target_metadata = None

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

def get_url():
    return os.getenv("DATABASE_URL")

def include_object(object, name, type_, reflected, compare_to):
    """
    Función para filtrar objetos en las migraciones automáticas.
    Excluye tablas y otros objetos relacionados con PostGIS y otras extensiones.
    """
    # Lista de prefijos de tablas a ignorar
    ignored_prefixes = [
        'spatial_ref_sys', 'topology', 'layer', 'tiger_', 'pagc_',
        'addr', 'addrfeat', 'bg', 'county', 'cousub', 'edges', 'faces',
        'featnames', 'place', 'state', 'tabblock', 'tract', 'zcta5',
        'zip_', 'direction_', 'secondary_', 'street_', 'countysub_', 'county_', 'place_'
    ]

    # Ignorar tablas específicas
    ignored_tables = {
        'spatial_ref_sys', 'topology', 'layer', 'geocode_settings',
        'geocode_settings_default', 'loader_lookuptables', 'loader_platform',
        'loader_variables', 'tabblock20'
    }

    if type_ == 'table':
        # Ignorar tablas específicas
        if name in ignored_tables:
            return False

        # Ignorar tablas con ciertos prefijos
        for prefix in ignored_prefixes:
            if name.startswith(prefix):
                return False

    # Incluir todos los demás objetos
    return True

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_url() # Usar la URL de la variable de entorno
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=include_object,  # Usar la función de filtrado
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = get_url() # Usar la URL de la variable de entorno
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_object=include_object,  # Usar la función de filtrado
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
