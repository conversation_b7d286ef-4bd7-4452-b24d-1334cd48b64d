from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
from datetime import datetime, timezone
import logging

from app.api import deps
from app.schemas import trip_schema
from app.schemas import trip_stop_schema
from app.models.user import User
from app.models.trip import TripStatusEnum
from app.models.trip_stop import TripStopStatusEnum
from app.services import trip_service
from app.websockets.connection import connection_manager

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=trip_schema.Trip, status_code=status.HTTP_201_CREATED)
def request_trip(
    trip_in: trip_schema.TripCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Solicitar un nuevo viaje. Solo los pasajeros pueden solicitar viajes.
    """
    # Verificar si el usuario tiene rol de pasajero
    is_passenger = False
    try:
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'usuario'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_passenger = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de pasajero: {e}")

    if not is_passenger:
        raise HTTPException(
            status_code=403,
            detail="Solo los pasajeros pueden solicitar viajes"
        )

    # Crear el viaje
    trip = trip_service.create_trip(db=db, trip_in=trip_in, passenger_id=current_user.id)

    # Buscar conductores cercanos (implementación simplificada)
    nearby_drivers = trip_service.find_nearby_drivers(
        db=db,
        latitude=trip.origin_latitude,
        longitude=trip.origin_longitude
    )

    if not nearby_drivers:
        # No hay conductores disponibles, actualizar el estado del viaje
        trip_update = trip_schema.TripUpdate(status=TripStatusEnum.NO_DISPONIBLE)
        trip = trip_service.update_trip(db=db, trip_id=trip.id, trip_update=trip_update)

        raise HTTPException(
            status_code=404,
            detail="No hay conductores disponibles en este momento"
        )

    # En una implementación real, aquí se enviarían notificaciones a los conductores
    # y se esperaría a que alguno acepte el viaje

    return trip

@router.get("/", response_model=List[trip_schema.Trip])
def list_trips(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_or_operator_user)
):
    """
    Listar viajes. Solo administradores y operadores pueden ver todos los viajes.
    """
    if status:
        try:
            status_enum = TripStatusEnum(status)
            # Implementar filtro por estado
            trips = [trip for trip in trip_service.get_trips(db, skip, limit) if trip.status == status_enum]
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Estado no válido. Valores permitidos: {[e.value for e in TripStatusEnum]}"
            )
    else:
        trips = trip_service.get_trips(db, skip, limit)

    return trips

@router.get("/active", response_model=List[trip_schema.Trip])
def list_active_trips(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_or_operator_user)
):
    """
    Listar viajes activos. Solo administradores y operadores pueden ver todos los viajes activos.
    """
    return trip_service.get_active_trips(db, skip, limit)

@router.get("/my-trips", response_model=List[trip_schema.Trip])
def list_my_trips(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Listar los viajes del usuario actual, ya sea como pasajero o como conductor.
    """
    # Verificar si el usuario tiene rol de pasajero o conductor
    is_passenger = False
    is_driver = False
    try:
        # Verificar si el usuario tiene rol de pasajero
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'usuario'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_passenger = role_query.first() is not None

        # Verificar si el usuario tiene rol de conductor
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'taxi'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_driver = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar roles: {e}")

    if is_passenger:
        return trip_service.get_trips_by_passenger(db, current_user.id, skip, limit)
    elif is_driver:
        return trip_service.get_trips_by_driver(db, current_user.id, skip, limit)
    else:
        # Si no es ni pasajero ni conductor, devolver lista vacía
        return []

@router.get("/{trip_id}", response_model=trip_schema.Trip)
def get_trip(
    trip_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Obtener detalles de un viaje específico.
    """
    trip = trip_service.get_trip(db, trip_id)
    if not trip:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    # Verificar si el usuario tiene permisos para ver este viaje
    is_admin_or_operator = False
    try:
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) IN ('administrador', 'operador')
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_admin_or_operator = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de admin/operador: {e}")

    # Solo el pasajero, el conductor, o un admin/operador pueden ver el viaje
    if not (is_admin_or_operator or trip.passenger_id == current_user.id or trip.driver_id == current_user.id):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos para ver este viaje"
        )

    return trip

@router.put("/{trip_id}", response_model=trip_schema.Trip)
def update_trip_status(
    trip_id: int,
    trip_update: trip_schema.TripUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Actualizar el estado de un viaje.
    """
    # Obtener el viaje
    trip = trip_service.get_trip(db, trip_id)
    if not trip:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    # Verificar permisos según el tipo de actualización
    is_admin_or_operator = False
    is_driver = False
    is_passenger = False

    try:
        # Verificar si el usuario es admin/operador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) IN ('administrador', 'operador')
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_admin_or_operator = role_query.first() is not None

        # Verificar si el usuario es conductor
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'taxi'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_driver = role_query.first() is not None

        # Verificar si el usuario es pasajero
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'usuario'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_passenger = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar roles: {e}")

    # Verificar permisos según el cambio de estado
    if trip_update.status:
        new_status = trip_update.status

        # Aceptar viaje: solo conductores o admin/operador
        if new_status == TripStatusEnum.ACEPTADO:
            if not (is_admin_or_operator or (is_driver and trip.driver_id is None)):
                raise HTTPException(
                    status_code=403,
                    detail="No tienes permisos para aceptar este viaje"
                )

            # Si es conductor, asignar su ID
            if is_driver and not is_admin_or_operator:
                trip_update.driver_id = current_user.id

                # Buscar vehículo del conductor
                from app.services import vehicle_service
                vehicles = vehicle_service.get_vehicles_by_driver(db, current_user.id)
                if vehicles:
                    trip_update.vehicle_id = vehicles[0].id

        # Cancelar viaje como pasajero
        elif new_status == TripStatusEnum.CANCELADO_PASAJERO:
            if not (is_admin_or_operator or (is_passenger and trip.passenger_id == current_user.id)):
                raise HTTPException(
                    status_code=403,
                    detail="No tienes permisos para cancelar este viaje como pasajero"
                )

        # Cancelar viaje como conductor
        elif new_status == TripStatusEnum.CANCELADO_CONDUCTOR:
            if not (is_admin_or_operator or (is_driver and trip.driver_id == current_user.id)):
                raise HTTPException(
                    status_code=403,
                    detail="No tienes permisos para cancelar este viaje como conductor"
                )

        # Otros cambios de estado
        else:
            if not (is_admin_or_operator or
                   (is_driver and trip.driver_id == current_user.id) or
                   (is_passenger and trip.passenger_id == current_user.id)):
                raise HTTPException(
                    status_code=403,
                    detail="No tienes permisos para actualizar este viaje"
                )

    # Actualizar el viaje
    updated_trip = trip_service.update_trip(db, trip_id, trip_update)
    if not updated_trip:
        raise HTTPException(
            status_code=400,
            detail="No se pudo actualizar el viaje"
        )

    return updated_trip

@router.post("/{trip_id}/rate", response_model=trip_schema.Trip)
def rate_trip(
    trip_id: int,
    rating: int,
    comment: Optional[str] = None,
    is_passenger_rating: bool = True,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Calificar un viaje, ya sea como pasajero o como conductor.
    """
    # Obtener el viaje
    trip = trip_service.get_trip(db, trip_id)
    if not trip:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    # Verificar que el viaje esté completado
    if trip.status != TripStatusEnum.COMPLETADO:
        raise HTTPException(
            status_code=400,
            detail="Solo se pueden calificar viajes completados"
        )

    # Verificar permisos
    if is_passenger_rating and trip.passenger_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Solo el pasajero puede calificar al conductor"
        )

    if not is_passenger_rating and trip.driver_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Solo el conductor puede calificar al pasajero"
        )

    # Verificar que la calificación sea válida (1-5)
    if rating < 1 or rating > 5:
        raise HTTPException(
            status_code=400,
            detail="La calificación debe estar entre 1 y 5"
        )

    # Calificar el viaje
    rated_trip = trip_service.rate_trip(
        db=db,
        trip_id=trip_id,
        is_passenger_rating=is_passenger_rating,
        rating=rating,
        comment=comment
    )

    if not rated_trip:
        raise HTTPException(
            status_code=400,
            detail="No se pudo calificar el viaje"
        )

    return rated_trip

@router.get("/{trip_id}/stops", response_model=List[trip_stop_schema.TripStop])
def get_trip_stops(
    trip_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Obtener las paradas intermedias de un viaje.
    """
    # Obtener el viaje
    trip = trip_service.get_trip(db, trip_id)
    if not trip:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    # Verificar permisos
    is_admin_or_operator = False
    try:
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) IN ('administrador', 'operador')
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_admin_or_operator = role_query.first() is not None
    except Exception as e:
        logger.error(f"Error al verificar rol de admin/operador: {e}")

    # Solo el pasajero, el conductor, o un admin/operador pueden ver las paradas
    if not (is_admin_or_operator or trip.passenger_id == current_user.id or trip.driver_id == current_user.id):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos para ver las paradas de este viaje"
        )

    # Obtener las paradas
    from app.models.trip_stop import TripStop
    stops = db.query(TripStop).filter(TripStop.trip_id == trip_id).order_by(TripStop.order).all()

    return stops

@router.post("/{trip_id}/stops", response_model=trip_stop_schema.TripStop)
def add_trip_stop(
    trip_id: int,
    stop_in: trip_stop_schema.TripStopCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Agregar una parada intermedia a un viaje.
    """
    # Obtener el viaje
    trip = trip_service.get_trip(db, trip_id)
    if not trip:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    # Verificar permisos
    is_admin_or_operator = False
    try:
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) IN ('administrador', 'operador')
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_admin_or_operator = role_query.first() is not None
    except Exception as e:
        logger.error(f"Error al verificar rol de admin/operador: {e}")

    # Solo el pasajero o un admin/operador pueden agregar paradas
    if not (is_admin_or_operator or trip.passenger_id == current_user.id):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos para agregar paradas a este viaje"
        )

    # Verificar que el viaje no esté completado o cancelado
    if trip.status in [TripStatusEnum.COMPLETADO, TripStatusEnum.CANCELADO_PASAJERO, TripStatusEnum.CANCELADO_CONDUCTOR]:
        raise HTTPException(
            status_code=400,
            detail="No se pueden agregar paradas a un viaje completado o cancelado"
        )

    # Crear la parada
    from app.models.trip_stop import TripStop, TripStopStatusEnum

    # Obtener el último orden
    last_stop = db.query(TripStop).filter(TripStop.trip_id == trip_id).order_by(TripStop.order.desc()).first()
    next_order = (last_stop.order + 1) if last_stop else 1

    # Crear la parada
    db_stop = TripStop(
        trip_id=trip_id,
        order=stop_in.order if stop_in.order else next_order,
        latitude=stop_in.latitude,
        longitude=stop_in.longitude,
        address=stop_in.address,
        status=TripStopStatusEnum.PENDIENTE,
        wait_time_seconds=stop_in.wait_time_seconds if stop_in.wait_time_seconds else 60,
        notes=stop_in.notes
    )

    # Guardar en la base de datos
    db.add(db_stop)
    db.commit()
    db.refresh(db_stop)

    # Actualizar el viaje para indicar que tiene paradas
    trip.has_stops = True
    db.commit()

    return db_stop
