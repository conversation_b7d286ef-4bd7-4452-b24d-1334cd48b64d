# File: backend/apps/chat/views.py
# -----------------------------------------------

import os
import uuid
import logging
import base64
import json
from datetime import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponseForbidden
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.conf import settings
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.incidents.models import Incidencia
from .models import MensajeChat

logger = logging.getLogger(__name__)

@login_required
def chat_incidencia_view(request, incidencia_id):
    """
    Vista para mostrar el chat de una incidencia
    """
    incidencia = get_object_or_404(Incidencia, id=incidencia_id)

    # Verificar permisos
    user = request.user
    if not tiene_permiso_chat(user, incidencia):
        return HttpResponseForbidden("No tienes permiso para acceder a este chat")

    # Marcar mensajes como leídos
    mensajes = MensajeChat.objects.filter(incidencia=incidencia).exclude(remitente=user)
    mensajes.filter(leido=False).update(leido=True)

    context = {
        'incidencia': incidencia,
        'mensajes': MensajeChat.objects.filter(incidencia=incidencia).order_by('fecha_envio'),
        'user': user,
    }

    return render(request, 'chat/chat_incidencia.html', context)

@login_required  # Usar login_required en lugar de permission_classes
@csrf_exempt  # Eximir de la verificación CSRF para solucionar problemas de autenticación
def guardar_audio_view(request):
    """
    API para guardar un archivo de audio

    Esta vista está exenta de la verificación CSRF para permitir solicitudes desde cualquier origen.
    La autenticación se maneja a través de la sesión de Django.
    """
    # No es necesario verificar la autenticación, ya que login_required lo hace automáticamente
    # Verificar si la solicitud es AJAX
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
    logger.info(f"Solicitud recibida de {request.user.username}, AJAX: {is_ajax}, Método: {request.method}")
    try:
        logger.info(f"Recibida solicitud para guardar audio de usuario {request.user.username}")

        # Obtener datos de la solicitud (JSON o formulario)
        if request.method == 'POST':
            if request.content_type and 'application/json' in request.content_type:
                try:
                    import json
                    data = json.loads(request.body)
                    logger.info("Datos recibidos como JSON")
                except json.JSONDecodeError:
                    logger.error("Error al decodificar JSON")
                    return JsonResponse({'error': 'Formato de datos incorrecto'}, status=400)
            else:
                data = request.POST
                logger.info("Datos recibidos como formulario")

            incidencia_id = data.get('incidencia_id')
            audio_data = data.get('audio_data')
        else:
            logger.error(f"Método no permitido: {request.method}")
            return JsonResponse({'error': 'Método no permitido'}, status=405)

        logger.info(f"Datos recibidos - incidencia_id: {incidencia_id}, audio_data: {'Presente' if audio_data else 'Ausente'}")

        if not incidencia_id or not audio_data:
            logger.error(f"Faltan datos requeridos - incidencia_id: {bool(incidencia_id)}, audio_data: {bool(audio_data)}")
            return JsonResponse({'error': 'Faltan datos requeridos'}, status=400)

        # Verificar que la incidencia exista
        try:
            from apps.incidents.models import Incidencia
            incidencia = get_object_or_404(Incidencia, id=incidencia_id)
            logger.info(f"Incidencia encontrada: #{incidencia.id}")
        except Exception as e:
            logger.error(f"Error al buscar incidencia {incidencia_id}: {str(e)}")
            return JsonResponse({'error': f'Incidencia no encontrada: {str(e)}'}, status=404)

        # Verificar permisos
        if not tiene_permiso_chat(request.user, incidencia):
            logger.error(f"Usuario {request.user.username} no tiene permiso para enviar mensajes en la incidencia {incidencia_id}")
            return JsonResponse({'error': 'No tienes permiso para enviar mensajes en este chat'}, status=403)

        # Decodificar el audio en base64
        try:
            # Verificar que audio_data sea una cadena
            if not isinstance(audio_data, str):
                logger.error(f"audio_data no es una cadena: {type(audio_data)}")
                return JsonResponse({'error': 'Formato de audio incorrecto'}, status=400)

            # Eliminar el prefijo 'data:audio/webm;base64,' si existe
            if ';base64,' in audio_data:
                prefix = audio_data.split(';base64,')[0]
                audio_data = audio_data.split(';base64,')[1]
                logger.info(f"Prefijo detectado: {prefix}")
            else:
                logger.warning("No se encontró el prefijo base64 en los datos de audio")

            # Verificar que audio_data tenga contenido después de procesar
            if not audio_data:
                logger.error("audio_data está vacío después de procesar")
                return JsonResponse({'error': 'Datos de audio vacíos'}, status=400)

            logger.info(f"Longitud de audio_data antes de decodificar: {len(audio_data)}")

            # Decodificar los datos
            try:
                audio_bytes = base64.b64decode(audio_data)
                logger.info(f"Audio decodificado correctamente, tamaño: {len(audio_bytes)} bytes")
            except Exception as e:
                logger.error(f"Error específico en b64decode: {str(e)}")
                return JsonResponse({'error': f'Error en b64decode: {str(e)}'}, status=400)

        except Exception as e:
            logger.error(f"Error general al decodificar audio: {str(e)}")
            return JsonResponse({'error': f'Error al decodificar el audio: {str(e)}'}, status=400)

        # Crear directorio para audios si no existe
        audio_dir = os.path.join(settings.MEDIA_ROOT, 'audios')

        # Verificar si se puede escribir en el directorio
        try:
            os.makedirs(audio_dir, exist_ok=True)
            # Verificar permisos de escritura
            test_file = os.path.join(audio_dir, 'test_write.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            logger.info(f"Directorio de audios con permisos de escritura: {audio_dir}")
        except (IOError, PermissionError) as e:
            # Si no se puede escribir, usar un directorio temporal
            import tempfile
            audio_dir = tempfile.gettempdir()
            logger.warning(f"No se puede escribir en {settings.MEDIA_ROOT}/audios, usando directorio temporal: {audio_dir}")

        # Asegurarse de que el directorio exista
        os.makedirs(audio_dir, exist_ok=True)

        # Log para depuración
        logger.info(f"Directorio de audios: {audio_dir}")

        # Determinar la extensión del archivo basado en los datos
        extension = 'mp3'  # Preferimos MP3 por su mejor compatibilidad y menor tamaño
        if 'data:audio/' in audio_data:
            mime_type = audio_data.split('data:audio/')[1].split(';')[0]
            if mime_type in ['webm', 'mp3', 'mp4', 'ogg', 'wav']:
                extension = mime_type
            logger.info(f"Tipo MIME detectado: {mime_type}")

        # Intentar convertir a MP3 si no es MP3 y ffmpeg está disponible
        if extension != 'mp3':
            try:
                import shutil
                if shutil.which('ffmpeg'):
                    logger.info(f"Intentando convertir audio a MP3 para reducir tamaño")
                    # Guardar el archivo original temporalmente
                    import tempfile
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f'.{extension}')
                    temp_file.write(audio_bytes)
                    temp_file.close()

                    # Crear un archivo temporal para el MP3
                    mp3_temp = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                    mp3_temp.close()

                    # Convertir usando ffmpeg
                    import subprocess
                    result = subprocess.run([
                        'ffmpeg', '-i', temp_file.name,
                        '-vn', '-ar', '44100', '-ac', '2', '-b:a', '96k',
                        mp3_temp.name
                    ], capture_output=True)

                    if result.returncode == 0:
                        # Leer el archivo MP3 convertido
                        with open(mp3_temp.name, 'rb') as f:
                            audio_bytes = f.read()
                        extension = 'mp3'
                        logger.info(f"Conversión a MP3 exitosa, nuevo tamaño: {len(audio_bytes)} bytes")
                    else:
                        logger.warning(f"Error al convertir a MP3: {result.stderr.decode()}")

                    # Limpiar archivos temporales
                    os.unlink(temp_file.name)
                    os.unlink(mp3_temp.name)
                else:
                    logger.info("ffmpeg no disponible, no se puede convertir a MP3")
            except Exception as e:
                logger.error(f"Error al convertir audio a MP3: {str(e)}")
                # Continuar con el formato original

        # Obtener información del usuario y la incidencia para organizar los archivos
        try:
            from apps.locations.models import Ciudad, UnidadRegional

            # Intentar obtener la ciudad del usuario
            ciudad_nombre = "Sin_Ciudad"
            unidad_regional_nombre = "Sin_UR"

            # Intentar obtener la ciudad de la incidencia
            try:
                if hasattr(incidencia, 'ciudad_reportada') and incidencia.ciudad_reportada:
                    ciudad = incidencia.ciudad_reportada
                    ciudad_nombre = ciudad.nombre.replace(' ', '_')
                    unidad_regional_nombre = ciudad.unidad_regional.nombre.replace(' ', '_')
            except Exception as e:
                logger.warning(f"No se pudo obtener la ciudad de la incidencia: {str(e)}")

            # Si no se pudo obtener de la incidencia, intentar obtenerla del usuario
            if ciudad_nombre == "Sin_Ciudad":
                try:
                    if hasattr(request.user, 'profile') and hasattr(request.user.profile, 'ciudad') and request.user.profile.ciudad:
                        ciudad = request.user.profile.ciudad
                        ciudad_nombre = ciudad.nombre.replace(' ', '_')
                        unidad_regional_nombre = ciudad.unidad_regional.nombre.replace(' ', '_')
                except Exception as e:
                    logger.warning(f"No se pudo obtener la ciudad del usuario: {str(e)}")

            # Crear estructura de directorios
            username = request.user.username.replace(' ', '_')
            fecha_actual = datetime.now().strftime('%Y-%m-%d')

            # Estructura: audios/UnidadRegional/Ciudad/Usuario/Fecha/
            audio_dir_estructura = os.path.join(
                audio_dir,
                unidad_regional_nombre,
                ciudad_nombre,
                username,
                fecha_actual
            )

            # Crear directorios si no existen
            os.makedirs(audio_dir_estructura, exist_ok=True)
            logger.info(f"Directorio creado: {audio_dir_estructura}")

            # Generar nombre único para el archivo
            hora_actual = datetime.now().strftime('%H-%M-%S')
            filename = f"audio_incidencia_{incidencia.id}_{hora_actual}_{uuid.uuid4().hex[:8]}.{extension}"
            filepath = os.path.join(audio_dir_estructura, filename)

            # Obtener el tipo MIME del prefijo
            mime_type = "audio/webm"  # Valor por defecto
            if 'prefix' in locals() and prefix:
                mime_type = prefix.replace('data:audio/', '')
                if ';' in mime_type:
                    mime_type = mime_type.split(';')[0]

            # Crear archivo de log
            log_filename = os.path.join(audio_dir_estructura, "audio_log.txt")
            with open(log_filename, 'a', encoding='utf-8') as log_file:
                log_entry = (
                    f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"Usuario: {request.user.username} (ID: {request.user.id})\n"
                    f"Incidencia: #{incidencia.id}\n"
                    f"Archivo: {filename}\n"
                    f"Tamaño: {len(audio_bytes)} bytes\n"
                    f"Tipo MIME: {mime_type}\n"
                    f"--------------------------------------------------\n"
                )
                log_file.write(log_entry)

            logger.info(f"Registro de audio añadido al log: {log_filename}")

        except Exception as e:
            logger.error(f"Error al crear estructura de directorios: {str(e)}")
            # Si hay un error, usar la estructura simple
            filename = f"audio_{uuid.uuid4()}.{extension}"
            filepath = os.path.join(audio_dir, filename)

        logger.info(f"Guardando audio como: {filename}")

        # Guardar el archivo
        try:
            with open(filepath, 'wb') as f:
                f.write(audio_bytes)
            logger.info(f"Archivo de audio guardado correctamente en: {filepath}")

            # Verificar que el archivo existe
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                logger.info(f"Verificación: archivo existe en {filepath}, tamaño: {file_size} bytes")
            else:
                logger.error(f"Verificación: el archivo NO existe en {filepath}")

            # Verificar permisos
            try:
                with open(filepath, 'rb') as f:
                    test_read = f.read(10)
                    logger.info(f"Verificación: archivo se puede leer correctamente")
            except Exception as e:
                logger.error(f"Verificación: no se puede leer el archivo: {str(e)}")

        except Exception as e:
            logger.error(f"Error al guardar el archivo de audio: {str(e)}")
            return JsonResponse({'error': f'Error al guardar el archivo: {str(e)}'}, status=500)

        # Crear URL para el archivo
        # Usar siempre la URL simple para mayor compatibilidad
        audio_url = f"{settings.MEDIA_URL}audios/{filename}"

        # Crear un enlace simbólico en el directorio simple si se usó la estructura de directorios
        if 'audio_dir_estructura' in locals() and filepath.startswith(audio_dir_estructura):
            try:
                # Asegurarse de que el directorio simple existe
                simple_dir = os.path.join(settings.MEDIA_ROOT, 'audios')
                os.makedirs(simple_dir, exist_ok=True)

                # Crear un enlace simbólico o copiar el archivo
                simple_path = os.path.join(simple_dir, filename)

                # Si el archivo ya existe en el directorio simple, no hacer nada
                if os.path.exists(simple_path):
                    logger.info(f"El archivo ya existe en {simple_path}")
                else:
                    # Intentar crear un enlace simbólico
                    try:
                        os.symlink(filepath, simple_path)
                        logger.info(f"Enlace simbólico creado: {simple_path} -> {filepath}")
                    except (OSError, NotImplementedError):
                        # Si no se puede crear un enlace simbólico, copiar el archivo
                        import shutil
                        shutil.copy2(filepath, simple_path)
                        logger.info(f"Archivo copiado a {simple_path}")
            except Exception as e:
                logger.error(f"Error al crear enlace o copia: {str(e)}")

        # Verificar que la URL sea accesible
        import requests
        try:
            full_url = f"https://patagoniaservers.com.ar:5007{audio_url}"
            logger.info(f"Verificando URL completa: {full_url}")
            # No verificar certificados SSL para evitar problemas en desarrollo
            response = requests.head(full_url, verify=False, timeout=2)
            logger.info(f"Verificación de URL: {response.status_code}")
        except Exception as e:
            logger.error(f"Error al verificar URL: {str(e)}")

        logger.info(f"URL del audio: {audio_url}")

        # Guardar mensaje en la base de datos
        try:
            from apps.chat.models import MensajeChat
            mensaje = MensajeChat.objects.create(
                incidencia=incidencia,
                remitente=request.user,
                tipo='AUDIO',
                audio_url=audio_url
            )
            logger.info(f"Mensaje de audio creado correctamente, ID: {mensaje.id}")
        except Exception as e:
            logger.error(f"Error al crear el mensaje en la base de datos: {str(e)}")
            return JsonResponse({'error': f'Error al guardar el mensaje: {str(e)}'}, status=500)

        # Enviar notificación por WebSocket
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()

            # Preparar datos del mensaje para enviar por WebSocket
            mensaje_data = {
                'type': 'chat_message',
                'id': mensaje.id,
                'incidencia_id': incidencia.id,
                'remitente_id': request.user.id,
                'remitente_nombre': request.user.username,
                'tipo': 'AUDIO',
                'texto': '',
                'audio_url': audio_url,
                'fecha_envio': mensaje.fecha_envio.isoformat(),
                'leido': False,
                'exportado_brigada': False
            }

            # Enviar mensaje al grupo de chat
            async_to_sync(channel_layer.group_send)(
                f'chat_incidencia_{incidencia.id}',
                mensaje_data
            )

            logger.info(f"Notificación WebSocket enviada para el audio {mensaje.id}")
        except Exception as e:
            logger.error(f"Error al enviar notificación WebSocket: {str(e)}")

        logger.info(f"Audio guardado exitosamente para la incidencia {incidencia.id} por el usuario {request.user.username}")

        return JsonResponse({
            'success': True,
            'mensaje_id': mensaje.id,
            'audio_url': audio_url
        }, status=201)

    except Exception as e:
        logger.error(f"Error al guardar audio: {str(e)}")
        return JsonResponse({'error': f'Error al guardar el audio: {str(e)}'}, status=500)

@login_required  # Usar login_required en lugar de permission_classes
@csrf_exempt  # Eximir de la verificación CSRF para solucionar problemas de autenticación
def guardar_imagen_view(request):
    """
    API para guardar una imagen
    """
    # Verificar si el usuario está autenticado
    if not request.user.is_authenticated:
        logger.error(f"Usuario no autenticado intentando guardar imagen. Sesión: {request.session.session_key if hasattr(request, 'session') else 'No hay sesión'}")
        return JsonResponse({'error': 'No autenticado'}, status=401)

    # Registrar información de la solicitud para depuración
    logger.info(f"Solicitud recibida de {request.user.username}, AJAX: {request.is_ajax() if hasattr(request, 'is_ajax') else request.headers.get('X-Requested-With') == 'XMLHttpRequest'}, Método: {request.method}")
    logger.info(f"Recibida solicitud para guardar imagen de usuario {request.user.username}")

    try:
        # Obtener datos de la solicitud (manejar tanto JSON como form-data)
        if request.content_type and 'application/json' in request.content_type:
            logger.info("Datos recibidos como JSON")
            try:
                data = json.loads(request.body.decode('utf-8'))
                incidencia_id = data.get('incidencia_id')
                imagen_data = data.get('imagen_data')
            except json.JSONDecodeError:
                logger.error("Error al decodificar JSON")
                return JsonResponse({'error': 'Error al decodificar JSON'}, status=400)
        else:
            logger.info("Datos recibidos como form-data")
            incidencia_id = request.POST.get('incidencia_id')
            imagen_data = request.POST.get('imagen_data')

            # Si no hay datos en POST, intentar obtenerlos de FILES
            if not imagen_data and 'imagen' in request.FILES:
                logger.info("Imagen recibida como archivo")
                imagen_file = request.FILES['imagen']
                # Leer el archivo y convertirlo a base64
                import base64
                imagen_data = f"data:image/{imagen_file.content_type.split('/')[-1]};base64," + base64.b64encode(imagen_file.read()).decode('utf-8')

        logger.info(f"Datos recibidos - incidencia_id: {incidencia_id}, imagen_data: {'Presente' if imagen_data else 'Ausente'}")

        if not incidencia_id or not imagen_data:
            logger.error("Faltan datos requeridos")
            return JsonResponse({'error': 'Faltan datos requeridos'}, status=400)

        # Verificar que la incidencia exista
        incidencia = get_object_or_404(Incidencia, id=incidencia_id)

        # Verificar permisos
        if not tiene_permiso_chat(request.user, incidencia):
            return Response({'error': 'No tienes permiso para enviar mensajes en este chat'},
                           status=status.HTTP_403_FORBIDDEN)

        # Decodificar la imagen en base64
        try:
            # Determinar el formato de la imagen
            formato = 'jpeg'
            if 'data:image/' in imagen_data:
                formato_data = imagen_data.split('data:image/')[1].split(';')[0]
                if formato_data in ['png', 'jpeg', 'jpg', 'gif']:
                    formato = formato_data

            # Eliminar el prefijo 'data:image/xxx;base64,' si existe
            if ';base64,' in imagen_data:
                imagen_data = imagen_data.split(';base64,')[1]

            imagen_bytes = base64.b64decode(imagen_data)
        except Exception as e:
            logger.error(f"Error al decodificar imagen: {str(e)}")
            return Response({'error': 'Error al decodificar la imagen'},
                           status=status.HTTP_400_BAD_REQUEST)

        # Crear directorio para imágenes si no existe
        imagen_dir = os.path.join(settings.MEDIA_ROOT, 'imagenes')

        # Verificar si se puede escribir en el directorio
        try:
            os.makedirs(imagen_dir, exist_ok=True)
            # Verificar permisos de escritura
            test_file = os.path.join(imagen_dir, 'test_write.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            logger.info(f"Directorio de imágenes con permisos de escritura: {imagen_dir}")
        except (IOError, PermissionError) as e:
            # Si no se puede escribir, usar un directorio temporal
            import tempfile
            imagen_dir = tempfile.gettempdir()
            logger.warning(f"No se puede escribir en {settings.MEDIA_ROOT}/imagenes, usando directorio temporal: {imagen_dir}")

        # Asegurarse de que el directorio exista
        os.makedirs(imagen_dir, exist_ok=True)

        logger.info(f"Directorio de imágenes: {imagen_dir}")

        # Verificar permisos de escritura en el directorio
        try:
            test_file = os.path.join(imagen_dir, 'test_write.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            logger.info(f"Verificación: directorio de imágenes tiene permisos de escritura")
        except Exception as e:
            logger.error(f"Verificación: no se puede escribir en el directorio de imágenes: {str(e)}")
            return JsonResponse({'error': f'No se puede escribir en el directorio de imágenes: {str(e)}'}, status=500)

        # Obtener información del usuario y la incidencia para organizar los archivos
        try:
            from apps.locations.models import Ciudad, UnidadRegional

            # Intentar obtener la ciudad del usuario
            ciudad_nombre = "Sin_Ciudad"
            unidad_regional_nombre = "Sin_UR"

            # Intentar obtener la ciudad de la incidencia
            try:
                if hasattr(incidencia, 'ciudad_reportada') and incidencia.ciudad_reportada:
                    ciudad = incidencia.ciudad_reportada
                    ciudad_nombre = ciudad.nombre.replace(' ', '_')
                    unidad_regional_nombre = ciudad.unidad_regional.nombre.replace(' ', '_')
            except Exception as e:
                logger.warning(f"No se pudo obtener la ciudad de la incidencia: {str(e)}")

            # Si no se pudo obtener de la incidencia, intentar obtenerla del usuario
            if ciudad_nombre == "Sin_Ciudad":
                try:
                    if hasattr(request.user, 'profile') and hasattr(request.user.profile, 'ciudad') and request.user.profile.ciudad:
                        ciudad = request.user.profile.ciudad
                        ciudad_nombre = ciudad.nombre.replace(' ', '_')
                        unidad_regional_nombre = ciudad.unidad_regional.nombre.replace(' ', '_')
                except Exception as e:
                    logger.warning(f"No se pudo obtener la ciudad del usuario: {str(e)}")

            # Crear estructura de directorios
            username = request.user.username.replace(' ', '_')
            fecha_actual = datetime.now().strftime('%Y-%m-%d')

            # Estructura: imagenes/UnidadRegional/Ciudad/Usuario/Fecha/
            imagen_dir_estructura = os.path.join(
                imagen_dir,
                unidad_regional_nombre,
                ciudad_nombre,
                username,
                fecha_actual
            )

            # Crear directorios si no existen
            os.makedirs(imagen_dir_estructura, exist_ok=True)
            logger.info(f"Directorio creado: {imagen_dir_estructura}")

            # Generar nombre único para el archivo
            hora_actual = datetime.now().strftime('%H-%M-%S')
            filename = f"imagen_incidencia_{incidencia.id}_{hora_actual}_{uuid.uuid4().hex[:8]}.{formato}"
            filepath = os.path.join(imagen_dir_estructura, filename)

            # Crear archivo de log
            log_filename = os.path.join(imagen_dir_estructura, "imagen_log.txt")
            with open(log_filename, 'a', encoding='utf-8') as log_file:
                log_entry = (
                    f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"Usuario: {request.user.username} (ID: {request.user.id})\n"
                    f"Incidencia: #{incidencia.id}\n"
                    f"Archivo: {filename}\n"
                    f"Tamaño: {len(imagen_bytes)} bytes\n"
                    f"Formato: {formato}\n"
                    f"--------------------------------------------------\n"
                )
                log_file.write(log_entry)

            logger.info(f"Registro de imagen añadido al log: {log_filename}")

        except Exception as e:
            logger.error(f"Error al crear estructura de directorios para imagen: {str(e)}")
            # Si hay un error, usar la estructura simple
            filename = f"imagen_{uuid.uuid4()}.{formato}"
            filepath = os.path.join(imagen_dir, filename)

        logger.info(f"Guardando imagen como: {filename}")

        # Guardar el archivo
        try:
            with open(filepath, 'wb') as f:
                f.write(imagen_bytes)

            # Verificar que el archivo existe
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                logger.info(f"Verificación: archivo existe en {filepath}, tamaño: {file_size} bytes")
            else:
                logger.error(f"Verificación: el archivo NO existe en {filepath}")
                return JsonResponse({'error': f'No se pudo guardar el archivo en {filepath}'}, status=500)

            # Verificar permisos
            try:
                with open(filepath, 'rb') as f:
                    test_read = f.read(10)
                    logger.info(f"Verificación: archivo se puede leer correctamente")
            except Exception as e:
                logger.error(f"Verificación: no se puede leer el archivo: {str(e)}")
                return JsonResponse({'error': f'No se puede leer el archivo guardado: {str(e)}'}, status=500)
        except Exception as e:
            logger.error(f"Error al guardar el archivo de imagen: {str(e)}")
            return JsonResponse({'error': f'Error al guardar el archivo: {str(e)}'}, status=500)

        # Crear URL para el archivo
        # Usar siempre la URL simple para mayor compatibilidad
        imagen_url = f"{settings.MEDIA_URL}imagenes/{filename}"

        # Crear un enlace simbólico en el directorio simple si se usó la estructura de directorios
        if 'imagen_dir_estructura' in locals() and filepath.startswith(imagen_dir_estructura):
            try:
                # Asegurarse de que el directorio simple existe
                simple_dir = os.path.join(settings.MEDIA_ROOT, 'imagenes')
                os.makedirs(simple_dir, exist_ok=True)

                # Crear un enlace simbólico o copiar el archivo
                simple_path = os.path.join(simple_dir, filename)

                # Si el archivo ya existe en el directorio simple, no hacer nada
                if os.path.exists(simple_path):
                    logger.info(f"El archivo ya existe en {simple_path}")
                else:
                    # Intentar crear un enlace simbólico
                    try:
                        os.symlink(filepath, simple_path)
                        logger.info(f"Enlace simbólico creado: {simple_path} -> {filepath}")
                    except (OSError, NotImplementedError):
                        # Si no se puede crear un enlace simbólico, copiar el archivo
                        import shutil
                        shutil.copy2(filepath, simple_path)
                        logger.info(f"Archivo copiado a {simple_path}")
            except Exception as e:
                logger.error(f"Error al crear enlace o copia: {str(e)}")

        logger.info(f"URL de la imagen: {imagen_url}")

        # Guardar mensaje en la base de datos
        mensaje = MensajeChat.objects.create(
            incidencia=incidencia,
            remitente=request.user,
            tipo='IMAGEN',
            imagen_url=imagen_url
        )

        # Enviar notificación por WebSocket
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()

            # Preparar datos del mensaje para enviar por WebSocket
            mensaje_data = {
                'type': 'chat_message',
                'id': mensaje.id,
                'incidencia_id': incidencia.id,
                'remitente_id': request.user.id,
                'remitente_nombre': request.user.username,
                'tipo': 'IMAGEN',
                'texto': '',
                'imagen_url': imagen_url,
                'fecha_envio': mensaje.fecha_envio.isoformat(),
                'leido': False,
                'exportado_brigada': False
            }

            # Enviar mensaje al grupo de chat
            async_to_sync(channel_layer.group_send)(
                f'chat_incidencia_{incidencia.id}',
                mensaje_data
            )

            logger.info(f"Notificación WebSocket enviada para la imagen {mensaje.id}")
        except Exception as e:
            logger.error(f"Error al enviar notificación WebSocket: {str(e)}")

        logger.info(f"Imagen guardada exitosamente para la incidencia {incidencia.id} por el usuario {request.user.username}")

        return JsonResponse({
            'success': True,
            'mensaje_id': mensaje.id,
            'imagen_url': imagen_url
        }, status=201)

    except Exception as e:
        logger.error(f"Error al guardar imagen: {str(e)}")
        return JsonResponse({'error': f'Error al guardar la imagen: {str(e)}'}, status=500)

@login_required  # Usar login_required en lugar de permission_classes
@csrf_exempt  # Eximir de la verificación CSRF para solucionar problemas de autenticación
def guardar_ubicacion_view(request):
    # Verificar si el usuario está autenticado
    if not request.user.is_authenticated:
        logger.error(f"Usuario no autenticado intentando guardar ubicación. Sesión: {request.session.session_key if hasattr(request, 'session') else 'No hay sesión'}")
        return Response({'error': 'No autenticado'}, status=status.HTTP_401_UNAUTHORIZED)
    """
    API para guardar una ubicación
    """
    try:
        incidencia_id = request.data.get('incidencia_id')
        lat = request.data.get('lat')
        lng = request.data.get('lng')

        if not incidencia_id or lat is None or lng is None:
            return Response({'error': 'Faltan datos requeridos'}, status=status.HTTP_400_BAD_REQUEST)

        # Verificar que la incidencia exista
        incidencia = get_object_or_404(Incidencia, id=incidencia_id)

        # Verificar permisos
        if not tiene_permiso_chat(request.user, incidencia):
            return Response({'error': 'No tienes permiso para enviar mensajes en este chat'},
                           status=status.HTTP_403_FORBIDDEN)

        # Crear directorio para logs de ubicación
        ubicacion_dir = os.path.join(settings.MEDIA_ROOT, 'ubicaciones')

        # Verificar si se puede escribir en el directorio
        try:
            os.makedirs(ubicacion_dir, exist_ok=True)
            # Verificar permisos de escritura
            test_file = os.path.join(ubicacion_dir, 'test_write.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            logger.info(f"Directorio de ubicaciones con permisos de escritura: {ubicacion_dir}")
        except (IOError, PermissionError) as e:
            # Si no se puede escribir, usar un directorio temporal
            import tempfile
            ubicacion_dir = tempfile.gettempdir()
            logger.warning(f"No se puede escribir en {settings.MEDIA_ROOT}/ubicaciones, usando directorio temporal: {ubicacion_dir}")

        # Asegurarse de que el directorio exista
        os.makedirs(ubicacion_dir, exist_ok=True)

        logger.info(f"Directorio de ubicaciones: {ubicacion_dir}")

        # Obtener información del usuario y la incidencia para organizar los archivos
        try:
            from apps.locations.models import Ciudad, UnidadRegional

            # Intentar obtener la ciudad del usuario
            ciudad_nombre = "Sin_Ciudad"
            unidad_regional_nombre = "Sin_UR"

            # Intentar obtener la ciudad de la incidencia
            try:
                if hasattr(incidencia, 'ciudad_reportada') and incidencia.ciudad_reportada:
                    ciudad = incidencia.ciudad_reportada
                    ciudad_nombre = ciudad.nombre.replace(' ', '_')
                    unidad_regional_nombre = ciudad.unidad_regional.nombre.replace(' ', '_')
            except Exception as e:
                logger.warning(f"No se pudo obtener la ciudad de la incidencia: {str(e)}")

            # Si no se pudo obtener de la incidencia, intentar obtenerla del usuario
            if ciudad_nombre == "Sin_Ciudad":
                try:
                    if hasattr(request.user, 'profile') and hasattr(request.user.profile, 'ciudad') and request.user.profile.ciudad:
                        ciudad = request.user.profile.ciudad
                        ciudad_nombre = ciudad.nombre.replace(' ', '_')
                        unidad_regional_nombre = ciudad.unidad_regional.nombre.replace(' ', '_')
                except Exception as e:
                    logger.warning(f"No se pudo obtener la ciudad del usuario: {str(e)}")

            # Crear estructura de directorios
            username = request.user.username.replace(' ', '_')
            fecha_actual = datetime.now().strftime('%Y-%m-%d')

            # Estructura: ubicaciones/UnidadRegional/Ciudad/Usuario/Fecha/
            ubicacion_dir_estructura = os.path.join(
                ubicacion_dir,
                unidad_regional_nombre,
                ciudad_nombre,
                username,
                fecha_actual
            )

            # Crear directorios si no existen
            os.makedirs(ubicacion_dir_estructura, exist_ok=True)
            logger.info(f"Directorio creado: {ubicacion_dir_estructura}")

            # Crear archivo de log
            log_filename = os.path.join(ubicacion_dir_estructura, "ubicacion_log.txt")
            with open(log_filename, 'a', encoding='utf-8') as log_file:
                log_entry = (
                    f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"Usuario: {request.user.username} (ID: {request.user.id})\n"
                    f"Incidencia: #{incidencia.id}\n"
                    f"Latitud: {lat}\n"
                    f"Longitud: {lng}\n"
                    f"--------------------------------------------------\n"
                )
                log_file.write(log_entry)

            logger.info(f"Registro de ubicación añadido al log: {log_filename}")

        except Exception as e:
            logger.error(f"Error al crear estructura de directorios para ubicación: {str(e)}")

        # Guardar mensaje en la base de datos
        from apps.chat.models import MensajeChat
        mensaje = MensajeChat.objects.create(
            incidencia=incidencia,
            remitente=request.user,
            tipo='UBICACION',
            ubicacion_lat=float(lat),
            ubicacion_lng=float(lng),
            texto=f"Ubicación compartida: {lat}, {lng}"
        )

        # Enviar notificación por WebSocket
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()

            # Preparar datos del mensaje para enviar por WebSocket
            mensaje_data = {
                'type': 'chat_message',
                'id': mensaje.id,
                'incidencia_id': incidencia.id,
                'remitente_id': request.user.id,
                'remitente_nombre': request.user.username,
                'tipo': 'UBICACION',
                'texto': f"Ubicación compartida: {lat}, {lng}",
                'ubicacion_lat': float(lat),
                'ubicacion_lng': float(lng),
                'fecha_envio': mensaje.fecha_envio.isoformat(),
                'leido': False,
                'exportado_brigada': False
            }

            # Enviar mensaje al grupo de chat
            async_to_sync(channel_layer.group_send)(
                f'chat_incidencia_{incidencia.id}',
                mensaje_data
            )

            logger.info(f"Notificación WebSocket enviada para la ubicación {mensaje.id}")
        except Exception as e:
            logger.error(f"Error al enviar notificación WebSocket: {str(e)}")

        logger.info(f"Ubicación guardada exitosamente para la incidencia {incidencia.id} por el usuario {request.user.username}")

        return JsonResponse({
            'success': True,
            'mensaje_id': mensaje.id,
            'lat': lat,
            'lng': lng
        }, status=201)

    except Exception as e:
        logger.error(f"Error al guardar ubicación: {str(e)}")
        return Response({'error': f'Error al guardar la ubicación: {str(e)}'},
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@login_required  # Usar login_required en lugar de permission_classes
@csrf_exempt  # Eximir de la verificación CSRF para solucionar problemas de autenticación
def exportar_chat_brigada_view(request, incidencia_id):
    # Verificar si el usuario está autenticado
    if not request.user.is_authenticated:
        logger.error(f"Usuario no autenticado intentando exportar chat. Sesión: {request.session.session_key if hasattr(request, 'session') else 'No hay sesión'}")
        return Response({'error': 'No autenticado'}, status=status.HTTP_401_UNAUTHORIZED)
    """
    API para exportar el chat a la brigada
    """
    try:
        incidencia = get_object_or_404(Incidencia, id=incidencia_id)

        # Verificar que el usuario sea operador o admin
        user = request.user
        if not (user.role == 'OPERADOR' or user.is_superuser or user.is_staff):
            return Response({'error': 'Solo los operadores pueden exportar el chat a la brigada'},
                           status=status.HTTP_403_FORBIDDEN)

        # Verificar que la incidencia tenga una brigada asignada
        if not incidencia.brigada_asignada:
            return Response({'error': 'La incidencia no tiene una brigada asignada'},
                           status=status.HTTP_400_BAD_REQUEST)

        # Marcar todos los mensajes como exportados
        mensajes = MensajeChat.objects.filter(incidencia=incidencia, exportado_brigada=False)
        mensajes.update(exportado_brigada=True)

        # Crear mensaje del sistema indicando la exportación
        MensajeChat.objects.create(
            incidencia=incidencia,
            remitente=user,
            tipo='SISTEMA',
            texto=f"El operador {user.username} ha exportado el chat a la brigada {incidencia.brigada_asignada.username}"
        )

        return Response({
            'success': True,
            'mensajes_exportados': mensajes.count()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error al exportar chat: {str(e)}")
        return Response({'error': f'Error al exportar el chat: {str(e)}'},
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@login_required
def reparar_archivos_audio_view(request):
    """
    Vista para reparar los archivos de audio
    """
    if not request.user.is_superuser and not request.user.is_staff:
        return JsonResponse({'error': 'No tienes permiso para ejecutar esta acción'}, status=403)

    try:
        from apps.chat.models import MensajeChat
        import os
        import shutil
        from django.conf import settings

        # Obtener todos los mensajes de audio
        mensajes_audio = MensajeChat.objects.filter(tipo='AUDIO')

        # Crear directorio simple si no existe
        simple_dir = os.path.join(settings.MEDIA_ROOT, 'audios')
        os.makedirs(simple_dir, exist_ok=True)

        # Contador de archivos procesados
        archivos_encontrados = 0
        archivos_copiados = 0
        archivos_no_encontrados = 0

        # Procesar cada mensaje
        for mensaje in mensajes_audio:
            if not mensaje.audio_url:
                continue

            # Obtener el nombre del archivo de la URL
            filename = os.path.basename(mensaje.audio_url)

            # Buscar el archivo en todas las ubicaciones posibles
            posibles_ubicaciones = [
                os.path.join(settings.MEDIA_ROOT, 'audios', filename),
                os.path.join(settings.MEDIA_ROOT, mensaje.audio_url.replace(settings.MEDIA_URL, '')),
                os.path.join(settings.MEDIA_ROOT, 'audios', 'Sin_UR', 'Sin_Ciudad', request.user.username, datetime.now().strftime('%Y-%m-%d'), filename),
            ]

            # Verificar si el archivo existe en alguna ubicación
            archivo_encontrado = False
            for ubicacion in posibles_ubicaciones:
                if os.path.exists(ubicacion):
                    archivo_encontrado = True
                    archivos_encontrados += 1

                    # Copiar el archivo al directorio simple
                    destino = os.path.join(simple_dir, filename)
                    if not os.path.exists(destino):
                        try:
                            shutil.copy2(ubicacion, destino)
                            archivos_copiados += 1
                        except Exception as e:
                            logger.error(f"Error al copiar {ubicacion} a {destino}: {str(e)}")
                    break

            if not archivo_encontrado:
                archivos_no_encontrados += 1
                logger.warning(f"Archivo no encontrado para mensaje {mensaje.id}: {mensaje.audio_url}")

                # Actualizar la URL del mensaje para usar el formato simple
                nuevo_filename = f"audio_{mensaje.id}.webm"
                nueva_url = f"{settings.MEDIA_URL}audios/{nuevo_filename}"
                mensaje.audio_url = nueva_url
                mensaje.save()

        return JsonResponse({
            'success': True,
            'archivos_encontrados': archivos_encontrados,
            'archivos_copiados': archivos_copiados,
            'archivos_no_encontrados': archivos_no_encontrados
        })
    except Exception as e:
        logger.error(f"Error al reparar archivos de audio: {str(e)}")
        return JsonResponse({'error': f'Error al reparar archivos de audio: {str(e)}'}, status=500)


@login_required
def reparar_archivos_imagen_view(request):
    """
    Vista para reparar los archivos de imagen
    """
    if not request.user.is_superuser and not request.user.is_staff:
        return JsonResponse({'error': 'No tienes permiso para ejecutar esta acción'}, status=403)

    try:
        from apps.chat.models import MensajeChat
        import os
        import shutil
        from django.conf import settings

        # Obtener todos los mensajes de imagen
        mensajes_imagen = MensajeChat.objects.filter(tipo='IMAGEN')

        # Crear directorio simple si no existe
        simple_dir = os.path.join(settings.MEDIA_ROOT, 'imagenes')
        os.makedirs(simple_dir, exist_ok=True)

        # Contador de archivos procesados
        archivos_encontrados = 0
        archivos_copiados = 0
        archivos_no_encontrados = 0

        # Procesar cada mensaje
        for mensaje in mensajes_imagen:
            if not mensaje.imagen_url:
                continue

            # Obtener el nombre del archivo de la URL
            filename = os.path.basename(mensaje.imagen_url)

            # Buscar el archivo en todas las ubicaciones posibles
            posibles_ubicaciones = [
                os.path.join(settings.MEDIA_ROOT, 'imagenes', filename),
                os.path.join(settings.MEDIA_ROOT, mensaje.imagen_url.replace(settings.MEDIA_URL, '')),
                os.path.join(settings.MEDIA_ROOT, 'imagenes', 'Sin_UR', 'Sin_Ciudad', request.user.username, datetime.now().strftime('%Y-%m-%d'), filename),
            ]

            # Verificar si el archivo existe en alguna ubicación
            archivo_encontrado = False
            for ubicacion in posibles_ubicaciones:
                if os.path.exists(ubicacion):
                    archivo_encontrado = True
                    archivos_encontrados += 1

                    # Copiar el archivo al directorio simple
                    destino = os.path.join(simple_dir, filename)
                    if not os.path.exists(destino):
                        try:
                            shutil.copy2(ubicacion, destino)
                            archivos_copiados += 1
                        except Exception as e:
                            logger.error(f"Error al copiar {ubicacion} a {destino}: {str(e)}")
                    break

            if not archivo_encontrado:
                archivos_no_encontrados += 1
                logger.warning(f"Archivo no encontrado para mensaje {mensaje.id}: {mensaje.imagen_url}")

                # Actualizar la URL del mensaje para usar el formato simple
                extension = 'jpg'  # Por defecto
                if '.png' in mensaje.imagen_url:
                    extension = 'png'
                elif '.gif' in mensaje.imagen_url:
                    extension = 'gif'
                elif '.webp' in mensaje.imagen_url:
                    extension = 'webp'

                nuevo_filename = f"imagen_{mensaje.id}.{extension}"
                nueva_url = f"{settings.MEDIA_URL}imagenes/{nuevo_filename}"
                mensaje.imagen_url = nueva_url
                mensaje.save()

        return JsonResponse({
            'success': True,
            'archivos_encontrados': archivos_encontrados,
            'archivos_copiados': archivos_copiados,
            'archivos_no_encontrados': archivos_no_encontrados
        })
    except Exception as e:
        logger.error(f"Error al reparar archivos de imagen: {str(e)}")
        return JsonResponse({'error': f'Error al reparar archivos de imagen: {str(e)}'}, status=500)


def tiene_permiso_chat(user, incidencia):
    """
    Verificar que el usuario tenga permiso para acceder al chat de la incidencia
    """
    # El usuario que reportó la incidencia siempre tiene permiso
    if user.id == incidencia.usuario_reporta.id:
        return True

    # Operadores asignados tienen permiso
    if incidencia.operador_asignado and user.id == incidencia.operador_asignado.id:
        return True

    # Brigadas asignadas tienen permiso
    if incidencia.brigada_asignada and user.id == incidencia.brigada_asignada.id:
        return True

    # Administradores tienen permiso
    if user.is_superuser or user.is_staff:
        return True

    # Operadores (no asignados) también tienen permiso
    if user.role == 'OPERADOR':
        return True

    return False
