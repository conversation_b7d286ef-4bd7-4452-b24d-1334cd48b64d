# File: backend/apps/core/route_service.py
# -----------------------------------------------

import requests
import json
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

# API key de OpenRouteService (en producción, esto debería estar en settings.py)
ORS_API_KEY = "5b3ce3597851110001cf6248a9b0a9a4e1c94a6f9e2f72a5b5e6a3c5"  # API key de ejemplo

def calculate_route(start_coords, end_coords):
    """
    Calcula una ruta entre dos puntos usando OpenRouteService.
    
    Args:
        start_coords (tuple): Coordenadas de inicio (longitud, latitud)
        end_coords (tuple): Coordenadas de destino (longitud, latitud)
        
    Returns:
        dict: Respuesta de la API con la ruta calculada
    """
    try:
        # URL de la API de OpenRouteService
        url = "https://api.openrouteservice.org/v2/directions/driving-car"
        
        # Parámetros de la solicitud
        params = {
            "api_key": ORS_API_KEY,
            "start": f"{start_coords[0]},{start_coords[1]}",
            "end": f"{end_coords[0]},{end_coords[1]}"
        }
        
        # Realizar la solicitud
        response = requests.get(url, params=params)
        
        # Verificar si la solicitud fue exitosa
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Error al calcular la ruta: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error al calcular la ruta: {str(e)}")
        return None

def get_route_coordinates(start_coords, end_coords):
    """
    Obtiene las coordenadas de la ruta entre dos puntos.
    
    Args:
        start_coords (tuple): Coordenadas de inicio (longitud, latitud)
        end_coords (tuple): Coordenadas de destino (longitud, latitud)
        
    Returns:
        list: Lista de coordenadas de la ruta [(lat1, lng1), (lat2, lng2), ...]
    """
    try:
        # Calcular la ruta
        route_data = calculate_route(start_coords, end_coords)
        
        if not route_data:
            return None
        
        # Extraer las coordenadas de la ruta
        coordinates = route_data['features'][0]['geometry']['coordinates']
        
        # Convertir las coordenadas a formato (latitud, longitud) para Leaflet
        route_coords = [(coord[1], coord[0]) for coord in coordinates]
        
        return route_coords
    except Exception as e:
        logger.error(f"Error al obtener las coordenadas de la ruta: {str(e)}")
        return None

def calculate_route_with_openrouteservice(start_coords, end_coords):
    """
    Alternativa usando la biblioteca openrouteservice.
    
    Args:
        start_coords (tuple): Coordenadas de inicio (longitud, latitud)
        end_coords (tuple): Coordenadas de destino (longitud, latitud)
        
    Returns:
        list: Lista de coordenadas de la ruta [(lat1, lng1), (lat2, lng2), ...]
    """
    try:
        import openrouteservice
        
        # Crear cliente de OpenRouteService
        client = openrouteservice.Client(key=ORS_API_KEY)
        
        # Calcular la ruta
        route = client.directions(
            coordinates=[start_coords, end_coords],
            profile='driving-car',
            format='geojson'
        )
        
        # Extraer las coordenadas de la ruta
        coordinates = route['features'][0]['geometry']['coordinates']
        
        # Convertir las coordenadas a formato (latitud, longitud) para Leaflet
        route_coords = [(coord[1], coord[0]) for coord in coordinates]
        
        # Obtener la distancia y duración
        distance = route['features'][0]['properties']['summary']['distance']  # en metros
        duration = route['features'][0]['properties']['summary']['duration']  # en segundos
        
        return {
            'coordinates': route_coords,
            'distance': distance,
            'duration': duration
        }
    except Exception as e:
        logger.error(f"Error al calcular la ruta con openrouteservice: {str(e)}")
        return None
