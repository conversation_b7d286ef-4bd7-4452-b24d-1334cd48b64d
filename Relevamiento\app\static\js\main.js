// --- Archivo: app/static/js/main.js ---
// Responsabilidades: Inicializar el mapa Leaflet en la página principal y persistir filtros.

// Variables globales
let map = null;
let allMarkers = [];
let allCameraMarkers = [];
const MAP_FILTERS_KEY = 'geoAppActiveMapFilters'; // Clave única para localStorage

document.addEventListener('DOMContentLoaded', function() {
    console.log("GeoApp main.js cargado.");

    const mapElement = document.getElementById('map');
    if (mapElement && typeof L !== 'undefined') {
        initializeMap(mapElement);
    }
});

function safeJsonParse(value, fallback) {
    if (value === null || value === undefined) return fallback;
    try {
        return JSON.parse(value);
    } catch (e) {
        console.warn("Error parseando JSON:", e);
        return fallback;
    }
}

function escapeHtml(text) {
    if (text === null || text === undefined) return '';
    let str = String(text);

    str = str.replace(/&/g, '&');
    str = str.replace(/</g, '<');
    str = str.replace(/>/g, '>');
    str = str.replace(/"/g, '"');
    str = str.replace(/'/g, "'");

    return str;
}

function saveMapFiltersToLocalStorage(selectedSources, selectedCities, selectedCameraTypes, showCameras) {
    try {
        const filters = {
            sources: selectedSources,
            cities: selectedCities,
            cameraTypes: selectedCameraTypes || [],
            showCameras: showCameras !== undefined ? showCameras : true
        };
        localStorage.setItem(MAP_FILTERS_KEY, JSON.stringify(filters));
        console.log("Filtros del mapa guardados en localStorage:", filters);
    } catch (e) {
        console.error("Error guardando filtros del mapa en localStorage:", e);
    }
}

function loadMapFiltersFromLocalStorage() {
    try {
        const saved = localStorage.getItem(MAP_FILTERS_KEY);
        if (saved) {
            const filters = JSON.parse(saved);
            console.log("Filtros del mapa cargados desde localStorage:", filters);
            return filters;
        }
    } catch (e) {
        console.error("Error cargando filtros del mapa desde localStorage:", e);
        // Opcional: limpiar datos corruptos si es necesario
        // localStorage.removeItem(MAP_FILTERS_KEY);
    }
    return null; // Devuelve null si no hay filtros guardados o hay un error
}


function initializeMap(mapElement) {
    const pointsData = safeJsonParse(mapElement.dataset.points, []);
    const mapCenter = safeJsonParse(mapElement.dataset.center, [-40.086, -67.738]);
    const mapZoom = parseInt(mapElement.dataset.zoom || '6');
    const savedFilters = loadMapFiltersFromLocalStorage(); // Cargar filtros guardados

    console.log(`Inicializando mapa en ${mapCenter} con zoom ${mapZoom} y ${pointsData.length} puntos.`);
    mapElement.innerHTML = ''; // Limpiar el contenido de "Cargando mapa..."

    try {
        map = L.map(mapElement).setView(mapCenter, mapZoom);
        window._leafletMapInstance = map; // Útil para depuración o acceso externo

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        allMarkers = []; // Reiniciar/inicializar la variable global
        allCameraMarkers = []; // Reiniciar marcadores de cámaras
        const sourceGroups = {};
        const cityGroups = {};
        const cameraTypeGroups = {};

        pointsData.forEach(function (point) {
            if (point && typeof point.lat === 'number' && typeof point.lon === 'number') {
                const estado = point.status || 'gris';
                const iconUrl = `/static/img/marker_${estado}.png`;

                const customIcon = L.icon({
                    iconUrl: iconUrl,
                    iconSize: [30, 40],
                    iconAnchor: [15, 40],
                    popupAnchor: [0, -35],
                    shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
                    shadowSize: [41, 41],
                    shadowAnchor: [12, 41]
                });

                const marker = L.marker([point.lat, point.lon], { icon: customIcon });
                const safeName = escapeHtml((point.name || 'Punto ' + point.id).replace(/[\r\n]+/g, ' '));
                const safeStatus = escapeHtml((point.status || 'N/A').replace(/[\r\n]+/g, ' '));
                const safeImageCount = point.image_count !== undefined ? point.image_count : 'N/A';
                const pointDetailUrl = `/points/${point.id}`;

                let popupContent = `<b>${safeName}</b><br>`;
                popupContent += `Estado: ${safeStatus}<br>`;
                popupContent += `Imágenes: ${safeImageCount}<br>`;
                popupContent += `<a href="${pointDetailUrl}">Ver Detalles</a>`;

                marker.bindPopup(popupContent);
                marker.pointId = point.id;

                const source = point.source || 'Desconocido';
                const city = point.city || 'Sin Ciudad';

                if (!sourceGroups[source]) sourceGroups[source] = [];
                if (!cityGroups[city]) cityGroups[city] = [];

                sourceGroups[source].push(marker);
                cityGroups[city].push(marker);

                marker.options.source = source;
                marker.options.city = city;

                allMarkers.push(marker);

                // Crear marcadores de cámaras si existen
                if (point.cameras && point.cameras.length > 0) {
                    point.cameras.forEach(function(camera) {
                        if (typeof window.CameraIcons !== 'undefined') {
                            const cameraMarker = window.CameraIcons.createCameraMarker(camera, point);
                            if (cameraMarker) {
                                // Agregar propiedades para filtrado
                                cameraMarker.options.source = point.source;
                                cameraMarker.options.city = point.city;

                                // Agrupar por tipo de cámara
                                const cameraType = camera.type || 'otra';
                                if (!cameraTypeGroups[cameraType]) cameraTypeGroups[cameraType] = [];
                                cameraTypeGroups[cameraType].push(cameraMarker);

                                allCameraMarkers.push(cameraMarker);
                            }
                        }
                    });
                }
            }
        });

        createCheckboxGroup('filter-source', 'Filtrar por Origen:', sourceGroups, savedFilters ? savedFilters.sources : null);
        createCheckboxGroup('filter-city', 'Filtrar por Ciudad:', cityGroups, savedFilters ? savedFilters.cities : null);

        // Crear grupo de filtros para cámaras si hay cámaras
        if (Object.keys(cameraTypeGroups).length > 0) {
            createCheckboxGroup('filter-camera-type', 'Filtrar por Tipo de Cámara:', cameraTypeGroups, savedFilters ? savedFilters.cameraTypes : null);
            createCameraToggle(savedFilters ? savedFilters.showCameras : true);
        }

        updateVisibleMarkers();

        if (allMarkers.length > 0) {
            const visibleMarkers = allMarkers.filter(m => map.hasLayer(m));
            if (visibleMarkers.length > 0) {
                 const visibleGroup = L.featureGroup(visibleMarkers);
                 if (!mapElement.dataset.center && !mapElement.dataset.zoom && visibleMarkers.length > 1) {
                    map.fitBounds(visibleGroup.getBounds().pad(0.1));
                } else if (visibleMarkers.length === 1 && !mapElement.dataset.center && !mapElement.dataset.zoom) {
                    map.setView(visibleMarkers[0].getLatLng(), 14);
                }
            } else if (!mapElement.dataset.center && !mapElement.dataset.zoom) {
                map.setView(mapCenter, mapZoom);
            }
        }
        console.log("Mapa inicializado correctamente.");
    } catch (error) {
        console.error("Error durante la inicialización del mapa:", error);
        mapElement.innerHTML = '<p class="text-danger text-center mt-5">Error al cargar el mapa.</p>';
    }
}

function createCheckboxGroup(containerId, labelText, groups, preSelectedValues) {
    const container = document.getElementById(`${containerId}-checkboxes`);
    if (!container) {
        console.error(`Contenedor de checkboxes '${containerId}-checkboxes' no encontrado.`);
        return;
    }
    container.innerHTML = `<label class="form-label fw-bold">${labelText}</label>`;

    Object.keys(groups).sort().forEach(key => {
        const safeKeyIdPart = key.replace(/[^a-zA-Z0-9_-]/g, '_').replace(/\s+/g, '-').toLowerCase();
        const id = `${containerId}-${safeKeyIdPart}`;

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = id;
        checkbox.value = key;
        checkbox.checked = preSelectedValues ? preSelectedValues.includes(key) : true;
        checkbox.className = 'form-check-input me-1';

        const label = document.createElement('label');
        label.htmlFor = id;
        label.textContent = ` ${key}`;
        label.className = 'form-check-label';

        const wrapper = document.createElement('div');
        wrapper.className = 'form-check';
        wrapper.appendChild(checkbox);
        wrapper.appendChild(label);

        container.appendChild(wrapper);
        checkbox.addEventListener('change', updateVisibleMarkers);
    });
}

function updateVisibleMarkers() {
    if (!map) {
        console.warn("updateVisibleMarkers llamado antes de que el mapa esté listo.");
        return;
    }

    const selectedSources = Array.from(document.querySelectorAll('#filter-source-checkboxes input:checked')).map(cb => cb.value);
    const selectedCities = Array.from(document.querySelectorAll('#filter-city-checkboxes input:checked')).map(cb => cb.value);
    const selectedCameraTypes = Array.from(document.querySelectorAll('#filter-camera-type-checkboxes input:checked')).map(cb => cb.value);
    const showCameras = document.getElementById('show-cameras-toggle') ? document.getElementById('show-cameras-toggle').checked : true;

    saveMapFiltersToLocalStorage(selectedSources, selectedCities, selectedCameraTypes, showCameras);

    // Filtrar marcadores de puntos
    allMarkers.forEach(marker => {
        const matchSource = selectedSources.includes(marker.options.source);
        const matchCity = selectedCities.includes(marker.options.city);
        if (matchSource && matchCity) {
            if (!map.hasLayer(marker)) {
                 map.addLayer(marker);
            }
        } else {
            if (map.hasLayer(marker)) {
                map.removeLayer(marker);
            }
        }
    });

    // Filtrar marcadores de cámaras
    allCameraMarkers.forEach(marker => {
        const matchSource = selectedSources.includes(marker.options.source);
        const matchCity = selectedCities.includes(marker.options.city);
        const matchCameraType = selectedCameraTypes.length === 0 || selectedCameraTypes.includes(marker.options.cameraType);

        if (matchSource && matchCity && matchCameraType && showCameras) {
            if (!map.hasLayer(marker)) {
                map.addLayer(marker);
            }
        } else {
            if (map.hasLayer(marker)) {
                map.removeLayer(marker);
            }
        }
    });
}

// Hacer toggleCheckboxes globalmente accesible
window.toggleCheckboxes = function(containerId, checkState) {
    const container = document.getElementById(containerId + '-checkboxes');
    if (!container) return;
    const checkboxes = container.querySelectorAll('input[type="checkbox"]');
    let changed = false;
    checkboxes.forEach(cb => {
        if (cb.checked !== checkState) {
            cb.checked = checkState;
            changed = true;
        }
    });
    if (changed) {
        updateVisibleMarkers();
    }
}

// --- UBICACIÓN DEL USUARIO ---
let userLocationCircle = null;
const miUbicacionButton = document.getElementById('btn-mi-ubicacion');

if (miUbicacionButton) {
    miUbicacionButton.addEventListener('click', () => {
        if (!navigator.geolocation) {
            alert("Geolocalización no soportada por este navegador.");
            return;
        }
        const currentMapInstance = map || window._leafletMapInstance;

        if (!currentMapInstance) {
            alert("El mapa no está listo todavía.");
            return;
        }

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lon = position.coords.longitude;

                currentMapInstance.setView([lat, lon], 15);
                if (userLocationCircle) {
                    currentMapInstance.removeLayer(userLocationCircle);
                }
                userLocationCircle = L.circle([lat, lon], {
                    color: 'black',
                    fillColor: 'black',
                    fillOpacity: 0.6,
                    radius: 20
                }).addTo(currentMapInstance);
            },
            (error) => {
                console.error("Error al obtener ubicación:", error);
                alert("No se pudo obtener tu ubicación.");
            },
            { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
        );
    });
}

// --- FUNCIONES PARA CÁMARAS ---
function createCameraToggle(initialState = true) {
    const container = document.getElementById('filter-camera-type-checkboxes');
    if (!container) {
        console.error("Contenedor de filtros de cámara no encontrado.");
        return;
    }

    // Crear toggle para mostrar/ocultar cámaras
    const toggleWrapper = document.createElement('div');
    toggleWrapper.className = 'form-check form-switch mb-3';
    toggleWrapper.style.borderTop = '1px solid #dee2e6';
    toggleWrapper.style.paddingTop = '10px';
    toggleWrapper.style.marginTop = '10px';

    const toggleInput = document.createElement('input');
    toggleInput.type = 'checkbox';
    toggleInput.id = 'show-cameras-toggle';
    toggleInput.className = 'form-check-input';
    toggleInput.checked = initialState;

    const toggleLabel = document.createElement('label');
    toggleLabel.htmlFor = 'show-cameras-toggle';
    toggleLabel.textContent = ' Mostrar Cámaras';
    toggleLabel.className = 'form-check-label fw-bold';

    toggleWrapper.appendChild(toggleInput);
    toggleWrapper.appendChild(toggleLabel);
    container.appendChild(toggleWrapper);

    toggleInput.addEventListener('change', updateVisibleMarkers);
}

// --- Fin del Archivo ---