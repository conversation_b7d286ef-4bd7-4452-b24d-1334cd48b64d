<!-- /app/templates/announcement_form.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<h1>{{ title }}</h1>

<form method="POST" action="">
    {{ form.hidden_tag() }}
    <div class="form-group">
        {{ render_field(form.title, class="form-control", placeholder="Título claro y conciso") }}
    </div>
    <div class="form-group">
        {{ render_field(form.content, class="form-control", rows="10", placeholder="Escribe aquí el contenido del anuncio...") }}
        {# Considerar usar un editor enriquecido (CKEditor, TinyMCE) aquí para formato #}
    </div>
    <div class="form-group">
        {{ render_field(form.visibility, class="form-control", id="visibilitySelect") }}
    </div>
    {# Campo de Iglesia Específica (se muestra/oculta con JS) #}
    <div class="form-group" id="targetChurchGroup" style="display: none;">
        {{ render_field(form.target_church, class="form-control") }}
    </div>

    <button type="submit" class="btn btn-primary">{{ form.submit.label }}</button>
    <a href="{{ url_for('routes.dashboard') }}" class="btn btn-secondary">Cancelar</a>
</form>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function() {
        const visibilitySelect = $('#visibilitySelect');
        const targetChurchGroup = $('#targetChurchGroup');

        function toggleTargetChurch() {
            if (visibilitySelect.val() === 'iglesia_especifica') {
                targetChurchGroup.slideDown(); // Muestra suavemente
            } else {
                targetChurchGroup.slideUp(); // Oculta suavemente
            }
        }

        // Llamar al inicio para establecer estado correcto
        toggleTargetChurch();

        // Llamar cada vez que cambie la visibilidad
        visibilitySelect.on('change', toggleTargetChurch);

        // Opcional: Inicializar Select2 si quieres búsqueda en iglesias
        // $('#target_church').select2({ placeholder: '-- Seleccionar Iglesia --', width: '100%' });
    });
</script>
{% endblock %}