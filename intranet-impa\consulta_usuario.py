#consulta_usuario.py
import sys
from app import create_app, db
from app.models import User, Pastor, Member, Church, MemberFunction, UserRelationship
from sqlalchemy import or_

app = create_app()

def print_user_info(user_id):
    with app.app_context():
        user = User.query.get(user_id)
        if not user:
            print(f"Usuario con ID {user_id} no encontrado.")
            return

        print("==== Información del Usuario ====")
        print(f"ID: {user.id}")
        print(f"Username: {user.username}")
        print(f"Email: {user.email}")
        print(f"Nombre Completo: {user.full_name}")
        print(f"Rol: {user.role}")
        print(f"Iglesia: {user.church.name if user.church else 'Ninguna'}")
        
        if user.role == 'pastorado':
            pastor = Pastor.query.filter_by(user_id=user.id).first()
            if pastor:
                print("---- <PERSON><PERSON> del Pastor ----")
                print(f"Dirección: {pastor.address}")
                print(f"Latitud: {pastor.latitude}")
                print(f"Longitud: {pastor.longitude}")
                print("Grado Pastoral:", pastor.grado)  # Muestra "diacono", "probando", etc.
            print("Roles de Pastor:")
            for role in user.pastor_roles:
                print(f"  - {role.role_name}")
        elif user.role == 'miembro':
            member = Member.query.filter_by(user_id=user.id).first()
            if member:
                print("---- Datos del Miembro ----")
                if member.functions:
                    funcs = ", ".join([f.name for f in member.functions])
                    print(f"Funciones asignadas: {funcs}")
                else:
                    print("Funciones asignadas: Sin funciones")
        else:
            print("No hay datos adicionales para este rol.")

        # Opcional: Mostrar relaciones familiares
        relationships = db.session.query(UserRelationship).filter(
            or_(UserRelationship.user_id_1 == user.id, UserRelationship.user_id_2 == user.id)
        ).all()
        if relationships:
            print("\n==== Relaciones Familiares ====")
            for rel in relationships:
                # Se puede mejorar la descripción; aquí se muestra el tipo y los IDs de los usuarios involucrados
                print(f"ID Relación: {rel.id} - {rel.tipo_de_relacion} (Usuarios: {rel.user_id_1} y {rel.user_id_2})")
        else:
            print("\nSin relaciones registradas.")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Uso: ./consulta_usuario.py <ID_usuario>")
        sys.exit(1)
    try:
        user_id = int(sys.argv[1])
    except ValueError:
        print("El ID debe ser un número entero.")
        sys.exit(1)
    print_user_info(user_id)

