# /app/utils.py
from flask_login import current_user
from functools import wraps
from flask import flash, redirect, url_for, request
from app.models import Church, JerarquiaPastores, JerarquiaSuperintendentes
from datetime import date
import pytz

def admin_or_secretary_required(func):
    @wraps(func)
    def decorated_view(*args, **kwargs):
        if current_user.role not in ('administrador', 'secretaria'):
            flash('No tienes permiso para acceder a esta página.', 'danger')
            return redirect(url_for('routes.dashboard'))
        return func(*args, **kwargs)
    return decorated_view

def utc_to_buenos_aires(utc_dt):
    if utc_dt is None:
        return ""
    local_tz = pytz.timezone("America/Argentina/Buenos_Aires")
    local_dt = utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
    return local_dt.strftime("%d-%m-%Y %H:%M")

def get_user_for_view():
    if current_user.is_authenticated and current_user.role in ('administrador', 'secretaria'):
        view_as_user_id = request.args.get('view_as')
        if view_as_user_id:
            try:
                view_as_user_id = int(view_as_user_id)
                view_as_user = User.query.get(view_as_user_id)
                if view_as_user and view_as_user.role not in ('administrador', 'secretaria'):
                    return view_as_user
                else:
                    return current_user
            except ValueError:
                return current_user
        return current_user
    return current_user

def describe_relationship(relationship, user_id):
    if relationship.user_id_1 == user_id:
        other_user = relationship.user2
        relationship_type = relationship.tipo_de_relacion
    elif relationship.user_id_2 == user_id:
        other_user = relationship.user1
        if relationship.tipo_de_relacion == 'Padre':
            relationship_type = 'Hijo/a'
        elif relationship.tipo_de_relacion == 'Madre':
            relationship_type = 'Hijo/a'
        elif relationship.tipo_de_relacion == 'Hijo':
            relationship_type = 'Padre'
        elif relationship.tipo_de_relacion == 'Hija':
            relationship_type = 'Madre'
        elif relationship.tipo_de_relacion == 'Esposo':
            relationship_type = 'Esposa'
        elif relationship.tipo_de_relacion == 'Esposa':
            relationship_type = 'Esposo'
        elif relationship.tipo_de_relacion == 'Hermano':
            relationship_type = 'Hermano/a'
        elif relationship.tipo_de_relacion == 'Hermana':
            relationship_type = 'Hermano/a'
        elif relationship.tipo_de_relacion == 'Abuelo':
            relationship_type = 'Nieto/a'
        elif relationship.tipo_de_relacion == 'Abuela':
            relationship_type = 'Nieto/a'
        elif relationship.tipo_de_relacion == 'Nieto':
            relationship_type = 'Abuelo/a'
        elif relationship.tipo_de_relacion == 'Nieta':
            relationship_type = 'Abuelo/a'
        elif relationship.tipo_de_relacion == 'Tio':
            relationship_type = 'Sobrino/a'
        elif relationship.tipo_de_relacion == 'Tia':
            relationship_type = 'Sobrino/a'
        elif relationship.tipo_de_relacion == 'Sobrino':
            relationship_type = 'Tio/a'
        elif relationship.tipo_de_relacion == 'Sobrina':
            relationship_type = 'Tio/a'
        elif relationship.tipo_de_relacion == 'Cuñado':
            relationship_type = 'Cuñado/a'
        elif relationship.tipo_de_relacion == 'Cuñada':
            relationship_type = 'Cuñado/a'
        elif relationship.tipo_de_relacion == 'Primo':
            relationship_type = 'Primo/a'
        elif relationship.tipo_de_relacion == 'Prima':
            relationship_type = 'Primo/a'
        elif relationship.tipo_de_relacion == 'Yerno':
            relationship_type = 'Suegro/a'
        elif relationship.tipo_de_relacion == 'Nuera':
            relationship_type = 'Suegro/a'
        elif relationship.tipo_de_relacion == 'Concuñado':
            relationship_type = 'Concuñado/a'
        elif relationship.tipo_de_relacion == 'Concuñada':
            relationship_type = 'Concuñado/a'
        else:
            relationship_type = relationship.tipo_de_relacion
    else:
        return None

    if other_user:
        full_name = f"{other_user.first_name} {other_user.last_name}"
        return f"{relationship_type} de {full_name}"
    else:
        return None

def pastor_required(func):
    @wraps(func)
    def decorated_view(*args, **kwargs):
        if current_user.role != 'pastorado':
            flash('No tienes permiso para acceder a esta página.', 'danger')
            return redirect(url_for('routes.dashboard'))
        return func(*args, **kwargs)
    return decorated_view

def calculate_age(birth_date):
    if not birth_date:
        return "N/A"
    today = date.today()
    age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
    return age

def get_iglesias_para_superintendente(user_id):
    # Obtener pastores que dependen del superintendente
    pastor_ids = [j.pastor_id for j in JerarquiaPastores.query.filter_by(supervisor_id=user_id).all()]
    return Church.query.filter(Church.pastor_id.in_(pastor_ids)).all()

def get_iglesias_para_jefe_sector(user_id):
    # Obtener superintendentes que dependen del jefe de sector
    super_ids = [r.superintendente_id for r in JerarquiaSuperintendentes.query.filter_by(jefe_sector_id=user_id).all()]
    # Con esos superintendentes, buscar los pastores que supervisan
    pastor_ids = [j.pastor_id for j in JerarquiaPastores.query.filter(JerarquiaPastores.supervisor_id.in_(super_ids)).all()]
    return Church.query.filter(Church.pastor_id.in_(pastor_ids)).all()