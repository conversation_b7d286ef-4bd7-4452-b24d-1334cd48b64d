import os
import datetime # Para añadir una marca de tiempo opcional

BASE_DIR_NAME = "rn_rural"
BACKEND_DIR_NAME = "backend"
PROJECT_NAME = "rn_rural_project"  # Django project name
APP_NAMES = ["core", "users", "incidents", "locations", "chat"]
APPS_WITH_WEBSOCKET_FILES = ["incidents", "locations", "chat"]

# --- Helper Function ---
def create_file(full_path, content="", project_root_for_comment=""):
    # Generar el comentario del encabezado
    if project_root_for_comment:
        relative_path = os.path.relpath(full_path, project_root_for_comment)
    else:
        relative_path = os.path.basename(full_path) # Fallback si no se provee raíz

    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    header_comment = f"# File: {relative_path}\n"
    # header_comment += f"# Created: {timestamp}\n" # Opcional: añadir timestamp
    header_comment += "# -----------------------------------------------\n\n"

    final_content = header_comment + content

    os.makedirs(os.path.dirname(full_path), exist_ok=True)
    with open(full_path, "w", encoding="utf-8") as f:
        f.write(final_content)
    print(f"Created: {full_path} (with header)")

# --- File Contents (sin cambios en el contenido principal, solo se les añadirá el header) ---

GITIGNORE_CONTENT = """# Byte-compiled / optimized / DLL files
__pycache__/
# ... (resto del contenido como antes)
"""
# ... (el resto de los contenidos de archivos GITIGNORE_CONTENT, README_CONTENT, etc. son los mismos que en el script anterior)
# Voy a omitir re-pegar todos los contenidos largos para brevedad,
# pero el script completo los tendría. Asumiremos que son los mismos.

GITIGNORE_CONTENT = """
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib60/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Environments
.env
.venv
env/
venv/
ENV/

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# IDE specific
.vscode/
.idea/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db
"""

README_CONTENT_TEMPLATE = f"""
# RN-Rural Project

Proyecto para la aplicación RN-Rural.

## Estructura

- `{BACKEND_DIR_NAME}/`: Contiene el backend Django.
- `mobile_app/`: (Placeholder) Contendrá el código de la aplicación móvil.
- `web_panel/`: (Placeholder) Contendrá el código del panel web para operadores.

## Próximos Pasos (Backend)

1.  Navega a la carpeta `{BACKEND_DIR_NAME}`:
    `cd {BASE_DIR_NAME}/{BACKEND_DIR_NAME}`
2.  Crea y activa un entorno virtual:
    `python -m venv venv`
    `source venv/bin/activate` (Linux/macOS) o `venv\\Scripts\\activate` (Windows)
3.  Instala las dependencias:
    `pip install -r requirements.txt`
4.  Copia `.env.example` a `.env` y configura tus variables de entorno (especialmente `DATABASE_URL` y `SECRET_KEY`).
    `cp .env.example .env`
5.  Asegúrate de que tu base de datos PostgreSQL con PostGIS esté en funcionamiento y accesible.
6.  Ejecuta las migraciones:
    `python manage.py makemigrations`
    `python manage.py migrate`
7.  Crea un superusuario:
    `python manage.py createsuperuser`
8.  Inicia el servidor de desarrollo:
    `python manage.py runserver`

Y para los WebSockets (con Daphne):
    `daphne -p 8001 {PROJECT_NAME}.asgi:application`
    (Asegúrate de que Daphne esté instalado: `pip install daphne`)

"""

REQUIREMENTS_TXT_CONTENT = """Django>=4.0
djangorestframework>=3.13
psycopg2-binary>=2.9
django-environ>=0.8
djangorestframework-gis>=1.0
channels>=4.0
channels-redis>=4.0
Pillow>=9.0
daphne>=4.0 # Para servir ASGI/WebSockets
# Celery (añadir más adelante si es necesario)
# celery>=5.2
# redis>=4.3
"""

DOTENV_EXAMPLE_CONTENT = f"""DEBUG=True
SECRET_KEY='django-insecure-cambiame-por-algo-muy-secreto-aqui!'
DATABASE_URL='postgis://rn_rural_user:tu_password@localhost:5432/rn_rural_db'
ALLOWED_HOSTS='localhost,127.0.0.1'

# Para Channels (si usas Redis como broker)
CHANNEL_LAYER_REDIS_HOST='localhost'
CHANNEL_LAYER_REDIS_PORT='6379'

# Para almacenamiento de medios (ej. S3) - añadir más adelante
# AWS_ACCESS_KEY_ID=''
# AWS_SECRET_ACCESS_KEY=''
# AWS_STORAGE_BUCKET_NAME=''
# AWS_S3_REGION_NAME=''
# AWS_S3_ENDPOINT_URL='' # Para MinIO u otros S3 compatibles
"""

MANAGE_PY_CONTENT_TEMPLATE = """#!/usr/bin/env python
\"\"\"Django's command-line utility for administrative tasks.\"\"\"
import os
import sys


def main():
    \"\"\"Run administrative tasks.\"\"\"
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'PROJECT_NAME_PLACEHOLDER.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
"""


# --- Django Project Files ---

PROJECT_INIT_PY_CONTENT = ""

PROJECT_SETTINGS_PY_CONTENT_TEMPLATE = f"""
import environ
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent # Corrected BASE_DIR for project layout
env = environ.Env(
    DEBUG=(bool, False)
)
# Assuming .env is in the 'backend' directory, alongside manage.py
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

SECRET_KEY = env('SECRET_KEY')
DEBUG = env('DEBUG')
ALLOWED_HOSTS = env('ALLOWED_HOSTS', cast=lambda v: [s.strip() for s in v.split(',')])


INSTALLED_APPS = [
    'daphne',  # Debe ir primero para Channels
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.gis',

    # Dependencias
    'rest_framework',
    'rest_framework_gis',
    'channels',

    # Mis Apps
    'apps.core.apps.CoreConfig',
    'apps.users.apps.UsersConfig',
    'apps.incidents.apps.IncidentsConfig',
    'apps.locations.apps.LocationsConfig',
    'apps.chat.apps.ChatConfig',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'PROJECT_NAME_PLACEHOLDER.urls'

TEMPLATES = [
    {{
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {{
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        }},
    }},
]

ASGI_APPLICATION = 'PROJECT_NAME_PLACEHOLDER.asgi.application'

DATABASES = {{
    'default': env.db_url(
        'DATABASE_URL',
        default='postgis://user:pass@host:port/dbname'
    )
}}
DATABASES['default']['ENGINE'] = 'django.contrib.gis.db.backends.postgis'

AUTH_USER_MODEL = 'users.User'

# Password validation
# ... (configuración estándar de Django)
AUTH_PASSWORD_VALIDATORS = [
    {{'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'}},
    {{'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'}},
    {{'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'}},
    {{'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'}},
]

LANGUAGE_CODE = 'es-ar'
TIME_ZONE = 'America/Argentina/Buenos_Aires'
USE_I18N = True
USE_TZ = True

STATIC_URL = 'static/'
# STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles_collected') # Para cuando ejecutes collectstatic
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {{
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        # 'rest_framework.renderers.BrowsableAPIRenderer', # Útil en desarrollo
    ),
    'DEFAULT_PARSER_CLASSES': (
        'rest_framework.parsers.JSONParser',
        'rest_framework_gis.parsers.GeoJSONParser',
    )
}}

CHANNEL_LAYERS = {{
    "default": {{
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {{
            "hosts": [(env('CHANNEL_LAYER_REDIS_HOST', default='localhost'), env.int('CHANNEL_LAYER_REDIS_PORT', default=6379))],
        }},
    }},
}}
# Si no tienes Redis al inicio, comenta lo anterior y descomenta lo siguiente:
# CHANNEL_LAYERS = {{
# "default": {{
# "BACKEND": "channels.layers.InMemoryChannelLayer"
# }}
# }}
"""

PROJECT_URLS_PY_CONTENT = f"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/users/', include('apps.users.urls')),
    path('api/incidents/', include('apps.incidents.urls')),
    path('api/locations/', include('apps.locations.urls')),
    path('api/chat/', include('apps.chat.urls')),
    # path('api/auth/', include('rest_framework.urls')), # Para login/logout en Browsable API
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    # Aquí podrías añadir swagger/redoc si lo usas
    # from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView
    # urlpatterns += [
    #     path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    #     path('api/schema/swagger-ui/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    #     path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    # ]
"""

PROJECT_ASGI_PY_CONTENT_TEMPLATE = f"""
import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack # O AuthMiddleware si no usas sesiones Django en WebSockets

# Importa tus archivos de routing de las apps aquí (asegúrate que los paths sean correctos)
import apps.incidents.routing
import apps.locations.routing
import apps.chat.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'PROJECT_NAME_PLACEHOLDER.settings')

# application = get_asgi_application() # Esto es para HTTP solo

application = ProtocolTypeRouter({{
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack( # Usar AuthMiddlewareStack si se quiere acceso a request.user
        URLRouter(
            apps.incidents.routing.websocket_urlpatterns +
            apps.locations.routing.websocket_urlpatterns +
            apps.chat.routing.websocket_urlpatterns
            # Añade más patrones de URL de WebSocket de otras apps aquí
        )
    ),
}})
"""

PROJECT_WSGI_PY_CONTENT_TEMPLATE = f"""
import os
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'PROJECT_NAME_PLACEHOLDER.settings')
application = get_asgi_application()
"""

# --- App Files (Templates) ---

APP_INIT_PY_CONTENT = ""
APP_ADMIN_PY_CONTENT = """from django.contrib import admin

# Register your models here.
"""
APP_MODELS_PY_CONTENT = """from django.db import models
# from django.contrib.gis.db import models as gis_models # Descomentar si usas GIS

# Create your models here.
"""
APP_TESTS_PY_CONTENT = """from django.test import TestCase

# Create your tests here.
"""
APP_VIEWS_PY_CONTENT = """from django.shortcuts import render
from rest_framework import viewsets
# Create your views here.
"""
APP_SERIALIZERS_PY_CONTENT = """from rest_framework import serializers
# from rest_framework_gis.serializers import GeoFeatureModelSerializer # Para GIS
# from .models import YourModel

# class YourModelSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = YourModel
#         fields = '__all__'
"""
APP_URLS_PY_CONTENT = """from django.urls import path, include
from rest_framework.routers import DefaultRouter
# from .views import YourViewSet

# router = DefaultRouter()
# router.register(r'yourmodel', YourViewSet)

urlpatterns = [
    # path('', include(router.urls)),
]
"""
APP_CONSUMERS_PY_CONTENT_TEMPLATE = """import json
from channels.generic.websocket import AsyncWebsocketConsumer

class YourAppConsumerPlaceholder(AsyncWebsocketConsumer):
    async def connect(self):
        # self.room_name = self.scope['url_route']['kwargs']['room_name']
        # self.room_group_name = f'chat_{self.room_name}'
        # await self.channel_layer.group_add(
        #     self.room_group_name,
        #     self.channel_name
        # )
        await self.accept()
        await self.send(text_data=json.dumps({
            'message': 'Connected to WebSocket!' # Placeholder
        }))


    async def disconnect(self, close_code):
        # await self.channel_layer.group_discard(
        #     self.room_group_name,
        #     self.channel_name
        # )
        print(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # # Send message to room group
        # await self.channel_layer.group_send(
        #     self.room_group_name,
        #     {
        #         'type': 'chat_message', # Corresponde a un método `async def chat_message(self, event)`
        #         'message': message
        #     }
        # )
        print(f"Received message: {message}")
        await self.send(text_data=json.dumps({
            'response_message': f"Server received: {message}"
        }))

    # # Ejemplo de manejador de mensajes de grupo
    # async def chat_message(self, event):
    #     message = event['message']
    #     # Send message to WebSocket
    #     await self.send(text_data=json.dumps({
    #         'message': message
    #     }))
"""
APP_ROUTING_PY_CONTENT_TEMPLATE = """from django.urls import re_path
# from . import consumers # Placeholder

websocket_urlpatterns = [
    # re_path(r'ws/app_name_placeholder/(?P<room_name>\\w+)/$', consumers.ConsumerNamePlaceholder.as_asgi()),
    # re_path(r'ws/app_name_placeholder/$', consumers.ConsumerNamePlaceholder.as_asgi()),
]
"""

# --- Specific Model Content ---
USERS_MODELS_PY_CONTENT = """
from django.contrib.auth.models import AbstractUser
from django.db import models
# from apps.locations.models import Ciudad # Descomentar cuando locations.models.Ciudad exista

class User(AbstractUser):
    class Role(models.TextChoices):
        CIUDADANO = "CIUDADANO", "Ciudadano"
        OPERADOR = "OPERADOR", "Operador 911"
        BRIGADA = "BRIGADA", "Brigada Rural"
        ADMIN = "ADMIN", "Administrador"

    role = models.CharField(max_length=20, choices=Role.choices, default=Role.CIUDADANO)
    # Otros campos como 'telefono', 'direccion_particular' pueden ir aquí o en UserProfile

    def __str__(self):
        return self.username

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    nombre_completo = models.CharField(max_length=255, blank=True)
    direccion = models.CharField(max_length=255, blank=True)
    # ciudad = models.ForeignKey('locations.Ciudad', on_delete=models.SET_NULL, null=True, blank=True) # Usar string para evitar import circular al inicio
    telefono = models.CharField(max_length=20, blank=True)
    edad = models.PositiveIntegerField(null=True, blank=True)

    def __str__(self):
        return f"Perfil de {self.user.username}"
"""

LOCATIONS_MODELS_PY_CONTENT = """
from django.contrib.gis.db import models as gis_models
from django.db import models
from django.conf import settings

class UnidadRegional(models.Model):
    nombre = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.nombre

    class Meta:
        verbose_name = "Unidad Regional"
        verbose_name_plural = "Unidades Regionales"

class Ciudad(models.Model):
    nombre = models.CharField(max_length=100)
    unidad_regional = models.ForeignKey(UnidadRegional, on_delete=models.PROTECT, related_name="ciudades")
    # centro_geografico = gis_models.PointField(null=True, blank=True, srid=4326)

    def __str__(self):
        return f"{self.nombre} ({self.unidad_regional.nombre})"

    class Meta:
        verbose_name = "Ciudad"
        verbose_name_plural = "Ciudades"
        unique_together = ('nombre', 'unidad_regional')

class BrigadaMovilLocation(models.Model):
    brigada_usuario = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'role': 'BRIGADA'},
        related_name='ubicacion_movil'
    )
    ubicacion_actual = gis_models.PointField(srid=4326)
    ultima_actualizacion = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Ubicación de {self.brigada_usuario.username} a las {self.ultima_actualizacion.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        verbose_name = "Ubicación de Móvil de Brigada"
        verbose_name_plural = "Ubicaciones de Móviles de Brigada"
        indexes = [
            gis_models.Index(fields=['ubicacion_actual']),
        ]
"""

INCIDENTS_MODELS_PY_CONTENT = """
from django.contrib.gis.db import models as gis_models
from django.db import models
from django.conf import settings
# from apps.locations.models import Ciudad # Descomentar

class Incidencia(models.Model):
    class EstadoChoices(models.TextChoices):
        NUEVA = "NUEVA", "Nueva"
        ASIGNADA_OPERADOR = "ASIGNADA_OPERADOR", "Asignada a Operador"
        EN_PROCESO_OPERADOR = "EN_PROCESO_OPERADOR", "En Proceso por Operador"
        DERIVADA_BRIGADA = "DERIVADA_BRIGADA", "Derivada a Brigada"
        EN_PROCESO_BRIGADA = "EN_PROCESO_BRIGADA", "En Proceso por Brigada"
        CERRADA_RESUELTA = "CERRADA_RESUELTA", "Cerrada - Resuelta"
        CERRADA_NO_RESUELTA = "CERRADA_NO_RESUELTA", "Cerrada - No Resuelta"
        CANCELADA = "CANCELADA", "Cancelada por Usuario"

    usuario_reporta = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="incidencias_reportadas",
        limit_choices_to={'role': 'CIUDADANO'}
    )
    descripcion_texto = models.TextField(blank=True, null=True)
    audio_url = models.URLField(max_length=500, blank=True, null=True)
    foto_url = models.URLField(max_length=500, blank=True, null=True)
    ubicacion_incidencia = gis_models.PointField(srid=4326)
    # ciudad_reportada = models.ForeignKey('locations.Ciudad', on_delete=models.SET_NULL, null=True, blank=True)
    estado = models.CharField(max_length=30, choices=EstadoChoices.choices, default=EstadoChoices.NUEVA)
    fecha_creacion = models.DateTimeField(auto_now_add=True)
    fecha_actualizacion = models.DateTimeField(auto_now=True)
    operador_asignado = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name="incidencias_asignadas_operador",
        limit_choices_to={'role': 'OPERADOR'}
    )
    brigada_asignada = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name="incidencias_asignadas_brigada",
        limit_choices_to={'role': 'BRIGADA'}
    )
    objects = gis_models.Manager()

    def __str__(self):
        return f"Incidencia #{self.id} por {self.usuario_reporta.username} - {self.estado}"

    class Meta:
        ordering = ['-fecha_creacion']
        indexes = [
            gis_models.Index(fields=['ubicacion_incidencia']),
        ]
"""

CHAT_MODELS_PY_CONTENT = """
from django.db import models
from django.conf import settings
# from apps.incidents.models import Incidencia # Descomentar

class MensajeChat(models.Model):
    incidencia = models.ForeignKey('incidents.Incidencia', on_delete=models.CASCADE, related_name="mensajes_chat")
    remitente = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="mensajes_enviados")
    texto = models.TextField()
    fecha_envio = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Mensaje de {self.remitente.username} en Incidencia #{self.incidencia.id} a las {self.fecha_envio}"

    class Meta:
        ordering = ['fecha_envio']
"""


# --- Main Script Logic ---
def main():
    # 1. Create base directory
    project_root_path = os.path.abspath(BASE_DIR_NAME)
    os.makedirs(project_root_path, exist_ok=True)
    print(f"Created base directory: {project_root_path}")

    # 2. Create top-level files and directories
    create_file(os.path.join(project_root_path, ".gitignore"), GITIGNORE_CONTENT, project_root_path)
    create_file(os.path.join(project_root_path, "README.md"), README_CONTENT_TEMPLATE, project_root_path)
    os.makedirs(os.path.join(project_root_path, "mobile_app"), exist_ok=True)
    os.makedirs(os.path.join(project_root_path, "web_panel"), exist_ok=True)

    # 3. Create backend structure
    backend_base_path = os.path.join(project_root_path, BACKEND_DIR_NAME)
    os.makedirs(backend_base_path, exist_ok=True)

    # Rellenar plantillas con el nombre del proyecto
    manage_py_final_content = MANAGE_PY_CONTENT_TEMPLATE.replace("PROJECT_NAME_PLACEHOLDER", PROJECT_NAME)
    create_file(os.path.join(backend_base_path, "manage.py"), manage_py_final_content, project_root_path)
    os.chmod(os.path.join(backend_base_path, "manage.py"), 0o755) # Make executable

    create_file(os.path.join(backend_base_path, "requirements.txt"), REQUIREMENTS_TXT_CONTENT, project_root_path)
    create_file(os.path.join(backend_base_path, ".env.example"), DOTENV_EXAMPLE_CONTENT, project_root_path)

    # Create Django project directory
    django_project_path = os.path.join(backend_base_path, PROJECT_NAME)
    os.makedirs(django_project_path, exist_ok=True)

    settings_py_final_content = PROJECT_SETTINGS_PY_CONTENT_TEMPLATE.replace("PROJECT_NAME_PLACEHOLDER", PROJECT_NAME)
    asgi_py_final_content = PROJECT_ASGI_PY_CONTENT_TEMPLATE.replace("PROJECT_NAME_PLACEHOLDER", PROJECT_NAME)
    wsgi_py_final_content = PROJECT_WSGI_PY_CONTENT_TEMPLATE.replace("PROJECT_NAME_PLACEHOLDER", PROJECT_NAME)

    create_file(os.path.join(django_project_path, "__init__.py"), PROJECT_INIT_PY_CONTENT, project_root_path)
    create_file(os.path.join(django_project_path, "settings.py"), settings_py_final_content, project_root_path)
    create_file(os.path.join(django_project_path, "urls.py"), PROJECT_URLS_PY_CONTENT, project_root_path) # No necesita placeholder
    create_file(os.path.join(django_project_path, "asgi.py"), asgi_py_final_content, project_root_path)
    create_file(os.path.join(django_project_path, "wsgi.py"), wsgi_py_final_content, project_root_path)

    # Create 'apps' directory
    apps_base_path = os.path.join(backend_base_path, "apps")
    os.makedirs(apps_base_path, exist_ok=True)
    create_file(os.path.join(apps_base_path, "__init__.py"), "", project_root_path)

    # Create individual apps
    for app_name in APP_NAMES:
        app_path = os.path.join(apps_base_path, app_name)
        os.makedirs(app_path, exist_ok=True)

        create_file(os.path.join(app_path, "__init__.py"), APP_INIT_PY_CONTENT, project_root_path)
        create_file(os.path.join(app_path, "admin.py"), APP_ADMIN_PY_CONTENT, project_root_path)

        app_config_content = f"""from django.apps import AppConfig

class {app_name.capitalize()}Config(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.{app_name}'
"""
        create_file(os.path.join(app_path, "apps.py"), app_config_content, project_root_path)

        # Specific models content
        if app_name == "users":
            models_content = USERS_MODELS_PY_CONTENT
        elif app_name == "locations":
            models_content = LOCATIONS_MODELS_PY_CONTENT
        elif app_name == "incidents":
            models_content = INCIDENTS_MODELS_PY_CONTENT
        elif app_name == "chat":
            models_content = CHAT_MODELS_PY_CONTENT
        else: # core or any other
            models_content = APP_MODELS_PY_CONTENT
        create_file(os.path.join(app_path, "models.py"), models_content, project_root_path)


        create_file(os.path.join(app_path, "tests.py"), APP_TESTS_PY_CONTENT, project_root_path)
        create_file(os.path.join(app_path, "views.py"), APP_VIEWS_PY_CONTENT, project_root_path)
        create_file(os.path.join(app_path, "serializers.py"), APP_SERIALIZERS_PY_CONTENT, project_root_path)
        create_file(os.path.join(app_path, "urls.py"), APP_URLS_PY_CONTENT, project_root_path)

        if app_name in APPS_WITH_WEBSOCKET_FILES:
            consumer_name = f"{app_name.capitalize()}Consumer"
            personalized_consumer_content = APP_CONSUMERS_PY_CONTENT_TEMPLATE.replace(
                "YourAppConsumerPlaceholder", consumer_name
            ).replace(
                "'Connected to WebSocket!' # Placeholder", f"'Connected to {app_name.capitalize()} WebSocket!'"
            )
            create_file(os.path.join(app_path, "consumers.py"), personalized_consumer_content, project_root_path)

            personalized_routing_content = APP_ROUTING_PY_CONTENT_TEMPLATE.replace(
                "# from . import consumers # Placeholder", f"from . import consumers"
            ).replace(
                "app_name_placeholder", app_name
            ).replace(
                "ConsumerNamePlaceholder", consumer_name
            )
            create_file(os.path.join(app_path, "routing.py"), personalized_routing_content, project_root_path)


    print("\n--- Project structure created successfully! ---")
    print("Each file now starts with a path comment.")
    print("Please see README.md for next steps.")
    print(f"Key file: {os.path.join(project_root_path, BACKEND_DIR_NAME, '.env.example')} (copy to .env and configure)")

if __name__ == "__main__":
    main()