#!/usr/bin/env python3
# --- Archivo: update_recovered_db.py ---
# Script para actualizar la estructura del archivo recuperado

import sqlite3
import shutil
from datetime import datetime

def update_recovered_database():
    """Actualizar la estructura de la base de datos recuperada."""
    print("🔧 Actualizando estructura de la base de datos recuperada...")

    db_path = "instance/app.db"

    # Hacer backup de seguridad
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = f"instance/app.db.before_update_{timestamp}"
    shutil.copy2(db_path, backup_path)
    print(f"✅ Backup creado: {backup_path}")

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Verificar estructura actual de user
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        print(f"📋 Columnas actuales en user: {column_names}")

        # Agregar columnas faltantes a la tabla user (sin DEFAULT CURRENT_TIMESTAMP)
        columns_to_add = [
            ('role', 'VARCHAR(20)'),
            ('is_active', 'BOOLEAN'),
            ('created_by', 'INTEGER'),
            ('created_at', 'TIMESTAMP'),
            ('updated_at', 'TIMESTAMP')
        ]

        for column_name, column_def in columns_to_add:
            if column_name not in column_names:
                try:
                    cursor.execute(f"ALTER TABLE user ADD COLUMN {column_name} {column_def};")
                    print(f"  ✅ Columna {column_name} agregada")
                except Exception as e:
                    print(f"  ❌ Error agregando {column_name}: {e}")

        # Actualizar valores por defecto para usuarios existentes
        cursor.execute("UPDATE user SET role = 'administrador' WHERE role IS NULL OR role = '';")
        cursor.execute("UPDATE user SET is_active = 1 WHERE is_active IS NULL;")
        cursor.execute("UPDATE user SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL;")
        cursor.execute("UPDATE user SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;")

        print("✅ Valores por defecto actualizados")

        # Verificar si existen las tablas de permisos
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        existing_tables = [table[0] for table in cursor.fetchall()]

        print(f"📋 Tablas existentes: {existing_tables}")

        # Crear tablas de permisos si no existen
        if 'user_city_permissions' not in existing_tables:
            cursor.execute('''
                CREATE TABLE user_city_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    city VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, city)
                );
            ''')
            print("✅ Tabla user_city_permissions creada")

        if 'user_source_permissions' not in existing_tables:
            cursor.execute('''
                CREATE TABLE user_source_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    source VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, source)
                );
            ''')
            print("✅ Tabla user_source_permissions creada")

        if 'user_permissions' not in existing_tables:
            cursor.execute('''
                CREATE TABLE user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    permission_type VARCHAR(50) NOT NULL,
                    permission_value BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, permission_type)
                );
            ''')
            print("✅ Tabla user_permissions creada")

        # Crear índices importantes
        indexes = [
            ("ix_user_username", "user", "username"),
            ("ix_user_email", "user", "email"),
            ("ix_point_city", "point", "city"),
            ("ix_point_source", "point", "source"),
            ("ix_point_status", "point", "status"),
            ("ix_image_point_id", "image", "point_id"),
            ("ix_camera_point_id", "camera", "point_id")
        ]

        for index_name, table_name, column_name in indexes:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name});")
                print(f"  ✅ Índice {index_name} creado")
            except Exception as e:
                print(f"  ⚠️ Error creando índice {index_name}: {e}")

        # Commit cambios
        conn.commit()

        # Verificar resultado final
        print("\n📊 Verificando resultado...")

        cursor.execute("PRAGMA table_info(user);")
        final_columns = [col[1] for col in cursor.fetchall()]
        print(f"📋 Columnas finales en user: {final_columns}")

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        final_tables = [table[0] for table in cursor.fetchall()]
        print(f"📋 Tablas finales: {final_tables}")

        # Mostrar estadísticas
        cursor.execute("SELECT COUNT(*) FROM user;")
        user_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM point;")
        point_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM image;")
        image_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM camera;")
        camera_count = cursor.fetchone()[0]

        print(f"\n📊 Datos preservados:")
        print(f"  👥 Usuarios: {user_count}")
        print(f"  📍 Puntos: {point_count}")
        print(f"  🖼️ Imágenes: {image_count}")
        print(f"  📷 Cámaras: {camera_count}")

        # Mostrar usuarios
        cursor.execute("SELECT username, role FROM user;")
        users = cursor.fetchall()
        print(f"\n👥 Usuarios disponibles:")
        for user in users:
            print(f"  - {user[0]} ({user[1]})")

        conn.close()

        return True

    except Exception as e:
        print(f"❌ Error actualizando base de datos: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal."""
    print("🔧 Actualizador de Base de Datos Recuperada")
    print("=" * 45)

    print("⚠️ Este proceso va a:")
    print("  1. Agregar columnas de permisos a la tabla user")
    print("  2. Crear tablas de permisos (user_city_permissions, etc.)")
    print("  3. Crear índices para optimizar rendimiento")
    print("  4. Preservar TODOS los datos recuperados")
    print("  5. Hacer la base de datos compatible con la aplicación")

    response = input("\n¿Continuar? (s/n): ").strip().lower()

    if response in ['s', 'si', 'y', 'yes']:
        if update_recovered_database():
            print("\n🎉 ¡Base de datos actualizada exitosamente!")
            print("\n🚀 Próximos pasos:")
            print("   1. Reiniciar aplicación: systemctl restart relevamiento")
            print("   2. Probar login con usuarios originales")
            print("   3. Verificar que todos los datos están disponibles")
            print("\n🔑 Usuarios disponibles:")
            print("   - Todos los usuarios originales con sus contraseñas")
            print("   - Todos mantienen rol 'administrador'")
        else:
            print("\n❌ Error actualizando base de datos")
    else:
        print("❌ Actualización cancelada")

if __name__ == "__main__":
    main()
