# test_ptt.py

import serial

try:
    ser = serial.Serial('COM1', baudrate=9600, timeout=1)  # Ajusta el puerto y los parámetros según tu configuración
    print(f"Puerto {ser.name} abierto.")
    ser.setDTR(True)  # Activa DTR (simula el PTT)
    input("Presiona Enter para desactivar el PTT...")  # Mantiene el PTT activado hasta que presiones Enter
    ser.setDTR(False)  # Desactiva DTR (desactiva el PTT)
    ser.close()  # Cierra el puerto serie
except serial.SerialException as e:
    print(f"Error al abrir el puerto: {e}")
