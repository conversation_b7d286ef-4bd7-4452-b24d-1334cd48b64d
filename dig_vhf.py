# dig_vhf.py

import time
import socketio
import json
from ptt_control import set_ptt
import base64
from io import BytesIO
import wave
import simpleaudio as sa

sio = socketio.Client()
serial_connection = None

def load_config():
    try:
        with open('config.json', 'r', encoding='utf-8') as file:
            config = json.load(file)
            if 'port_number' not in config:
                raise ValueError("El archivo de configuración no contiene 'port_number'.")
            return config
    except FileNotFoundError:
        print("Error: config.json no encontrado.")
        return None
    except ValueError as ve:
        print("Error en la configuración:", ve)
        return None

def play_audio(audio_data, config):
    try:
        print("Iniciando reproducción de audio y activación de PTT")
        audio_bytes = base64.b64decode(audio_data)
        audio_stream = BytesIO(audio_bytes)

        # Activar PTT antes de reproducir el audio
        print("Activando PTT...")
        activar_ptt(config)

        # Esperar un momento para asegurar que el PTT esté activado
        time.sleep(0.5)

        print("Reproduciendo audio...")
        with wave.open(audio_stream, 'rb') as wave_obj:
            # Obtener información del audio
            channels = wave_obj.getnchannels()
            sample_width = wave_obj.getsampwidth()
            frame_rate = wave_obj.getframerate()
            frames = wave_obj.readframes(wave_obj.getnframes())

            print(f"Información del audio: canales={channels}, sample_width={sample_width}, frame_rate={frame_rate}, frames_length={len(frames)}")

            # Crear y reproducir el objeto de audio
            play_obj = sa.WaveObject(frames, channels, sample_width, frame_rate).play()

            # Esperar a que termine la reproducción
            print("Esperando a que termine la reproducción...")
            play_obj.wait_done()
            print("Reproducción finalizada")

        # Esperar un momento antes de desactivar el PTT
        time.sleep(0.5)

        # Desactivar PTT después de reproducir el audio
        print("Desactivando PTT...")
        desactivar_ptt(config)
        print("PTT desactivado")

    except Exception as e:
        print(f"Error al reproducir el audio: {e}")
        # Asegurar que el PTT se desactive en caso de error
        try:
            desactivar_ptt(config)
            print("PTT desactivado después de error")
        except Exception as err:
            print(f"Error al desactivar PTT después de error: {err}")

def activar_ptt(config):
    global serial_connection
    serial_connection = set_ptt(True, config, serial_connection)

def desactivar_ptt(config):
    global serial_connection
    serial_connection = set_ptt(False, config, serial_connection)

def connect_to_socket(config):
    @sio.event
    def connect():
        print("Conectado al servidor")

    @sio.event
    def disconnect():
        print("Desconectado del servidor")

    @sio.on('transmit_audio', namespace='/node')
    def on_transmit_audio(data):
        print(f"Audio recibido de {data.get('user', 'desconocido')}")
        if data.get('user') != config['username'] and 'audio' in data:
            play_audio(data['audio'], config)

    @sio.on('audio_received', namespace='/node')
    def on_audio_received(data):
        print(f"Evento audio_received de {data.get('user', 'desconocido')}")
        if data.get('user') != config['username'] and 'audio' in data:
            play_audio(data['audio'], config)

    sio.connect(f"wss://{config['node_url']}",
                headers={'node_id': config['node_id'], 'username': config['username']},
                namespaces=['/node'])

if __name__ == '__main__':
    config = load_config()
    if config:
        try:
            while True:
                time.sleep(0.5)
        except KeyboardInterrupt:
            sio.disconnect()
    else:
        print("Error al cargar la configuración.")
