# dig_vhf.py

import time
import socketio
import json
from ptt_control import set_ptt
import base64
from io import BytesIO
import wave
import simpleaudio as sa

sio = socketio.Client()
serial_connection = None

def load_config():
    try:
        with open('config.json', 'r', encoding='utf-8') as file:
            config = json.load(file)
            if 'port_number' not in config:
                raise ValueError("El archivo de configuración no contiene 'port_number'.")
            return config
    except FileNotFoundError:
        print("Error: config.json no encontrado.")
        return None
    except ValueError as ve:
        print("Error en la configuración:", ve)
        return None

def play_audio(audio_data, config):
    try:
        audio_bytes = base64.b64decode(audio_data)
        audio_stream = BytesIO(audio_bytes)
        activar_ptt(config)
        time.sleep(1)
        with wave.open(audio_stream, 'rb') as wave_obj:
            play_obj = sa.WaveObject(
                wave_obj.readframes(wave_obj.getnframes()),
                wave_obj.getnchannels(),
                wave_obj.getsampwidth(),
                wave_obj.getframerate()
            ).play()
            play_obj.wait_done()
        time.sleep(1)
        desactivar_ptt(config)
    except Exception as e:
        print("Error al reproducir el audio:", e)

def activar_ptt(config):
    global serial_connection
    serial_connection = set_ptt(True, config, serial_connection)

def desactivar_ptt(config):
    global serial_connection
    serial_connection = set_ptt(False, config, serial_connection)

def connect_to_socket(config):
    @sio.event
    def connect():
        pass
    @sio.event
    def disconnect():
        pass
    sio.connect(f"wss://{config['node_url']}",
                headers={'node_id': config['node_id'], 'username': config['username']},
                namespaces=['/node'])

if __name__ == '__main__':
    config = load_config()
    if config:
        try:
            while True:
                time.sleep(0.5)
        except KeyboardInterrupt:
            sio.disconnect()
    else:
        print("Error al cargar la configuración.")
