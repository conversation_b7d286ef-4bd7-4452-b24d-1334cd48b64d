# File: backend/apps/incidents/views.py
# -----------------------------------------------
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.views.decorators.http import require_POST
from .forms import IncidenciaForm, AsignarBrigadaForm, CambiarEstadoIncidenciaForm
from django.contrib.gis.geos import Point # Para crear el punto de ubicación
from rest_framework import viewsets
from django.views.generic import ListView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from .models import Incidencia # Ya deberías tenerla importada si tienes crear_incidencia_view

@login_required
def crear_incidencia_view(request):
    if request.user.role != 'CIUDADANO':
        messages.error(request, "Solo los ciudadanos pueden reportar incidencias.")
        # Redirige al dashboard correspondiente según el rol si no es ciudadano
        if request.user.role == 'OPERADOR':
            return redirect('dashboard_operador')
        elif request.user.role == 'BRIGADA':
            return redirect('dashboard_brigada')
        elif request.user.is_superuser or request.user.is_staff:
            return redirect('admin:index')
        else:
            return redirect('login') # O una página de "acceso denegado"

    if request.method == 'POST':
        form = IncidenciaForm(request.POST, request.FILES)
        if form.is_valid():
            incidencia = form.save(commit=False)
            incidencia.usuario_reporta = request.user

            # ---- Obtener y asignar ubicación desde el formulario ----
            lat_str = request.POST.get('latitud')
            lon_str = request.POST.get('longitud')

            if lat_str and lon_str: # Verifica que ambos valores existan
                try:
                    lat = float(lat_str)
                    lon = float(lon_str)
                    incidencia.ubicacion_incidencia = Point(lon, lat, srid=4326) # OJO: Point es (longitud, latitud)
                except ValueError:
                    messages.warning(request, "No se pudo procesar la ubicación. Se guardará sin coordenadas específicas.")
                    # Decide si quieres un punto por defecto o permitir null si el modelo lo permite
                    if incidencia._meta.get_field('ubicacion_incidencia').null:
                        incidencia.ubicacion_incidencia = None
                    else:
                        # Si el campo no puede ser null, debes asignar un valor o el save() fallará
                        messages.error(request, "Error crítico: El campo de ubicación no puede estar vacío pero no se proveyeron coordenadas válidas.")
                        # Podrías renderizar el form de nuevo con un error aquí, o tener un punto por defecto muy obvio
                        # incidencia.ubicacion_incidencia = Point(0,0, srid=4326) # No recomendado sin advertencia
            else:
                messages.info(request, "No se detectó ubicación GPS. El reporte se ha enviado sin coordenadas específicas.")
                # Si el campo no puede ser null, necesitas manejar esto.
                if not incidencia._meta.get_field('ubicacion_incidencia').null:
                     messages.error(request, "Error crítico: El campo de ubicación no puede estar vacío.")
                     # incidencia.ubicacion_incidencia = Point(0,0, srid=4326) # No recomendado sin advertencia

            # ---- Manejo de Archivos (Placeholder) ----
            # ...

            try:
                incidencia.save()
                messages.success(request, "Incidencia reportada exitosamente.")
                return redirect('dashboard_ciudadano') # Corregido
            except Exception as e:
                # Esto es importante si, por ejemplo, ubicacion_incidencia es obligatoria y no se pudo establecer
                messages.error(request, f"No se pudo guardar la incidencia: {e}")
        else:
            messages.error(request, "Por favor corrige los errores en el formulario.")
    else:
        form = IncidenciaForm()

    return render(request, 'incidents/crear_incidencia.html', {'form': form})

# Create your views here.

# Mixin para verificar si el usuario es Operador o Admin/Superusuario
class OperadorOAdminRequiredMixin(UserPassesTestMixin):
    def test_func(self):
        return self.request.user.is_authenticated and \
               (self.request.user.role == 'OPERADOR' or self.request.user.is_superuser or self.request.user.is_staff)

    def handle_no_permission(self):
        messages.error(self.request, "Acceso no autorizado.")
        return redirect('login') # O a donde consideres apropiado

class IncidenciaListViewOperador(LoginRequiredMixin, OperadorOAdminRequiredMixin, ListView):
    model = Incidencia
    template_name = 'incidents/lista_incidencias_operador.html' # Crearemos esta plantilla
    context_object_name = 'incidencias'
    paginate_by = 10 # Opcional: para paginación

    def get_queryset(self):
        # Mostrar todas las incidencias y permitir filtros
        queryset = Incidencia.objects.all()

        # Obtener el filtro de estado de la URL
        estado_filter = self.request.GET.get('estado', None)
        brigada_filter = self.request.GET.get('brigada', None)

        # Aplicar filtro por estado si existe
        if estado_filter:
            if estado_filter == 'nuevas':
                queryset = queryset.filter(estado='NUEVA')
            elif estado_filter == 'proceso':
                queryset = queryset.filter(estado__in=['ASIGNADA_OPERADOR', 'EN_PROCESO_OPERADOR'])
            elif estado_filter == 'derivadas':
                queryset = queryset.filter(estado__in=['DERIVADA_BRIGADA', 'EN_PROCESO_BRIGADA'])
            elif estado_filter == 'resueltas':
                queryset = queryset.filter(estado__in=['CERRADA_RESUELTA', 'CERRADA_NO_RESUELTA'])
            # Si no hay filtro o el filtro es 'todas', se muestran todas las incidencias

        # Aplicar filtro por brigada si existe
        if brigada_filter:
            try:
                brigada_id = int(brigada_filter)
                from apps.users.models import User
                brigada = User.objects.filter(id=brigada_id, role='BRIGADA').first()
                if brigada:
                    queryset = queryset.filter(brigada_asignada=brigada)
                    # Añadir el nombre de la brigada al contexto para mostrarlo en la plantilla
                    self.brigada_nombre = brigada.username
            except (ValueError, TypeError):
                pass

        return queryset.order_by('-fecha_creacion') # Las más recientes primero

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Obtener el filtro de estado de la URL
        estado_filter = self.request.GET.get('estado', None)
        brigada_filter = self.request.GET.get('brigada', None)

        # Añadir título según el filtro
        if estado_filter == 'nuevas':
            context['titulo_pagina'] = "Incidencias Nuevas"
            context['estado_filtro'] = 'nuevas'
        elif estado_filter == 'proceso':
            context['titulo_pagina'] = "Incidencias En Proceso"
            context['estado_filtro'] = 'proceso'
        elif estado_filter == 'derivadas':
            if hasattr(self, 'brigada_nombre') and brigada_filter:
                context['titulo_pagina'] = f"Incidencias Asignadas a Brigada: {self.brigada_nombre}"
            else:
                context['titulo_pagina'] = "Incidencias Asignadas a Brigadas"
            context['estado_filtro'] = 'derivadas'
        elif estado_filter == 'resueltas':
            context['titulo_pagina'] = "Incidencias Resueltas"
            context['estado_filtro'] = 'resueltas'
        else:
            if hasattr(self, 'brigada_nombre') and brigada_filter:
                context['titulo_pagina'] = f"Todas las Incidencias de Brigada: {self.brigada_nombre}"
            else:
                context['titulo_pagina'] = "Panel de Incidencias - Operador"
            context['estado_filtro'] = 'todas'

        # Añadir contadores para cada estado
        context['count_nuevas'] = Incidencia.objects.filter(estado='NUEVA').count()
        context['count_proceso'] = Incidencia.objects.filter(estado__in=['ASIGNADA_OPERADOR', 'EN_PROCESO_OPERADOR']).count()
        context['count_derivadas'] = Incidencia.objects.filter(estado__in=['DERIVADA_BRIGADA', 'EN_PROCESO_BRIGADA']).count()
        context['count_resueltas'] = Incidencia.objects.filter(estado__in=['CERRADA_RESUELTA', 'CERRADA_NO_RESUELTA']).count()
        context['count_todas'] = Incidencia.objects.all().count()

        # Añadir información para el mapa
        context['mostrar_mapa'] = True

        return context

class IncidenciaDetailViewOperador(LoginRequiredMixin, OperadorOAdminRequiredMixin, DetailView):
    model = Incidencia
    template_name = 'incidents/detalle_incidencia_operador.html' # Crearemos esta plantilla
    context_object_name = 'incidencia'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['titulo_pagina'] = f"Detalle Incidencia #{self.object.id}"

        # Añadir formulario para asignar brigada
        context['asignar_brigada_form'] = AsignarBrigadaForm()

        # Añadir formulario para cambiar estado
        context['cambiar_estado_form'] = CambiarEstadoIncidenciaForm(estado_actual=self.object.estado)

        return context

class IncidenciaDetailViewCiudadano(LoginRequiredMixin, DetailView):
    model = Incidencia
    template_name = 'incidents/detalle_incidencia_ciudadano.html'
    context_object_name = 'incidencia'

    def dispatch(self, request, *args, **kwargs):
        # Verificar que el usuario sea el que reportó la incidencia
        incidencia = self.get_object()
        if request.user != incidencia.usuario_reporta and not request.user.is_staff:
            messages.error(request, "No tienes permiso para ver esta incidencia.")
            return redirect('dashboard_ciudadano')
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['titulo_pagina'] = f"Detalle Incidencia #{self.object.id}"

        # Obtener la ubicación actual del usuario
        from apps.locations.models import UbicacionUsuario
        try:
            ubicacion_obj = UbicacionUsuario.objects.get(usuario=self.request.user)
            if ubicacion_obj.posicion_actual:
                context['ubicacion_actual_usuario'] = {
                    "lat": ubicacion_obj.posicion_actual.y,
                    "lng": ubicacion_obj.posicion_actual.x
                }
        except UbicacionUsuario.DoesNotExist:
            context['ubicacion_actual_usuario'] = None

        # Si hay una brigada asignada, obtener su ubicación
        if self.object.brigada_asignada and (self.object.estado == 'DERIVADA_BRIGADA' or self.object.estado == 'EN_PROCESO_BRIGADA'):
            try:
                brigada_ubicacion = UbicacionUsuario.objects.get(usuario=self.object.brigada_asignada)
                if brigada_ubicacion.posicion_actual and self.object.ubicacion_incidencia:
                    # Datos de ubicación de la brigada
                    context['brigada_ubicacion'] = {
                        "lat": brigada_ubicacion.posicion_actual.y,
                        "lng": brigada_ubicacion.posicion_actual.x,
                        "ultima_actualizacion": brigada_ubicacion.fecha_actualizacion.strftime("%d/%m/%Y %H:%M")
                    }

                    # Calcular la ruta entre la brigada y la incidencia
                    from apps.core.route_service import calculate_route_with_openrouteservice
                    import logging
                    logger = logging.getLogger(__name__)

                    # Coordenadas en formato (longitud, latitud) para OpenRouteService
                    start_coords = (brigada_ubicacion.posicion_actual.x, brigada_ubicacion.posicion_actual.y)
                    end_coords = (self.object.ubicacion_incidencia.x, self.object.ubicacion_incidencia.y)

                    logger.info(f"Calculando ruta desde {start_coords} hasta {end_coords}")

                    try:
                        # Intentar calcular la ruta usando OpenRouteService
                        route_data = calculate_route_with_openrouteservice(start_coords, end_coords)

                        if route_data:
                            logger.info(f"Ruta calculada con éxito: {len(route_data['coordinates'])} puntos, {route_data['distance']} metros, {route_data['duration']} segundos")
                            context['ruta'] = {
                                'coordenadas': route_data['coordinates'],
                                'distancia': route_data['distance'],
                                'duracion': route_data['duration']
                            }
                        else:
                            logger.warning("No se pudo calcular la ruta con OpenRouteService")

                            # Calcular distancia en línea recta como fallback
                            import math

                            # Convertir coordenadas a radianes
                            lat1 = math.radians(brigada_ubicacion.posicion_actual.y)
                            lon1 = math.radians(brigada_ubicacion.posicion_actual.x)
                            lat2 = math.radians(self.object.ubicacion_incidencia.y)
                            lon2 = math.radians(self.object.ubicacion_incidencia.x)

                            # Fórmula de Haversine
                            dlon = lon2 - lon1
                            dlat = lat2 - lat1
                            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
                            c = 2 * math.asin(math.sqrt(a))
                            r = 6371000  # Radio de la Tierra en metros

                            # Calcular distancia en metros
                            distancia = c * r

                            # Estimar duración (asumiendo velocidad promedio de 40 km/h)
                            duracion = (distancia / 1000) / 40 * 3600  # Convertir a segundos

                            # Crear coordenadas para una línea recta
                            coordenadas = [
                                (brigada_ubicacion.posicion_actual.y, brigada_ubicacion.posicion_actual.x),
                                (self.object.ubicacion_incidencia.y, self.object.ubicacion_incidencia.x)
                            ]

                            context['ruta'] = {
                                'coordenadas': coordenadas,
                                'distancia': distancia,
                                'duracion': duracion,
                                'es_linea_recta': True  # Indicar que es una línea recta
                            }

                            logger.info(f"Calculada distancia en línea recta: {distancia} metros, {duracion} segundos")
                    except Exception as e:
                        logger.error(f"Error al calcular la ruta: {str(e)}", exc_info=True)
            except UbicacionUsuario.DoesNotExist:
                context['brigada_ubicacion'] = None
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error al calcular la ruta: {str(e)}")
                context['brigada_ubicacion'] = {
                    "lat": brigada_ubicacion.posicion_actual.y,
                    "lng": brigada_ubicacion.posicion_actual.x,
                    "ultima_actualizacion": brigada_ubicacion.fecha_actualizacion.strftime("%d/%m/%Y %H:%M")
                }

        return context

@login_required
@require_POST
def asignar_brigada_view(request, pk):
    """
    Vista para asignar una brigada a una incidencia.
    """
    # Verificar que el usuario sea un operador o admin
    if not (request.user.role == 'OPERADOR' or request.user.is_superuser or request.user.is_staff):
        messages.error(request, "No tienes permiso para realizar esta acción.")
        return redirect('login')

    # Obtener la incidencia
    incidencia = get_object_or_404(Incidencia, pk=pk)

    # Procesar el formulario
    form = AsignarBrigadaForm(request.POST)
    if form.is_valid():
        brigada = form.cleaned_data['brigada']

        # Verificar que la incidencia esté en un estado que permita asignar brigada
        if incidencia.estado == 'EN_PROCESO_OPERADOR':
            try:
                # Asignar la brigada
                incidencia.brigada_asignada = brigada
                incidencia.estado = 'DERIVADA_BRIGADA'
                incidencia.save()

                # Aquí podríamos registrar la acción en un log si existiera el campo
                # Por ahora, simplemente guardamos los cambios
                # from django.utils import timezone
                # Si en el futuro se añade un campo log_acciones al modelo, descomentar estas líneas
                # incidencia.log_acciones = (incidencia.log_acciones or '') + f"\n[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] Asignada a brigada {brigada.username} por {request.user.username}"
                # incidencia.save(update_fields=['log_acciones'])

                messages.success(request, f"Incidencia #{incidencia.id} asignada exitosamente a la brigada {brigada.username}.")

                # Enviar notificación a la brigada
                try:
                    from apps.notifications.services import send_incidencia_asignada_notification
                    from datetime import datetime

                    # Importar datetime si no está importado
                    if 'datetime' not in globals():
                        from datetime import datetime

                    notification_sent = send_incidencia_asignada_notification(brigada, incidencia)
                    if notification_sent:
                        print(f"Notificación enviada a la brigada {brigada.username} sobre la incidencia #{incidencia.id}")
                    else:
                        print(f"No se pudo enviar la notificación a la brigada {brigada.username}")
                except Exception as e:
                    print(f"Error al enviar notificación: {str(e)}")

            except Exception as e:
                messages.error(request, f"Error al asignar la brigada: {str(e)}")
        else:
            messages.warning(request, "La incidencia debe estar en estado 'En Proceso por Operador' para asignar una brigada. Por favor, cambie el estado primero.")
    else:
        messages.error(request, "Error en el formulario. Por favor, selecciona una brigada válida.")

    return redirect('incidents:detalle_incidencia_operador', pk=pk)

@login_required
@require_POST
def cambiar_estado_incidencia_view(request, pk):
    """
    Vista para cambiar el estado de una incidencia.
    """
    # Verificar que el usuario sea un operador o admin
    if not (request.user.role == 'OPERADOR' or request.user.is_superuser or request.user.is_staff):
        messages.error(request, "No tienes permiso para realizar esta acción.")
        return redirect('login')

    # Obtener la incidencia
    incidencia = get_object_or_404(Incidencia, pk=pk)

    # Procesar el formulario
    form = CambiarEstadoIncidenciaForm(request.POST, estado_actual=incidencia.estado)
    if form.is_valid():
        nuevo_estado = form.cleaned_data['nuevo_estado']
        estado_anterior = incidencia.estado

        try:
            # Verificar que la transición de estado sea válida
            if nuevo_estado == 'ASIGNADA_OPERADOR' and incidencia.estado == 'NUEVA':
                # Asignar el operador actual
                incidencia.operador_asignado = request.user

            # Actualizar el estado
            incidencia.estado = nuevo_estado

            # Aquí podríamos registrar la acción en un log si existiera el campo
            # Por ahora, simplemente guardamos los cambios
            # from django.utils import timezone
            # Si en el futuro se añade un campo log_acciones al modelo, descomentar estas líneas
            # incidencia.log_acciones = (incidencia.log_acciones or '') + f"\n[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] Estado cambiado de '{estado_anterior}' a '{nuevo_estado}' por {request.user.username}"

            # Guardar los cambios
            incidencia.save()

            # Mensaje de éxito con información específica según el estado
            if nuevo_estado == 'EN_PROCESO_OPERADOR':
                messages.success(request, f"Estado actualizado a '{incidencia.get_estado_display()}'. Ahora puedes asignar una brigada a esta incidencia.")
            elif nuevo_estado == 'DERIVADA_BRIGADA':
                messages.success(request, f"Estado actualizado a '{incidencia.get_estado_display()}'. La brigada ha sido notificada.")
            elif nuevo_estado in ['CERRADA_RESUELTA', 'CERRADA_NO_RESUELTA']:
                messages.success(request, f"Incidencia #{incidencia.id} cerrada como '{incidencia.get_estado_display()}'.")
            else:
                messages.success(request, f"Estado de la incidencia #{incidencia.id} actualizado a '{incidencia.get_estado_display()}'.")

        except Exception as e:
            messages.error(request, f"Error al actualizar el estado: {str(e)}")
    else:
        messages.error(request, "Error en el formulario. Por favor, selecciona un estado válido.")

    return redirect('incidents:detalle_incidencia_operador', pk=pk)

@login_required
def buscar_incidencia_view(request):
    """
    Vista para buscar incidencias con diferentes criterios.
    """
    # Verificar que el usuario sea un operador o admin
    if not (request.user.role == 'OPERADOR' or request.user.is_superuser or request.user.is_staff):
        messages.error(request, "No tienes permiso para realizar esta acción.")
        return redirect('login')

    # Inicializar variables
    incidencias = None
    search_performed = False

    # Verificar si se ha realizado una búsqueda
    if any(param for param in request.GET.keys() if param in ['id', 'usuario', 'estado', 'texto']):
        search_performed = True

        # Iniciar la consulta base
        query = Incidencia.objects.all()

        # Filtrar por ID si se proporciona
        id_incidencia = request.GET.get('id', '')
        if id_incidencia:
            query = query.filter(id=id_incidencia)

        # Filtrar por usuario que reportó
        usuario = request.GET.get('usuario', '')
        if usuario:
            query = query.filter(usuario_reporta__username__icontains=usuario)

        # Filtrar por estado
        estado = request.GET.get('estado', '')
        if estado:
            query = query.filter(estado=estado)

        # Filtrar por texto en la descripción
        texto = request.GET.get('texto', '')
        if texto:
            query = query.filter(descripcion_texto__icontains=texto)

        # Ordenar los resultados
        incidencias = query.order_by('-fecha_creacion')

    # Preparar el contexto
    context = {
        'incidencias': incidencias,
        'search_performed': search_performed,
        'titulo_pagina': "Buscar Incidencia"
    }

    return render(request, 'incidents/buscar_incidencia.html', context)