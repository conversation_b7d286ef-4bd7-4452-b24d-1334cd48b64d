# File: backend/apps/incidents/urls.py
# -----------------------------------------------

from django.urls import path, include
from rest_framework.routers import DefaultRouter
# from .views import YourViewSet
from . import views

app_name = 'incidents' # Buena práctica para namespacing

# router = DefaultRouter()
# router.register(r'yourmodel', YourViewSet)

urlpatterns = [
    path('reportar/', views.crear_incidencia_view, name='reportar_incidencia'),
    path('operador/lista/', views.IncidenciaListViewOperador.as_view(), name='lista_incidencias_operador'),
    path('operador/detalle/<int:pk>/', views.IncidenciaDetailViewOperador.as_view(), name='detalle_incidencia_operador'),
    path('operador/asignar-brigada/<int:pk>/', views.asignar_brigada_view, name='asignar_brigada'),
    path('operador/cambiar-estado/<int:pk>/', views.cambiar_estado_incidencia_view, name='cambiar_estado_incidencia'),
    path('operador/buscar/', views.buscar_incidencia_view, name='buscar_incidencia'),
    path('ciudadano/detalle/<int:pk>/', views.IncidenciaDetailViewCiudadano.as_view(), name='detalle_incidencia_ciudadano'),
    # path('', include(router.urls)),
]
