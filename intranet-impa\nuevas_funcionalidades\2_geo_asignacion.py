# 2_geo_asignacion.py
# Sistema de Geo-asignación Automática para Intranet IMPA

"""
GEO-ASIGNACIÓN AUTOMÁTICA
=========================

Funcionalidades:
- Asignación automática de nuevos miembros por proximidad geográfica
- Mapas interactivos con zonas de influencia
- Cálculo de distancias y rutas optimizadas
- Gestión de territorios por iglesia
- Reportes geográficos y estadísticas
- Integración con Google Maps/OpenStreetMap

Inspirado en Software Redil pero adaptado para corporaciones de iglesias.
"""

import math
import requests
import json
from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from app import db

# ============================================================================
# 1. MODELOS DE BASE DE DATOS
# ============================================================================

class GeographicZone(db.Model):
    """Zonas geográficas de influencia por iglesia"""
    __tablename__ = 'geographic_zones'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=False)
    center_latitude = db.Column(db.Float, nullable=False)
    center_longitude = db.Column(db.Float, nullable=False)
    radius_km = db.Column(db.Float, default=5.0)  # Radio en kilómetros
    polygon_coordinates = db.Column(db.Text)  # JSON con coordenadas del polígono
    color = db.Column(db.String(7), default='#3388ff')  # Color hex para el mapa
    is_active = db.Column(db.Boolean, default=True)
    priority = db.Column(db.Integer, default=1)  # Prioridad para asignación
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    church = db.relationship('Church', backref='geographic_zones')
    assignments = db.relationship('GeoAssignment', backref='zone', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<GeographicZone(name='{self.name}', church='{self.church.name}')>"

class GeoAssignment(db.Model):
    """Asignaciones geográficas de miembros"""
    __tablename__ = 'geo_assignments'
    
    id = db.Column(db.Integer, primary_key=True)
    member_id = db.Column(db.Integer, db.ForeignKey('members.id'), nullable=False)
    zone_id = db.Column(db.Integer, db.ForeignKey('geographic_zones.id'), nullable=False)
    assigned_church_id = db.Column(db.Integer, db.ForeignKey('churches.id'), nullable=False)
    assignment_type = db.Column(db.String(20), default='automatic')  # automatic, manual
    distance_km = db.Column(db.Float)  # Distancia calculada
    assignment_date = db.Column(db.DateTime, default=datetime.utcnow)
    assigned_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    
    # Relaciones
    member = db.relationship('Member', backref='geo_assignments')
    assigned_church = db.relationship('Church', backref='geo_assignments')
    assigned_by = db.relationship('User', backref='geo_assignments_made')

class AddressGeocoding(db.Model):
    """Cache de geocodificación de direcciones"""
    __tablename__ = 'address_geocoding'
    
    id = db.Column(db.Integer, primary_key=True)
    address = db.Column(db.String(500), nullable=False, unique=True)
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    formatted_address = db.Column(db.String(500))
    geocoding_service = db.Column(db.String(50), default='google')
    geocoded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<AddressGeocoding(address='{self.address}')>"

# ============================================================================
# 2. SERVICIOS DE GEOCODIFICACIÓN
# ============================================================================

class GeocodingService:
    """Servicio para geocodificación de direcciones"""
    
    GOOGLE_MAPS_API_KEY = None  # Configurar en config.py
    
    @staticmethod
    def geocode_address(address):
        """Geocodificar dirección usando Google Maps API"""
        # Verificar cache primero
        cached = AddressGeocoding.query.filter_by(address=address).first()
        if cached:
            return {
                'latitude': cached.latitude,
                'longitude': cached.longitude,
                'formatted_address': cached.formatted_address
            }
        
        # Si no está en cache, usar API
        if GeocodingService.GOOGLE_MAPS_API_KEY:
            return GeocodingService._geocode_google(address)
        else:
            return GeocodingService._geocode_nominatim(address)
    
    @staticmethod
    def _geocode_google(address):
        """Geocodificar usando Google Maps API"""
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'address': address,
            'key': GeocodingService.GOOGLE_MAPS_API_KEY
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                result = data['results'][0]
                location = result['geometry']['location']
                
                # Guardar en cache
                geocoding = AddressGeocoding(
                    address=address,
                    latitude=location['lat'],
                    longitude=location['lng'],
                    formatted_address=result['formatted_address'],
                    geocoding_service='google'
                )
                db.session.add(geocoding)
                db.session.commit()
                
                return {
                    'latitude': location['lat'],
                    'longitude': location['lng'],
                    'formatted_address': result['formatted_address']
                }
        except Exception as e:
            print(f"Error geocodificando con Google: {e}")
        
        return None
    
    @staticmethod
    def _geocode_nominatim(address):
        """Geocodificar usando OpenStreetMap Nominatim (gratuito)"""
        url = "https://nominatim.openstreetmap.org/search"
        params = {
            'q': address,
            'format': 'json',
            'limit': 1,
            'countrycodes': 'ar'  # Limitar a Argentina
        }
        
        headers = {
            'User-Agent': 'Intranet-IMPA/1.0'  # Requerido por Nominatim
        }
        
        try:
            response = requests.get(url, params=params, headers=headers)
            data = response.json()
            
            if data:
                result = data[0]
                
                # Guardar en cache
                geocoding = AddressGeocoding(
                    address=address,
                    latitude=float(result['lat']),
                    longitude=float(result['lon']),
                    formatted_address=result['display_name'],
                    geocoding_service='nominatim'
                )
                db.session.add(geocoding)
                db.session.commit()
                
                return {
                    'latitude': float(result['lat']),
                    'longitude': float(result['lon']),
                    'formatted_address': result['display_name']
                }
        except Exception as e:
            print(f"Error geocodificando con Nominatim: {e}")
        
        return None

# ============================================================================
# 3. SERVICIOS DE GEO-ASIGNACIÓN
# ============================================================================

class GeoAssignmentService:
    """Servicio para asignación geográfica automática"""
    
    @staticmethod
    def calculate_distance(lat1, lon1, lat2, lon2):
        """Calcular distancia entre dos puntos usando fórmula de Haversine"""
        R = 6371  # Radio de la Tierra en kilómetros
        
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = (math.sin(dlat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    @staticmethod
    def find_nearest_church(member_address):
        """Encontrar la iglesia más cercana a una dirección"""
        # Geocodificar dirección del miembro
        member_coords = GeocodingService.geocode_address(member_address)
        if not member_coords:
            return None
        
        from app.models import Church
        
        # Obtener todas las iglesias con coordenadas
        churches = Church.query.filter(
            Church.latitude.isnot(None),
            Church.longitude.isnot(None)
        ).all()
        
        nearest_church = None
        min_distance = float('inf')
        
        for church in churches:
            distance = GeoAssignmentService.calculate_distance(
                member_coords['latitude'], member_coords['longitude'],
                church.latitude, church.longitude
            )
            
            if distance < min_distance:
                min_distance = distance
                nearest_church = church
        
        return {
            'church': nearest_church,
            'distance': min_distance,
            'member_coordinates': member_coords
        }
    
    @staticmethod
    def auto_assign_member(member_id, address=None):
        """Asignar automáticamente un miembro a la iglesia más cercana"""
        from app.models import Member, User
        
        member = Member.query.get(member_id)
        if not member:
            return None
        
        # Usar dirección proporcionada o la del usuario
        if not address:
            user = User.query.get(member.user_id)
            address = user.address
        
        if not address:
            return None
        
        # Encontrar iglesia más cercana
        assignment_data = GeoAssignmentService.find_nearest_church(address)
        if not assignment_data:
            return None
        
        nearest_church = assignment_data['church']
        distance = assignment_data['distance']
        
        # Verificar si hay una zona geográfica específica
        zone = GeoAssignmentService.find_zone_for_coordinates(
            assignment_data['member_coordinates']['latitude'],
            assignment_data['member_coordinates']['longitude']
        )
        
        # Crear asignación
        assignment = GeoAssignment(
            member_id=member_id,
            zone_id=zone.id if zone else None,
            assigned_church_id=nearest_church.id,
            assignment_type='automatic',
            distance_km=distance
        )
        
        db.session.add(assignment)
        
        # Actualizar iglesia del miembro si es diferente
        if member.church_id != nearest_church.id:
            member.church_id = nearest_church.id
        
        db.session.commit()
        
        return assignment
    
    @staticmethod
    def find_zone_for_coordinates(latitude, longitude):
        """Encontrar zona geográfica para coordenadas específicas"""
        zones = GeographicZone.query.filter_by(is_active=True).all()
        
        for zone in zones:
            # Verificar si está dentro del radio
            distance = GeoAssignmentService.calculate_distance(
                latitude, longitude,
                zone.center_latitude, zone.center_longitude
            )
            
            if distance <= zone.radius_km:
                return zone
        
        return None
    
    @staticmethod
    def create_zone(church_id, name, center_lat, center_lon, radius_km=5.0, **kwargs):
        """Crear nueva zona geográfica"""
        zone = GeographicZone(
            name=name,
            church_id=church_id,
            center_latitude=center_lat,
            center_longitude=center_lon,
            radius_km=radius_km,
            **kwargs
        )
        
        db.session.add(zone)
        db.session.commit()
        return zone
    
    @staticmethod
    def bulk_auto_assign():
        """Asignación automática masiva de miembros sin asignación"""
        from app.models import Member, User
        
        # Obtener miembros sin asignación geográfica activa
        unassigned_members = db.session.query(Member).join(User).filter(
            ~Member.id.in_(
                db.session.query(GeoAssignment.member_id).filter(
                    GeoAssignment.is_active == True
                )
            ),
            User.address.isnot(None),
            User.address != ''
        ).all()
        
        assignments_made = []
        
        for member in unassigned_members:
            try:
                assignment = GeoAssignmentService.auto_assign_member(member.id)
                if assignment:
                    assignments_made.append(assignment)
            except Exception as e:
                print(f"Error asignando miembro {member.id}: {e}")
                continue
        
        return assignments_made

# ============================================================================
# 4. FORMULARIOS GEO-ASIGNACIÓN
# ============================================================================

from flask_wtf import FlaskForm
from wtforms import StringField, FloatField, SelectField, IntegerField, BooleanField, TextAreaField
from wtforms.validators import DataRequired, Optional, NumberRange

class GeographicZoneForm(FlaskForm):
    """Formulario para crear/editar zonas geográficas"""
    name = StringField('Nombre de la Zona', validators=[DataRequired()])
    church_id = SelectField('Iglesia', coerce=int, validators=[DataRequired()])
    center_latitude = FloatField('Latitud Central', validators=[DataRequired(), NumberRange(min=-90, max=90)])
    center_longitude = FloatField('Longitud Central', validators=[DataRequired(), NumberRange(min=-180, max=180)])
    radius_km = FloatField('Radio (km)', validators=[DataRequired(), NumberRange(min=0.1, max=100)], default=5.0)
    color = StringField('Color (hex)', validators=[Optional()], default='#3388ff')
    priority = IntegerField('Prioridad', validators=[NumberRange(min=1, max=10)], default=1)
    is_active = BooleanField('Zona Activa', default=True)
    submit = SubmitField('Guardar Zona')

class ManualAssignmentForm(FlaskForm):
    """Formulario para asignación manual"""
    member_id = SelectField('Miembro', coerce=int, validators=[DataRequired()])
    church_id = SelectField('Iglesia Asignada', coerce=int, validators=[DataRequired()])
    zone_id = SelectField('Zona Geográfica', coerce=int, validators=[Optional()])
    notes = TextAreaField('Notas', validators=[Optional()])
    submit = SubmitField('Asignar Manualmente')

# ============================================================================
# 5. RUTAS GEO-ASIGNACIÓN (para routes.py)
# ============================================================================

GEO_ROUTES_EXAMPLE = """
# Añadir estas rutas a routes.py

from nuevas_funcionalidades.geo_asignacion import (
    GeoAssignmentService, GeographicZoneForm, ManualAssignmentForm,
    GeographicZone, GeoAssignment
)

@routes_bp.route('/geo/zones')
@login_required
@admin_or_secretary_required
def geo_zones():
    zones = GeographicZone.query.all()
    return render_template('geo/zones.html', zones=zones)

@routes_bp.route('/geo/zones/create', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def create_geo_zone():
    form = GeographicZoneForm()
    form.church_id.choices = [(c.id, c.name) for c in Church.query.all()]
    
    if form.validate_on_submit():
        zone = GeoAssignmentService.create_zone(
            church_id=form.church_id.data,
            name=form.name.data,
            center_lat=form.center_latitude.data,
            center_lon=form.center_longitude.data,
            radius_km=form.radius_km.data,
            color=form.color.data,
            priority=form.priority.data,
            is_active=form.is_active.data
        )
        flash('Zona geográfica creada exitosamente', 'success')
        return redirect(url_for('routes.geo_zones'))
    
    return render_template('geo/create_zone.html', form=form)

@routes_bp.route('/geo/auto-assign', methods=['POST'])
@login_required
@admin_or_secretary_required
def geo_auto_assign():
    assignments = GeoAssignmentService.bulk_auto_assign()
    flash(f'{len(assignments)} miembros asignados automáticamente', 'success')
    return redirect(url_for('routes.geo_assignments'))

@routes_bp.route('/geo/assignments')
@login_required
def geo_assignments():
    if current_user.role in ['administrador', 'secretaria']:
        assignments = GeoAssignment.query.filter_by(is_active=True).all()
    else:
        # Solo asignaciones de su iglesia
        assignments = GeoAssignment.query.filter_by(
            assigned_church_id=current_user.church_id,
            is_active=True
        ).all()
    
    return render_template('geo/assignments.html', assignments=assignments)

@routes_bp.route('/geo/map')
@login_required
def geo_map():
    zones = GeographicZone.query.filter_by(is_active=True).all()
    churches = Church.query.filter(
        Church.latitude.isnot(None),
        Church.longitude.isnot(None)
    ).all()
    
    return render_template('geo/map.html', zones=zones, churches=churches)

@routes_bp.route('/api/geo/nearest-church')
@login_required
def api_nearest_church():
    address = request.args.get('address')
    if not address:
        return jsonify({'error': 'Dirección requerida'}), 400
    
    result = GeoAssignmentService.find_nearest_church(address)
    if not result:
        return jsonify({'error': 'No se pudo geocodificar la dirección'}), 400
    
    return jsonify({
        'church': {
            'id': result['church'].id,
            'name': result['church'].name,
            'address': result['church'].address
        },
        'distance': round(result['distance'], 2),
        'coordinates': result['member_coordinates']
    })
"""

# ============================================================================
# 6. REPORTES GEOGRÁFICOS
# ============================================================================

class GeoReports:
    """Generador de reportes geográficos"""
    
    @staticmethod
    def zone_coverage_report():
        """Reporte de cobertura por zonas"""
        zones = GeographicZone.query.filter_by(is_active=True).all()
        
        report = []
        for zone in zones:
            assignments_count = GeoAssignment.query.filter_by(
                zone_id=zone.id, is_active=True
            ).count()
            
            report.append({
                'zone': zone,
                'assignments_count': assignments_count,
                'coverage_percentage': (assignments_count / zone.church.members.count()) * 100 if zone.church.members.count() > 0 else 0
            })
        
        return report
    
    @staticmethod
    def distance_analysis():
        """Análisis de distancias de asignación"""
        assignments = GeoAssignment.query.filter(
            GeoAssignment.is_active == True,
            GeoAssignment.distance_km.isnot(None)
        ).all()
        
        if not assignments:
            return None
        
        distances = [a.distance_km for a in assignments]
        
        return {
            'total_assignments': len(assignments),
            'avg_distance': sum(distances) / len(distances),
            'min_distance': min(distances),
            'max_distance': max(distances),
            'assignments_under_5km': len([d for d in distances if d <= 5]),
            'assignments_over_10km': len([d for d in distances if d > 10])
        }

print("Sistema de Geo-asignación Automática - Listo para implementar")
print("Dependencias adicionales:")
print("- requests (para geocodificación)")
print("- Leaflet.js (para mapas interactivos)")
print("- Google Maps API Key (opcional, para mejor precisión)")
