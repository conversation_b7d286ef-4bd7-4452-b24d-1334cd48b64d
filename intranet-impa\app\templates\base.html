<!-- /app/templates/base.html -->
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON><PERSON><PERSON><PERSON> por Defecto{% endblock %} - Intranet IMPA</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" integrity="sha384-JcKb8q3iqJ61gNV9KGb8thSsNjpSL0n8PARn9HuZOnIxN0hoP+VmmDGMN5t9UJ0Z" crossorigin="anonymous">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Tu CSS personalizado -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <!-- Leaflet.draw CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css"/>
     <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    {% block head %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <a class="navbar-brand" href="{{ url_for('routes.dashboard') }}">
            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Logo IMPA" width="30" height="30" class="d-inline-block align-top mr-1" onerror="this.style.display='none'; this.nextSibling.style.display='inline';"> <span style="display:none;">IMPA</span>
            Intranet IMPA
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

       <div class="collapse navbar-collapse" id="navbarNavDropdown">
            <!-- Menú Principal (Izquierda) -->
            <ul class="navbar-nav mr-auto">
                {% if current_user.is_authenticated %}
                    {% set view_user = get_user_for_view() %}
                    <li class="nav-item {{ 'active' if request.endpoint == 'routes.dashboard' else '' }}">
                        <a class="nav-link" href="{{ url_for('routes.dashboard') }}"><i class="fas fa-home fa-fw mr-1"></i>Escritorio</a>
                    </li>

                    <!-- ======================================= -->
                    <!-- == NUEVO MENÚ COMUNICACIÓN == -->
                    <!-- ======================================= -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownComunicacion" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-comments fa-fw mr-1"></i>Comunicación
                        </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdownComunicacion">
                            <a class="dropdown-item" href="{{ url_for('routes.list_announcements') }}"><i class="fas fa-bullhorn fa-fw mr-2"></i>Anuncios</a>
                            {% if view_user.role in ['administrador', 'secretaria', 'pastorado'] %} {# Usar view_user aquí para consistencia con "Ver como" #}
                                <a class="dropdown-item" href="{{ url_for('routes.manage_announcement') }}"><i class="fas fa-plus-circle fa-fw mr-2"></i>Crear Anuncio</a>
                            {% endif %}
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('routes.inbox') }}"><i class="fas fa-inbox fa-fw mr-2"></i>Buzón de Entrada
                                {% set inbox_unread = get_user_for_view().messages_received.filter_by(is_read=False).count() %}
                                {% if inbox_unread > 0 %}
                                    <span class="badge badge-danger ml-1">{{ inbox_unread }}</span>
                                {% endif %}
                            </a>
                            <a class="dropdown-item" href="{{ url_for('routes.send_message') }}"><i class="fas fa-paper-plane fa-fw mr-2"></i>Enviar Mensaje</a>
                             {# Futuro: Añadir enlace a Bandeja de Enviados aquí #}
                             {# <a class="dropdown-item" href="{{ url_for('routes.sent_messages') }}"><i class="fas fa-share-square fa-fw mr-2"></i>Mensajes Enviados</a> #}
                        </div>
                    </li>
                    <!-- ======================================= -->
                    <!-- == FIN MENÚ COMUNICACIÓN == -->
                    <!-- ======================================= -->

                    <!-- Menús específicos por Rol (usando view_user) -->
                    {% if view_user.role in ('administrador', 'secretaria') %}
                        <!-- Menú Gestión Admin/Sec -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownGestion" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-cogs fa-fw mr-1"></i>Gestión
                            </a>
                            <div class="dropdown-menu" aria-labelledby="navbarDropdownGestion">
                                <h6 class="dropdown-header">Usuarios y Roles</h6>
                                <a class="dropdown-item" href="{{ url_for('routes.list_users') }}"><i class="fas fa-users fa-fw mr-2"></i>Listar Usuarios</a>
                                <a class="dropdown-item" href="{{ url_for('routes.register') }}"><i class="fas fa-user-plus fa-fw mr-2"></i>Registrar Usuario</a>
                                <a class="dropdown-item" href="{{ url_for('routes.list_pastores') }}"><i class="fas fa-user-tie fa-fw mr-2"></i>Listar Pastores</a>
                                <a class="dropdown-item" href="{{ url_for('routes.asignar_jerarquias') }}"><i class="fas fa-sitemap fa-fw mr-2"></i>Jerarquías</a>
                                <div class="dropdown-divider"></div>
                                <h6 class="dropdown-header">Iglesias e Inventario</h6>
                                <a class="dropdown-item" href="{{ url_for('routes.list_churches') }}"><i class="fas fa-church fa-fw mr-2"></i>Listar Iglesias</a>
                                <a class="dropdown-item" href="{{ url_for('routes.create_church') }}"><i class="fas fa-plus fa-fw mr-2"></i>Crear Iglesia</a>
                                <a class="dropdown-item" href="{{ url_for('routes.list_inventories') }}"><i class="fas fa-boxes fa-fw mr-2"></i>Inventarios</a>
                                <div class="dropdown-divider"></div>
                                <h6 class="dropdown-header">Otros</h6>
                                <a class="dropdown-item" href="{{ url_for('routes.add_relationship') }}"><i class="fas fa-user-friends fa-fw mr-2"></i>Relaciones Familiares</a>
                                <a class="dropdown-item" href="{{ url_for('routes.church_members_count') }}"><i class="fas fa-calculator fa-fw mr-2"></i>Miembros por Iglesia</a>
                            </div>
                        </li>
                        <!-- Menú Documentos Admin/Sec -->
                         <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownDocs" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-folder-open fa-fw mr-1"></i>Documentos
                            </a>
                            <div class="dropdown-menu" aria-labelledby="navbarDropdownDocs">
                                <a class="dropdown-item" href="{{ url_for('routes.list_documents') }}"><i class="fas fa-list fa-fw mr-2"></i>Ver/Aprobar Docs</a>
                                <a class="dropdown-item" href="{{ url_for('routes.upload_document') }}"><i class="fas fa-file-upload fa-fw mr-2"></i>Subir Documento</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('routes.acta_preview') }}"><i class="fas fa-file-signature fa-fw mr-2"></i>Generar Acta</a>
                                <a class="dropdown-item" href="{{ url_for('routes.diploma_preview') }}"><i class="fas fa-certificate fa-fw mr-2"></i>Generar Diploma</a>
                                <a class="dropdown-item" href="{{ url_for('routes.seleccionar_pastor_credencial') }}"><i class="fas fa-id-card fa-fw mr-2"></i>Generar Credencial</a>
                            </div>
                        </li>
                        <!-- Menú Sistema Académico -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownAcademic" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-graduation-cap fa-fw mr-1"></i>Sistema Académico
                            </a>
                            <div class="dropdown-menu" aria-labelledby="navbarDropdownAcademic">
                                <a class="dropdown-item" href="{{ url_for('routes.academic_dashboard') }}"><i class="fas fa-tachometer-alt fa-fw mr-2"></i>Dashboard Académico</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('routes.academic_schools') }}"><i class="fas fa-school fa-fw mr-2"></i>Escuelas Bíblicas</a>
                                <a class="dropdown-item" href="{{ url_for('routes.pastoral_programs') }}"><i class="fas fa-user-graduate fa-fw mr-2"></i>Programas Pastorales</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('routes.academic_reports') }}"><i class="fas fa-chart-bar fa-fw mr-2"></i>Reportes Académicos</a>
                            </div>
                        </li>
                        <!-- Menú Otros Admin/Sec -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownOtrosAdmin" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-tools fa-fw mr-1"></i>Otros
                            </a>
                             <div class="dropdown-menu" aria-labelledby="navbarDropdownOtrosAdmin">
                                <a class="dropdown-item" href="{{ url_for('routes.list_reviews') }}"><i class="fas fa-history fa-fw mr-2"></i>Ver Histórico</a>
                                <a class="dropdown-item" href="{{ url_for('routes.choose_review_user') }}"><i class="fas fa-pen-alt fa-fw mr-2"></i>Agregar Histórico</a>
                                <a class="dropdown-item" href="{{ url_for('routes.calendar_view') }}"><i class="far fa-calendar-alt fa-fw mr-2"></i>Calendario</a>
                                <a class="dropdown-item" href="{{ url_for('routes.map_view') }}"><i class="fas fa-map-marked-alt fa-fw mr-2"></i>Ver Mapa</a>
                             </div>
                        </li>

                    {% elif view_user.role == 'pastorado' %}
                        <!-- Menú Gestión Pastoral -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownPastor" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-church fa-fw mr-1"></i>Gestión Pastoral
                            </a>
                            <div class="dropdown-menu" aria-labelledby="navbarDropdownPastor">
                                <a class="dropdown-item" href="{{ url_for('routes.pastor_members') }}"><i class="fas fa-users fa-fw mr-2"></i>Miembros de mi Iglesia</a>
                                <a class="dropdown-item" href="{{ url_for('routes.register_member') }}"><i class="fas fa-user-plus fa-fw mr-2"></i>Registrar Miembro</a>
                                <a class="dropdown-item" href="{{ url_for('routes.list_pending_transfers') }}"><i class="fas fa-sync-alt fa-fw mr-2"></i>Transferencias Pendientes</a>
                                <div class="dropdown-divider"></div>
                                 {% if current_user.church_id %} {# El PASTOR actual debe tener iglesia para ver esto #}
                                    <a class="dropdown-item" href="{{ url_for('routes.inventory', church_id=current_user.church_id) }}"><i class="fas fa-boxes fa-fw mr-2"></i>Inventario Iglesia</a>
                                    <a class="dropdown-item" href="{{ url_for('routes.pastor_economia') }}"><i class="fas fa-dollar-sign fa-fw mr-2"></i>Economía Iglesia</a>
                                {% else %}
                                    <span class="dropdown-item disabled">Inventario/Economía (Sin iglesia)</span>
                                {% endif %}
                                <a class="dropdown-item" href="{{ url_for('routes.map_view') }}"><i class="fas fa-map-marked-alt fa-fw mr-2"></i>Ver Mapa</a>
                             </div>
                        </li>
                        <!-- Menú Documentos Pastor -->
                         <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownDocsPastor" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-folder-open fa-fw mr-1"></i>Documentos
                            </a>
                            <div class="dropdown-menu" aria-labelledby="navbarDropdownDocsPastor">
                                <a class="dropdown-item" href="{{ url_for('routes.list_documents') }}"><i class="fas fa-list fa-fw mr-2"></i>Ver Documentos</a>
                                <a class="dropdown-item" href="{{ url_for('routes.upload_document') }}"><i class="fas fa-file-upload fa-fw mr-2"></i>Subir Documento</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('routes.acta_preview') }}"><i class="fas fa-file-signature fa-fw mr-2"></i>Generar Acta</a>
                                <a class="dropdown-item" href="{{ url_for('routes.diploma_preview') }}"><i class="fas fa-certificate fa-fw mr-2"></i>Generar Diploma</a>
                             </div>
                        </li>
                        <!-- Menú Sistema Académico Pastor -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownAcademicPastor" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-graduation-cap fa-fw mr-1"></i>Sistema Académico
                            </a>
                            <div class="dropdown-menu" aria-labelledby="navbarDropdownAcademicPastor">
                                <a class="dropdown-item" href="{{ url_for('routes.academic_dashboard') }}"><i class="fas fa-tachometer-alt fa-fw mr-2"></i>Mi Dashboard Académico</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('routes.academic_schools') }}"><i class="fas fa-school fa-fw mr-2"></i>Escuelas de mi Iglesia</a>
                                <a class="dropdown-item" href="{{ url_for('routes.pastoral_programs') }}"><i class="fas fa-user-graduate fa-fw mr-2"></i>Programas Pastorales</a>
                            </div>
                        </li>
                        <!-- Menú Otros Pastor -->
                         <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownOtrosPastor" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-tools fa-fw mr-1"></i>Otros
                            </a>
                             <div class="dropdown-menu" aria-labelledby="navbarDropdownOtrosPastor">
                                <a class="dropdown-item" href="{{ url_for('routes.list_reviews') }}"><i class="fas fa-history fa-fw mr-2"></i>Ver Histórico (Iglesia)</a>
                                <a class="dropdown-item" href="{{ url_for('routes.choose_review_user') }}"><i class="fas fa-pen-alt fa-fw mr-2"></i>Agregar Histórico (Miembro)</a>
                                <a class="dropdown-item" href="{{ url_for('routes.calendar_view') }}"><i class="far fa-calendar-alt fa-fw mr-2"></i>Calendario</a>
                             </div>
                        </li>

                    {% elif view_user.role == 'miembro' %}
                        <!-- Menú Miembro -->
                         <li class="nav-item {{ 'active' if request.endpoint == 'routes.list_documents' else '' }}">
                             <a class="nav-link" href="{{ url_for('routes.list_documents') }}"><i class="fas fa-folder-open fa-fw mr-1"></i>Documentos</a>
                         </li>
                         <li class="nav-item {{ 'active' if request.endpoint == 'routes.calendar_view' else '' }}">
                              <a class="nav-link" href="{{ url_for('routes.calendar_view') }}"><i class="far fa-calendar-alt fa-fw mr-1"></i>Calendario</a>
                         </li>
                         {# Se accede a editar perfil desde el menú de usuario #}
                    {% endif %}
                    <!-- Fin Menús específicos por Rol -->

                {% else %} {# No autenticado #}
                    <li class="nav-item {{ 'active' if request.endpoint == 'routes.login' else '' }}">
                        <a class="nav-link" href="{{ url_for('routes.login') }}">Iniciar Sesión</a>
                    </li>
                {% endif %}
            </ul>

            <!-- Menú de Usuario (Derecha) -->
            <ul class="navbar-nav">
                {% if current_user.is_authenticated %}
                     <!-- Dropdown de Usuario -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarUserDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-user-circle mr-1"></i>
                            {{ current_user.username }} {# Usuario REAL logueado #}
                            {# Indicador si está viendo como otro #}
                            {% if get_user_for_view().id != current_user.id %}
                                <span class="badge badge-warning ml-1">Viendo como {{ get_user_for_view().username }}</span>
                            {% endif %}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarUserDropdown">
                            <a class="dropdown-item" href="{{ url_for('routes.edit_own_profile') }}">
                                <i class="fas fa-user-edit fa-fw mr-2"></i>Editar Mi Perfil
                            </a>
                            {# <a class="dropdown-item" href="{{ url_for('routes.user_detail', user_id=current_user.id) }}"><i class="fas fa-address-card fa-fw mr-2"></i>Ver Mi Perfil (Público?)</a> #}
                            <div class="dropdown-divider"></div>
                             {# Botón para Volver a Mi Vista si aplica #}
                            {% if current_user.role in ('administrador', 'secretaria') and get_user_for_view().id != current_user.id %}
                                <a class="dropdown-item text-warning font-weight-bold" href="{{ url_for('routes.dashboard') }}" title="Dejar de ver como {{ get_user_for_view().username }}">
                                    <i class="fas fa-eye-slash fa-fw mr-2"></i>Volver a mi vista
                                </a>
                                <div class="dropdown-divider"></div>
                            {% endif %}
                            <a class="dropdown-item" href="{{ url_for('routes.logout') }}">
                                <i class="fas fa-sign-out-alt fa-fw mr-2"></i>Cerrar Sesión
                            </a>
                        </div>
                    </li>
                {% endif %}
            </ul>
       </div>
    </nav>

    <div class="container mt-4">
        {# Mensajes Flash #}
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {# Bloque Principal de Contenido #}
        {% block content %}{% endblock %}
    </div>

    {# Footer #}
    <footer class="footer mt-auto py-3 bg-light">
      <div class="container text-center">
        <span class="text-muted">Intranet IMPA © {{ now().year }}</span>
      </div>
    </footer>

     {# SCRIPTS #}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js" integrity="sha384-B4gt1jrGC7Jh4AgTPSdUtOBvfO8shuf57BaghqFfPlYxofvL8/KUEfYiJOMMV+rV" crossorigin="anonymous"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(function () {
          $('[data-toggle="tooltip"]').tooltip()
        })
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>