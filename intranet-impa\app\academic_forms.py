# app/academic_forms.py
# Formularios para el Sistema Académico

from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, IntegerField, BooleanField, DateField, FloatField, SubmitField
from wtforms.validators import DataRequired, Optional, NumberRange, Length

# ============================================================================
# FORMULARIOS PARA ESCUELAS BÍBLICAS
# ============================================================================

class AcademicSchoolForm(FlaskForm):
    """Formulario para crear/editar escuelas bíblicas"""
    name = StringField('Nombre de la Escuela', validators=[DataRequired(), Length(max=255)])
    description = TextAreaField('Descripción', validators=[Optional()])
    church_id = SelectField('Iglesia', coerce=int, validators=[DataRequired()])
    director_id = SelectField('Director', coerce=int, validators=[Optional()])
    start_date = DateField('Fecha de Inicio', validators=[DataRequired()])
    end_date = DateField('Fecha de Fin', validators=[Optional()])
    max_students = IntegerField('Máximo de Estudiantes', validators=[NumberRange(min=1, max=500)], default=50)
    auto_enrollment = BooleanField('Matrícula Automática')
    submit = SubmitField('Guardar Escuela')

class AcademicCurriculumForm(FlaskForm):
    """Formulario para crear/editar pensums académicos"""
    name = StringField('Nombre del Pensum', validators=[DataRequired(), Length(max=255)])
    description = TextAreaField('Descripción', validators=[Optional()])
    school_id = SelectField('Escuela', coerce=int, validators=[DataRequired()])
    duration_months = IntegerField('Duración (meses)', validators=[DataRequired(), NumberRange(min=1, max=60)])
    submit = SubmitField('Guardar Pensum')

class AcademicSubjectForm(FlaskForm):
    """Formulario para crear/editar materias"""
    name = StringField('Nombre de la Materia', validators=[DataRequired(), Length(max=255)])
    code = StringField('Código', validators=[DataRequired(), Length(max=20)])
    description = TextAreaField('Descripción', validators=[Optional()])
    curriculum_id = SelectField('Pensum', coerce=int, validators=[DataRequired()])
    credits = IntegerField('Créditos', validators=[NumberRange(min=1, max=10)], default=1)
    level = IntegerField('Nivel/Semestre', validators=[NumberRange(min=1, max=10)], default=1)
    is_mandatory = BooleanField('Materia Obligatoria', default=True)
    teacher_id = SelectField('Profesor', coerce=int, validators=[Optional()])
    submit = SubmitField('Guardar Materia')

class AcademicEnrollmentForm(FlaskForm):
    """Formulario para matricular estudiantes"""
    student_id = SelectField('Estudiante', coerce=int, validators=[DataRequired()])
    school_id = SelectField('Escuela', coerce=int, validators=[DataRequired()])
    curriculum_id = SelectField('Pensum', coerce=int, validators=[DataRequired()])
    notes = TextAreaField('Notas', validators=[Optional()])
    submit = SubmitField('Matricular Estudiante')

class AcademicGradeForm(FlaskForm):
    """Formulario para asignar calificaciones"""
    enrollment_id = SelectField('Estudiante', coerce=int, validators=[DataRequired()])
    subject_id = SelectField('Materia', coerce=int, validators=[DataRequired()])
    grade_value = FloatField('Calificación', validators=[DataRequired(), NumberRange(min=1.0, max=5.0)])
    grade_type = SelectField('Tipo de Calificación', choices=[
        ('partial', 'Parcial'),
        ('final', 'Final'),
        ('makeup', 'Recuperación')
    ], validators=[DataRequired()])
    evaluation_date = DateField('Fecha de Evaluación', validators=[DataRequired()])
    comments = TextAreaField('Comentarios', validators=[Optional()])
    submit = SubmitField('Asignar Calificación')

# ============================================================================
# FORMULARIOS PARA INSTITUTO PASTORAL
# ============================================================================

class PastoralInstituteForm(FlaskForm):
    """Formulario para crear/editar instituto pastoral"""
    name = StringField('Nombre del Instituto', validators=[DataRequired(), Length(max=255)])
    description = TextAreaField('Descripción', validators=[Optional()])
    rector_id = SelectField('Rector', coerce=int, validators=[Optional()])
    academic_coordinator_id = SelectField('Coordinador Académico', coerce=int, validators=[Optional()])
    submit = SubmitField('Guardar Instituto')

class PastoralProgramForm(FlaskForm):
    """Formulario para crear/editar programas pastorales"""
    name = StringField('Nombre del Programa', validators=[DataRequired(), Length(max=255)])
    description = TextAreaField('Descripción', validators=[Optional()])
    institute_id = SelectField('Instituto', coerce=int, validators=[DataRequired()])
    program_type = SelectField('Tipo de Programa', choices=[
        ('certificate', 'Certificado'),
        ('diploma', 'Diploma'),
        ('specialization', 'Especialización'),
        ('masters', 'Maestría')
    ], validators=[DataRequired()])
    duration_months = IntegerField('Duración (meses)', validators=[DataRequired(), NumberRange(min=1, max=60)])
    total_credits = IntegerField('Total de Créditos', validators=[NumberRange(min=1, max=200)], default=0)
    min_ministry_years = IntegerField('Años Mínimos en Ministerio', validators=[NumberRange(min=0, max=50)], default=0)
    max_students = IntegerField('Máximo de Estudiantes', validators=[NumberRange(min=1, max=100)], default=30)
    requires_approval = BooleanField('Requiere Aprobación para Matrícula', default=True)
    submit = SubmitField('Guardar Programa')

class PastoralSubjectForm(FlaskForm):
    """Formulario para crear/editar materias pastorales"""
    name = StringField('Nombre de la Materia', validators=[DataRequired(), Length(max=255)])
    code = StringField('Código', validators=[DataRequired(), Length(max=20)])
    description = TextAreaField('Descripción', validators=[Optional()])
    program_id = SelectField('Programa', coerce=int, validators=[DataRequired()])
    credits = IntegerField('Créditos', validators=[NumberRange(min=1, max=10)], default=3)
    semester = IntegerField('Semestre', validators=[NumberRange(min=1, max=10)], default=1)
    is_mandatory = BooleanField('Materia Obligatoria', default=True)
    professor_id = SelectField('Profesor', coerce=int, validators=[Optional()])
    submit = SubmitField('Guardar Materia')

class PastoralEnrollmentForm(FlaskForm):
    """Formulario para matricular pastores"""
    student_id = SelectField('Pastor', coerce=int, validators=[DataRequired()])
    program_id = SelectField('Programa', coerce=int, validators=[DataRequired()])
    notes = TextAreaField('Notas', validators=[Optional()])
    submit = SubmitField('Matricular Pastor')

class PastoralGradeForm(FlaskForm):
    """Formulario para calificar pastores"""
    enrollment_id = SelectField('Pastor Estudiante', coerce=int, validators=[DataRequired()])
    subject_id = SelectField('Materia', coerce=int, validators=[DataRequired()])
    grade_value = FloatField('Calificación', validators=[DataRequired(), NumberRange(min=1.0, max=5.0)])
    grade_type = SelectField('Tipo de Evaluación', choices=[
        ('assignment', 'Tarea'),
        ('exam', 'Examen'),
        ('project', 'Proyecto'),
        ('final', 'Final')
    ], validators=[DataRequired()])
    evaluation_date = DateField('Fecha de Evaluación', validators=[DataRequired()])
    comments = TextAreaField('Comentarios', validators=[Optional()])
    feedback = TextAreaField('Retroalimentación Detallada', validators=[Optional()])
    improvement_areas = TextAreaField('Áreas de Mejora', validators=[Optional()])
    submit = SubmitField('Asignar Calificación')

class ApprovalForm(FlaskForm):
    """Formulario para aprobar matrículas pastorales"""
    enrollment_id = SelectField('Matrícula', coerce=int, validators=[DataRequired()])
    approval_notes = TextAreaField('Notas de Aprobación', validators=[Optional()])
    submit = SubmitField('Aprobar Matrícula')

# ============================================================================
# FORMULARIOS DE BÚSQUEDA Y FILTROS
# ============================================================================

class AcademicSearchForm(FlaskForm):
    """Formulario para búsqueda en sistema académico"""
    search_term = StringField('Buscar', validators=[Optional()])
    church_filter = SelectField('Filtrar por Iglesia', coerce=int, validators=[Optional()])
    status_filter = SelectField('Filtrar por Estado', choices=[
        ('', 'Todos'),
        ('active', 'Activos'),
        ('completed', 'Completados'),
        ('dropped', 'Retirados'),
        ('suspended', 'Suspendidos')
    ], validators=[Optional()])
    submit = SubmitField('Buscar')

class PastoralSearchForm(FlaskForm):
    """Formulario para búsqueda en programas pastorales"""
    search_term = StringField('Buscar', validators=[Optional()])
    program_filter = SelectField('Filtrar por Programa', coerce=int, validators=[Optional()])
    status_filter = SelectField('Filtrar por Estado', choices=[
        ('', 'Todos'),
        ('pending', 'Pendientes'),
        ('approved', 'Aprobados'),
        ('active', 'Activos'),
        ('completed', 'Completados'),
        ('dropped', 'Retirados')
    ], validators=[Optional()])
    submit = SubmitField('Buscar')

# ============================================================================
# FORMULARIOS DE REPORTES
# ============================================================================

class AcademicReportForm(FlaskForm):
    """Formulario para generar reportes académicos"""
    report_type = SelectField('Tipo de Reporte', choices=[
        ('school_stats', 'Estadísticas por Escuela'),
        ('student_progress', 'Progreso de Estudiantes'),
        ('completion_rates', 'Tasas de Finalización'),
        ('grade_analysis', 'Análisis de Calificaciones')
    ], validators=[DataRequired()])
    school_id = SelectField('Escuela', coerce=int, validators=[Optional()])
    start_date = DateField('Fecha de Inicio', validators=[Optional()])
    end_date = DateField('Fecha de Fin', validators=[Optional()])
    submit = SubmitField('Generar Reporte')

class PastoralReportForm(FlaskForm):
    """Formulario para generar reportes pastorales"""
    report_type = SelectField('Tipo de Reporte', choices=[
        ('program_stats', 'Estadísticas por Programa'),
        ('pastor_progress', 'Progreso de Pastores'),
        ('completion_rates', 'Tasas de Finalización'),
        ('certification_report', 'Reporte de Certificaciones')
    ], validators=[DataRequired()])
    program_id = SelectField('Programa', coerce=int, validators=[Optional()])
    start_date = DateField('Fecha de Inicio', validators=[Optional()])
    end_date = DateField('Fecha de Fin', validators=[Optional()])
    submit = SubmitField('Generar Reporte')

# ============================================================================
# FORMULARIOS DE CONFIGURACIÓN
# ============================================================================

class AcademicSettingsForm(FlaskForm):
    """Formulario para configuración del sistema académico"""
    auto_enrollment_enabled = BooleanField('Habilitar Matrícula Automática Global')
    default_grade_scale = SelectField('Escala de Calificación por Defecto', choices=[
        ('1-5', 'Escala 1.0 - 5.0'),
        ('1-10', 'Escala 1.0 - 10.0'),
        ('0-100', 'Escala 0 - 100')
    ], default='1-5')
    min_passing_grade = FloatField('Calificación Mínima para Aprobar', 
                                  validators=[NumberRange(min=1.0, max=5.0)], default=3.0)
    certificate_template = SelectField('Plantilla de Certificados', choices=[
        ('basic', 'Básica'),
        ('formal', 'Formal'),
        ('decorative', 'Decorativa')
    ], default='formal')
    notification_enabled = BooleanField('Habilitar Notificaciones Automáticas', default=True)
    submit = SubmitField('Guardar Configuración')

# ============================================================================
# FORMULARIOS PARA IMPORTACIÓN/EXPORTACIÓN
# ============================================================================

class ImportStudentsForm(FlaskForm):
    """Formulario para importar estudiantes masivamente"""
    school_id = SelectField('Escuela Destino', coerce=int, validators=[DataRequired()])
    curriculum_id = SelectField('Pensum', coerce=int, validators=[DataRequired()])
    import_file = StringField('Archivo CSV', validators=[DataRequired()])  # En template será file input
    auto_enroll = BooleanField('Matricular Automáticamente', default=True)
    submit = SubmitField('Importar Estudiantes')

class ExportDataForm(FlaskForm):
    """Formulario para exportar datos académicos"""
    export_type = SelectField('Tipo de Exportación', choices=[
        ('students', 'Lista de Estudiantes'),
        ('grades', 'Calificaciones'),
        ('certificates', 'Certificados Emitidos'),
        ('schools', 'Escuelas y Programas')
    ], validators=[DataRequired()])
    format_type = SelectField('Formato', choices=[
        ('csv', 'CSV'),
        ('excel', 'Excel'),
        ('pdf', 'PDF')
    ], validators=[DataRequired()])
    school_id = SelectField('Filtrar por Escuela', coerce=int, validators=[Optional()])
    start_date = DateField('Fecha de Inicio', validators=[Optional()])
    end_date = DateField('Fecha de Fin', validators=[Optional()])
    submit = SubmitField('Exportar Datos')
