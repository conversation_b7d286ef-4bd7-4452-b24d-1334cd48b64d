# File: backend/apps/users/models.py
# -----------------------------------------------


from django.contrib.auth.models import AbstractUser
from django.db import models
# from apps.locations.models import Ciudad # Descomentar cuando locations.models.Ciudad exista

class User(AbstractUser):
    class Role(models.TextChoices):
        CIUDADANO = "CIUDADANO", "Ciudadano"
        OPERADOR = "OPERADOR", "Operador 911"
        BRIGADA = "BRIGADA", "Brigada Rural"
        ADMIN = "ADMIN", "Administrador"

    role = models.CharField(max_length=20, choices=Role.choices, default=Role.CIUDADANO)
    # Otros campos como 'telefono', 'direccion_particular' pueden ir aquí o en UserProfile

    def __str__(self):
        return self.username

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    nombre_completo = models.CharField(max_length=255, blank=True)
    direccion = models.CharField(max_length=255, blank=True)
    # ciudad = models.ForeignKey('locations.Ciudad', on_delete=models.SET_NULL, null=True, blank=True) # Usar string para evitar import circular al inicio
    telefono = models.CharField(max_length=20, blank=True)
    edad = models.PositiveIntegerField(null=True, blank=True)

    def __str__(self):
        return f"Perfil de {self.user.username}"
