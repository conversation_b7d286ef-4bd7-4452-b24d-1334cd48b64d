#!/usr/bin/env python3
# --- Archivo: direct_fix.py ---
# Script para arreglar directamente el problema de SQLAlchemy

import os
import sys
import sqlite3
import subprocess

def check_database_schema():
    """Verificar el esquema actual de la base de datos."""
    print("🔍 Verificando esquema de la base de datos...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Obtener esquema de la tabla user
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='user';")
        schema = cursor.fetchone()
        
        if schema:
            print("📋 Esquema actual de tabla user:")
            print(schema[0])
            
            # Verificar si tiene las columnas necesarias
            if 'role' in schema[0] and 'is_active' in schema[0]:
                print("✅ La tabla user tiene las columnas necesarias")
                conn.close()
                return True
            else:
                print("❌ La tabla user NO tiene las columnas necesarias")
                conn.close()
                return False
        else:
            print("❌ Tabla user no existe")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Error verificando esquema: {e}")
        return False

def recreate_user_table_with_data():
    """Recrear la tabla user preservando los datos."""
    print("🔄 Recreando tabla user con datos...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Obtener datos existentes
        cursor.execute("SELECT username, email, password_hash FROM user;")
        existing_users = cursor.fetchall()
        print(f"💾 Datos existentes: {len(existing_users)} usuarios")
        
        # Eliminar tabla actual
        cursor.execute("DROP TABLE IF EXISTS user;")
        
        # Crear nueva tabla con estructura completa
        cursor.execute('''
            CREATE TABLE user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(64) NOT NULL UNIQUE,
                email VARCHAR(120) UNIQUE,
                password_hash VARCHAR(256) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'administrador',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("✅ Nueva tabla user creada")
        
        # Restaurar datos
        for user_data in existing_users:
            cursor.execute('''
                INSERT INTO user (username, email, password_hash, role, is_active, created_at, updated_at)
                VALUES (?, ?, ?, 'administrador', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', user_data)
        
        print(f"✅ {len(existing_users)} usuarios restaurados")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error recreando tabla: {e}")
        return False

def stop_all_python_processes():
    """Detener todos los procesos Python relacionados."""
    print("🛑 Deteniendo procesos Python...")
    
    try:
        # Detener aplicación Flask
        subprocess.run(['pkill', '-f', 'python.*run.py'], capture_output=True)
        
        # Detener cualquier proceso Python en el directorio
        subprocess.run(['pkill', '-f', f'python.*{os.getcwd()}'], capture_output=True)
        
        print("✅ Procesos detenidos")
        return True
        
    except Exception as e:
        print(f"⚠️  Error deteniendo procesos: {e}")
        return False

def clear_all_cache():
    """Limpiar todo el cache de Python."""
    print("🧹 Limpiando cache completo...")
    
    try:
        # Limpiar archivos .pyc
        subprocess.run(['find', '.', '-name', '*.pyc', '-delete'], capture_output=True)
        
        # Limpiar directorios __pycache__
        subprocess.run(['find', '.', '-name', '__pycache__', '-type', 'd', '-exec', 'rm', '-rf', '{}', '+'], capture_output=True)
        
        # Limpiar cache de importaciones en Python
        for module_name in list(sys.modules.keys()):
            if 'app' in module_name or 'models' in module_name:
                if module_name in sys.modules:
                    del sys.modules[module_name]
        
        print("✅ Cache limpiado")
        return True
        
    except Exception as e:
        print(f"❌ Error limpiando cache: {e}")
        return False

def test_direct_sql():
    """Probar acceso directo con SQL."""
    print("🧪 Probando acceso directo con SQL...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Probar consulta directa
        cursor.execute("SELECT username, role, is_active FROM user;")
        users = cursor.fetchall()
        
        print(f"✅ SQL directo funciona: {len(users)} usuarios")
        for user in users:
            print(f"  👤 {user[0]} ({user[1]}) - Activo: {user[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error con SQL directo: {e}")
        return False

def restart_application():
    """Reiniciar la aplicación."""
    print("🚀 Reiniciando aplicación...")
    
    try:
        # Usar systemctl para reiniciar
        result = subprocess.run(['systemctl', 'restart', 'relevamiento'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Aplicación reiniciada con systemctl")
        else:
            print("⚠️  Error con systemctl, intentando manualmente...")
            
            # Iniciar manualmente
            subprocess.Popen(['python', 'run.py'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
            print("✅ Aplicación iniciada manualmente")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reiniciando aplicación: {e}")
        return False

def test_flask_app():
    """Probar que la aplicación Flask funcione."""
    print("🧪 Probando aplicación Flask...")
    
    import time
    time.sleep(3)  # Esperar que inicie
    
    try:
        import requests
        
        # Probar página principal
        response = requests.get("https://patagoniaservers.com.ar:5006/", verify=False, timeout=10)
        
        if response.status_code == 200:
            print("✅ Aplicación Flask responde")
            return True
        else:
            print(f"❌ Aplicación Flask error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error probando Flask: {e}")
        return False

def main():
    """Función principal."""
    print("🔧 Reparación Directa del Sistema")
    print("=" * 40)
    
    # Verificar esquema actual
    if check_database_schema():
        print("✅ El esquema de la base de datos es correcto")
    else:
        print("❌ Recreando tabla user...")
        if not recreate_user_table_with_data():
            print("❌ No se pudo recrear la tabla")
            sys.exit(1)
    
    # Detener procesos
    stop_all_python_processes()
    
    # Limpiar cache
    clear_all_cache()
    
    # Probar SQL directo
    if not test_direct_sql():
        print("❌ SQL directo no funciona")
        sys.exit(1)
    
    # Reiniciar aplicación
    restart_application()
    
    # Probar aplicación Flask
    if test_flask_app():
        print("\n🎉 ¡Sistema reparado exitosamente!")
        print("\n🚀 Información de acceso:")
        print("   URL: https://patagoniaservers.com.ar:5006/")
        print("   Usuario: admin")
        print("   Contraseña: isaias52")
        print("   Rol: administrador")
        
        print("\n📋 Próximos pasos:")
        print("   1. Probar login en la web")
        print("   2. Crear templates para gestión de usuarios")
        print("   3. Implementar filtros en vistas")
    else:
        print("\n❌ La aplicación aún tiene problemas")
        print("\n💡 Verifica manualmente:")
        print("   systemctl status relevamiento")
        print("   journalctl -u relevamiento -f")

if __name__ == "__main__":
    main()
