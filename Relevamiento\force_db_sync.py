#!/usr/bin/env python3
# --- Archivo: force_db_sync.py ---
# Script para forzar sincronización entre SQLAlchemy y la base de datos

import os
import sys
import sqlite3

def restore_backup():
    """Restaurar desde el backup más reciente."""
    print("🔄 Restaurando desde backup...")
    
    # Buscar el backup más reciente
    import glob
    backups = glob.glob('app_backup_*.db')
    if not backups:
        print("❌ No se encontraron backups")
        return False
    
    latest_backup = max(backups, key=os.path.getctime)
    print(f"📁 Usando backup: {latest_backup}")
    
    try:
        import shutil
        shutil.copy2(latest_backup, 'app.db')
        print("✅ Base de datos restaurada desde backup")
        return True
    except Exception as e:
        print(f"❌ Error restaurando backup: {e}")
        return False

def verify_and_fix_structure():
    """Verificar y arreglar la estructura de la base de datos."""
    print("🔍 Verificando estructura de la base de datos...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Verificar si la tabla user existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user';")
        if not cursor.fetchone():
            print("❌ Tabla user no existe")
            conn.close()
            return False
        
        # Verificar columnas de la tabla user
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 Columnas actuales: {column_names}")
        
        required_columns = {
            'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
            'username': 'VARCHAR(64) NOT NULL UNIQUE',
            'email': 'VARCHAR(120) UNIQUE',
            'password_hash': 'VARCHAR(256) NOT NULL',
            'role': 'VARCHAR(20) NOT NULL DEFAULT \'operador\'',
            'is_active': 'BOOLEAN NOT NULL DEFAULT TRUE',
            'created_by': 'INTEGER',
            'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        }
        
        missing_columns = set(required_columns.keys()) - set(column_names)
        
        if missing_columns:
            print(f"⚠️  Columnas faltantes: {missing_columns}")
            
            # Agregar columnas faltantes
            for column in missing_columns:
                column_def = required_columns[column]
                try:
                    cursor.execute(f"ALTER TABLE user ADD COLUMN {column} {column_def};")
                    print(f"  ✅ Agregada columna: {column}")
                except Exception as e:
                    print(f"  ❌ Error agregando {column}: {e}")
        else:
            print("✅ Todas las columnas requeridas están presentes")
        
        # Verificar tablas de permisos
        permission_tables = {
            'user_city_permissions': '''
                CREATE TABLE IF NOT EXISTS user_city_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    city VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, city)
                );
            ''',
            'user_source_permissions': '''
                CREATE TABLE IF NOT EXISTS user_source_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    source VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, source)
                );
            ''',
            'user_permissions': '''
                CREATE TABLE IF NOT EXISTS user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    permission_type VARCHAR(50) NOT NULL,
                    permission_value BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, permission_type)
                );
            '''
        }
        
        for table_name, create_sql in permission_tables.items():
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}';")
            if not cursor.fetchone():
                print(f"🔧 Creando tabla: {table_name}")
                cursor.execute(create_sql)
            else:
                print(f"✅ Tabla {table_name} ya existe")
        
        # Actualizar usuarios existentes
        cursor.execute("UPDATE user SET role = 'administrador' WHERE role IS NULL OR role = '';")
        cursor.execute("UPDATE user SET is_active = TRUE WHERE is_active IS NULL;")
        cursor.execute("UPDATE user SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL;")
        cursor.execute("UPDATE user SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;")
        
        conn.commit()
        conn.close()
        
        print("✅ Estructura de base de datos verificada y corregida")
        return True
        
    except Exception as e:
        print(f"❌ Error verificando estructura: {e}")
        return False

def create_minimal_models():
    """Crear un archivo temporal con modelos mínimos para testing."""
    print("🔧 Creando modelos temporales para testing...")
    
    minimal_models = '''
# Modelos mínimos para testing
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    __tablename__ = 'user'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(256), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='operador')
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    created_by = db.Column(db.Integer, nullable=True)
    created_at = db.Column(db.DateTime, nullable=True)
    updated_at = db.Column(db.DateTime, nullable=True)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'administrador'
'''
    
    with open('test_models.py', 'w') as f:
        f.write(minimal_models)
    
    print("✅ Modelos temporales creados")

def test_with_minimal_app():
    """Probar con una aplicación Flask mínima."""
    print("🧪 Probando con aplicación mínima...")
    
    try:
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from test_models import User, db
        
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        db.init_app(app)
        
        with app.app_context():
            # Probar consulta
            users = User.query.all()
            print(f"✅ Consulta exitosa: {len(users)} usuarios encontrados")
            
            for user in users:
                print(f"  👤 {user.username} ({user.role}) - Activo: {user.is_active}")
                print(f"    🔐 Es admin: {user.is_admin()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error con aplicación mínima: {e}")
        return False
    finally:
        # Limpiar archivo temporal
        if os.path.exists('test_models.py'):
            os.remove('test_models.py')

def main():
    """Función principal."""
    print("🔧 Sincronizador Forzado de Base de Datos")
    print("=" * 50)
    
    # Restaurar desde backup
    if not restore_backup():
        print("❌ No se pudo restaurar desde backup")
        sys.exit(1)
    
    # Verificar y arreglar estructura
    if not verify_and_fix_structure():
        print("❌ No se pudo arreglar la estructura")
        sys.exit(1)
    
    # Crear modelos temporales
    create_minimal_models()
    
    # Probar con aplicación mínima
    if test_with_minimal_app():
        print("\n🎉 ¡Base de datos sincronizada exitosamente!")
        print("\n🚀 Ahora puedes:")
        print("   1. Limpiar cache: find . -name '*.pyc' -delete")
        print("   2. Reiniciar app: pkill -f 'python.*run.py' && python run.py &")
        print("   3. Probar login: admin / isaias52")
    else:
        print("\n❌ Aún hay problemas con la sincronización")

if __name__ == "__main__":
    main()
