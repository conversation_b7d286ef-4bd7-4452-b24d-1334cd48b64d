from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.api import deps
from app.websockets.location import location_websocket_endpoint

# Crear un router para los endpoints WebSocket
websocket_router = APIRouter()

@websocket_router.websocket("/ws/location/vehicle/{vehicle_id}")
async def vehicle_location_websocket(
    websocket: WebSocket,
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """Endpoint WebSocket para actualizaciones de ubicación de vehículos."""
    await location_websocket_endpoint(websocket, vehicle_id=vehicle_id, db=db)

@websocket_router.websocket("/ws/location/trip/{trip_id}")
async def trip_location_websocket(
    websocket: WebSocket,
    trip_id: int,
    db: Session = Depends(get_db)
):
    """Endpoint WebSocket para actualizaciones de ubicación relacionadas con un viaje."""
    await location_websocket_endpoint(websocket, trip_id=trip_id, db=db)
