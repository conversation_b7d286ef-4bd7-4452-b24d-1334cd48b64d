<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Editar Nodo</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body class="body-background">
  <header>
    <h1>Editar Nodo</h1>
  </header>
  <main>
    <form method="POST">
      <div class="form-group">
        <label for="name">Nombre del Nodo:</label>
        <input type="text" id="name" name="name" value="{{ node.name }}" required>
      </div>
      <div class="form-group">
        <label for="parent_id">Nodo Padre:</label>
        <select id="parent_id" name="parent_id">
          <option value="">Ninguno</option>
          {% for parent_node in parent_nodes %}
            <option value="{{ parent_node.id }}" {% if node.parent_id == parent_node.id %}selected{% endif %}>{{ parent_node.name }}</option>
          {% endfor %}
        </select>
      </div>
      <button type="submit">Actualizar Nodo</button>
    </form>
    <a href="{{ url_for('admin.list_nodes') }}">Volver a Listado de Nodos</a>
  </main>
  <footer>
    <p>© 2024 Tu Sitio Web</p>
  </footer>
</body>
</html>
