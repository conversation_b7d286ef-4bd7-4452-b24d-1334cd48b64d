{# File: backend/apps/incidents/templates/incidents/crear_incidencia.html #}
{% extends "core/base.html" %}

{% block title %}Reportar Incidencia - RN-Rural{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-3">Reportar Nueva Incidencia</h2>
    <hr>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div id="geolocation-status" class="alert alert-info" role="alert" style="display: none;">
        <i class="fas fa-location-arrow"></i> Obteniendo ubicación... Por favor, permite el acceso si tu navegador lo solicita.
    </div>

    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Datos de la Incidencia</h4>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="incidenciaForm">
                        {% csrf_token %}

                        {# Campos ocultos para latitud y longitud #}
                        <input type="hidden" name="latitud" id="id_latitud">
                        <input type="hidden" name="longitud" id="id_longitud">

                        <div class="mb-4">
                            <label for="{{ form.descripcion_texto.id_for_label }}" class="form-label">
                                <i class="fas fa-comment-alt"></i> <strong>{{ form.descripcion_texto.label }}</strong>
                            </label>
                            {{ form.descripcion_texto }}
                            {% if form.descripcion_texto.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.descripcion_texto.errors|striptags }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Describe con detalle la situación para que podamos atenderla adecuadamente.
                            </small>
                        </div>

                        {# Aquí irían los campos para audio, foto más adelante #}
                        <hr>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'dashboard_ciudadano' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times"></i> Cancelar
                            </a>
                            <button type="submit" class="btn btn-danger" id="submitButton">
                                <i class="fas fa-paper-plane"></i> Enviar Reporte
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const latitudInput = document.getElementById('id_latitud');
    const longitudInput = document.getElementById('id_longitud');
    const statusDiv = document.getElementById('geolocation-status');
    const submitButton = document.getElementById('submitButton');

    if (navigator.geolocation) {
        statusDiv.style.display = 'block'; // Muestra el mensaje "Obteniendo ubicación..."
        submitButton.disabled = true; // Deshabilita el botón mientras se obtiene la ubicación

        navigator.geolocation.getCurrentPosition(
            function(position) {
                latitudInput.value = position.coords.latitude;
                longitudInput.value = position.coords.longitude;
                console.log("Ubicación obtenida: ", position.coords.latitude, position.coords.longitude);
                statusDiv.textContent = 'Ubicación obtenida exitosamente.';
                statusDiv.classList.remove('alert-info');
                statusDiv.classList.add('alert-success');
                submitButton.disabled = false; // Habilita el botón
            },
            function(error) {
                console.error("Error al obtener la geolocalización: ", error);
                let errorMessage = 'No se pudo obtener tu ubicación. ';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += "Has denegado el permiso para la Geolocalización.";
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += "La información de ubicación no está disponible.";
                        break;
                    case error.TIMEOUT:
                        errorMessage += "La solicitud para obtener la ubicación del usuario ha caducado.";
                        break;
                    case error.UNKNOWN_ERROR:
                        errorMessage += "Ha ocurrido un error desconocido.";
                        break;
                }
                statusDiv.textContent = errorMessage + " Puedes enviar el reporte sin ubicación o intentarlo más tarde.";
                statusDiv.classList.remove('alert-info');
                statusDiv.classList.add('alert-warning');
                submitButton.disabled = false; // Habilita el botón incluso si falla, para permitir envío sin GPS
            },
            {
                enableHighAccuracy: true, // Intenta obtener la ubicación más precisa
                timeout: 10000,           // Tiempo máximo para obtener la ubicación (10 segundos)
                maximumAge: 0             // No usar una ubicación en caché
            }
        );
    } else {
        statusDiv.textContent = "Geolocalización no es soportada por este navegador. El reporte se enviará sin ubicación.";
        statusDiv.style.display = 'block';
        statusDiv.classList.remove('alert-info');
        statusDiv.classList.add('alert-danger');
        // El botón de envío no se deshabilita aquí, permitiendo el envío.
    }
});
</script>
{% endblock %}