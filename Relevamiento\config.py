# config.py
import os

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'cambia-esta-clave-secreta-en-produccion' # ¡IMPORTANTE CAMBIAR!
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'instance', 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.path.join(basedir, 'app', 'static', 'uploads')
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024 # 16MB
    # --- NUEVA CONFIGURACIÓN ---
    # Tamaño máximo para las imágenes redimensionadas (ancho, alto) en píxeles
    # Se mantendrá la proporción original.
    MAX_IMAGE_SIZE = (1280, 1024)