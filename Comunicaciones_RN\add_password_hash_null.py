# migrations/versions/add_password_hash_null.py
"""Allow password_hash to be nullable temporarily"""
from alembic import op
import sqlalchemy as sa

# Revisar el identificador de la migración anterior y siguiente
revision = 'add_password_hash_null'
down_revision = 'cfae30c9159c'
branch_labels = None
depends_on = None

def upgrade():
    with op.batch_alter_table('user') as batch_op:
        batch_op.add_column(sa.Column('password_hash', sa.String(length=150), nullable=True))

def downgrade():
    with op.batch_alter_table('user') as batch_op:
        batch_op.drop_column('password_hash')
