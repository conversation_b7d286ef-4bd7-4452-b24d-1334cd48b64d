{% extends "base_layout_operator.html" %}

{% block title %}Panel de Operador - Taxis{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    .stats-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    .trip-card {
        border-left: 4px solid #0d6efd;
        margin-bottom: 10px;
        transition: all 0.3s;
    }
    .trip-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .trip-card.pending {
        border-left-color: #dc3545;
    }
    .trip-card.in-progress {
        border-left-color: #ffc107;
    }
    .trip-card.completed {
        border-left-color: #198754;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Panel de Operador</h1>
        <p class="lead">Bienvenido al panel de operador del sistema de taxis.</p>
    </div>
</div>

<!-- Estadísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card bg-danger text-white">
            <div class="card-body text-center">
                <i class="bi bi-exclamation-triangle card-icon"></i>
                <div class="stats-value">{{ stats.pending_trips|default(0) }}</div>
                <div class="stats-label text-white-50">Viajes Pendientes</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-arrow-right-circle card-icon"></i>
                <div class="stats-value">{{ stats.in_progress_trips|default(0) }}</div>
                <div class="stats-label text-white-50">Viajes en Curso</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-check-circle card-icon"></i>
                <div class="stats-value">{{ stats.completed_trips|default(0) }}</div>
                <div class="stats-label text-white-50">Viajes Completados</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-taxi-front card-icon"></i>
                <div class="stats-value">{{ stats.active_vehicles|default(0) }}</div>
                <div class="stats-label text-white-50">Vehículos Activos</div>
            </div>
        </div>
    </div>
</div>

<!-- Mapa y Viajes Pendientes -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mapa de Vehículos y Viajes</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Viajes Pendientes</h5>
                <span class="badge bg-danger">{{ pending_trips|default([])|length }}</span>
            </div>
            <div class="card-body">
                <div class="trip-list">
                    {% for trip in pending_trips|default([]) %}
                    <div class="card trip-card pending">
                        <div class="card-body">
                            <h6 class="card-title">Viaje #{{ trip.id }}</h6>
                            <p class="card-text">
                                <small>
                                    <i class="bi bi-geo-alt"></i> {{ trip.origin_address|truncate(20) }} → {{ trip.destination_address|truncate(20) }}
                                </small>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ trip.requested_at|default('') }}</small>
                                <a href="{{ url_for('trip_details_route', trip_id=trip.id) }}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-eye"></i> Ver
                                </a>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <p class="text-center text-muted">No hay viajes pendientes</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Viajes en Curso y Acciones Rápidas -->
<div class="row">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Viajes en Curso</h5>
                <span class="badge bg-warning">{{ in_progress_trips|default([])|length }}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Pasajero</th>
                                <th>Conductor</th>
                                <th>Estado</th>
                                <th>Origen</th>
                                <th>Destino</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for trip in in_progress_trips|default([]) %}
                            <tr>
                                <td>{{ trip.id }}</td>
                                <td>{{ trip.passenger_name|default('') }}</td>
                                <td>{{ trip.driver_name|default('Sin asignar') }}</td>
                                <td>
                                    <span class="badge bg-warning">{{ trip.status }}</span>
                                </td>
                                <td>{{ trip.origin_address|truncate(15) }}</td>
                                <td>{{ trip.destination_address|truncate(15) }}</td>
                                <td>
                                    <a href="{{ url_for('trip_details_route', trip_id=trip.id) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center">No hay viajes en curso</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Acciones Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('trips_management_route') }}" class="btn btn-lg btn-outline-primary">
                        <i class="bi bi-geo-alt me-2"></i> Gestionar Viajes
                    </a>
                    <a href="{{ url_for('realtime_map_route') }}" class="btn btn-lg btn-outline-success">
                        <i class="bi bi-map me-2"></i> Mapa en Tiempo Real
                    </a>
                    <button class="btn btn-lg btn-outline-info" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-2"></i> Actualizar Datos
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 12); // Buenos Aires como ejemplo

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Iconos personalizados
    const vehicleIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    const tripIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon-2x.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    // Función para cargar vehículos y viajes activos
    async function loadMapData() {
        try {
            // Limpiar marcadores existentes
            map.eachLayer(layer => {
                if (layer instanceof L.Marker) {
                    map.removeLayer(layer);
                }
            });

            // Cargar vehículos activos
            const vehiclesResponse = await fetchWithAuth('/api/v1/vehicles/active');
            if (vehiclesResponse.ok) {
                const vehicles = await vehiclesResponse.json();
                
                // Añadir marcadores para cada vehículo
                vehicles.forEach(vehicle => {
                    if (vehicle.last_latitude && vehicle.last_longitude) {
                        const marker = L.marker([vehicle.last_latitude, vehicle.last_longitude], {icon: vehicleIcon})
                            .addTo(map)
                            .bindPopup(`
                                <strong>Vehículo: ${vehicle.plate_number}</strong><br>
                                Conductor: ${vehicle.driver_name || 'Sin asignar'}<br>
                                Estado: ${vehicle.status}
                            `);
                    }
                });
            }

            // Cargar viajes pendientes
            const tripsResponse = await fetchWithAuth('/api/v1/trips/pending');
            if (tripsResponse.ok) {
                const trips = await tripsResponse.json();
                
                // Añadir marcadores para cada viaje pendiente
                trips.forEach(trip => {
                    if (trip.origin_latitude && trip.origin_longitude) {
                        const marker = L.marker([trip.origin_latitude, trip.origin_longitude], {icon: tripIcon})
                            .addTo(map)
                            .bindPopup(`
                                <strong>Viaje #${trip.id}</strong><br>
                                Origen: ${trip.origin_address || 'No especificado'}<br>
                                Destino: ${trip.destination_address || 'No especificado'}<br>
                                Estado: ${trip.status}<br>
                                <a href="/trip/${trip.id}" class="btn btn-sm btn-primary mt-2">Ver detalles</a>
                            `);
                    }
                });
            }
        } catch (error) {
            console.error('Error al cargar datos del mapa:', error);
        }
    }

    // Función para actualizar todos los datos
    function refreshData() {
        loadMapData();
        window.location.reload();
    }

    // Cargar datos al iniciar
    loadMapData();
    
    // Actualizar cada 30 segundos
    setInterval(loadMapData, 30000);
</script>
{% endblock %}
