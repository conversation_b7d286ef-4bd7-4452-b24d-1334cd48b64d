#!/usr/bin/env python3
# --- Archivo: backup_data.py ---
# Script para hacer backup completo de todos los datos

import sqlite3
import json
import os
from datetime import datetime

def backup_all_data():
    """Hacer backup completo de todos los datos en formato JSON."""
    
    if not os.path.exists('app.db'):
        print("❌ No existe app.db")
        return False
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas encontradas: {tables}")
        
        backup_data = {
            'timestamp': datetime.now().isoformat(),
            'tables': {}
        }
        
        # Backup de cada tabla
        for table in tables:
            if table == 'sqlite_sequence':
                continue
                
            print(f"💾 Haciendo backup de tabla: {table}")
            
            try:
                # Obtener estructura de la tabla
                cursor.execute(f"PRAGMA table_info({table});")
                columns_info = cursor.fetchall()
                columns = [col[1] for col in columns_info]
                
                # Obtener todos los datos
                cursor.execute(f"SELECT * FROM {table};")
                rows = cursor.fetchall()
                
                # Convertir a formato JSON-friendly
                table_data = []
                for row in rows:
                    row_dict = {}
                    for i, value in enumerate(row):
                        if i < len(columns):
                            row_dict[columns[i]] = value
                    table_data.append(row_dict)
                
                backup_data['tables'][table] = {
                    'columns': columns,
                    'column_info': columns_info,
                    'data': table_data,
                    'count': len(table_data)
                }
                
                print(f"  ✅ {len(table_data)} registros guardados")
                
            except Exception as e:
                print(f"  ❌ Error en tabla {table}: {e}")
        
        # Guardar backup en archivo JSON
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_completo_{timestamp}.json'
        
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Backup completo guardado en: {backup_filename}")
        
        # Hacer también backup de la base de datos SQLite
        import shutil
        db_backup_filename = f'app_backup_{timestamp}.db'
        shutil.copy2('app.db', db_backup_filename)
        print(f"✅ Backup de base de datos guardado en: {db_backup_filename}")
        
        # Mostrar resumen
        print("\n📊 Resumen del backup:")
        for table, info in backup_data['tables'].items():
            print(f"  📋 {table}: {info['count']} registros")
        
        conn.close()
        return backup_filename, db_backup_filename
        
    except Exception as e:
        print(f"❌ Error haciendo backup: {e}")
        return False

def verify_backup(backup_filename):
    """Verificar que el backup se hizo correctamente."""
    
    try:
        with open(backup_filename, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        print(f"🔍 Verificando backup: {backup_filename}")
        print(f"📅 Fecha: {backup_data['timestamp']}")
        
        for table, info in backup_data['tables'].items():
            print(f"  ✅ {table}: {info['count']} registros")
            
            # Mostrar muestra de datos para verificar
            if info['data'] and len(info['data']) > 0:
                sample = info['data'][0]
                print(f"    📝 Muestra: {list(sample.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verificando backup: {e}")
        return False

def main():
    """Función principal."""
    print("💾 Backup Completo de Datos")
    print("=" * 30)
    
    result = backup_all_data()
    
    if result:
        backup_filename, db_backup_filename = result
        
        if verify_backup(backup_filename):
            print(f"\n🎉 ¡Backup completado exitosamente!")
            print(f"\n📁 Archivos creados:")
            print(f"  📄 Datos JSON: {backup_filename}")
            print(f"  🗃️  Base de datos: {db_backup_filename}")
            print(f"\n💡 Ahora puedes proceder con seguridad a:")
            print(f"   python clean_rebuild.py")
        else:
            print(f"\n❌ Error verificando el backup")
    else:
        print(f"\n❌ Error creando el backup")

if __name__ == "__main__":
    main()
