# /icon_tray.py

import webbrowser
from pystray import Icon, MenuItem as Item, <PERSON><PERSON>
from PIL import Image
import sys

def open_website(host, port):
    """Abre el navegador web en la dirección del servidor local."""
    webbrowser.open(f"http://{host}:{port}")

def exit_app(icon, item):
    """Cierra la aplicación."""
    icon.stop()
    sys.exit()  # Asegura que la aplicación se cierra correctamente

def create_tray_icon(host, port):
    """Crea el ícono de la bandeja del sistema con opciones."""
    # Cargar una imagen para el icono de la bandeja
    image = Image.open("static/verde.png")

    # Crear el menú con la opción para abrir el sitio web y salir de la aplicación
    menu = Menu(
        Item("Abrir", lambda: open_website(host, port)),
        Item("Salir", exit_app)
    )

    # Crear el icono de la bandeja
    icon = Icon("VHF Node", image, menu=menu)

    # Ejecutar el icono en la bandeja
    icon.run()
