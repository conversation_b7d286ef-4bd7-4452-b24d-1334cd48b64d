<!-- /app/templates/pastorado/request_transfer_form.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container my-4">
    <h1>{{ title }}</h1>

    <div class="card mb-4">
        <div class="card-header">Información del Miembro</div>
        <div class="card-body">
            <p><strong>Nombre:</strong> {{ user.full_name }}</p>
            <p><strong>Iglesia Actual:</strong> {{ current_church.name if current_church else 'N/A' }}</p>
        </div>
    </div>

    <form method="POST" action="">
        {{ form.hidden_tag() }}

        <div class="form-group">
            {{ render_field(form.target_church, class="form-control " + ('is-invalid' if form.target_church.errors else '') ) }}
             {% if form.target_church.errors %}
                <div class="invalid-feedback">
                    {{ form.target_church.errors|join(', ') }}
                </div>
             {% endif %}
             {% if not form.target_church.choices or (form.target_church.choices|length == 1 and form.target_church.choices[0][0] == 0) %}
                 <small class="form-text text-muted">No hay otras iglesias disponibles o ninguna tiene pastor asignado.</small>
             {% endif %}
        </div>

        <div class="form-group">
            {{ render_field(form.request_notes, class="form-control", rows="3", placeholder="Añade un motivo o nota para el pastor destino (opcional)") }}
        </div>

        <button type="submit" class="btn btn-primary"
                {% if not form.target_church.choices or (form.target_church.choices|length == 1 and form.target_church.choices[0][0] == 0) %}disabled{% endif %}>
            {{ form.submit.label }}
        </button>
        <a href="{{ url_for('routes.pastor_member_detail', user_id=user.id) or url_for('routes.pastor_members') }}" class="btn btn-secondary">Cancelar</a> {# Volver al detalle o lista #}
    </form>

</div>
{% endblock %}