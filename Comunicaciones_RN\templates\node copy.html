<!-- templates/node.html -->

<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Node: {{ node.name }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.0/socket.io.min.js"></script>
    <style>
        /* ... (Estilos CSS - Mantenidos, sin cambios) ... */
        .body-background::before {
            background: url('{{ url_for('static', filename='RNCom.png') }}') no-repeat center center;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }
        .audio-button img {
            cursor: pointer;
            width: 100px;
            height: 100px;
            margin-bottom: 5px;
        }
        .chat-container {
            display: flex;
            justify-content: center;
            width: 80%;
            max-width: 1200px;
            margin-top: 5px;
        }
        .chat-box {
            border: 1px solid #726e6e;
            padding: 10px;
            height: 420px;
            width: 70%;
            max-width: 800px;
            min-width: 400px;
            overflow-y: auto;
            background-color: #f0f8ff;
            margin-bottom: 10px;
            border-radius: 10px;
            word-wrap: break-word;
        }
        .user-list {
            border: 1px solid #a89999;
            padding: 10px;
            height: 420px;
            width: 25%;
            max-width: 300px;
            min-width: 200px;
            overflow-y: auto;
            background-color: #f0f8ff;
            margin-bottom: 10px;
            margin-left: 10px;
            border-radius: 10px;
        }
        .chat-input {
            display: flex;
            align-items: center;
            width: 80%;
            max-width: 1200px;
            margin-bottom: 20px;
        }
        .chat-input input[type="text"] {
            flex: 1;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-right: 10px;
        }
        .chat-input button {
            padding: 5px 10px;
            background-color: #006994;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .chat-input button:hover {
            background-color: #004d73;
        }
        .message strong {
            color: #006994;
        }
        .timestamp {
            font-size: 0.8em;
            color: #888;
            margin-left: 10px;
        }
        .user-list h3 {
            margin-top: 0;
        }
        .user-item {
            display: flex;
            align-items: center;
        }
        .audio-icon {
            display: none;
            width: 20px;
            height: 20px;
            margin-left: 10px;
        }
        .audio-icon.active {
            display: inline;
        }
        #dragDropArea {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin-top: 10px;
            margin-bottom: 20px;
            border-radius: 10px;
            background-color: #f8f9fa;
            font-weight: bold;
            color: #6c757d;
            transition: border-color 0.3s ease;
        }
         #dragDropArea:hover {
            border-color: #007bff;
        }

        /* Estilos para el botón de notificaciones */
        #notificationsButton {
            width: 30px;
            height: 30px;
            border-radius: 50%; /* Forma circular */
            background-color: red; /* Inicialmente rojo (desactivado) */
            cursor: pointer;
            margin-left: 10px; /* Espacio del chat input, si lo tienes */
            border: none; /* Quita el borde predeterminado */
            outline: none; /* Quita el contorno al hacer clic */
            transition: background-color 0.3s ease; /* Transición suave */
        }
        #notificationsButton.active {
            background-color: green;
        }
    </style>
</head>
<!--  data-node-id, data-username y data-node-name aquí -->
<body class="body-background" data-node-id="{{ node.id }}" data-username="{{ current_user.username }}" data-node-name="{{ node.name }}">
    <header>
        <h1> Usuario {{ current_user.username }} En Nodo: {{ node.name }}</h1>
    </header>
    <main>
        {% if is_city %}
            <div class="audio-buttons">
                <img id="transmitAudio" src="{{ url_for('static', filename='verde.png') }}" alt="Transmitir Audio">
            </div>
            <div class="chat-container">
                <div class="chat-box" id="messages"></div>
                <div class="user-list" id="userListDiv">
                    <h3>Usuarios Conectados</h3>
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="chatInput" placeholder="Escribe tu mensaje...">
                <button id="sendButton">Enviar</button>                  
            </div>
                <div class="download-link">
                    {% if current_user.role == 'admin' %}
                    <input type="file" id="fileInput" style="display: none;">
                    <button id="uploadFileButton">Cargar Archivo</button>
                    </div>
                    <div id="dragDropArea">Arrastra y suelta archivos aquí...</div>
                    {% endif %}
                  </div>

            </div>

            <!-- Script DENTRO de is_city, ANTES de cargar webrtc.js -->
            <script>
                const rojoImageUrl = "{{ url_for('static', filename='rojo.png') }}";
                const verdeImageUrl = "{{ url_for('static', filename='verde.png') }}";
            </script>
            <script src="{{ url_for('static', filename='webrtc.js') }}?v=8"></script>

        {% else %}
            <ul>
                {% for subnode in subnodes %}
                    <li><a href="{{ url_for('views.node_view', node_id=subnode.id) }}">{{ subnode.name }}</a></li>
                {% endfor %}
            </ul>
        {% endif %}

        <div class="navigation-buttons" style="text-align: center; margin-top: 20px;">
             <!-- Usar enlaces normales -->
            <a href="{{ url_for('views.dashboard') }}" style="margin-right: 10px;">Volver</a>
            <a href="{{ url_for('views.dashboard') }}">Volver al Dashboard</a>
        </div>
    </main>
    <footer>
        <p>© 2024 Tu Sitio Web</p>
    </footer>
</body>
</html>