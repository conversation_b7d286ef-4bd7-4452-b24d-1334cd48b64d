{# File: backend/apps/core/templates/core/estadisticas.html #}
{% extends "core/base.html" %}

{% block title %}Estadísticas - RN-Rural{% endblock %}

{% block extra_head %}
<style>
    .card-dashboard {
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    .card-dashboard:hover {
        transform: translateY(-5px);
    }
    .card-header-custom {
        border-radius: 10px 10px 0 0;
        padding: 15px;
        font-weight: bold;
    }
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        text-align: center;
        padding: 20px 0;
    }
    .stats-label {
        text-align: center;
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 15px;
    }
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
    .bg-nueva { background-color: #0dcaf0; color: white; }
    .bg-proceso { background-color: #ffc107; color: black; }
    .bg-derivada { background-color: #fd7e14; color: white; }
    .bg-resuelta { background-color: #198754; color: white; }
    .bg-cerrada { background-color: #6c757d; color: white; }
    .text-nueva { color: #0dcaf0; }
    .text-proceso { color: #ffc107; }
    .text-derivada { color: #fd7e14; }
    .text-resuelta { color: #198754; }
    .text-cerrada { color: #6c757d; }
    .date-filter {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }
    .date-filter .form-control {
        max-width: 200px;
    }
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-3">Estadísticas del Sistema</h2>
    <hr>

    <div class="date-filter">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-calendar-alt"></i> Desde</span>
            <input type="date" id="fecha_inicio" class="form-control" value="{{ fecha_inicio|date:'Y-m-d' }}">
        </div>
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-calendar-alt"></i> Hasta</span>
            <input type="date" id="fecha_fin" class="form-control" value="{{ fecha_fin|date:'Y-m-d' }}">
        </div>
        <button id="filtrar_fechas" class="btn btn-primary">
            <i class="fas fa-filter"></i> Filtrar
        </button>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-nueva">
                    <div class="text-center">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="stats-number text-nueva">{{ count_nuevas }}</div>
                    <div class="stats-label">Incidencias Nuevas</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-proceso">
                    <div class="text-center">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="stats-number text-proceso">{{ count_proceso }}</div>
                    <div class="stats-label">Incidencias En Proceso</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-derivada">
                    <div class="text-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="stats-number text-derivada">{{ count_derivadas }}</div>
                    <div class="stats-label">Incidencias Derivadas</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-resuelta">
                    <div class="text-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="stats-number text-resuelta">{{ count_resueltas }}</div>
                    <div class="stats-label">Incidencias Resueltas</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-chart-pie"></i> Distribución por Estado</h4>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="estadoChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-chart-line"></i> Incidencias por Día</h4>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="timelineChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-chart-bar"></i> Rendimiento de Brigadas</h4>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="brigadasChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Datos para el gráfico de estados
        const estadoData = {
            labels: ['Nuevas', 'En Proceso', 'Derivadas', 'Resueltas'],
            datasets: [{
                data: [{{ count_nuevas }}, {{ count_proceso }}, {{ count_derivadas }}, {{ count_resueltas }}],
                backgroundColor: ['#0dcaf0', '#ffc107', '#fd7e14', '#198754'],
                borderWidth: 1
            }]
        };

        // Configuración del gráfico de estados
        const estadoConfig = {
            type: 'pie',
            data: estadoData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        };

        // Crear gráfico de estados
        const estadoChart = new Chart(
            document.getElementById('estadoChart'),
            estadoConfig
        );

        // Datos para el gráfico de línea de tiempo
        const timelineData = {
            labels: [{% for fecha, _ in incidencias_por_dia %}'{{ fecha|date:"d/m/Y" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
            datasets: [{
                label: 'Incidencias Reportadas',
                data: [{% for _, count in incidencias_por_dia %}{{ count }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                tension: 0.4,
                fill: true
            }]
        };

        // Configuración del gráfico de línea de tiempo
        const timelineConfig = {
            type: 'line',
            data: timelineData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        };

        // Crear gráfico de línea de tiempo
        const timelineChart = new Chart(
            document.getElementById('timelineChart'),
            timelineConfig
        );

        // Datos para el gráfico de brigadas
        const brigadasData = {
            labels: [{% for brigada in rendimiento_brigadas %}'{{ brigada.username }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
            datasets: [{
                label: 'Incidencias Resueltas',
                data: [{% for brigada in rendimiento_brigadas %}{{ brigada.resueltas }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                backgroundColor: '#198754',
                borderWidth: 1
            }, {
                label: 'Incidencias Pendientes',
                data: [{% for brigada in rendimiento_brigadas %}{{ brigada.pendientes }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                backgroundColor: '#fd7e14',
                borderWidth: 1
            }]
        };

        // Configuración del gráfico de brigadas
        const brigadasConfig = {
            type: 'bar',
            data: brigadasData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        };

        // Crear gráfico de brigadas
        const brigadasChart = new Chart(
            document.getElementById('brigadasChart'),
            brigadasConfig
        );

        // Filtro de fechas
        document.getElementById('filtrar_fechas').addEventListener('click', function() {
            const fechaInicio = document.getElementById('fecha_inicio').value;
            const fechaFin = document.getElementById('fecha_fin').value;
            
            if (fechaInicio && fechaFin) {
                window.location.href = `{% url 'estadisticas' %}?fecha_inicio=${fechaInicio}&fecha_fin=${fechaFin}`;
            }
        });
    });
</script>
{% endblock %}
