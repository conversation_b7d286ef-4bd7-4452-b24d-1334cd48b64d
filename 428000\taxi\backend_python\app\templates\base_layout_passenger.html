<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Panel de Pasajero - Taxis{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        header {
            background-color: #6f42c1;
            color: white;
            padding: 1rem;
        }
        header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        header nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header nav a {
            color: #e2d9f3;
            text-decoration: none;
            margin-left: 15px;
        }
        header nav a:hover {
            color: white;
        }
        main {
            flex: 1;
            padding: 2rem;
        }
        footer {
            background-color: #6f42c1;
            color: white;
            text-align: center;
            padding: 1rem;
            margin-top: auto;
        }
        .passenger-badge {
            background-color: #5a32a3;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }
    </style>
    {% block head_extra %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#">Panel de Pasajero <span class="passenger-badge">USUARIO</span></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        {% if current_user %}
                            <li class="nav-item">
                                <span class="nav-link">Bienvenido, {{ current_user.email }}
                                    {% if request.state.user_roles_from_token %}
                                        ({{ request.state.user_roles_from_token[0] }})
                                    {% else %}
                                        (Sin rol)
                                    {% endif %}
                                </span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('passenger_dashboard_route') }}">
                                    <i class="bi bi-speedometer2"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('passenger_request_trip_route') }}">
                                    <i class="bi bi-plus-circle"></i> Solicitar Viaje
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('passenger_trips_route') }}">
                                    <i class="bi bi-geo-alt"></i> Mis Viajes
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('passenger_profile_route') }}">
                                    <i class="bi bi-person"></i> Mi Perfil
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('web_logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> Cerrar Sesión
                                </a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('login_page') }}">Iniciar Sesión</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container">
        {% block content %}{% endblock %}
    </main>

    <footer>
        <p>&copy; 2023 Sistema de Taxis</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Función para obtener cookies por nombre
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        // Guardar token en localStorage si está presente en la respuesta de login
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        if (token) {
            localStorage.setItem('taxi_passenger_token', token);
        }

        async function fetchWithAuth(url, options = {}) {
            // Intentar obtener el token de la cookie primero
            let authToken = getCookie('admin_access_token');

            // Si no hay token en la cookie, intentar obtenerlo del localStorage
            if (!authToken) {
                authToken = localStorage.getItem('taxi_passenger_token');
            }

            const headers = {
                ...options.headers,
                'Authorization': `Bearer ${authToken}`
            };

            try {
                const response = await fetch(url, { ...options, headers, credentials: 'include' });
                if (response.status === 401) { // No autorizado o token expirado
                    localStorage.removeItem('taxi_passenger_token');
                    window.location.href = "{{ url_for('login_page') }}"; // Redirigir a login
                }
                return response;
            } catch (error) {
                console.error('Error en fetchWithAuth:', error);
                throw error;
            }
        }
    </script>
    {% block scripts_extra %}{% endblock %}
</body>
</html>
