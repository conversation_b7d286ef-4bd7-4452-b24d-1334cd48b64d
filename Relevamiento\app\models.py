# --- Archivo: app/models.py ---
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from app import db, login # Importa db y login desde __init__.py
import json # Necesario para get_annotations en Image

# --- Clase User CON SISTEMA DE ROLES Y PERMISOS ---
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), index=True, unique=True, nullable=False)
    email = db.Column(db.String(120), index=True, unique=True, nullable=True)
    password_hash = db.Column(db.String(256), nullable=False)

    # Nuevos campos para sistema de roles
    role = db.Column(db.String(20), default='operador', nullable=False)  # administrador, supervisor, operador, visualizador
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_by = db.Column(db.Integer, db.<PERSON><PERSON>ey('user.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relaciones
    images = db.relationship('Image', backref='uploader', lazy='dynamic')
    city_permissions = db.relationship('UserCityPermission', backref='user', lazy='dynamic', cascade="all, delete-orphan")
    source_permissions = db.relationship('UserSourcePermission', backref='user', lazy='dynamic', cascade="all, delete-orphan")
    permissions = db.relationship('UserPermission', backref='user', lazy='dynamic', cascade="all, delete-orphan")
    created_users = db.relationship('User', backref=db.backref('creator', remote_side=[id]), lazy='dynamic')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        if self.password_hash is None: return False
        return check_password_hash(self.password_hash, password)

    # --- MÉTODOS DE ROLES ---
    def is_admin(self):
        """Verifica si el usuario es administrador."""
        return self.role == 'administrador'

    def is_supervisor(self):
        """Verifica si el usuario es supervisor o administrador."""
        return self.role in ['administrador', 'supervisor']

    def is_operator(self):
        """Verifica si el usuario es operador o superior."""
        return self.role in ['administrador', 'supervisor', 'operador']

    def can_manage_users(self):
        """Verifica si puede gestionar usuarios."""
        return self.is_admin()

    # --- MÉTODOS DE PERMISOS ---
    def has_permission(self, permission_type):
        """Verifica si tiene un permiso específico."""
        if self.is_admin():
            return True  # Admin tiene todos los permisos

        permission = self.permissions.filter_by(permission_type=permission_type).first()
        return permission.permission_value if permission else False

    def can_access_city(self, city):
        """Verifica si puede acceder a una ciudad específica."""
        if self.is_admin():
            return True  # Admin puede ver todas las ciudades

        if not city:
            return True  # Si no hay ciudad especificada, permitir acceso

        return self.city_permissions.filter_by(city=city).first() is not None

    def can_access_source(self, source):
        """Verifica si puede acceder a una fuente/capa específica."""
        if self.is_admin():
            return True  # Admin puede ver todas las fuentes

        if not source:
            return True  # Si no hay fuente especificada, permitir acceso

        return self.source_permissions.filter_by(source=source).first() is not None

    def get_allowed_cities(self):
        """Obtiene lista de ciudades permitidas."""
        if self.is_admin():
            # Admin puede ver todas las ciudades
            from sqlalchemy import distinct
            cities = db.session.query(distinct(Point.city)).filter(Point.city.isnot(None)).all()
            return [city[0] for city in cities]

        return [perm.city for perm in self.city_permissions.all()]

    def get_allowed_sources(self):
        """Obtiene lista de fuentes/capas permitidas."""
        if self.is_admin():
            # Admin puede ver todas las fuentes
            from sqlalchemy import distinct
            sources = db.session.query(distinct(Point.source)).filter(Point.source.isnot(None)).all()
            return [source[0] for source in sources]

        return [perm.source for perm in self.source_permissions.all()]

    def can_view_points(self):
        """Verifica si puede ver puntos."""
        return self.has_permission('points_view') or self.is_admin()

    def can_edit_points(self):
        """Verifica si puede editar puntos."""
        return self.has_permission('points_edit') or self.is_admin()

    def can_create_points(self):
        """Verifica si puede crear puntos."""
        return self.has_permission('points_create') or self.is_admin()

    def can_delete_points(self):
        """Verifica si puede eliminar puntos."""
        return self.has_permission('points_delete') or self.is_admin()

    def can_view_cameras(self):
        """Verifica si puede ver cámaras."""
        return self.has_permission('cameras_view') or self.is_admin()

    def can_edit_cameras(self):
        """Verifica si puede editar cámaras."""
        return self.has_permission('cameras_edit') or self.is_admin()

    def can_create_cameras(self):
        """Verifica si puede crear cámaras."""
        return self.has_permission('cameras_create') or self.is_admin()

    def can_delete_cameras(self):
        """Verifica si puede eliminar cámaras."""
        return self.has_permission('cameras_delete') or self.is_admin()

    def can_view_reports(self):
        """Verifica si puede ver reportes."""
        return self.has_permission('reports_view') or self.is_admin()

    def can_export_data(self):
        """Verifica si puede exportar datos."""
        return self.has_permission('reports_export') or self.is_admin()

    def __repr__(self):
        return f'<User {self.username} ({self.role})>'

@login.user_loader
def load_user(id):
    try:
        return db.session.get(User, int(id))
    except Exception as e:
        print(f"Error loading user {id}: {e}")
        return None

# --- Clase Point (CON get_status_color AÑADIDO) ---
class Point(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    name = db.Column(db.String(128), nullable=True) # Nombre/Dirección
    city = db.Column(db.String(80), nullable=True, index=True)
    description = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(20), default='azul', index=True, nullable=False)
    source = db.Column(db.String(128), nullable=True, index=True)  # nombre del archivo o capa
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    images = db.relationship('Image', backref='point', lazy='dynamic', cascade="all, delete-orphan")

    def __repr__(self):
        return f'<Point {self.id} ({self.latitude:.4f}, {self.longitude:.4f})>'

    # --- MÉTODO AÑADIDO / DESCOMENTADO ---
    def get_status_color(self):
        """Devuelve el nombre del color asociado al estado actual del punto."""
        color_map = {
            'verde': 'green',
            'amarillo': 'yellow',
            'azul': 'blue',
            'rojo': 'red',
        }
        # Devuelve el color mapeado o 'grey' si el estado no está en el mapa
        return color_map.get(self.status, 'grey')
    # --- FIN MÉTODO AÑADIDO ---

    def to_dict(self, include_cameras=False): # Método para serializar a diccionario (usado en la API/mapa)
        """Convierte el objeto Point en un diccionario serializable."""
        # Llama a get_status_color para obtener el color actual
        color = self.get_status_color()
        data = {
            'id': self.id,
            'lat': self.latitude,
            'lon': self.longitude,
            'name': self.name or f"Punto {self.id}",
            'city': self.city, # Incluir ciudad
            'description': self.description,
            'status': self.status,
            'color': color, # Incluir color basado en estado
            'source': self.source or 'desconocido',  # Incluir capa
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'image_count': self.images.count() # Contar imágenes
        }

        # Incluir datos de cámaras si se solicita
        if include_cameras:
            cameras_data = []
            for camera in self.cameras.all():
                camera_coords = camera.get_coordinates()
                camera_data = {
                    'id': camera.id,
                    'type': camera.type,
                    'direction': camera.direction,
                    'coordinates': camera_coords,
                    'location_source': camera.location_source,
                    'location_accuracy': camera.location_accuracy,
                    'created_at': camera.created_at.isoformat() if camera.created_at else None,
                    'has_own_coordinates': camera.has_own_coordinates()
                }
                cameras_data.append(camera_data)
            data['cameras'] = cameras_data
            data['camera_count'] = len(cameras_data)

        return data

# --- Clase Image (sin cambios) ---
class Image(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(256), nullable=False, unique=True)
    original_filename = db.Column(db.String(256), nullable=True)
    upload_timestamp = db.Column(db.DateTime, index=True, default=datetime.utcnow)
    notes = db.Column(db.Text, nullable=True)
    point_id = db.Column(db.Integer, db.ForeignKey('point.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    annotations_json = db.Column(db.Text, nullable=True) # Campo para anotaciones Konva

    def get_url(self):
        from flask import url_for
        return url_for('static', filename=f'uploads/{self.filename}', _external=False)

    def get_annotations(self):
        if self.annotations_json:
            try: return json.loads(self.annotations_json)
            except json.JSONDecodeError: return None
        return None

    def __repr__(self):
        return f'<Image {self.filename} for Point {self.point_id}>'

class Camera(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    point_id = db.Column(db.Integer, db.ForeignKey('point.id'), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # 'domo', 'fija', 'otra'
    direction = db.Column(db.String(128), nullable=False)  # Hacia dónde apunta
    photo_filename = db.Column(db.String(256), nullable=True)  # Imagen opcional
    # Coordenadas específicas de la cámara
    latitude = db.Column(db.Float, nullable=True)  # Latitud de la cámara
    longitude = db.Column(db.Float, nullable=True)  # Longitud de la cámara
    location_source = db.Column(db.String(20), nullable=True)  # 'gps', 'manual', 'point'
    location_accuracy = db.Column(db.Float, nullable=True)  # Precisión en metros (si viene del GPS)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    annotations_json = db.Column(db.Text, nullable=True)  # Campo para anotaciones Konva
    point = db.relationship('Point', backref=db.backref('cameras', lazy='dynamic', cascade="all, delete-orphan"))

    def get_photo_url(self):
        from flask import url_for
        if self.photo_filename:
            return url_for('static', filename=f'uploads/{self.photo_filename}', _external=False)
        return None

    def get_coordinates(self):
        """Devuelve las coordenadas de la cámara o las del punto si no tiene propias."""
        if self.latitude is not None and self.longitude is not None:
            return {
                'lat': self.latitude,
                'lon': self.longitude,
                'source': self.location_source or 'unknown',
                'accuracy': self.location_accuracy
            }
        elif self.point:
            return {
                'lat': self.point.latitude,
                'lon': self.point.longitude,
                'source': 'point',
                'accuracy': None
            }
        return None

    def has_own_coordinates(self):
        """Verifica si la cámara tiene sus propias coordenadas."""
        return self.latitude is not None and self.longitude is not None

    def __repr__(self):
        return f'<Camera {self.id} ({self.type}) for Point {self.point_id}>'

# --- CLASES PARA SISTEMA DE PERMISOS ---
class UserCityPermission(db.Model):
    """Permisos de usuario para ciudades específicas."""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    city = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    __table_args__ = (db.UniqueConstraint('user_id', 'city', name='unique_user_city'),)

    def __repr__(self):
        return f'<UserCityPermission {self.user.username} -> {self.city}>'

class UserSourcePermission(db.Model):
    """Permisos de usuario para fuentes/capas específicas."""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    source = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    __table_args__ = (db.UniqueConstraint('user_id', 'source', name='unique_user_source'),)

    def __repr__(self):
        return f'<UserSourcePermission {self.user.username} -> {self.source}>'

class UserPermission(db.Model):
    """Permisos específicos de usuario."""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    permission_type = db.Column(db.String(50), nullable=False)  # 'points_view', 'cameras_edit', etc.
    permission_value = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (db.UniqueConstraint('user_id', 'permission_type', name='unique_user_permission'),)

    def __repr__(self):
        return f'<UserPermission {self.user.username} -> {self.permission_type}: {self.permission_value}>'

# --- CONSTANTES DE PERMISOS ---
PERMISSION_TYPES = {
    'points_view': 'Ver puntos',
    'points_edit': 'Editar puntos',
    'points_create': 'Crear puntos',
    'points_delete': 'Eliminar puntos',
    'cameras_view': 'Ver cámaras',
    'cameras_edit': 'Editar cámaras',
    'cameras_create': 'Crear cámaras',
    'cameras_delete': 'Eliminar cámaras',
    'reports_view': 'Ver reportes',
    'reports_export': 'Exportar datos',
    'users_manage': 'Gestionar usuarios'
}

ROLE_TYPES = {
    'administrador': 'Administrador',
    'supervisor': 'Supervisor',
    'operador': 'Operador',
    'visualizador': 'Visualizador'
}

# Permisos por defecto para cada rol
DEFAULT_PERMISSIONS = {
    'administrador': {
        # Admin tiene todos los permisos (se maneja en código)
    },
    'supervisor': {
        'points_view': True,
        'points_edit': True,
        'points_create': True,
        'cameras_view': True,
        'cameras_edit': True,
        'cameras_create': True,
        'reports_view': True,
        'reports_export': True
    },
    'operador': {
        'points_view': True,
        'points_edit': True,
        'cameras_view': True,
        'cameras_edit': True,
        'reports_view': True
    },
    'visualizador': {
        'points_view': True,
        'cameras_view': True,
        'reports_view': True
    }
}

# --- MODELOS DE AUDITORÍA ---
# ----------------------------------------------------------------------
#  RE-EXPORTA MODELOS DE AUDITORÍA (definidos en app/models_audit.py)
# ----------------------------------------------------------------------
from app.models_audit import AuditLog, UserSession  # noqa: E402, F401