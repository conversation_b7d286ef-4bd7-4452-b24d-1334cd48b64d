<!-- /app/templates/pastorado/review_transfer_form.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container my-4">
    <h1>{{ title }}</h1>

    <div class="card mb-4">
        <div class="card-header">Detalles de la Solicitud</div>
        <div class="card-body">
            <p><strong>Miembro a Transferir:</strong> {{ member_user.full_name }}</p>
            <p><strong>Iglesia Origen:</strong> {{ transfer_request.origin_church.name if transfer_request.origin_church else 'N/A' }}</p>
            <p><strong><PERSON><PERSON><PERSON> (Tu Iglesia):</strong> {{ transfer_request.target_church.name }}</p>
            <p><strong>Solicitado por:</strong> Pastor {{ transfer_request.requesting_pastor.full_name if transfer_request.requesting_pastor else 'N/A' }}</p>
            <p><strong><PERSON><PERSON>:</strong> {{ transfer_request.created_at | to_local }}</p>
            <p><strong>Notas del Solicitante:</strong></p>
            <blockquote class="blockquote">
                 <p class="mb-0 small"><em>{{ transfer_request.request_notes or 'Sin notas.' }}</em></p>
            </blockquote>
        </div>
    </div>

    <form method="POST" action="">
        {{ form.hidden_tag() }}

        <div class="form-group">
             {{ render_field(form.approval_notes, class="form-control", rows="3", placeholder="Añade notas sobre tu decisión (opcional)") }}
        </div>

        <div class="btn-group" role="group" aria-label="Acciones de aprobación">
            {{ form.submit_approve(class="btn btn-success") }}
            {{ form.submit_reject(class="btn btn-danger") }}
        </div>
        <a href="{{ url_for('routes.list_pending_transfers') }}" class="btn btn-secondary ml-2">Volver a Pendientes</a>

    </form>

</div>
{% endblock %}