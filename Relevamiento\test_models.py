#!/usr/bin/env python3
# --- Archivo: test_models.py ---
# Script para probar que los modelos funcionen correctamente

import os
import sys

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_models():
    """Probar que los modelos funcionen correctamente."""
    
    try:
        from app import create_app, db
        from app.models import User, UserCityPermission, UserSourcePermission, UserPermission
        
        print("🧪 Probando modelos...")
        
        # Crear aplicación Flask
        app = create_app()
        
        with app.app_context():
            print("📋 Verificando modelos...")
            
            # Probar consulta básica de usuarios
            try:
                users = User.query.all()
                print(f"✅ Usuarios encontrados: {len(users)}")
                
                for user in users:
                    print(f"  👤 {user.username} ({user.role}) - Activo: {user.is_active}")
                    
                    # Probar métodos de permisos
                    print(f"    🔐 Es admin: {user.is_admin()}")
                    print(f"    🔐 Puede gestionar usuarios: {user.can_manage_users()}")
                    print(f"    🔐 Puede ver puntos: {user.can_view_points()}")
                    print(f"    🔐 Puede editar cámaras: {user.can_edit_cameras()}")
                    
                    # Probar permisos geográficos
                    ciudades = user.get_allowed_cities()
                    fuentes = user.get_allowed_sources()
                    print(f"    🏙️  Ciudades permitidas: {len(ciudades)}")
                    print(f"    📋 Fuentes permitidas: {len(fuentes)}")
                
            except Exception as e:
                print(f"❌ Error consultando usuarios: {e}")
                return False
            
            # Probar creación de permisos de prueba
            try:
                print("\n🔧 Probando creación de permisos...")
                
                # Obtener el primer usuario
                user = User.query.first()
                if user:
                    # Crear permiso de ciudad de prueba
                    city_perm = UserCityPermission(user_id=user.id, city="Test City")
                    db.session.add(city_perm)
                    
                    # Crear permiso de fuente de prueba
                    source_perm = UserSourcePermission(user_id=user.id, source="Test Source")
                    db.session.add(source_perm)
                    
                    # Crear permiso específico de prueba
                    permission = UserPermission(
                        user_id=user.id,
                        permission_type="test_permission",
                        permission_value=True
                    )
                    db.session.add(permission)
                    
                    db.session.commit()
                    print("✅ Permisos de prueba creados")
                    
                    # Verificar que se crearon
                    city_count = user.city_permissions.count()
                    source_count = user.source_permissions.count()
                    perm_count = user.permissions.count()
                    
                    print(f"  🏙️  Permisos de ciudad: {city_count}")
                    print(f"  📋 Permisos de fuente: {source_count}")
                    print(f"  🔐 Permisos específicos: {perm_count}")
                    
                    # Limpiar permisos de prueba
                    db.session.delete(city_perm)
                    db.session.delete(source_perm)
                    db.session.delete(permission)
                    db.session.commit()
                    print("✅ Permisos de prueba eliminados")
                
            except Exception as e:
                print(f"❌ Error probando permisos: {e}")
                return False
            
            print("\n✅ Todos los modelos funcionan correctamente")
            return True
        
    except ImportError as e:
        print(f"❌ Error importando módulos: {e}")
        return False
    
    except Exception as e:
        print(f"❌ Error general: {e}")
        return False

def test_login():
    """Probar que el login funcione con el nuevo sistema."""
    
    try:
        from app import create_app
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            print("\n🔑 Probando sistema de login...")
            
            # Buscar usuario admin
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                print("❌ Usuario admin no encontrado")
                return False
            
            print(f"✅ Usuario admin encontrado: {admin.username}")
            print(f"  🎭 Rol: {admin.role}")
            print(f"  ✅ Activo: {admin.is_active}")
            
            # Probar verificación de contraseña
            test_password = "isaias52"  # La contraseña que usaste
            if admin.check_password(test_password):
                print(f"✅ Contraseña verificada correctamente")
            else:
                print(f"❌ Error verificando contraseña")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Error probando login: {e}")
        return False

def main():
    """Función principal."""
    print("🧪 Probador de Modelos - Sistema de Permisos")
    print("=" * 50)
    
    # Probar modelos
    if not test_models():
        print("\n❌ Error en los modelos")
        sys.exit(1)
    
    # Probar login
    if not test_login():
        print("\n❌ Error en el sistema de login")
        sys.exit(1)
    
    print("\n🎉 ¡Todos los tests pasaron exitosamente!")
    print("\n🚀 El sistema está listo para usar:")
    print("   Usuario: admin")
    print("   Contraseña: isaias52")
    print("   Rol: administrador")

if __name__ == "__main__":
    main()
