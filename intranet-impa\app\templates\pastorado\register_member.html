<!-- /app/templates/pastorado/register_member.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}
{% block title %}Registrar Miembro{% endblock %}

{% block content %}
<div class="container my-4">
  <h1>Registrar Miembro</h1>
  <form method="POST" action="">
    {{ form.hidden_tag() }}
    <div class="form-group">
      {{ render_field(form.username, class="form-control") }}
    </div>
    <!-- Separar full_name en dos campos -->
    <div class="form-group">
      {{ render_field(form.first_name, class="form-control", placeholder="Nombre") }}
    </div>
    <div class="form-group">
      {{ render_field(form.last_name, class="form-control", placeholder="Apellido") }}
    </div>
    <div class="form-group">
      {{ render_field(form.email, class="form-control", placeholder="Correo Electrónico") }}
    </div>
    <div class="form-group">
      {{ render_field(form.password, class="form-control", placeholder="Contraseña") }}
    </div>
    <div class="form-group">
      {{ render_field(form.confirm_password, class="form-control", placeholder="Confirmar Contraseña") }}
    </div>
    {{ form.submit(class="btn btn-primary") }}
  </form>
</div>
{% endblock %}