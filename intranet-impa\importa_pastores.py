#!/usr/bin/env python
# scripts/import_pastores.py
# Script para importar registros de pastores desde un CSV a la BD, actualizando si ya existen

import csv
from datetime import datetime
import os

from app import create_app, db
from app.models import User, Pastor, UserRelationship

def generate_username(first_name, last_name, used_usernames):
    """
    Genera un username único basado en la primera letra del primer nombre
    y la última palabra del apellido.
    """
    base = (first_name.strip()[0] + last_name.strip().split()[-1]).lower()
    username = base
    i = 1
    while username in used_usernames or User.query.filter_by(username=username).first() is not None:
        username = f"{base}{i}"
        i += 1
    used_usernames.add(username)
    return username

def parse_date(date_str):
    """
    Convierte la cadena de fecha (formato dd.mm.yyyy) al objeto date.
    """
    date_str = date_str.strip()
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, "%d.%m.%Y").date()
    except ValueError:
        return None

def parse_gps(gps_str):
    """
    Convierte la cadena GPS (formato "lat,lon") a dos floats.
    """
    gps_str = gps_str.strip()
    if not gps_str:
        return None, None
    try:
        lat_str, lon_str = gps_str.split(',')
        return float(lat_str), float(lon_str)
    except Exception:
        return None, None

def main():
    app = create_app()
    app.app_context().push()
    
    used_usernames = set()  # Para evitar duplicados durante la importación

    csv_file = os.path.join(os.path.dirname(__file__), "pastores.csv")
    with open(csv_file, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Datos generales del usuario
            apellido = row["Apellido"].strip()
            nombre = row["Nombre"].strip()
            dni = row["DNI"].strip()
            ciudad = row["Ciudad"].strip()
            estado_civil = row["Estado civil"].strip()
            telefono = row["Teléfono"].strip()
            email = row["Correo Electrónico"].strip()
            if not email:
                email = ""
            direccion = row["Dirección Particular"].strip()
            
            # Datos para Pastor
            matricula = row["Matrícula"].strip()
            # Normalizar el campo "Grado"
            grado = row["Grado"].strip()
            grado_map = {
                "A. Presbítero": "Presbistero",
                "B. Diácono": "Diacono",
                "C. Probando": "Probando"
            }
            grado = grado_map.get(grado, grado)
            fecha_promocion = parse_date(row["Fecha de promoción"])
            fecha_graduacion = parse_date(row["Fecha de graduación"])
            foto_pastor = row["Foto Pastor"].strip()
            if foto_pastor and len(foto_pastor) > 255:
                foto_pastor = foto_pastor[:255]
            gps_str = row["Ubicacion Gps"].strip()
            lat, lon = parse_gps(gps_str)
            
            # Generar username para el usuario principal
            username = generate_username(nombre, apellido, used_usernames)
            if not email:
                email = f"{username}@example.com"
            
            # Buscar si ya existe el usuario (por DNI, luego email, luego username)
            user = None
            if dni:
                user = User.query.filter_by(dni=dni).first()
            if not user and email:
                user = User.query.filter_by(email=email).first()
            if not user:
                user = User.query.filter_by(username=username).first()
            
            if user:
                # Actualizar datos del usuario existente
                user.first_name = nombre
                user.last_name = apellido
                user.dni = dni if dni else None
                user.ciudad = ciudad if ciudad else None
                user.estado_civil = estado_civil if estado_civil else None
                user.phone_number = telefono if telefono else None
                user.address = direccion if direccion else None
                user.email = email
                db.session.add(user)
                db.session.flush()  # Para asegurar que user.id esté disponible
            else:
                # Crear usuario nuevo
                user = User(
                    username=username,
                    password="123",  # Contraseña por defecto
                    email=email,
                    role="pastorado",
                    first_name=nombre,
                    last_name=apellido,
                    dni=dni if dni else None,
                    ciudad=ciudad if ciudad else None,
                    estado_civil=estado_civil if estado_civil else None,
                    phone_number=telefono if telefono else None,
                    address=direccion if direccion else None
                )
                db.session.add(user)
                db.session.flush()
            
            # Actualizar o crear el registro en Pastor
            pastor = Pastor.query.filter_by(user_id=user.id).first()
            if pastor:
                pastor.matricula = matricula if matricula else pastor.matricula
                pastor.grado = grado if grado else pastor.grado
                pastor.fecha_promocion = fecha_promocion if fecha_promocion else pastor.fecha_promocion
                pastor.fecha_graduacion = fecha_graduacion if fecha_graduacion else pastor.fecha_graduacion
                pastor.foto_pastor = foto_pastor if foto_pastor else pastor.foto_pastor
                pastor.latitude = lat if lat is not None else pastor.latitude
                pastor.longitude = lon if lon is not None else pastor.longitude
                pastor.address = direccion if direccion else pastor.address
                db.session.add(pastor)
            else:
                pastor = Pastor(
                    user_id=user.id,
                    matricula=matricula if matricula else None,
                    grado=grado if grado else None,
                    fecha_promocion=fecha_promocion,
                    fecha_graduacion=fecha_graduacion,
                    foto_pastor=foto_pastor if foto_pastor else None,
                    latitude=lat,
                    longitude=lon,
                    address=direccion if direccion else None
                )
                db.session.add(pastor)
            
            # Procesar datos de la pareja: usar "Pastora" si tiene datos, de lo contrario "Esposa"
            rel_data = ""
            pastora_val = row.get("Pastora", "").strip()
            esposa_val = row.get("Esposa", "").strip()
            if pastora_val:
                rel_data = pastora_val
            elif esposa_val:
                rel_data = esposa_val

            if rel_data:
                # Se asume formato "Apellido Nombre(s)"
                tokens = rel_data.split()
                if tokens:
                    if len(tokens) >= 2:
                        rel_apellido = tokens[0]
                        rel_nombre = " ".join(tokens[1:])
                    else:
                        rel_apellido = tokens[0]
                        rel_nombre = ""
                    
                    rel_username = generate_username(rel_nombre if rel_nombre else rel_apellido, rel_apellido, used_usernames)
                    rel_email = f"{rel_username}@example.com"
                    rel_user = User.query.filter_by(email=rel_email).first()
                    if not rel_user:
                        rel_user = User(
                            username=rel_username,
                            password="123",
                            email=rel_email,
                            role="pastorado",
                            first_name=rel_nombre if rel_nombre else rel_apellido,
                            last_name=rel_apellido,
                            dni=None,
                            ciudad=None,
                            estado_civil=None,
                            phone_number=None,
                            address=None
                        )
                        db.session.add(rel_user)
                        db.session.flush()
                    else:
                        rel_user.first_name = rel_nombre if rel_nombre else rel_apellido
                        rel_user.last_name = rel_apellido
                        db.session.add(rel_user)
                        db.session.flush()
                    
                    rel_pastor = Pastor.query.filter_by(user_id=rel_user.id).first()
                    if not rel_pastor:
                        rel_pastor = Pastor(user_id=rel_user.id)
                        db.session.add(rel_pastor)
                    
                    # Crear la relación si no existe
                    existing_relation = UserRelationship.query.filter_by(user_id_1=user.id, user_id_2=rel_user.id).first()
                    if not existing_relation:
                        relacion = UserRelationship(
                            user_id_1=user.id,
                            user_id_2=rel_user.id,
                            tipo_de_relacion="esposa"
                        )
                        db.session.add(relacion)
        db.session.commit()
    print("Importación finalizada.")

if __name__ == "__main__":
    main()
