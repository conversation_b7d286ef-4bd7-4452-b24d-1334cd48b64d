# quick_start_example.py
# Ejemplo práctico de implementación rápida de mejoras críticas

"""
EJEMPLO DE IMPLEMENTACIÓN RÁPIDA - MEJORAS CRÍTICAS
==================================================

Este archivo muestra cómo implementar las mejoras más críticas
de seguridad y rendimiento en tu aplicación Intranet IMPA.

PASOS PARA IMPLEMENTAR:
1. Instalar dependencias nuevas
2. Actualizar config.py
3. Modificar app/__init__.py
4. Actualizar una ruta de ejemplo
5. Crear script de inicialización

TIEMPO ESTIMADO: 2-3 horas
"""

# ============================================================================
# 1. DEPENDENCIAS NUEVAS (añadir a requirements.txt)
# ============================================================================

NEW_REQUIREMENTS = """
# Añadir estas líneas a requirements.txt:
Flask-Limiter==3.3.1
Flask-Caching==2.0.2
redis==4.5.4
python-magic==0.4.27
"""

# ============================================================================
# 2. CONFIGURACIÓN MEJORADA (actualizar config.py)
# ============================================================================

# Reemplazar el contenido de config.py con esto:
IMPROVED_CONFIG = """
# config.py - Versión mejorada con seguridad
import os
import secrets
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()

# Obtener la ruta base del directorio actual
BASEDIR = os.path.abspath(os.path.dirname(__file__))

class Config:
    # SEGURIDAD MEJORADA
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    
    # Configuración de sesiones seguras
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    SESSION_COOKIE_SECURE = os.environ.get('FLASK_ENV') == 'production'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # Base de datos (mantener configuración existente)
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{os.environ.get('DB_USER')}:{os.environ.get('DB_PASSWORD')}@"
        f"{os.environ.get('DB_HOST')}/{os.environ.get('DB_NAME')}"
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Pool de conexiones optimizado
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'max_overflow': 20,
    }
    
    # CACHE CON REDIS
    CACHE_TYPE = "redis"
    CACHE_REDIS_URL = os.environ.get('REDIS_URL', "redis://localhost:6379/0")
    CACHE_DEFAULT_TIMEOUT = 300
    
    # RATE LIMITING
    RATELIMIT_STORAGE_URL = os.environ.get('REDIS_URL', "redis://localhost:6379/1")
    RATELIMIT_DEFAULT = "200 per day, 50 per hour"
    
    # ARCHIVOS (mantener configuración existente pero mejorada)
    UPLOAD_FOLDER = os.path.join(BASEDIR, 'app', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'docx'}
    
    # LOGGING
    LOG_FOLDER = os.path.join(BASEDIR, 'logs')
    
    # Crear directorios si no existen
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    os.makedirs(LOG_FOLDER, exist_ok=True)
"""

# ============================================================================
# 3. INICIALIZACIÓN MEJORADA (actualizar app/__init__.py)
# ============================================================================

IMPROVED_INIT = """
# app/__init__.py - Versión mejorada
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_bcrypt import Bcrypt
from flask_migrate import Migrate
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_caching import Cache
from config import Config
import datetime
import pytz
import logging
from logging.handlers import RotatingFileHandler
import os

# Crear instancias de extensiones
db = SQLAlchemy()
login_manager = LoginManager()
bcrypt = Bcrypt()
migrate = Migrate()
limiter = Limiter(key_func=get_remote_address)
cache = Cache()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Inicializar extensiones
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'routes.login'
    login_manager.login_message_category = 'info'
    bcrypt.init_app(app)
    migrate.init_app(app, db)
    
    # NUEVAS EXTENSIONES
    limiter.init_app(app)
    cache.init_app(app)
    
    # Configurar logging mejorado
    if not app.debug and not app.testing:
        # Log general
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = RotatingFileHandler('logs/intranet.log', 
                                         maxBytes=10240000, backupCount=10)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Intranet IMPA startup')
    
    # Headers de seguridad
    @app.after_request
    def security_headers(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        if app.config.get('SESSION_COOKIE_SECURE'):
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        return response
    
    # Registrar filtros Jinja (mantener existentes)
    from app.utils import utc_to_buenos_aires, calculate_age
    app.jinja_env.filters['to_local'] = utc_to_buenos_aires
    app.jinja_env.filters['calculate_age'] = calculate_age
    
    # Registrar blueprints
    from app.routes import routes_bp
    app.register_blueprint(routes_bp)
    
    return app
"""

# ============================================================================
# 4. EJEMPLO DE RUTA MEJORADA (actualizar en routes.py)
# ============================================================================

IMPROVED_ROUTE_EXAMPLE = """
# Ejemplo de cómo mejorar una ruta existente en routes.py

from flask import current_app
from app import limiter, cache
import magic
import os
from werkzeug.utils import secure_filename

# EJEMPLO 1: Login con rate limiting
@routes_bp.route('/login', methods=['GET', 'POST'])
@limiter.limit("5 per minute")  # NUEVO: Rate limiting
def login():
    if current_user.is_authenticated:
        return redirect(url_for('routes.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user and bcrypt.check_password_hash(user.password, form.password.data) and user.is_active:
            login_user(user, remember=form.remember.data)
            
            # NUEVO: Log de seguridad
            current_app.logger.info(f'Login successful: {user.username} from {request.remote_addr}')
            
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('routes.dashboard'))
        else:
            # NUEVO: Log de intento fallido
            current_app.logger.warning(f'Login failed: {form.username.data} from {request.remote_addr}')
            flash('Usuario o contraseña incorrectos', 'danger')
    
    return render_template('login.html', title='Iniciar Sesión', form=form)

# EJEMPLO 2: Lista de iglesias con cache
@routes_bp.route('/churches')
@login_required
@cache.cached(timeout=300, key_prefix='churches_list')  # NUEVO: Cache por 5 minutos
def list_churches():
    if current_user.role not in ['administrador', 'secretaria']:
        flash('No tienes permiso para ver esta página.', 'danger')
        return redirect(url_for('routes.dashboard'))
    
    # NUEVO: Paginación mejorada
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    churches = Church.query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('admin/list_churches.html', churches=churches)

# EJEMPLO 3: Upload de archivos con validación mejorada
@routes_bp.route('/upload_document', methods=['POST'])
@login_required
@limiter.limit("10 per minute")  # NUEVO: Rate limiting para uploads
def upload_document():
    if current_user.role not in ['administrador', 'secretaria']:
        flash('No tienes permiso para subir documentos.', 'danger')
        return redirect(url_for('routes.dashboard'))
    
    file = request.files.get('file')
    if not file or not file.filename:
        flash('No se seleccionó ningún archivo.', 'danger')
        return redirect(request.url)
    
    # NUEVO: Validación de archivo mejorada
    if not validate_file_secure(file):
        current_app.logger.warning(f'Invalid file upload attempt by {current_user.username}: {file.filename}')
        flash('Archivo no válido o tipo no permitido.', 'danger')
        return redirect(request.url)
    
    # NUEVO: Nombre de archivo seguro
    filename = secure_filename(file.filename)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
    safe_filename = timestamp + filename
    
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], safe_filename)
    file.save(file_path)
    
    # NUEVO: Log de upload exitoso
    current_app.logger.info(f'File uploaded by {current_user.username}: {safe_filename}')
    
    # Resto de la lógica para guardar en base de datos...
    flash('Documento subido exitosamente.', 'success')
    return redirect(url_for('routes.documents'))

def validate_file_secure(file):
    \"\"\"Validación segura de archivos\"\"\"
    # Verificar extensión
    allowed_extensions = current_app.config['ALLOWED_EXTENSIONS']
    if not any(file.filename.lower().endswith('.' + ext) for ext in allowed_extensions):
        return False
    
    # Verificar tipo MIME real
    file_content = file.read(1024)  # Leer solo los primeros 1024 bytes
    file.seek(0)  # Resetear puntero
    
    try:
        mime_type = magic.from_buffer(file_content, mime=True)
        allowed_mimes = {
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'text/plain'
        }
        if mime_type not in allowed_mimes:
            return False
    except:
        return False
    
    # Verificar tamaño
    file.seek(0, 2)  # Ir al final del archivo
    size = file.tell()
    file.seek(0)  # Resetear puntero
    
    if size > current_app.config['MAX_CONTENT_LENGTH']:
        return False
    
    return True
"""

# ============================================================================
# 5. SCRIPT DE INICIALIZACIÓN (crear como init_improvements.py)
# ============================================================================

INIT_SCRIPT = """
#!/usr/bin/env python3
# init_improvements.py - Script para inicializar mejoras

import os
import secrets
from app import create_app, db
from sqlalchemy import text

def generate_env_file():
    \"\"\"Generar archivo .env con configuración segura\"\"\"
    env_content = f'''# Configuración de seguridad
SECRET_KEY={secrets.token_hex(32)}
FLASK_ENV=development

# Base de datos (actualizar con tus valores)
DB_USER=tu_usuario_mysql
DB_PASSWORD=tu_password_mysql
DB_HOST=localhost
DB_NAME=intranet_impa

# Cache y Rate Limiting
REDIS_URL=redis://localhost:6379/0

# Configuración de archivos
MAX_CONTENT_LENGTH=16777216
'''
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ Archivo .env creado con configuración segura")
    print("⚠️  IMPORTANTE: Actualiza las credenciales de base de datos en .env")

def create_database_indexes():
    \"\"\"Crear índices para mejorar rendimiento\"\"\"
    app = create_app()
    
    with app.app_context():
        try:
            # Índices más importantes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
                "CREATE INDEX IF NOT EXISTS idx_users_church_id ON users(church_id)",
                "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
                "CREATE INDEX IF NOT EXISTS idx_members_church_id ON members(church_id)",
                "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)",
            ]
            
            for index_sql in indexes:
                db.session.execute(text(index_sql))
            
            db.session.commit()
            print("✅ Índices de base de datos creados")
            
        except Exception as e:
            print(f"❌ Error creando índices: {e}")

def create_directories():
    \"\"\"Crear directorios necesarios\"\"\"
    directories = ['logs', 'app/uploads', 'mejoras']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Directorio creado: {directory}")

def main():
    print("🚀 Inicializando mejoras para Intranet IMPA...")
    print()
    
    # 1. Crear directorios
    create_directories()
    
    # 2. Generar archivo .env
    if not os.path.exists('.env'):
        generate_env_file()
    else:
        print("⚠️  Archivo .env ya existe, no se sobrescribirá")
    
    # 3. Crear índices de base de datos
    create_database_indexes()
    
    print()
    print("✅ Inicialización completada!")
    print()
    print("📋 PRÓXIMOS PASOS:")
    print("1. Instalar dependencias: pip install Flask-Limiter Flask-Caching redis python-magic")
    print("2. Instalar Redis: sudo apt-get install redis-server (Linux) o usar Docker")
    print("3. Actualizar credenciales en archivo .env")
    print("4. Aplicar cambios en config.py y app/__init__.py")
    print("5. Reiniciar la aplicación")

if __name__ == '__main__':
    main()
"""

# ============================================================================
# INSTRUCCIONES DE USO
# ============================================================================

USAGE_INSTRUCTIONS = """
INSTRUCCIONES DE USO - IMPLEMENTACIÓN RÁPIDA
============================================

1. PREPARACIÓN:
   python mejoras/quick_start_example.py  # Ejecutar este script para ver las instrucciones
   
2. INSTALAR DEPENDENCIAS:
   pip install Flask-Limiter Flask-Caching redis python-magic
   
3. INSTALAR REDIS:
   # Ubuntu/Debian:
   sudo apt-get install redis-server
   
   # Windows (Docker):
   docker run -d -p 6379:6379 redis:alpine
   
   # macOS:
   brew install redis

4. CREAR SCRIPT DE INICIALIZACIÓN:
   # Copiar el contenido de INIT_SCRIPT a un archivo llamado init_improvements.py
   python init_improvements.py

5. ACTUALIZAR ARCHIVOS:
   # Reemplazar contenido de config.py con IMPROVED_CONFIG
   # Reemplazar contenido de app/__init__.py con IMPROVED_INIT
   # Añadir ejemplos de rutas mejoradas a routes.py

6. REINICIAR APLICACIÓN:
   flask run

7. VERIFICAR FUNCIONAMIENTO:
   # Verificar que Redis esté funcionando:
   redis-cli ping  # Debería responder "PONG"
   
   # Verificar logs:
   tail -f logs/intranet.log

TIEMPO ESTIMADO: 2-3 horas
IMPACTO: Mejora significativa en seguridad y rendimiento
"""

if __name__ == '__main__':
    print(USAGE_INSTRUCTIONS)
