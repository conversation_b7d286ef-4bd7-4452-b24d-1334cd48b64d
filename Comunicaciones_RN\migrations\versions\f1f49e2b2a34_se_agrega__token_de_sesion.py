"""se agrega _token de sesion

Revision ID: f1f49e2b2a34
Revises: 2976d5302661
Create Date: 2024-07-24 19:51:12.091031

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f1f49e2b2a34'
down_revision = '2976d5302661'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('session_token', sa.String(length=150), nullable=True))
        batch_op.create_unique_constraint('uq_user_session_token', ['session_token'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_constraint('uq_user_session_token', type_='unique')
        batch_op.drop_column('session_token')
    # ### end Alembic commands ###
