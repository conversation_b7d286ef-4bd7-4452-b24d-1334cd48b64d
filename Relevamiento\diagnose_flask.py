#!/usr/bin/env python3
# --- Archivo: diagnose_flask.py ---
# Script para diagnosticar problemas de Flask

import os
import sys
import subprocess

def check_flask_app_status():
    """Verificar el estado de la aplicación Flask."""
    print("🔍 Diagnosticando aplicación Flask...")
    
    try:
        # Verificar proceso
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        flask_processes = [line for line in result.stdout.split('\n') if 'run.py' in line]
        
        print(f"📊 Procesos Flask encontrados: {len(flask_processes)}")
        for process in flask_processes:
            print(f"  {process}")
        
        # Verificar puerto
        result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
        port_5006 = [line for line in result.stdout.split('\n') if ':5006' in line]
        
        print(f"🔌 Puerto 5006 en uso: {len(port_5006) > 0}")
        for port in port_5006:
            print(f"  {port}")
        
        return len(flask_processes) > 0
        
    except Exception as e:
        print(f"❌ Error verificando estado: {e}")
        return False

def test_direct_import():
    """Probar importación directa de la aplicación."""
    print("🧪 Probando importación directa...")
    
    try:
        # Limpiar módulos
        modules_to_remove = []
        for module_name in sys.modules:
            if module_name.startswith('app'):
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            del sys.modules[module_name]
        
        # Importar aplicación
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app
        app = create_app()
        
        print(f"✅ Aplicación creada: {app}")
        print(f"📋 Blueprints registrados: {list(app.blueprints.keys())}")
        
        # Verificar login manager
        if hasattr(app, 'login_manager'):
            print(f"✅ Login manager presente: {app.login_manager}")
            
            if hasattr(app.login_manager, '_user_callback'):
                if app.login_manager._user_callback:
                    print("✅ User callback registrado")
                else:
                    print("❌ User callback NO registrado")
            else:
                print("❌ User callback no existe")
        else:
            print("❌ Login manager NO presente")
        
        # Probar contexto de aplicación
        with app.app_context():
            from app.models import User
            users = User.query.all()
            print(f"✅ Base de datos accesible: {len(users)} usuarios")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en importación directa: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_config():
    """Verificar configuración."""
    print("⚙️  Verificando configuración...")
    
    try:
        from config import Config
        config = Config()
        
        print(f"📄 Base de datos: {config.SQLALCHEMY_DATABASE_URI}")
        print(f"🔐 Secret key configurada: {'SECRET_KEY' in dir(config)}")
        print(f"📁 Upload folder: {config.UPLOAD_FOLDER}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verificando configuración: {e}")
        return False

def check_database():
    """Verificar base de datos."""
    print("🗃️  Verificando base de datos...")
    
    try:
        import sqlite3
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Verificar tabla user
        cursor.execute("SELECT COUNT(*) FROM user;")
        user_count = cursor.fetchone()[0]
        print(f"👥 Usuarios en DB: {user_count}")
        
        # Verificar estructura
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f"📋 Columnas user: {column_names}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verificando base de datos: {e}")
        return False

def check_logs():
    """Verificar logs recientes."""
    print("📝 Verificando logs recientes...")
    
    try:
        result = subprocess.run(['journalctl', '-u', 'relevamiento', '--no-pager', '-n', '10'], 
                              capture_output=True, text=True)
        
        print("📋 Últimos 10 logs:")
        for line in result.stdout.split('\n')[-10:]:
            if line.strip():
                print(f"  {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verificando logs: {e}")
        return False

def main():
    """Función principal."""
    print("🔍 Diagnóstico Completo de Flask")
    print("=" * 40)
    
    # Verificar estado de la aplicación
    app_running = check_flask_app_status()
    print()
    
    # Verificar configuración
    check_config()
    print()
    
    # Verificar base de datos
    check_database()
    print()
    
    # Verificar importación directa
    import_ok = test_direct_import()
    print()
    
    # Verificar logs
    check_logs()
    print()
    
    # Resumen
    print("📊 RESUMEN:")
    print(f"  🚀 Aplicación corriendo: {'✅' if app_running else '❌'}")
    print(f"  📦 Importación OK: {'✅' if import_ok else '❌'}")
    
    if app_running and import_ok:
        print("\n🎉 La aplicación parece estar funcionando")
        print("💡 Si hay problemas de login, verifica:")
        print("   - Que el user_loader esté registrado")
        print("   - Que no haya errores en los logs")
    else:
        print("\n❌ Hay problemas con la aplicación")
        print("💡 Revisa los errores mostrados arriba")

if __name__ == "__main__":
    main()
