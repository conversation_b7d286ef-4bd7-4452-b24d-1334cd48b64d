# sistema_academico_pastoral.py
# Sistema Académico Pastoral para Intranet IMPA

"""
SISTEMA ACADÉMICO PASTORAL - NIVEL CORPORATIVO
==============================================

Extensión del sistema académico para incluir:
- Formación pastoral a nivel corporativo
- Nuevo rol "Instituto" para gestión académica
- Escuelas pastorales centralizadas
- Pensums específicos para pastores
- Certificaciones ministeriales
- Gestión académica especializada

Esta extensión permite que los pastores también sean estudiantes
en programas de formación continua gestionados desde el nivel corporativo.
"""

from datetime import datetime, date
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Float, Date, Enum
from sqlalchemy.orm import relationship
from app import db

# ============================================================================
# 1. NUEVOS ROLES Y PERMISOS
# ============================================================================

# Añadir a la tabla users o como enum
ACADEMIC_ROLES = {
    'instituto': {
        'name': 'Instituto',
        'description': 'Gestión académica corporativa',
        'permissions': [
            'manage_pastoral_schools',
            'manage_pastoral_enrollments', 
            'assign_pastoral_grades',
            'generate_pastoral_certificates',
            'view_all_academic_reports',
            'manage_academic_calendar',
            'approve_pastoral_graduations'
        ]
    },
    'rector': {
        'name': 'Rector',
        'description': 'Director académico corporativo',
        'permissions': [
            'all_instituto_permissions',
            'create_pastoral_programs',
            'approve_curriculum_changes',
            'manage_academic_staff'
        ]
    },
    'profesor_corporativo': {
        'name': 'Profesor Corporativo',
        'description': 'Docente de programas pastorales',
        'permissions': [
            'teach_pastoral_subjects',
            'assign_grades_pastoral',
            'view_student_progress'
        ]
    }
}

# ============================================================================
# 2. MODELOS ESPECÍFICOS PARA FORMACIÓN PASTORAL
# ============================================================================

class PastoralInstitute(db.Model):
    """Instituto de formación pastoral corporativo"""
    __tablename__ = 'pastoral_institutes'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    
    # Gestión académica
    rector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    academic_coordinator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # Configuración
    is_active = db.Column(db.Boolean, default=True)
    accreditation_info = db.Column(db.Text)  # Información de acreditación
    academic_calendar = db.Column(db.JSON)  # Calendario académico
    
    # Metadatos
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    rector = db.relationship('User', foreign_keys=[rector_id], backref='rectored_institutes')
    coordinator = db.relationship('User', foreign_keys=[academic_coordinator_id], backref='coordinated_institutes')
    pastoral_programs = db.relationship('PastoralProgram', backref='institute', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<PastoralInstitute(name='{self.name}')>"

class PastoralProgram(db.Model):
    """Programas de formación pastoral"""
    __tablename__ = 'pastoral_programs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    institute_id = db.Column(db.Integer, db.ForeignKey('pastoral_institutes.id'), nullable=False)
    
    # Configuración académica
    program_type = db.Column(db.Enum('diploma', 'certificate', 'specialization', 'masters'), default='certificate')
    duration_months = db.Column(db.Integer, nullable=False)
    total_credits = db.Column(db.Integer, default=0)
    min_grade_to_pass = db.Column(db.Float, default=3.0)
    
    # Requisitos de ingreso
    entry_requirements = db.Column(db.JSON)  # Requisitos específicos
    target_roles = db.Column(db.JSON)  # Roles objetivo: ['pastorado', 'superintendente', etc.]
    min_ministry_years = db.Column(db.Integer, default=0)  # Años mínimos en ministerio
    
    # Estado y fechas
    is_active = db.Column(db.Boolean, default=True)
    enrollment_start = db.Column(db.Date)
    enrollment_end = db.Column(db.Date)
    program_start = db.Column(db.Date)
    program_end = db.Column(db.Date)
    
    # Configuración
    max_students = db.Column(db.Integer, default=30)
    requires_approval = db.Column(db.Boolean, default=True)  # Requiere aprobación para matrícula
    
    # Metadatos
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    created_by = db.relationship('User', backref='created_pastoral_programs')
    subjects = db.relationship('PastoralSubject', backref='program', cascade='all, delete-orphan')
    enrollments = db.relationship('PastoralEnrollment', backref='program', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<PastoralProgram(name='{self.name}', type='{self.program_type}')>"

class PastoralSubject(db.Model):
    """Materias de programas pastorales"""
    __tablename__ = 'pastoral_subjects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(20), nullable=False)
    description = db.Column(db.Text)
    program_id = db.Column(db.Integer, db.ForeignKey('pastoral_programs.id'), nullable=False)
    
    # Configuración académica
    credits = db.Column(db.Integer, default=3)
    semester = db.Column(db.Integer, default=1)
    prerequisites = db.Column(db.JSON)  # IDs de materias prerequisito
    is_mandatory = db.Column(db.Boolean, default=True)
    
    # Docencia
    professor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    teaching_hours = db.Column(db.Integer, default=40)
    practical_hours = db.Column(db.Integer, default=0)
    
    # Contenido
    syllabus = db.Column(db.Text)  # Programa de la materia
    learning_objectives = db.Column(db.JSON)  # Objetivos de aprendizaje
    evaluation_methods = db.Column(db.JSON)  # Métodos de evaluación
    bibliography = db.Column(db.Text)  # Bibliografía
    
    # Estado
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    professor = db.relationship('User', backref='taught_pastoral_subjects')
    grades = db.relationship('PastoralGrade', backref='subject', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<PastoralSubject(name='{self.name}', code='{self.code}')>"

class PastoralEnrollment(db.Model):
    """Matrículas en programas pastorales"""
    __tablename__ = 'pastoral_enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # Pastor estudiante
    program_id = db.Column(db.Integer, db.ForeignKey('pastoral_programs.id'), nullable=False)
    
    # Estado de matrícula
    enrollment_date = db.Column(db.Date, default=date.today)
    status = db.Column(db.Enum('pending', 'approved', 'active', 'completed', 'dropped', 'suspended'), default='pending')
    
    # Aprobación
    approved_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    approval_date = db.Column(db.Date, nullable=True)
    approval_notes = db.Column(db.Text)
    
    # Progreso académico
    current_semester = db.Column(db.Integer, default=1)
    credits_completed = db.Column(db.Integer, default=0)
    cumulative_gpa = db.Column(db.Float, default=0.0)
    
    # Finalización
    completion_date = db.Column(db.Date, nullable=True)
    final_gpa = db.Column(db.Float, nullable=True)
    graduation_date = db.Column(db.Date, nullable=True)
    
    # Certificación
    certificate_type = db.Column(db.String(100))  # Tipo de certificado otorgado
    certificate_issued = db.Column(db.Boolean, default=False)
    certificate_number = db.Column(db.String(50), unique=True)
    certificate_date = db.Column(db.Date, nullable=True)
    
    # Observaciones
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    student = db.relationship('User', foreign_keys=[student_id], backref='pastoral_enrollments')
    approved_by = db.relationship('User', foreign_keys=[approved_by_id], backref='approved_pastoral_enrollments')
    grades = db.relationship('PastoralGrade', backref='enrollment', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<PastoralEnrollment(student='{self.student.first_name}', program='{self.program.name}')>"

class PastoralGrade(db.Model):
    """Calificaciones en programas pastorales"""
    __tablename__ = 'pastoral_grades'
    
    id = db.Column(db.Integer, primary_key=True)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('pastoral_enrollments.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('pastoral_subjects.id'), nullable=False)
    
    # Calificación
    grade_value = db.Column(db.Float, nullable=False)
    grade_type = db.Column(db.Enum('assignment', 'exam', 'project', 'final'), default='assignment')
    weight = db.Column(db.Float, default=1.0)  # Peso de la calificación
    
    # Evaluación
    evaluation_date = db.Column(db.Date, default=date.today)
    professor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    evaluation_method = db.Column(db.String(100))  # Método de evaluación usado
    
    # Detalles
    comments = db.Column(db.Text)
    feedback = db.Column(db.Text)  # Retroalimentación detallada
    improvement_areas = db.Column(db.Text)  # Áreas de mejora
    
    # Estado
    is_final = db.Column(db.Boolean, default=False)
    requires_remediation = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    professor = db.relationship('User', backref='assigned_pastoral_grades')
    
    def __repr__(self):
        return f"<PastoralGrade(grade={self.grade_value}, subject='{self.subject.name}')>"

class PastoralCertificate(db.Model):
    """Certificados pastorales emitidos"""
    __tablename__ = 'pastoral_certificates'
    
    id = db.Column(db.Integer, primary_key=True)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('pastoral_enrollments.id'), nullable=False)
    certificate_number = db.Column(db.String(50), unique=True, nullable=False)
    
    # Información del certificado
    certificate_type = db.Column(db.String(100), nullable=False)
    title_conferred = db.Column(db.String(255))  # Título otorgado
    specialization = db.Column(db.String(255))  # Especialización si aplica
    
    # Fechas
    issue_date = db.Column(db.Date, default=date.today)
    valid_from = db.Column(db.Date, default=date.today)
    valid_until = db.Column(db.Date, nullable=True)  # Para certificaciones que expiran
    
    # Autoridades
    issued_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    signed_by_rector = db.Column(db.Boolean, default=False)
    signed_by_coordinator = db.Column(db.Boolean, default=False)
    
    # Archivo
    certificate_file_path = db.Column(db.String(500))  # Ruta del PDF generado
    
    # Estado
    is_active = db.Column(db.Boolean, default=True)
    revocation_reason = db.Column(db.Text, nullable=True)
    revoked_date = db.Column(db.Date, nullable=True)
    
    # Metadatos
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relaciones
    enrollment = db.relationship('PastoralEnrollment', backref='certificates')
    issued_by = db.relationship('User', backref='issued_pastoral_certificates')
    
    def __repr__(self):
        return f"<PastoralCertificate(number='{self.certificate_number}', type='{self.certificate_type}')>"

# ============================================================================
# 3. SERVICIOS PARA GESTIÓN PASTORAL ACADÉMICA
# ============================================================================

class PastoralAcademicService:
    """Servicio para gestión académica pastoral"""
    
    @staticmethod
    def create_institute(name, description, rector_id=None, coordinator_id=None):
        """Crear instituto pastoral"""
        institute = PastoralInstitute(
            name=name,
            description=description,
            rector_id=rector_id,
            academic_coordinator_id=coordinator_id
        )
        db.session.add(institute)
        db.session.commit()
        return institute
    
    @staticmethod
    def create_pastoral_program(institute_id, name, program_type, duration_months, 
                              target_roles=None, min_ministry_years=0, **kwargs):
        """Crear programa pastoral"""
        program = PastoralProgram(
            institute_id=institute_id,
            name=name,
            program_type=program_type,
            duration_months=duration_months,
            target_roles=target_roles or ['pastorado'],
            min_ministry_years=min_ministry_years,
            **kwargs
        )
        db.session.add(program)
        db.session.commit()
        return program
    
    @staticmethod
    def enroll_pastor(pastor_id, program_id, enrolled_by_id):
        """Matricular pastor en programa (requiere aprobación)"""
        from app.models import User
        
        # Verificar elegibilidad
        pastor = User.query.get(pastor_id)
        program = PastoralProgram.query.get(program_id)
        
        if not pastor or pastor.role not in program.target_roles:
            raise ValueError("Pastor no elegible para este programa")
        
        # Verificar si ya está matriculado
        existing = PastoralEnrollment.query.filter_by(
            student_id=pastor_id,
            program_id=program_id,
            status__in=['pending', 'approved', 'active']
        ).first()
        
        if existing:
            raise ValueError("Pastor ya está matriculado en este programa")
        
        # Crear matrícula pendiente
        enrollment = PastoralEnrollment(
            student_id=pastor_id,
            program_id=program_id,
            status='pending' if program.requires_approval else 'approved'
        )
        
        db.session.add(enrollment)
        db.session.commit()
        
        return enrollment
    
    @staticmethod
    def approve_enrollment(enrollment_id, approved_by_id, notes=None):
        """Aprobar matrícula pastoral"""
        enrollment = PastoralEnrollment.query.get(enrollment_id)
        if not enrollment or enrollment.status != 'pending':
            return False
        
        enrollment.status = 'approved'
        enrollment.approved_by_id = approved_by_id
        enrollment.approval_date = date.today()
        enrollment.approval_notes = notes
        
        db.session.commit()
        return True
    
    @staticmethod
    def assign_pastoral_grade(enrollment_id, subject_id, grade_value, professor_id, 
                            grade_type='assignment', **kwargs):
        """Asignar calificación pastoral"""
        grade = PastoralGrade(
            enrollment_id=enrollment_id,
            subject_id=subject_id,
            grade_value=grade_value,
            professor_id=professor_id,
            grade_type=grade_type,
            **kwargs
        )
        
        db.session.add(grade)
        
        # Actualizar GPA si es calificación final
        if grade_type == 'final':
            PastoralAcademicService.update_student_gpa(enrollment_id)
        
        db.session.commit()
        return grade
    
    @staticmethod
    def update_student_gpa(enrollment_id):
        """Actualizar GPA del estudiante"""
        enrollment = PastoralEnrollment.query.get(enrollment_id)
        
        # Obtener calificaciones finales
        final_grades = PastoralGrade.query.filter_by(
            enrollment_id=enrollment_id,
            grade_type='final'
        ).all()
        
        if final_grades:
            # Calcular GPA ponderado por créditos
            total_points = 0
            total_credits = 0
            
            for grade in final_grades:
                credits = grade.subject.credits
                total_points += grade.grade_value * credits
                total_credits += credits
            
            enrollment.cumulative_gpa = total_points / total_credits if total_credits > 0 else 0
            enrollment.credits_completed = total_credits
    
    @staticmethod
    def check_graduation_eligibility(enrollment_id):
        """Verificar elegibilidad para graduación"""
        enrollment = PastoralEnrollment.query.get(enrollment_id)
        program = enrollment.program
        
        # Verificar materias completadas
        completed_subjects = PastoralGrade.query.filter_by(
            enrollment_id=enrollment_id,
            grade_type='final'
        ).filter(PastoralGrade.grade_value >= program.min_grade_to_pass).count()
        
        required_subjects = PastoralSubject.query.filter_by(
            program_id=program.id,
            is_mandatory=True
        ).count()
        
        # Verificar créditos
        credits_needed = program.total_credits
        credits_completed = enrollment.credits_completed
        
        # Verificar GPA mínimo
        min_gpa = program.min_grade_to_pass
        current_gpa = enrollment.cumulative_gpa
        
        eligible = (
            completed_subjects >= required_subjects and
            credits_completed >= credits_needed and
            current_gpa >= min_gpa
        )
        
        return {
            'eligible': eligible,
            'completed_subjects': completed_subjects,
            'required_subjects': required_subjects,
            'credits_completed': credits_completed,
            'credits_needed': credits_needed,
            'current_gpa': current_gpa,
            'min_gpa': min_gpa
        }
    
    @staticmethod
    def graduate_pastor(enrollment_id, graduated_by_id, certificate_type=None):
        """Graduar pastor y generar certificado"""
        enrollment = PastoralEnrollment.query.get(enrollment_id)
        
        # Verificar elegibilidad
        eligibility = PastoralAcademicService.check_graduation_eligibility(enrollment_id)
        if not eligibility['eligible']:
            raise ValueError("Pastor no cumple requisitos para graduación")
        
        # Actualizar estado
        enrollment.status = 'completed'
        enrollment.completion_date = date.today()
        enrollment.final_gpa = enrollment.cumulative_gpa
        enrollment.graduation_date = date.today()
        
        # Generar certificado
        certificate_number = PastoralAcademicService.generate_certificate_number()
        
        certificate = PastoralCertificate(
            enrollment_id=enrollment_id,
            certificate_number=certificate_number,
            certificate_type=certificate_type or enrollment.program.program_type,
            title_conferred=f"{enrollment.program.name}",
            issued_by_id=graduated_by_id
        )
        
        db.session.add(certificate)
        enrollment.certificate_issued = True
        enrollment.certificate_number = certificate_number
        enrollment.certificate_date = date.today()
        
        db.session.commit()
        
        # Generar PDF del certificado
        PastoralAcademicService.generate_certificate_pdf(certificate.id)
        
        return certificate
    
    @staticmethod
    def generate_certificate_number():
        """Generar número único de certificado"""
        import secrets
        year = date.today().year
        random_part = secrets.token_hex(4).upper()
        return f"IMPA-{year}-{random_part}"
    
    @staticmethod
    def generate_certificate_pdf(certificate_id):
        """Generar PDF del certificado pastoral"""
        certificate = PastoralCertificate.query.get(certificate_id)
        
        # Aquí se integraría con el sistema de generación de PDFs existente
        # Similar al sistema de credenciales pero para certificados pastorales
        
        certificate_data = {
            'certificate_number': certificate.certificate_number,
            'student_name': f"{certificate.enrollment.student.first_name} {certificate.enrollment.student.last_name}",
            'program_name': certificate.enrollment.program.name,
            'institute_name': certificate.enrollment.program.institute.name,
            'completion_date': certificate.enrollment.completion_date,
            'final_gpa': certificate.enrollment.final_gpa,
            'certificate_type': certificate.certificate_type,
            'issue_date': certificate.issue_date
        }
        
        # Generar PDF usando sistema existente
        # pdf_path = generate_pastoral_certificate_pdf(certificate_data)
        # certificate.certificate_file_path = pdf_path
        
        db.session.commit()
        return certificate

print("Sistema Académico Pastoral - Listo para implementar")
print("Nuevas funcionalidades:")
print("- Instituto pastoral corporativo")
print("- Programas de formación para pastores")
print("- Nuevo rol 'Instituto' para gestión académica")
print("- Certificaciones ministeriales")
print("- Gestión de matrículas pastorales")
