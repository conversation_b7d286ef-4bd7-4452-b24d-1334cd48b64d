# --- Archivo: app/routes.py ---

import os
import json
from datetime import datetime
from flask import ( Blueprint, render_template, current_app,
                    jsonify, request, flash, redirect, url_for)
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename # Necesario para upload_dbf
from sqlalchemy import and_, or_

# Asume que ahora se llama importer.py, si no, cambia a dbf_importer
# Y que la función se llama import_points_from_dbf
try:
    from app.importer import import_points_from_dbf
except ImportError:
    # Intenta con el nombre antiguo si falla
    from app.dbf_importer import import_points_from_dbf

from app.models import Point, Camera, Image
from app.forms import ReportsFilterForm
from app import db

# Definir el Blueprint UNA SOLA VEZ
bp = Blueprint('main', __name__)

@bp.route('/')
@bp.route('/index')
@login_required
def index():
    """Muestra el mapa principal con todos los puntos."""
    points_data = [] # Siempre empieza como lista vacía
    map_center = [-40.086, -67.738] # Centro aproximado Patagonia/RN
    map_zoom = 6

    try:
        points = Point.query.order_by(Point.name).all()
        # Crear la lista de diccionarios desde el modelo incluyendo cámaras
        points_data = [p.to_dict(include_cameras=True) for p in points]
        current_app.logger.info(f"Cargando mapa principal para usuario {current_user.username} con {len(points_data)} puntos.")
    except Exception as e:
        current_app.logger.error(f"Error al obtener puntos para el mapa: {e}")
        points_data = [] # Asegurar lista vacía en caso de error

    # Calcular centro y zoom basado en la lista (sin cambios)
    if points_data:
       # Filtrar puntos sin coordenadas válidas por si acaso
       valid_points = [p for p in points_data if isinstance(p.get('lat'), (int, float)) and isinstance(p.get('lon'), (int, float))]
       if valid_points:
           lats = [p['lat'] for p in valid_points]
           lons = [p['lon'] for p in valid_points]
           # Calcular centroide
           map_center = [sum(lats) / len(lats), sum(lons) / len(lons)]
           # Calcular zoom (simplificado)
           if len(valid_points) > 1:
               lat_range = max(lats) - min(lats)
               lon_range = max(lons) - min(lons)
               if lat_range < 0.5 and lon_range < 0.5: map_zoom = 13
               elif lat_range < 1 and lon_range < 1: map_zoom = 12
               elif lat_range < 5 and lon_range < 5: map_zoom = 8
               else: map_zoom = 6
           else:
               map_zoom = 14 # Zoom más cercano si hay un solo punto

    return render_template('index.html',
                           title='Mapa de Puntos',
                           # Pasa la lista Python directamente
                           points_data=points_data,
                           map_center=map_center,
                           map_zoom=map_zoom)

# Opcional: Un endpoint API para obtener los puntos como JSON (útil para JS)
@bp.route('/api/points')
@login_required
def get_points_api():
    """Devuelve todos los puntos en formato JSON."""
    include_cameras = request.args.get('include_cameras', 'false').lower() == 'true'
    try:
        points = Point.query.all()
        points_data = [p.to_dict(include_cameras=include_cameras) for p in points]
        return jsonify(points_data)
    except Exception as e:
        current_app.logger.error(f"Error en API /api/points: {e}")
        return jsonify(error="Error al obtener puntos"), 500

# --- Rutas Adicionales que tenías ---

# Nota: Esta ruta usa una ruta DBF hardcodeada y podría causar
# los mismos problemas de importación que tenías antes si se llama
# desde __init__.py o similar. Úsala con precaución.
@bp.route('/importar_dbf', methods=['POST'])
@login_required
def importar_dbf_manual():
    # Añadir verificación de permisos más robusta si es necesario
    if not current_user.is_authenticated: # Simplificado, ajusta si tienes roles
        flash("No tienes permisos para esta acción.", "danger")
        return redirect(url_for('main.index'))

    # Considera hacer esta ruta configurable o quitarla si no se usa
    dbf_path = os.path.abspath(os.path.join(current_app.root_path, '..', 'db', 'puntos.dbf'))
    current_app.logger.info(f"Intentando importación manual desde: {dbf_path}")
    if os.path.exists(dbf_path):
        try:
            # Llama a la función importadora (asegúrate que el nombre sea correcto)
            import_points_from_dbf(dbf_path)
            flash("Puntos importados correctamente desde el DBF predeterminado.", "success")
        except Exception as e:
             current_app.logger.error(f"Error durante importación manual de DBF: {e}")
             flash(f"Error durante la importación: {e}", "danger")
    else:
        flash(f"No se encontró el archivo DBF predeterminado: {dbf_path}", "warning")

    return redirect(url_for('main.index'))

# Ruta para subir un DBF y luego importarlo
@bp.route('/upload_dbf', methods=['POST'])
@login_required
def upload_dbf():
    # Añadir verificación de permisos
    if not current_user.is_authenticated: # Ajusta según tus roles/permisos
        flash("No tienes permisos para esta acción.", "danger")
        return redirect(url_for('main.index'))

    if 'dbf_file' not in request.files:
        flash("No se encontró el archivo en la solicitud.", "warning")
        return redirect(request.referrer or url_for('main.index')) # Volver a la pág anterior

    dbf_file = request.files['dbf_file']

    if dbf_file.filename == '':
        flash("No se seleccionó ningún archivo.", "warning")
        return redirect(request.referrer or url_for('main.index'))

    if dbf_file and dbf_file.filename.lower().endswith('.dbf'):
        filename = secure_filename(dbf_file.filename)
        # Guardar en una ubicación temporal o de subidas designada
        # Usar UPLOAD_FOLDER de la configuración es mejor
        save_path = current_app.config.get('UPLOAD_FOLDER', os.path.join(current_app.instance_path, 'uploads'))
        # Crear subdirectorio para DBFs subidos (opcional)
        dbf_upload_dir = os.path.join(save_path, 'dbf_uploads')
        os.makedirs(dbf_upload_dir, exist_ok=True)
        filepath = os.path.join(dbf_upload_dir, filename)

        try:
            dbf_file.save(filepath)
            current_app.logger.info(f"Archivo DBF '{filename}' subido a {filepath}")
            # Llama a la función importadora
            import_points_from_dbf(filepath)
            flash(f"Archivo '{filename}' subido e importado correctamente.", "success")
            # Opcional: Borrar archivo subido después de importar si no se necesita más
            # os.remove(filepath)
        except Exception as e:
             current_app.logger.error(f"Error al guardar o importar el DBF subido '{filename}': {e}")
             flash(f"Error al procesar el archivo DBF subido: {e}", "danger")
    else:
        flash("Archivo inválido o no es un archivo DBF.", "danger")

    return redirect(request.referrer or url_for('main.index'))

# --- NUEVA RUTA DE REPORTES ---
@bp.route('/reportes', methods=['GET', 'POST'])
@login_required
def reportes():
    """Página de reportes con filtros avanzados."""
    form = ReportsFilterForm()

    # Poblar dinámicamente las opciones de ciudad y origen
    cities = db.session.query(Point.city).filter(Point.city.isnot(None)).distinct().order_by(Point.city).all()
    form.city.choices = [('', 'Todas las ciudades')] + [(city[0], city[0]) for city in cities]

    sources = db.session.query(Point.source).filter(Point.source.isnot(None)).distinct().order_by(Point.source).all()
    form.source.choices = [('', 'Todos los orígenes')] + [(source[0], source[0]) for source in sources]

    # Inicializar query base
    query = Point.query

    # Variables para mostrar resultados
    points_data = []
    total_points = 0

    # Si se envió el formulario o hay parámetros GET
    if form.validate_on_submit() or request.args:
        # Limpiar filtros si se presionó el botón limpiar
        if 'clear' in request.form:
            return redirect(url_for('main.reportes'))

        # Obtener valores de filtros (desde form o args)
        point_id = form.point_id.data or request.args.get('point_id')
        city = form.city.data or request.args.get('city')
        status = form.status.data or request.args.get('status')
        source = form.source.data or request.args.get('source')
        camera_type = form.camera_type.data or request.args.get('camera_type')
        has_images = form.has_images.data or request.args.get('has_images')
        has_cameras = form.has_cameras.data or request.args.get('has_cameras')
        date_from = form.date_from.data or request.args.get('date_from')
        date_to = form.date_to.data or request.args.get('date_to')
        modified_from = form.modified_from.data or request.args.get('modified_from')
        modified_to = form.modified_to.data or request.args.get('modified_to')

        # Aplicar filtros
        if point_id:
            try:
                query = query.filter(Point.id == int(point_id))
            except ValueError:
                flash('ID de punto debe ser un número válido.', 'warning')

        if city:
            query = query.filter(Point.city == city)

        if status:
            query = query.filter(Point.status == status)

        if source:
            query = query.filter(Point.source == source)

        # Filtro por tipo de cámara (requiere join)
        if camera_type:
            query = query.join(Camera).filter(Camera.type == camera_type)

        # Filtro por presencia de imágenes
        if has_images == 'si':
            query = query.join(Image)
        elif has_images == 'no':
            query = query.outerjoin(Image).filter(Image.id.is_(None))

        # Filtro por presencia de cámaras
        if has_cameras == 'si':
            if camera_type:  # Ya hicimos join arriba
                pass
            else:
                query = query.join(Camera)
        elif has_cameras == 'no':
            query = query.outerjoin(Camera).filter(Camera.id.is_(None))

        # Filtros de fecha de creación
        if date_from:
            if isinstance(date_from, str):
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Point.created_at >= date_from)

        if date_to:
            if isinstance(date_to, str):
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            # Agregar un día para incluir todo el día
            date_to_end = datetime.combine(date_to, datetime.max.time())
            query = query.filter(Point.created_at <= date_to_end)

        # Filtros de fecha de modificación
        if modified_from:
            if isinstance(modified_from, str):
                modified_from = datetime.strptime(modified_from, '%Y-%m-%d').date()
            query = query.filter(Point.updated_at >= modified_from)

        if modified_to:
            if isinstance(modified_to, str):
                modified_to = datetime.strptime(modified_to, '%Y-%m-%d').date()
            modified_to_end = datetime.combine(modified_to, datetime.max.time())
            query = query.filter(Point.updated_at <= modified_to_end)

        # Ejecutar query y obtener resultados
        try:
            # Usar distinct() para evitar duplicados cuando hay joins
            points = query.distinct().order_by(Point.id).all()
            total_points = len(points)

            # Preparar datos para mostrar
            for point in points:
                # Obtener cámaras asociadas con coordenadas
                cameras = point.cameras.all()
                cameras_info = []
                for cam in cameras:
                    # Determinar coordenadas de la cámara
                    if cam.has_own_coordinates():
                        cam_coordinates = {
                            'lat': cam.latitude,
                            'lon': cam.longitude,
                            'source': cam.location_source,
                            'accuracy': cam.location_accuracy
                        }
                    else:
                        cam_coordinates = {
                            'lat': point.latitude,
                            'lon': point.longitude,
                            'source': 'point',
                            'accuracy': None
                        }

                    cameras_info.append({
                        'id': cam.id,
                        'type': cam.type,
                        'direction': cam.direction,
                        'coordinates': cam_coordinates,
                        'has_own_coordinates': cam.has_own_coordinates(),
                        'created_at': cam.created_at.isoformat() if cam.created_at else None
                    })

                # Contar imágenes
                image_count = point.images.count()

                point_data = {
                    'id': point.id,
                    'name': point.name or f"Punto {point.id}",
                    'city': point.city,
                    'status': point.status,
                    'source': point.source,
                    'lat': point.latitude,  # Agregar coordenadas para el mapa
                    'lon': point.longitude,  # Agregar coordenadas para el mapa
                    'created_at': point.created_at,
                    'updated_at': point.updated_at,
                    'image_count': image_count,
                    'cameras': cameras_info,
                    'has_images': image_count > 0,
                    'has_cameras': len(cameras_info) > 0
                }
                points_data.append(point_data)

            current_app.logger.info(f"Reporte generado por {current_user.username}: {total_points} puntos encontrados")

        except Exception as e:
            current_app.logger.error(f"Error en consulta de reportes: {e}")
            flash('Error al ejecutar la consulta. Revise los filtros aplicados.', 'danger')
            points_data = []
            total_points = 0

    return render_template('reportes.html',
                           title='Reportes',
                           form=form,
                           points_data=points_data,
                           total_points=total_points)