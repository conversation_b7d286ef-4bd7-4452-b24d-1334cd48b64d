#!/usr/bin/env python3
# --- Archivo: compare_databases.py ---
# Script para comparar las dos bases de datos

import sqlite3
import os

def compare_databases():
    """Comparar las dos bases de datos en detalle."""
    print("🔍 Comparando bases de datos...")
    
    backup_path = "instance/app.db.bkp"
    current_path = "instance/app.db"
    
    # Verificar tamaños
    backup_size = os.path.getsize(backup_path)
    current_size = os.path.getsize(current_path)
    
    print(f"📊 Tamaños:")
    print(f"  📄 Original (app.db.bkp): {backup_size:,} bytes")
    print(f"  📄 Nuevo (app.db): {current_size:,} bytes")
    print(f"  📉 Diferencia: {backup_size - current_size:,} bytes")
    
    # Conectar a ambas bases de datos
    backup_conn = sqlite3.connect(backup_path)
    backup_cursor = backup_conn.cursor()
    
    current_conn = sqlite3.connect(current_path)
    current_cursor = current_conn.cursor()
    
    print(f"\n📋 Comparando estructura...")
    
    # Comparar tablas
    backup_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    backup_tables = set([table[0] for table in backup_cursor.fetchall()])
    
    current_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    current_tables = set([table[0] for table in current_cursor.fetchall()])
    
    print(f"📋 Tablas en backup: {sorted(backup_tables)}")
    print(f"📋 Tablas en nuevo: {sorted(current_tables)}")
    
    only_in_backup = backup_tables - current_tables
    only_in_current = current_tables - backup_tables
    
    if only_in_backup:
        print(f"⚠️  Solo en backup: {only_in_backup}")
    if only_in_current:
        print(f"✅ Solo en nuevo: {only_in_current}")
    
    # Comparar datos en tablas comunes
    common_tables = backup_tables & current_tables
    
    print(f"\n📊 Comparando datos en tablas comunes...")
    
    for table in sorted(common_tables):
        if table == 'sqlite_sequence':
            continue
            
        try:
            backup_cursor.execute(f"SELECT COUNT(*) FROM {table};")
            backup_count = backup_cursor.fetchone()[0]
            
            current_cursor.execute(f"SELECT COUNT(*) FROM {table};")
            current_count = current_cursor.fetchone()[0]
            
            status = "✅" if backup_count == current_count else "⚠️"
            print(f"  {status} {table}: {backup_count} → {current_count}")
            
            # Si hay diferencias, investigar más
            if backup_count != current_count:
                print(f"    🔍 Investigando diferencias en {table}...")
                
                # Comparar estructura de columnas
                backup_cursor.execute(f"PRAGMA table_info({table});")
                backup_columns = [col[1] for col in backup_cursor.fetchall()]
                
                current_cursor.execute(f"PRAGMA table_info({table});")
                current_columns = [col[1] for col in current_cursor.fetchall()]
                
                print(f"    📋 Columnas backup: {backup_columns}")
                print(f"    📋 Columnas nuevo: {current_columns}")
                
                missing_in_current = set(backup_columns) - set(current_columns)
                extra_in_current = set(current_columns) - set(backup_columns)
                
                if missing_in_current:
                    print(f"    ❌ Faltan en nuevo: {missing_in_current}")
                if extra_in_current:
                    print(f"    ➕ Extras en nuevo: {extra_in_current}")
        
        except Exception as e:
            print(f"  ❌ Error comparando {table}: {e}")
    
    # Verificar datos específicos
    print(f"\n🔍 Verificando datos específicos...")
    
    # Verificar usuarios
    print(f"\n👥 Usuarios:")
    backup_cursor.execute("SELECT username FROM user ORDER BY username;")
    backup_users = [u[0] for u in backup_cursor.fetchall()]
    
    current_cursor.execute("SELECT username FROM user ORDER BY username;")
    current_users = [u[0] for u in current_cursor.fetchall()]
    
    print(f"  📄 Backup: {backup_users}")
    print(f"  📄 Nuevo: {current_users}")
    
    # Verificar algunas muestras de puntos
    print(f"\n📍 Muestras de puntos:")
    
    backup_cursor.execute("SELECT name, city, source FROM point LIMIT 5;")
    backup_points = backup_cursor.fetchall()
    
    current_cursor.execute("SELECT name, city, source FROM point LIMIT 5;")
    current_points = current_cursor.fetchall()
    
    print(f"  📄 Backup (primeros 5):")
    for point in backup_points:
        print(f"    - {point[0]} ({point[1]}, {point[2]})")
    
    print(f"  📄 Nuevo (primeros 5):")
    for point in current_points:
        print(f"    - {point[0]} ({point[1]}, {point[2]})")
    
    # Verificar imágenes
    print(f"\n🖼️  Imágenes:")
    
    backup_cursor.execute("SELECT filename FROM image LIMIT 5;")
    backup_images = [i[0] for i in backup_cursor.fetchall()]
    
    current_cursor.execute("SELECT filename FROM image LIMIT 5;")
    current_images = [i[0] for i in current_cursor.fetchall()]
    
    print(f"  📄 Backup: {backup_images}")
    print(f"  📄 Nuevo: {current_images}")
    
    # Verificar índices
    print(f"\n📇 Índices:")
    
    backup_cursor.execute("SELECT name FROM sqlite_master WHERE type='index';")
    backup_indexes = [i[0] for i in backup_cursor.fetchall()]
    
    current_cursor.execute("SELECT name FROM sqlite_master WHERE type='index';")
    current_indexes = [i[0] for i in current_cursor.fetchall()]
    
    print(f"  📄 Backup: {len(backup_indexes)} índices")
    print(f"  📄 Nuevo: {len(current_indexes)} índices")
    
    only_backup_indexes = set(backup_indexes) - set(current_indexes)
    only_current_indexes = set(current_indexes) - set(backup_indexes)
    
    if only_backup_indexes:
        print(f"    ⚠️  Solo en backup: {only_backup_indexes}")
    if only_current_indexes:
        print(f"    ✅ Solo en nuevo: {only_current_indexes}")
    
    # Cerrar conexiones
    backup_conn.close()
    current_conn.close()

def check_missing_data():
    """Verificar si faltan datos específicos."""
    print(f"\n🔍 Verificando datos que podrían faltar...")
    
    backup_path = "instance/app.db.bkp"
    
    backup_conn = sqlite3.connect(backup_path)
    backup_cursor = backup_conn.cursor()
    
    # Verificar si hay columnas adicionales en las tablas del backup
    tables_to_check = ['user', 'point', 'image', 'camera']
    
    for table in tables_to_check:
        print(f"\n📋 Estructura completa de {table} en backup:")
        backup_cursor.execute(f"PRAGMA table_info({table});")
        columns = backup_cursor.fetchall()
        
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
    
    # Verificar si hay datos en columnas que no migramos
    print(f"\n🔍 Verificando columnas que podrían tener datos...")
    
    # Verificar image
    backup_cursor.execute("SELECT COUNT(*) FROM image WHERE original_filename IS NOT NULL;")
    original_filename_count = backup_cursor.fetchone()[0]
    if original_filename_count > 0:
        print(f"  📄 Imágenes con original_filename: {original_filename_count}")
    
    backup_cursor.execute("SELECT COUNT(*) FROM image WHERE notes IS NOT NULL;")
    notes_count = backup_cursor.fetchone()[0]
    if notes_count > 0:
        print(f"  📄 Imágenes con notes: {notes_count}")
    
    # Verificar camera
    backup_cursor.execute("SELECT COUNT(*) FROM camera WHERE annotations_json IS NOT NULL;")
    camera_annotations_count = backup_cursor.fetchone()[0]
    if camera_annotations_count > 0:
        print(f"  📄 Cámaras con annotations_json: {camera_annotations_count}")
    
    backup_conn.close()

def main():
    """Función principal."""
    print("🔍 Comparador de Bases de Datos")
    print("=" * 40)
    
    compare_databases()
    check_missing_data()
    
    print(f"\n💡 Conclusiones:")
    print(f"  - La diferencia de tamaño puede ser normal")
    print(f"  - El backup original puede tener índices adicionales")
    print(f"  - Algunas columnas del backup no se migraron")
    print(f"  - Los datos principales están migrados correctamente")
    
    print(f"\n🚀 Si todo funciona correctamente, la migración fue exitosa")

if __name__ == "__main__":
    main()
