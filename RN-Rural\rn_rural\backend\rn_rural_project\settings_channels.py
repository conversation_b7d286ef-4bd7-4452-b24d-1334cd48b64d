# File: backend/rn_rural_project/settings_channels.py
# -----------------------------------------------

# Configuración de Django Channels
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [('127.0.0.1', 6379)],
        },
    },
}

# Asegúrate de que 'channels' y 'apps.notifications' estén en INSTALLED_APPS
if 'channels' not in INSTALLED_APPS:
    INSTALLED_APPS.append('channels')

if 'apps.notifications' not in INSTALLED_APPS:
    INSTALLED_APPS.append('apps.notifications')

# Configurar ASGI application (si no está ya configurado)
ASGI_APPLICATION = 'rn_rural_project.asgi.application'
