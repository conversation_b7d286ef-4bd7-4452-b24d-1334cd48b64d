# File: backend/rn_rural_project/urls.py
# -----------------------------------------------


from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('', include('apps.core.urls')),
    path('admin/', admin.site.urls),
    path('api/users/', include('apps.users.urls')),
    path('api/incidents/', include('apps.incidents.urls', namespace='api-incidents')), # <--- AÑADE UN NAMESPACE ÚNICO AQUÍ
    path('api/locations/', include('apps.locations.urls')),
    path('api/chat/', include('apps.chat.urls', namespace='api-chat')),
    path('chat/', include('apps.chat.urls', namespace='chat')),

    # URLs de la aplicación de incidencias (para vistas HTML)
    path('incidencias/', include('apps.incidents.urls')), # Esta usará el app_name 'incidents' de apps.incidents.urls
]

# Servir archivos de medios siempre, no solo en modo DEBUG
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    # Aquí podrías añadir swagger/redoc si lo usas
    # from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView
    # urlpatterns += [
    #     path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    #     path('api/schema/swagger-ui/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    #     path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    # ]
