# File: backend/apps/incidents/admin.py
# -----------------------------------------------
from django.contrib import admin
from .models import Incidencia
from django.contrib.gis import admin as gis_admin # Importa gis_admin
from leaflet.admin import LeafletGeoAdmin # Importa LeafletGeoAdmin

@admin.register(Incidencia) # Usar el decorador @admin.register es más pythonico
class IncidenciaAdmin(LeafletGeoAdmin): # Hereda de LeafletGeoAdmin
    list_display = ('id', 'usuario_reporta', 'estado', 'fecha_creacion', 'ubicacion_incidencia')
    list_filter = ('estado', 'fecha_creacion')
    search_fields = ('usuario_reporta__username', 'descripcion_texto')
    # LeafletGeoAdmin se encargará de mostrar el mapa para ubicacion_incidencia
    # Puedes personalizar más cosas aquí si es necesario
