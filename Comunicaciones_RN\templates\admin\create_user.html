<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Crear Usuario</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
  <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
  <style>
    .body-background::before {
      background: url('{{ url_for('static', filename='RNCom.png') }}') no-repeat center center;
    }
  </style>
</head>
<body class="body-background">
  <header>
    <h1>Crear Usuario</h1>
  </header>
  <main>
    <form method="POST">
      <div class="form-group">
        <label for="username">Usuario:</label>
        <input type="text" id="username" name="username" required>
      </div>
      <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required>
      </div>
      <div class="form-group">
        <label for="first_name">Nombre:</label>
        <input type="text" id="first_name" name="first_name" required>
      </div>
      <div class="form-group">
        <label for="last_name">Apellido:</label>
        <input type="text" id="last_name" name="last_name" required>
      </div>
      <div class="form-group">
        <label for="badge_number">Legajo:</label>
        <input type="text" id="badge_number" name="badge_number" required>
      </div>
      <div class="form-group">
        <label for="organization">Organismo:</label>
        <select id="organization" name="organization" required>
          {% for org in organismos %}
            <option value="{{ org }}">{{ org }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group">
        <label for="role">Rol:</label>
        <select id="role" name="role" required>
          {% for role in roles %}
            <option value="{{ role }}">{{ role }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group">
        <label for="node_access">Acceso a Nodos:</label>
        <div>
          {% for node in nodes %}
            <input type="checkbox" id="node_{{ node.id }}" name="node_access" value="{{ node.name }}">
            <label for="node_{{ node.id }}">{{ node.name }}</label><br>
          {% endfor %}
        </div>
      </div>
      <div class="form-group">
        <label for="regional_units">Unidades Regionales:</label>
        <div>
          {% for node in nodes %}
            {% if node.name.startswith('Unidad Regional') %}
              <input type="checkbox" id="unit_{{ node.id }}" name="regional_units" value="{{ node.name }}">
              <label for="unit_{{ node.id }}">{{ node.name }}</label><br>
            {% endif %}
          {% endfor %}
        </div>
      </div>
      <div class="form-group">
        <input type="checkbox" id="police_access" name="police_access">
        <label for="police_access">Acceso a Policía</label>
      </div>
      <button type="submit">Crear Usuario</button>
    </form>
    <a href="{{ url_for('admin.list_users') }}">Volver a Listado de Usuarios</a>
  </main>
  <footer>
    <p>© 2024 Tu Sitio Web</p>
  </footer>
</body>
</html>
