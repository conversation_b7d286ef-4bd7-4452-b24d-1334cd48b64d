# Instrucciones para configurar Django Channels

Para habilitar las notificaciones en tiempo real y el cálculo de rutas, sigue estos pasos:

## 1. Instalar dependencias

Asegúrate de instalar las dependencias necesarias:

```bash
pip install channels==4.0.0 channels-redis==4.1.0 daphne==4.0.0 openrouteservice==2.3.3 requests>=2.31.0
```

## 2. Configurar Redis

Asegúrate de que Redis esté instalado y funcionando:

```bash
sudo apt-get update
sudo apt-get install redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

## 3. Añadir la configuración de Channels a settings.py

Añade la siguiente línea al final de tu archivo `rn_rural_project/settings.py`:

```python
# Cargar configuración de Channels
try:
    from .settings_channels import *
except ImportError:
    pass
```

## 4. Verificar la configuración de ASGI

Asegúrate de que el archivo `rn_rural_project/asgi.py` incluya la configuración para WebSockets y que la aplicación `notifications` esté incluida en los patrones de URL.

## 5. Reiniciar el servicio

Después de hacer estos cambios, reinicia el servicio:

```bash
sudo systemctl daemon-reload
sudo systemctl restart rural.service
```

## 6. Verificar que todo funcione correctamente

Verifica que el servicio esté funcionando correctamente:

```bash
sudo systemctl status rural.service
sudo systemctl status redis-server
```

## Solución de problemas

Si encuentras problemas, revisa los logs:

```bash
sudo journalctl -u rural.service -f
sudo journalctl -u redis-server -f
```
