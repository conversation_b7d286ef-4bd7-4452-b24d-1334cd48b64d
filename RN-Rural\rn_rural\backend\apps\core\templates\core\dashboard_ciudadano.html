{# File: backend/apps/core/templates/core/dashboard_ciudadano.html #}
{% extends "core/base.html" %}

{% block title %}{{ titulo_pagina|default:"Mi Portal Ciudadano" }} - RN-Rural{% endblock %}

{% block extra_head %}
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
  <style>
    #mapCiudadano {
      height: 50vh; /* Altura del mapa, puedes ajustarla */
      width: 100%;
      margin-bottom: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .card-dashboard {
      margin-bottom: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }
    .card-dashboard:hover {
      transform: translateY(-5px);
    }
    .card-header-custom {
      border-radius: 10px 10px 0 0;
      padding: 15px;
      font-weight: bold;
    }
    .bg-nueva { background-color: #0dcaf0; color: white; }
    .bg-proceso { background-color: #ffc107; color: black; }
    .bg-derivada { background-color: #fd7e14; color: white; }
    .bg-resuelta { background-color: #198754; color: white; }
    .bg-cerrada { background-color: #6c757d; color: white; }

    .incidencia-card {
      margin-bottom: 15px;
      border-left-width: 5px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: transform 0.2s ease;
    }
    .incidencia-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .incidencia-nueva { border-left-color: #0dcaf0; /* Bootstrap info */ }
    .incidencia-en-proceso { border-left-color: #ffc107; /* Bootstrap warning */ }
    .incidencia-derivada { border-left-color: #fd7e14; /* Bootstrap orange */ }
    .incidencia-resuelta { border-left-color: #198754; /* Bootstrap success */ }
    .incidencia-cerrada { border-left-color: #6c757d; /* Bootstrap secondary */ }

    .map-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255,255,255,0.8);
      padding: 10px;
      border-radius: 5px;
      z-index: 1000;
    }
    .map-error {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
      display: none;
    }
    /* Estilos para los marcadores de incidencias */
    .marker-incidencia-nueva img {
      filter: hue-rotate(190deg) saturate(1.5); /* Azul */
    }
    .marker-incidencia-en-proceso img {
      filter: hue-rotate(40deg) saturate(1.5); /* Amarillo/Naranja */
    }
    .marker-incidencia-resuelta img {
      filter: hue-rotate(120deg) saturate(1.5); /* Verde */
    }

    .map-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;
      background: white;
      padding: 10px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .map-legend {
      position: absolute;
      bottom: 30px;
      right: 10px;
      z-index: 1000;
      background: white;
      padding: 10px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      font-size: 0.8rem;
    }
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }
    .legend-color {
      width: 20px;
      height: 20px;
      margin-right: 5px;
      border-radius: 50%;
    }
    .legend-nueva { background-color: #0dcaf0; }
    .legend-proceso { background-color: #ffc107; }
    .legend-derivada { background-color: #fd7e14; }
    .legend-resuelta { background-color: #198754; }
    .legend-usuario { background-color: #3388ff; }

    .btn-reportar {
      border-radius: 8px;
      font-weight: bold;
      padding: 12px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .btn-reportar:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
    }
  </style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-3">
        <div class="col-md-12">
            <h2 class="mb-3">{{ titulo_pagina|default:"Mi Portal Ciudadano" }}: {{ user.username }}</h2>
            <hr>
        </div>
    </div>

    <div class="row">
        {# Columna Izquierda: Mapa y Botón de Reportar #}
        <div class="col-lg-7 col-md-6 mb-4 order-md-1">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Mi Ubicación / Área de Incidencia</h4>
                    </div>
                </div>
                <div class="card-body p-0 position-relative">
                    <div id="map-container" style="position: relative;">
                        <div id="mapCiudadano"></div>
                        <div id="map-loading" class="map-loading">Cargando mapa...</div>
                        <div id="map-error" class="map-error">No se pudo cargar el mapa. Por favor, verifica tu conexión.</div>
                    </div>

                    <div class="map-legend">
                        <h6 class="mb-2">Leyenda</h6>
                        <div class="legend-item">
                            <div class="legend-color legend-usuario"></div>
                            <span>Tu ubicación</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-nueva"></div>
                            <span>Incidencia Nueva</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-proceso"></div>
                            <span>Incidencia En Proceso</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-resuelta"></div>
                            <span>Incidencia Resuelta</span>
                        </div>
                    </div>
                </div>
            </div>
            {{ ubicacion_actual_usuario|json_script:"user-location-data-json" }}
            <a href="{% url 'incidents:reportar_incidencia' %}" class="btn btn-danger btn-lg mt-3 w-100 btn-reportar">
                <i class="fas fa-exclamation-triangle"></i> Reportar Nueva Incidencia
            </a>
        </div>

        {# Columna Derecha: Lista de Incidencias Reportadas #}
        <div class="col-lg-5 col-md-6 order-md-2">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-clipboard-list"></i> Mis Incidencias Reportadas</h4>
                    </div>
                </div>
                <div class="card-body">
                    {% if incidencias_reportadas %}
                        <div class="list-group" style="max-height: calc(50vh); overflow-y: auto;">
                            {% for incidencia in incidencias_reportadas %}
                                <div class="list-group-item list-group-item-action flex-column align-items-start incidencia-card
                                            {% if incidencia.estado == 'NUEVA' %}incidencia-nueva
                                            {% elif incidencia.estado == 'EN_PROCESO_OPERADOR' or incidencia.estado == 'EN_PROCESO_BRIGADA' %}incidencia-en-proceso
                                            {% elif incidencia.estado == 'DERIVADA_BRIGADA' %}incidencia-derivada
                                            {% elif incidencia.estado == 'CERRADA_RESUELTA' %}incidencia-resuelta
                                            {% else %}incidencia-cerrada{% endif %}">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">Incidencia #{{ incidencia.id }}</h5>
                                        <small class="text-muted">{{ incidencia.fecha_creacion|date:"d/m/Y H:i" }}</small>
                                    </div>
                                    <p class="mb-1">
                                        <span class="badge
                                            {% if incidencia.estado == 'NUEVA' %}bg-nueva
                                            {% elif incidencia.estado == 'EN_PROCESO_OPERADOR' or incidencia.estado == 'EN_PROCESO_BRIGADA' %}bg-proceso
                                            {% elif incidencia.estado == 'DERIVADA_BRIGADA' %}bg-derivada
                                            {% elif incidencia.estado == 'CERRADA_RESUELTA' %}bg-resuelta
                                            {% else %}bg-cerrada{% endif %}">
                                            {{ incidencia.get_estado_display }}
                                        </span>
                                    </p>
                                    <p class="mb-1"><em>{{ incidencia.descripcion_texto|truncatewords:20|default:"Sin descripción." }}</em></p>
                                    <small class="text-muted">
                                        {% if incidencia.brigada_asignada %}
                                            <i class="fas fa-users"></i> Intervino: Brigada {{ incidencia.brigada_asignada.username }}
                                        {% elif incidencia.operador_asignado %}
                                            <i class="fas fa-headset"></i> Atendida por: Operador {{ incidencia.operador_asignado.username }}
                                        {% else %}
                                            <i class="fas fa-clock"></i> Pendiente de asignación.
                                        {% endif %}
                                    </small>
                                    <a href="{% url 'incidents:detalle_incidencia_ciudadano' pk=incidencia.id %}" class="btn btn-sm btn-outline-primary mt-2">
                                        <i class="fas fa-eye"></i> Ver Detalles
                                    </a>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> Aún no has reportado ninguna incidencia.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Elementos del DOM
    const mapElement = document.getElementById('mapCiudadano');
    const mapLoading = document.getElementById('map-loading');
    const mapError = document.getElementById('map-error');

    if (!mapElement) {
        console.error("Elemento del mapa #mapCiudadano no encontrado.");
        return;
    }

    // Función para mostrar un error en el mapa (definida aquí para poder usarla antes)
    function showMapError(message) {
        if (mapLoading) mapLoading.style.display = 'none';
        if (mapError) {
            mapError.textContent = message || "No se pudo cargar el mapa. Por favor, verifica tu conexión.";
            mapError.style.display = 'block';
        }
    }

    // Asegurarse de que Leaflet esté cargado
    if (typeof L === 'undefined') {
        console.error("Leaflet no está cargado. Verifica la conexión a Internet.");
        showMapError("No se pudo cargar la biblioteca de mapas. Verifica tu conexión a Internet.");
        return;
    }

    // --- Configuración de Iconos de Leaflet ---
    try { delete L.Icon.Default.prototype._getIconUrl; } catch(e) { /* Ignorar error si ya fue borrado o no existe */ }
    L.Icon.Default.mergeOptions({
        iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
        iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
        shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
    });

    // Definir iconos personalizados para incidencias según estado
    const iconoIncidenciaNueva = L.icon({
        iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
        shadowSize: [41, 41],
        className: 'marker-incidencia-nueva'
    });

    const iconoIncidenciaEnProceso = L.icon({
        iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
        shadowSize: [41, 41],
        className: 'marker-incidencia-en-proceso'
    });

    const iconoIncidenciaResuelta = L.icon({
        iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
        shadowSize: [41, 41],
        className: 'marker-incidencia-resuelta'
    });

    // Variables para el mapa
    let initialLat = -40.8; // Latitud por defecto (centro de Río Negro aprox.)
    let initialLng = -63.0; // Longitud por defecto
    let initialZoom = 6;    // Zoom general para mostrar la provincia
    let userMarker = null;  // Variable para guardar el marcador del usuario
    let incidenciaMarkers = []; // Array para guardar los marcadores de incidencias

    // Inicializar el mapa con la vista por defecto
    let map;
    try {
        map = L.map(mapElement, {
            // Opciones adicionales para mejorar la estabilidad
            preferCanvas: true,
            attributionControl: true,
            zoomControl: true,
            minZoom: 3,
            maxZoom: 18
        }).setView([initialLat, initialLng], initialZoom);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 18,
            attribution: 'Map data © <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        console.log("Mapa inicializado correctamente");
    } catch (error) {
        console.error("Error al inicializar el mapa:", error);
        showMapError("Error al inicializar el mapa. Por favor, recarga la página.");
        // Asegurarse de que el indicador de carga desaparezca
        if (mapLoading) mapLoading.style.display = 'none';
    }

    // Función para actualizar la ubicación del usuario en el mapa
    function updateUserLocationOnMap(lat, lng, message, zoomLevel = 15) {
        // Validar que las coordenadas sean números válidos
        if (lat === null || lng === null || isNaN(parseFloat(lat)) || isNaN(parseFloat(lng))) {
            console.error("Coordenadas inválidas:", lat, lng);
            if (mapLoading) mapLoading.style.display = 'none';
            return;
        }

        try {
            if (userMarker) {
                map.removeLayer(userMarker); // Quita el marcador anterior si existe
            }

            // Convertir explícitamente a números para asegurar que son valores numéricos
            lat = parseFloat(lat);
            lng = parseFloat(lng);

            // Validar que las coordenadas estén en rangos válidos
            if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
                console.error("Coordenadas fuera de rango:", lat, lng);
                if (mapLoading) mapLoading.style.display = 'none';
                return;
            }

            map.setView([lat, lng], zoomLevel);
            userMarker = L.marker([lat, lng]).addTo(map).bindPopup(message).openPopup();
            console.log(message + ": " + lat + ", " + lng);
        } catch (error) {
            console.error("Error al actualizar ubicación en el mapa:", error);
        } finally {
            // Ocultar el indicador de carga
            if (mapLoading) mapLoading.style.display = 'none';
        }
    }

    // La función showMapError ya está definida arriba

    // Función para obtener el icono según el estado de la incidencia
    function getIncidenciaIcon(estado) {
        if (estado === 'NUEVA' || estado === 'ASIGNADA_OPERADOR') {
            return iconoIncidenciaNueva;
        } else if (estado === 'EN_PROCESO_OPERADOR' || estado === 'EN_PROCESO_BRIGADA' || estado === 'DERIVADA_BRIGADA') {
            return iconoIncidenciaEnProceso;
        } else if (estado === 'CERRADA_RESUELTA') {
            return iconoIncidenciaResuelta;
        } else {
            return L.Icon.Default; // Icono por defecto para otros estados
        }
    }

    // Intentar obtener la ubicación del usuario desde el backend (pasada por la vista)
    try {
        const ubicacionUsuarioDesdeBackend = JSON.parse(document.getElementById('user-location-data-json')?.textContent || 'null');

        if (ubicacionUsuarioDesdeBackend && ubicacionUsuarioDesdeBackend.lat && ubicacionUsuarioDesdeBackend.lng) {
            // Usar ubicación del backend
            initialLat = ubicacionUsuarioDesdeBackend.lat;
            initialLng = ubicacionUsuarioDesdeBackend.lng;
            initialZoom = 15;
            map.setView([initialLat, initialLng], initialZoom);
            updateUserLocationOnMap(initialLat, initialLng, "Tu última ubicación registrada");
        } else {
            // Si no hay ubicación del backend, intenta la geolocalización del navegador
            if (navigator.geolocation) {
                console.log("Intentando obtener geolocalización del navegador...");
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        updateUserLocationOnMap(position.coords.latitude, position.coords.longitude, "Tu ubicación actual detectada");
                    },
                    function(error) {
                        console.warn("No se pudo obtener la geolocalización del navegador: " + error.message);
                        if (mapLoading) mapLoading.style.display = 'none';
                    },
                    {timeout: 10000, enableHighAccuracy: true, maximumAge: 0}
                );
            } else {
                console.warn("Geolocalización no es soportada por este navegador.");
                if (mapLoading) mapLoading.style.display = 'none';
            }
        }
    } catch (error) {
        console.error("Error al procesar la ubicación del usuario:", error);
        showMapError("Error al procesar la ubicación del usuario.");
    }

    // Mostrar las incidencias del usuario en el mapa
    let incidenciasLayer = L.layerGroup().addTo(map);

    // Procesar cada incidencia directamente
    {% for incidencia in incidencias_reportadas %}
        {% if incidencia.ubicacion_incidencia and incidencia.ubicacion_incidencia.y and incidencia.ubicacion_incidencia.x %}
        try {
            // Validar y convertir coordenadas a números
            const lat = parseFloat("{{ incidencia.ubicacion_incidencia.y }}");
            const lng = parseFloat("{{ incidencia.ubicacion_incidencia.x }}");

            // Verificar que las coordenadas sean válidas
            if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
                const incidenciaMarker = L.marker([lat, lng], {
                    icon: getIncidenciaIcon("{{ incidencia.estado }}")
                })
                .bindPopup("<b>Incidencia #{{ incidencia.id }}</b><br>Estado: {{ incidencia.get_estado_display }}<br>{{ incidencia.descripcion_texto|default:'Sin descripción'|escapejs }}")
                .addTo(incidenciasLayer);

                incidenciaMarkers.push(incidenciaMarker);
            } else {
                console.error("Coordenadas inválidas para incidencia #{{ incidencia.id }}:", lat, lng);
            }
        } catch (error) {
            console.error("Error al mostrar incidencia #{{ incidencia.id }}:", error);
        }
        {% endif %}
    {% endfor %}

    // Ajustar la vista para mostrar todas las incidencias si hay más de una
    if (incidenciaMarkers.length > 1) {
        try {
            const group = new L.featureGroup(incidenciaMarkers);
            map.fitBounds(group.getBounds().pad(0.1)); // Añadir un poco de padding
        } catch (error) {
            console.error("Error al ajustar la vista del mapa:", error);
        }
    }

    // Evento cuando el mapa termina de cargar
    map.on('load', function() {
        if (mapLoading) mapLoading.style.display = 'none';
    });

    // Si después de 5 segundos sigue cargando, ocultar el indicador de carga
    setTimeout(function() {
        if (mapLoading) mapLoading.style.display = 'none';
    }, 5000);
});
</script>
{% endblock %}