#!/usr/bin/env python3
"""
Script para verificar el problema con el rol de administrador.
"""

from sqlalchemy import create_engine, text
from app.core.config import settings
from app.db.database import SessionLocal
from app.models.user import User, UserRoleModel, RoleEnum

def check_admin_role():
    print("Verificando el rol de administrador...")
    
    with SessionLocal() as db:
        try:
            # Verificar el enum RoleEnum en Python
            print("\nValores del enum RoleEnum en Python:")
            for role_enum_member in RoleEnum: # Renombrado para claridad
                print(f"  - {role_enum_member.name} = '{role_enum_member.value}'")
            
            # Verificar el valor específico de ADMINISTRADOR
            print(f"\nValor de RoleEnum.ADMINISTRADOR: '{RoleEnum.ADMINISTRADOR.value}'")
            
            # Verificar los valores del enum en la base de datos
            result = db.execute(text("""
            SELECT typname, enumlabel
            FROM pg_enum e
            JOIN pg_type t ON e.enumtypid = t.oid
            WHERE typname = 'roleenum'
            ORDER BY enumsortorder;
            """))
            
            print("\nValores del enum roleenum en la base de datos:")
            for row in result:
                print(f"  - '{row[1]}'") # Acceso por índice está bien aquí
            
            # Verificar el rol de administrador en la tabla roles
            # Usar el valor del enum directamente es más seguro si el enum de Python es la fuente de verdad
            admin_role_value = RoleEnum.ADMINISTRADOR.value
            admin_role = db.query(UserRoleModel).filter(UserRoleModel.name == admin_role_value).first()
            print(f"\nRol de '{admin_role_value}' en la tabla roles:")
            if admin_role:
                print(f"  - ID: {admin_role.id}, Nombre: '{admin_role.name}', Descripción: '{admin_role.description}'")
            else:
                print(f"  - No encontrado con el valor '{admin_role_value}'")
            
            # Verificar el usuario administrador
            admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
            print(f"\nUsuario administrador (<EMAIL>):")
            if admin_user:
                print(f"  - ID: {admin_user.id}, Email: '{admin_user.email}'")
                print("  - Roles:")
                for role in admin_user.roles:
                    print(f"    - ID: {role.id}, Nombre: '{role.name}', Descripción: '{role.description}'")
            else:
                print("  - No encontrado")
        except Exception as e:
            print(f"Error al verificar el rol de administrador: {e}")

if __name__ == "__main__":
    check_admin_role()