# dig_vhf.py

import time
import socketio
import json
from ptt_control import set_ptt
import base64
from io import BytesIO
import wave
import simpleaudio as sa

sio = socketio.Client()
serial_connection = None

# Cargar la configuración
def load_config():
    try:
        with open('config.json', 'r', encoding='utf-8') as file:
            config = json.load(file)
            if 'port_number' not in config:
                raise ValueError("El archivo de configuración no contiene 'port_number'. Asegúrate de definir el número del puerto COM.")
            return config
    except FileNotFoundError:
        print("Error: No se encontró el archivo de configuración 'ptt_client_config.json'.")
        return None
    except ValueError as ve:
        print(f"Error en el archivo de configuración: {ve}")
        return None

# Reproducir el audio y gestionar el PTT
def play_audio(audio_data, config):
    try:
        audio_bytes = base64.b64decode(audio_data)
        audio_stream = BytesIO(audio_bytes)

        # Activar el PTT un segundo antes de la reproducción del audio
        print("Activando PTT 1 segundo antes de reproducir el audio...", flush=True)
        activar_ptt(config)
        time.sleep(1)  # Esperar un segundo antes de reproducir el audio

        # Leer y reproducir el audio
        with wave.open(audio_stream, 'rb') as wave_obj:
            play_obj = sa.WaveObject(wave_obj.readframes(wave_obj.getnframes()), wave_obj.getnchannels(), wave_obj.getsampwidth(), wave_obj.getframerate()).play()
            play_obj.wait_done()

        print("Audio reproducido con éxito.", flush=True)

        # Desactivar el PTT un segundo después de que el audio finaliza
        print("Desactivando PTT 1 segundo después de que el audio terminó...", flush=True)
        time.sleep(1)  # Esperar un segundo después de la reproducción
        desactivar_ptt(config)

    except Exception as e:
        print(f"Error al reproducir el audio: {e}", flush=True)

# Funciones para activar y desactivar el PTT localmente
def activar_ptt(config):
    global serial_connection
    print("Activando PTT en la máquina local...", flush=True)
    serial_connection = set_ptt(True, config, serial_connection)
    print("PTT activado.", flush=True)

def desactivar_ptt(config):
    global serial_connection
    print("Desactivando PTT en la máquina local...", flush=True)
    serial_connection = set_ptt(False, config, serial_connection)
    print("PTT desactivado.", flush=True)

# Conectar al servidor WebSocket
# Conexión al servidor de WebSocket
def connect_to_socket(config):
    @sio.event
    def connect():
        print(f"Conectado al servidor Socket.IO para transmitir audio en el nodo {config['node_id']}")

    @sio.event
    def disconnect():
        print("Desconectado del servidor Socket.IO")

    sio.connect(f"wss://{config['node_url']}",
                headers={'node_id': config['node_id'], 'username': config['username']},
                namespaces=['/node'])

def listen_to_events(config):
    connect_to_socket(config)
    try:
        while True:
            time.sleep(0.5)
    except KeyboardInterrupt:
        sio.disconnect()

if __name__ == '__main__':
    config = load_config()
    if config:
        listen_to_events(config)
    else:
        print("No se pudo cargar la configuración. Verifica el archivo ptt_client_config.json.")
