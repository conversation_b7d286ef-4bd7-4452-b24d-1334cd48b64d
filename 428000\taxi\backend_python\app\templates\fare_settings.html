{% extends "base_layout.html" %}

{% block title %}Configuración de Tarifas - Panel de Taxis{% endblock %}

{% block head_extra %}
<style>
    .fare-card {
        transition: transform 0.3s;
        margin-bottom: 20px;
    }
    .fare-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .fare-icon {
        font-size: 2rem;
        margin-bottom: 15px;
    }
    .fare-value {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .fare-description {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .chart-container {
        height: 300px;
        margin-bottom: 30px;
    }
    .settings-section {
        margin-bottom: 30px;
    }
    .settings-section h4 {
        margin-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
    }
    .time-slot {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 15px;
    }
    .time-slot-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    .zone-map {
        height: 300px;
        background-color: #f0f0f0;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Configuración de Tarifas</h2>
                <div>
                    <button type="button" class="btn btn-light me-2" id="historyBtn">
                        <i class="bi bi-clock-history"></i> Historial
                    </button>
                    <button type="button" class="btn btn-success" id="saveAllBtn">
                        <i class="bi bi-save"></i> Guardar Cambios
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Resumen de tarifas actuales -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card fare-card text-center">
                            <div class="card-body">
                                <div class="fare-icon text-primary">
                                    <i class="bi bi-flag"></i>
                                </div>
                                <h5 class="card-title">Tarifa Base</h5>
                                <p class="fare-value">${{ base_fare }}</p>
                                <p class="fare-description">Cargo inicial por viaje</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card fare-card text-center">
                            <div class="card-body">
                                <div class="fare-icon text-success">
                                    <i class="bi bi-rulers"></i>
                                </div>
                                <h5 class="card-title">Por Kilómetro</h5>
                                <p class="fare-value">${{ per_km_fare }}</p>
                                <p class="fare-description">Cargo por kilómetro recorrido</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card fare-card text-center">
                            <div class="card-body">
                                <div class="fare-icon text-warning">
                                    <i class="bi bi-hourglass-split"></i>
                                </div>
                                <h5 class="card-title">Por Minuto</h5>
                                <p class="fare-value">${{ per_minute_fare }}</p>
                                <p class="fare-description">Cargo por minuto de espera</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card fare-card text-center">
                            <div class="card-body">
                                <div class="fare-icon text-danger">
                                    <i class="bi bi-cash-coin"></i>
                                </div>
                                <h5 class="card-title">Tarifa Mínima</h5>
                                <p class="fare-value">${{ minimum_fare }}</p>
                                <p class="fare-description">Cargo mínimo por viaje</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gráfico de tarifas -->
                <div class="chart-container">
                    <h4>Evolución de Tarifas</h4>
                    <p class="text-muted">Gráfico simulado de la evolución de tarifas en los últimos meses</p>
                    <div class="bg-light p-5 text-center">
                        <p>Aquí se mostraría un gráfico con la evolución de las tarifas</p>
                    </div>
                </div>

                <!-- Configuración de tarifas básicas -->
                <div class="settings-section">
                    <h4>Tarifas Básicas</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="baseFare" class="form-label">Tarifa Base ($)</label>
                                <input type="number" class="form-control" id="baseFare" value="{{ base_fare }}" step="0.01" min="0">
                                <div class="form-text">Cargo inicial por cada viaje</div>
                            </div>
                            <div class="mb-3">
                                <label for="perKmFare" class="form-label">Tarifa por Kilómetro ($)</label>
                                <input type="number" class="form-control" id="perKmFare" value="{{ per_km_fare }}" step="0.01" min="0">
                                <div class="form-text">Cargo por cada kilómetro recorrido</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="perMinuteFare" class="form-label">Tarifa por Minuto ($)</label>
                                <input type="number" class="form-control" id="perMinuteFare" value="{{ per_minute_fare }}" step="0.01" min="0">
                                <div class="form-text">Cargo por cada minuto de espera</div>
                            </div>
                            <div class="mb-3">
                                <label for="minimumFare" class="form-label">Tarifa Mínima ($)</label>
                                <input type="number" class="form-control" id="minimumFare" value="{{ minimum_fare }}" step="0.01" min="0">
                                <div class="form-text">Cargo mínimo por viaje</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configuración de tarifas por horario -->
                <div class="settings-section">
                    <h4>Tarifas por Horario</h4>
                    <p>Configure multiplicadores de tarifa para diferentes horarios del día</p>
                    
                    <div class="time-slots-container">
                        <!-- Horario normal -->
                        <div class="time-slot">
                            <div class="time-slot-header">
                                <h5>Horario Normal (6:00 - 22:00)</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="normalTimeActive" checked>
                                    <label class="form-check-label" for="normalTimeActive">Activo</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="normalMultiplier" class="form-label">Multiplicador</label>
                                        <input type="number" class="form-control" id="normalMultiplier" value="1.0" step="0.1" min="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="normalDays" class="form-label">Días aplicables</label>
                                        <select class="form-select" id="normalDays" multiple>
                                            <option value="1" selected>Lunes</option>
                                            <option value="2" selected>Martes</option>
                                            <option value="3" selected>Miércoles</option>
                                            <option value="4" selected>Jueves</option>
                                            <option value="5" selected>Viernes</option>
                                            <option value="6" selected>Sábado</option>
                                            <option value="0" selected>Domingo</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Horario nocturno -->
                        <div class="time-slot">
                            <div class="time-slot-header">
                                <h5>Horario Nocturno (22:00 - 6:00)</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="nightTimeActive" checked>
                                    <label class="form-check-label" for="nightTimeActive">Activo</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="nightMultiplier" class="form-label">Multiplicador</label>
                                        <input type="number" class="form-control" id="nightMultiplier" value="1.5" step="0.1" min="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="nightDays" class="form-label">Días aplicables</label>
                                        <select class="form-select" id="nightDays" multiple>
                                            <option value="1" selected>Lunes</option>
                                            <option value="2" selected>Martes</option>
                                            <option value="3" selected>Miércoles</option>
                                            <option value="4" selected>Jueves</option>
                                            <option value="5" selected>Viernes</option>
                                            <option value="6" selected>Sábado</option>
                                            <option value="0" selected>Domingo</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fin de semana -->
                        <div class="time-slot">
                            <div class="time-slot-header">
                                <h5>Fin de Semana</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="weekendActive" checked>
                                    <label class="form-check-label" for="weekendActive">Activo</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="weekendMultiplier" class="form-label">Multiplicador</label>
                                        <input type="number" class="form-control" id="weekendMultiplier" value="1.2" step="0.1" min="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="weekendDays" class="form-label">Días aplicables</label>
                                        <select class="form-select" id="weekendDays" multiple>
                                            <option value="6" selected>Sábado</option>
                                            <option value="0" selected>Domingo</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configuración de tarifas por zona -->
                <div class="settings-section">
                    <h4>Tarifas por Zona</h4>
                    <p>Configure multiplicadores de tarifa para diferentes zonas geográficas</p>
                    
                    <div class="zone-map">
                        <p class="text-muted">Aquí se mostraría un mapa para configurar zonas</p>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Zona</th>
                                    <th>Descripción</th>
                                    <th>Multiplicador</th>
                                    <th>Activo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Centro</td>
                                    <td>Zona céntrica de la ciudad</td>
                                    <td>
                                        <input type="number" class="form-control" value="1.0" step="0.1" min="1">
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Norte</td>
                                    <td>Zona norte de la ciudad</td>
                                    <td>
                                        <input type="number" class="form-control" value="1.1" step="0.1" min="1">
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Sur</td>
                                    <td>Zona sur de la ciudad</td>
                                    <td>
                                        <input type="number" class="form-control" value="1.2" step="0.1" min="1">
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Botón de guardar -->
                <div class="d-grid gap-2 col-md-6 mx-auto mt-4">
                    <button class="btn btn-primary btn-lg" id="saveSettingsBtn">
                        <i class="bi bi-save"></i> Guardar Configuración
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
    // Guardar configuración
    document.getElementById('saveSettingsBtn').addEventListener('click', function() {
        // Recopilar datos de los formularios
        const baseFare = document.getElementById('baseFare').value;
        const perKmFare = document.getElementById('perKmFare').value;
        const perMinuteFare = document.getElementById('perMinuteFare').value;
        const minimumFare = document.getElementById('minimumFare').value;
        
        // Validar datos
        if (!baseFare || !perKmFare || !perMinuteFare || !minimumFare) {
            alert('Por favor complete todos los campos de tarifas básicas');
            return;
        }
        
        // Aquí implementar la lógica para guardar la configuración
        alert('Configuración guardada correctamente');
    });
    
    // Guardar todos los cambios
    document.getElementById('saveAllBtn').addEventListener('click', function() {
        document.getElementById('saveSettingsBtn').click();
    });
    
    // Ver historial de cambios
    document.getElementById('historyBtn').addEventListener('click', function() {
        alert('Mostrando historial de cambios de tarifas');
        // Implementar lógica para mostrar historial
    });
</script>
{% endblock %}
