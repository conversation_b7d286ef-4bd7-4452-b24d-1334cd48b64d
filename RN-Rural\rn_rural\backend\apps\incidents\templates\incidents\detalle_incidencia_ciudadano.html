{% extends "core/base.html" %}
{% load static %}
{# File: backend/apps/incidents/templates/incidents/detalle_incidencia_ciudadano.html #}

{% block title %}Detalle de Incidencia #{{ incidencia.id }} - RN-Rural{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@latest/dist/leaflet-routing-machine.css" />
<link rel="stylesheet" href="{% static 'css/dashboard.css' %}">
<style>
    #mapIncidencia {
        height: 400px;
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .info-box {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 5px solid #0d6efd;
    }

    .distance-box {
        background-color: #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
        text-align: center;
        border: 1px solid #dee2e6;
    }

    .distance-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #0d6efd;
    }

    .eta-value {
        font-size: 1.2rem;
        color: #6c757d;
    }

    .status-badge {
        font-size: 1rem;
        padding: 8px 15px;
        border-radius: 20px;
        display: inline-block;
        margin-bottom: 15px;
    }

    .timeline {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 15px;
        padding-bottom: 15px;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -30px;
        top: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #0d6efd;
        border: 3px solid white;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .timeline-date {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .timeline-content {
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-3">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_ciudadano' %}">Mi Portal</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Incidencia #{{ incidencia.id }}</li>
                </ol>
            </nav>
            <h2 class="mb-3">Detalle de Incidencia #{{ incidencia.id }}</h2>
            <hr>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Ubicación de la Incidencia</h4>
                        <span class="badge
                            {% if incidencia.estado == 'NUEVA' %}bg-nueva
                            {% elif incidencia.estado == 'EN_PROCESO_OPERADOR' or incidencia.estado == 'ASIGNADA_OPERADOR' %}bg-proceso
                            {% elif incidencia.estado == 'DERIVADA_BRIGADA' or incidencia.estado == 'EN_PROCESO_BRIGADA' %}bg-derivada
                            {% elif incidencia.estado == 'CERRADA_RESUELTA' %}bg-resuelta
                            {% else %}bg-cerrada{% endif %}">
                            {{ incidencia.get_estado_display }}
                        </span>
                    </div>
                </div>
                <div class="card-body p-0 position-relative">
                    <div id="mapIncidencia"></div>

                    <div class="map-legend">
                        <h6 class="mb-2">Leyenda</h6>
                        <div class="legend-item">
                            <div class="legend-color legend-incidencia"></div>
                            <span>Incidencia</span>
                        </div>
                        {% if incidencia.estado == 'DERIVADA_BRIGADA' or incidencia.estado == 'EN_PROCESO_BRIGADA' %}
                        <div class="legend-item">
                            <div class="legend-color legend-brigada"></div>
                            <span>Brigada</span>
                        </div>
                        {% endif %}
                        <div class="legend-item">
                            <div class="legend-color legend-usuario"></div>
                            <span>Tu ubicación</span>
                        </div>
                    </div>
                </div>
            </div>

            {% if incidencia.estado == 'DERIVADA_BRIGADA' or incidencia.estado == 'EN_PROCESO_BRIGADA' %}
            <div class="distance-box">
                <h5><i class="fas fa-truck"></i> Brigada en camino</h5>
                <div class="distance-value" id="distance-value">Calculando...</div>
                <div class="eta-value" id="eta-value">Tiempo estimado: Calculando...</div>
            </div>
            {% endif %}
        </div>

        <div class="col-lg-4">
            <div class="card card-dashboard">
                <div class="card-header card-header-custom bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-info-circle"></i> Información</h4>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <h5>Detalles de la Incidencia</h5>
                        <p><strong>Fecha de reporte:</strong> {{ incidencia.fecha_creacion|date:"d/m/Y H:i" }}</p>
                        <p><strong>Estado actual:</strong> {{ incidencia.get_estado_display }}</p>
                        <p><strong>Descripción:</strong> {{ incidencia.descripcion_texto|default:"Sin descripción." }}</p>

                        <div class="mt-3">
                            <a href="{% url 'chat:chat_incidencia' incidencia.id %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-comments"></i> Abrir Chat
                            </a>
                            <small class="text-muted d-block mt-1">
                                Comunícate con el operador asignado
                            </small>
                        </div>
                    </div>

                    {% if incidencia.brigada_asignada %}
                    <div class="info-box">
                        <h5>Brigada Asignada</h5>
                        <p><strong>Brigada:</strong> {{ incidencia.brigada_asignada.username }}</p>
                        <p><strong>Asignada el:</strong> {{ incidencia.fecha_actualizacion|date:"d/m/Y H:i" }}</p>
                    </div>
                    {% endif %}

                    <div class="info-box">
                        <h5>Historial</h5>
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-date">{{ incidencia.fecha_creacion|date:"d/m/Y H:i" }}</div>
                                <div class="timeline-content">Incidencia reportada</div>
                            </div>

                            {% if incidencia.operador_asignado %}
                            <div class="timeline-item">
                                <div class="timeline-date">{{ incidencia.fecha_actualizacion|date:"d/m/Y H:i" }}</div>
                                <div class="timeline-content">Asignada a operador: {{ incidencia.operador_asignado.username }}</div>
                            </div>
                            {% endif %}

                            {% if incidencia.brigada_asignada %}
                            <div class="timeline-item">
                                <div class="timeline-date">{{ incidencia.fecha_actualizacion|date:"d/m/Y H:i" }}</div>
                                <div class="timeline-content">Derivada a brigada: {{ incidencia.brigada_asignada.username }}</div>
                            </div>
                            {% endif %}

                            {% if incidencia.estado == 'CERRADA_RESUELTA' or incidencia.estado == 'CERRADA_NO_RESUELTA' %}
                            <div class="timeline-item">
                                <div class="timeline-date">{{ incidencia.fecha_actualizacion|date:"d/m/Y H:i" }}</div>
                                <div class="timeline-content">Incidencia {{ incidencia.get_estado_display }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{ incidencia.ubicacion_incidencia.y|json_script:"incidencia-lat" }}
{{ incidencia.ubicacion_incidencia.x|json_script:"incidencia-lng" }}
{% if incidencia.brigada_asignada %}
{{ brigada_ubicacion|json_script:"brigada-ubicacion" }}
{% if ruta %}
{{ ruta|json_script:"ruta-data" }}
{% endif %}
{% endif %}
{{ ubicacion_actual_usuario|json_script:"user-location-data-json" }}
{% endblock %}

{% block extra_scripts %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://unpkg.com/leaflet-routing-machine@latest/dist/leaflet-routing-machine.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar variables
        let map, userMarker, incidenciaMarker, brigadaMarker, routingControl;

        // Obtener coordenadas de la incidencia
        const incidenciaLat = parseFloat(JSON.parse(document.getElementById('incidencia-lat').textContent));
        const incidenciaLng = parseFloat(JSON.parse(document.getElementById('incidencia-lng').textContent));

        // Obtener ubicación del usuario
        const userLocationData = JSON.parse(document.getElementById('user-location-data-json').textContent);

        // Inicializar el mapa
        map = L.map('mapIncidencia').setView([incidenciaLat, incidenciaLng], 15);

        // Añadir capa de mapa base
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Añadir marcador de la incidencia
        const incidenciaIcon = L.divIcon({
            className: 'custom-div-icon',
            html: `<div style="background-color:#0dcaf0; width:30px; height:30px; border-radius:50%; border:3px solid white; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;"><i class="fas fa-exclamation-triangle"></i></div>`,
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });

        incidenciaMarker = L.marker([incidenciaLat, incidenciaLng], { icon: incidenciaIcon }).addTo(map);
        incidenciaMarker.bindPopup(`<b>Incidencia #{{ incidencia.id }}</b><br>{{ incidencia.descripcion_texto|default:"Sin descripción"|escapejs }}`);

        // Añadir marcador del usuario si hay datos
        if (userLocationData && userLocationData.lat && userLocationData.lng) {
            const userIcon = L.divIcon({
                className: 'custom-div-icon',
                html: `<div style="background-color:#3388ff; width:30px; height:30px; border-radius:50%; border:3px solid white; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;"><i class="fas fa-user"></i></div>`,
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            userMarker = L.marker([userLocationData.lat, userLocationData.lng], { icon: userIcon }).addTo(map);
            userMarker.bindPopup("Tu ubicación actual");
        }

        // Si hay una brigada asignada y estamos en estado derivada o en proceso por brigada
        {% if incidencia.estado == 'DERIVADA_BRIGADA' or incidencia.estado == 'EN_PROCESO_BRIGADA' %}
            const brigadaUbicacion = JSON.parse(document.getElementById('brigada-ubicacion').textContent);

            if (brigadaUbicacion && brigadaUbicacion.lat && brigadaUbicacion.lng) {
                // Añadir marcador de la brigada
                const brigadaIcon = L.divIcon({
                    className: 'custom-div-icon',
                    html: `<div style="background-color:#fd7e14; width:30px; height:30px; border-radius:50%; border:3px solid white; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;"><i class="fas fa-truck"></i></div>`,
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                });

                brigadaMarker = L.marker([brigadaUbicacion.lat, brigadaUbicacion.lng], { icon: brigadaIcon }).addTo(map);
                brigadaMarker.bindPopup(`<b>Brigada {{ incidencia.brigada_asignada.username }}</b><br>En camino a tu ubicación`);

                // Función para calcular distancia en línea recta (Haversine)
                function calcularDistanciaLinea(lat1, lon1, lat2, lon2) {
                    const R = 6371; // Radio de la Tierra en km
                    const dLat = (lat2 - lat1) * Math.PI / 180;
                    const dLon = (lon2 - lon1) * Math.PI / 180;
                    const a =
                        Math.sin(dLat/2) * Math.sin(dLat/2) +
                        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                        Math.sin(dLon/2) * Math.sin(dLon/2);
                    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                    const d = R * c; // Distancia en km
                    return d;
                }

                // Función para formatear la distancia
                function formatearDistancia(distanciaKm) {
                    if (distanciaKm >= 1) {
                        return distanciaKm.toFixed(1) + ' km';
                    } else {
                        return Math.round(distanciaKm * 1000) + ' m';
                    }
                }

                // Función para formatear el tiempo
                function formatearTiempo(minutos) {
                    if (minutos > 60) {
                        const horas = Math.floor(minutos / 60);
                        const mins = minutos % 60;
                        return horas + ' h ' + mins + ' min';
                    } else {
                        return minutos + ' minutos';
                    }
                }

                // Calcular distancia en línea recta como fallback
                const distanciaLinea = calcularDistanciaLinea(
                    brigadaUbicacion.lat,
                    brigadaUbicacion.lng,
                    incidenciaLat,
                    incidenciaLng
                );

                // Actualizar la distancia con el cálculo en línea recta
                const distanceElement = document.getElementById('distance-value');
                const etaElement = document.getElementById('eta-value');

                if (distanceElement && etaElement) {
                    // Mostrar distancia en línea recta mientras se calcula la ruta
                    distanceElement.textContent = 'Distancia aproximada: ' + formatearDistancia(distanciaLinea);

                    // Estimar tiempo basado en velocidad promedio de 40 km/h
                    const minutosEstimados = Math.round((distanciaLinea / 40) * 60);
                    etaElement.textContent = 'Tiempo estimado aproximado: ' + formatearTiempo(minutosEstimados);
                }

                // Verificar si tenemos datos de ruta precalculados del servidor
                const rutaData = document.getElementById('ruta-data');

                if (rutaData) {
                    try {
                        const rutaJson = JSON.parse(rutaData.textContent);
                        console.log('Datos de ruta del servidor:', rutaJson);

                        if (rutaJson.coordenadas && rutaJson.coordenadas.length > 0) {
                            // Dibujar la ruta usando los datos del servidor
                            let routeStyle = {
                                color: '#fd7e14',
                                weight: 5,
                                opacity: 0.8
                            };

                            // Si es una línea recta, usar un estilo diferente
                            if (rutaJson.es_linea_recta) {
                                routeStyle = {
                                    color: '#fd7e14',
                                    weight: 4,
                                    opacity: 0.7,
                                    dashArray: '10, 10'
                                };
                            }

                            const routePolyline = L.polyline(rutaJson.coordenadas, routeStyle).addTo(map);

                            // Ajustar la vista para mostrar toda la ruta
                            map.fitBounds(routePolyline.getBounds(), { padding: [50, 50] });

                            // Actualizar la información de distancia y tiempo
                            if (distanceElement && etaElement) {
                                // Convertir distancia a km si es mayor a 1000m
                                const distanciaKm = rutaJson.distancia / 1000;

                                if (rutaJson.es_linea_recta) {
                                    distanceElement.textContent = 'Distancia aproximada: ' + formatearDistancia(distanciaKm);
                                } else {
                                    distanceElement.textContent = 'Distancia: ' + formatearDistancia(distanciaKm);
                                }

                                // Convertir tiempo a minutos
                                const minutos = Math.round(rutaJson.duracion / 60);

                                if (rutaJson.es_linea_recta) {
                                    etaElement.textContent = 'Tiempo estimado aproximado: ' + formatearTiempo(minutos);
                                } else {
                                    etaElement.textContent = 'Tiempo estimado de llegada: ' + formatearTiempo(minutos);
                                }
                            }

                            console.log('Ruta dibujada con éxito usando datos del servidor');
                            return; // Salir de la función si se dibujó la ruta con éxito
                        }
                    } catch (error) {
                        console.error('Error al procesar los datos de ruta del servidor:', error);
                        // Continuar con el cálculo de ruta en el cliente como fallback
                    }
                }

                // Si no hay datos de ruta del servidor o hubo un error, solicitar cálculo de ruta al backend
                console.log('Solicitando cálculo de ruta al backend...');

                // Construir la URL con los parámetros de coordenadas
                const url = `/api/map/calcular-ruta/?start_lat=${brigadaUbicacion.lat}&start_lng=${brigadaUbicacion.lng}&end_lat=${incidenciaLat}&end_lng=${incidenciaLng}`;

                // Realizar la solicitud al backend
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Error de red: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.route && data.route.coordinates && data.route.coordinates.length > 0) {
                            console.log('Ruta calculada por el backend:', data);

                            // Determinar el estilo de la ruta
                            let routeStyle = {
                                color: '#fd7e14',
                                weight: 5,
                                opacity: 0.8
                            };

                            // Si es una línea recta, usar un estilo diferente
                            if (data.route.es_linea_recta) {
                                routeStyle = {
                                    color: '#fd7e14',
                                    weight: 4,
                                    opacity: 0.7,
                                    dashArray: '10, 10'
                                };
                            }

                            // Dibujar la ruta
                            const routePolyline = L.polyline(data.route.coordinates, routeStyle).addTo(map);

                            // Ajustar la vista para mostrar toda la ruta
                            map.fitBounds(routePolyline.getBounds(), { padding: [50, 50] });

                            // Actualizar la información de distancia y tiempo
                            if (distanceElement && etaElement) {
                                // Convertir distancia a km
                                const distanciaKm = data.route.distance / 1000;

                                if (data.route.es_linea_recta) {
                                    distanceElement.textContent = 'Distancia aproximada: ' + formatearDistancia(distanciaKm);
                                } else {
                                    distanceElement.textContent = 'Distancia: ' + formatearDistancia(distanciaKm);
                                }

                                // Convertir tiempo a minutos
                                const minutos = Math.round(data.route.duration / 60);

                                if (data.route.es_linea_recta) {
                                    etaElement.textContent = 'Tiempo estimado aproximado: ' + formatearTiempo(minutos);
                                } else {
                                    etaElement.textContent = 'Tiempo estimado de llegada: ' + formatearTiempo(minutos);
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error al solicitar ruta al backend:', error);
                        // Ya tenemos la distancia en línea recta como fallback
                    });

                // Ajustar la vista para mostrar todos los marcadores
                const bounds = L.latLngBounds([
                    [incidenciaLat, incidenciaLng],
                    [brigadaUbicacion.lat, brigadaUbicacion.lng]
                ]);

                if (userLocationData && userLocationData.lat && userLocationData.lng) {
                    bounds.extend([userLocationData.lat, userLocationData.lng]);
                }

                map.fitBounds(bounds, { padding: [50, 50] });

                // Configurar WebSocket para actualizaciones en tiempo real
                setupWebSocket();
            }
        {% endif %}

        // Función para configurar WebSocket
        function setupWebSocket() {
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${wsProtocol}//${window.location.host}/ws/incidencia/{{ incidencia.id }}/`;

            const socket = new WebSocket(wsUrl);

            socket.onopen = function(e) {
                console.log('WebSocket conectado');
            };

            socket.onmessage = function(e) {
                const data = JSON.parse(e.data);

                if (data.type === 'brigada_location_update') {
                    updateBrigadaLocation(data.lat, data.lng);
                }
            };

            socket.onclose = function(e) {
                console.log('WebSocket desconectado');
                // Intentar reconectar después de 5 segundos
                setTimeout(setupWebSocket, 5000);
            };

            socket.onerror = function(e) {
                console.error('Error en WebSocket:', e);
            };
        }

        // Función para actualizar la ubicación de la brigada
        function updateBrigadaLocation(lat, lng) {
            if (brigadaMarker) {
                brigadaMarker.setLatLng([lat, lng]);

                // Actualizar la ruta
                if (routingControl) {
                    try {
                        routingControl.setWaypoints([
                            L.latLng(lat, lng),
                            L.latLng(incidenciaLat, incidenciaLng)
                        ]);
                    } catch (error) {
                        console.error('Error al actualizar la ruta:', error);

                        // Calcular distancia en línea recta como fallback
                        const distanciaLinea = calcularDistanciaLinea(
                            lat,
                            lng,
                            incidenciaLat,
                            incidenciaLng
                        );

                        // Actualizar la distancia con el cálculo en línea recta
                        const distanceElement = document.getElementById('distance-value');
                        const etaElement = document.getElementById('eta-value');

                        if (distanceElement && etaElement) {
                            // Mostrar distancia en línea recta
                            distanceElement.textContent = 'Distancia aproximada: ' + formatearDistancia(distanciaLinea);

                            // Estimar tiempo basado en velocidad promedio de 40 km/h
                            const minutosEstimados = Math.round((distanciaLinea / 40) * 60);
                            etaElement.textContent = 'Tiempo estimado aproximado: ' + formatearTiempo(minutosEstimados);
                        }
                    }
                } else {
                    // Si no hay routingControl, calcular distancia en línea recta
                    const distanciaLinea = calcularDistanciaLinea(
                        lat,
                        lng,
                        incidenciaLat,
                        incidenciaLng
                    );

                    // Actualizar la distancia con el cálculo en línea recta
                    const distanceElement = document.getElementById('distance-value');
                    const etaElement = document.getElementById('eta-value');

                    if (distanceElement && etaElement) {
                        // Mostrar distancia en línea recta
                        distanceElement.textContent = 'Distancia aproximada: ' + formatearDistancia(distanciaLinea);

                        // Estimar tiempo basado en velocidad promedio de 40 km/h
                        const minutosEstimados = Math.round((distanciaLinea / 40) * 60);
                        etaElement.textContent = 'Tiempo estimado aproximado: ' + formatearTiempo(minutosEstimados);
                    }
                }

                // Enviar la ubicación actualizada al servidor para recalcular la ruta
                // Esto se implementaría con una llamada AJAX al servidor
                console.log('Ubicación de brigada actualizada. Se debería enviar al servidor para recalcular la ruta.');
            }
        }
    });
</script>
{% endblock %}
