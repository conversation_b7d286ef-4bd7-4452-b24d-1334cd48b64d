# File: backend/rn_rural_project/settings.py
# -----------------------------------------------

import environ
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent
env = environ.Env(
    # Define un valor por defecto para DEBUG si no está en .env
    DEBUG=(bool, True) # Puedes cambiar el valor por defecto a False para producción
)
# Lee el archivo .env desde BASE_DIR (es decir, /home/<USER>/rn_rural/backend/.env)
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

SECRET_KEY = env('SECRET_KEY')
# DEBUG se leerá desde el archivo .env. Si no está allí, usará el valor por defecto definido arriba.
DEBUG = env('DEBUG') 
ALLOWED_HOSTS = env('ALLOWED_HOSTS', cast=lambda v: [s.strip() for s in v.split(',')])


INSTALLED_APPS = [
    'daphne',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'whitenoise.runserver_nostatic', # <--- AÑADIR PARA WHITENOISE (si usas runserver en DEBUG=False)
    'django.contrib.staticfiles',    # Debe ir antes de tus apps y de whitenoise si no usas runserver_nostatic
    'django.contrib.gis',
    'leaflet',

    # Dependencias
    'rest_framework',
    'rest_framework_gis',
    'channels',

    # Mis Apps
    'apps.core.apps.CoreConfig',
    'apps.users.apps.UsersConfig',
    'apps.incidents.apps.IncidentsConfig',
    'apps.locations.apps.LocationsConfig',
    'apps.chat.apps.ChatConfig',
    'apps.notifications.apps.NotificationsConfig',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware', # <--- AÑADIR AQUÍ (después de SecurityMiddleware)
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'rn_rural_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')], # Directorio global de plantillas
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

ASGI_APPLICATION = 'rn_rural_project.asgi.application'
# WSGI_APPLICATION = 'rn_rural_project.wsgi.application' # Comentado si usas ASGI con Daphne

DATABASES = {
    'default': env.db_url(
        'DATABASE_URL',
        default='postgis://user:pass@host:port/dbname' # Un fallback por si no está en .env
    )
}
DATABASES['default']['ENGINE'] = 'django.contrib.gis.db.backends.postgis'

AUTH_USER_MODEL = 'users.User'

AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]

LANGUAGE_CODE = 'es-ar'
TIME_ZONE = 'America/Argentina/Buenos_Aires' # Ajusta a tu zona horaria real si es diferente
USE_I18N = True
USE_TZ = True


# --- Configuración de Archivos Estáticos ---
STATIC_URL = 'static/'

# Directorio donde `collectstatic` reunirá todos los archivos estáticos para producción.
# Este directorio NO debe estar versionado en Git.
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles_collected')

# Opcional: Directorios adicionales donde Django buscará archivos estáticos (no dentro de apps).
# STATICFILES_DIRS = [
#     os.path.join(BASE_DIR, "static_global"), 
# ]

# Para WhiteNoise: almacenamiento optimizado para producción.
# Esto crea archivos con hashes en sus nombres para un cacheo eficiente.
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
# --- Fin Configuración de Archivos Estáticos ---


# --- Configuración de Archivos de Medios ---
MEDIA_URL = '/media/'
# Directorio donde se guardarán los archivos subidos por los usuarios.
# Asegúrate de que el usuario bajo el cual corre el servidor tenga permisos de escritura aquí.
MEDIA_ROOT = os.path.join(BASE_DIR, 'media_root') # Cambié el nombre para evitar confusión con 'media' de apps.
# --- Fin Configuración de Archivos de Medios ---


DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        # 'rest_framework.renderers.BrowsableAPIRenderer',
    ),
    'DEFAULT_PARSER_CLASSES': (
        'rest_framework.parsers.JSONParser',
        #'rest_framework_gis.parsers.GeoJSONParser', # <--- DESCOMENTADO (asumiendo que el ModuleNotFoundError se resolvió)
    )
}

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [(env('CHANNEL_LAYER_REDIS_HOST', default='localhost'), env.int('CHANNEL_LAYER_REDIS_PORT', default=6379))],
        },
    },
}

LEAFLET_CONFIG = {
    'DEFAULT_CENTER': (-40.8, -63.0),
    'DEFAULT_ZOOM': 6,
    'MIN_ZOOM': 3,
    'MAX_ZOOM': 18,
    'TILES': [
        ('OpenStreetMap', 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            'attribution': '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }),
    ],
}