# .flaskenv

# Script principal de la aplicación
FLASK_APP=run.py

# Habilita el modo de desarrollo (reloader, debugger)
FLASK_ENV=development
# FLASK_DEBUG=1 # Alternativa/Redundante si FLASK_ENV=development

# Hace que el servidor escuche en todas las interfaces de red disponibles
# Permitirá el acceso desde otras IPs en tu red local
FLASK_RUN_HOST=0.0.0.0

# Puerto en el que escuchará el servidor de desarrollo
FLASK_RUN_PORT=5006

# Opcional: Mantener la clave secreta fuera de aquí es mejor práctica
# SECRET_KEY='123456' # Mejor en config.py o variable de entorno real