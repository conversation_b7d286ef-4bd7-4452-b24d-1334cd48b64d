<!-- app/templates/audit/logs.html -->
{% extends "base.html" %}

{% block title %}Logs de Auditoría{% endblock %}

{% block extra_css %}
<style>
    .filters-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .log-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .log-row {
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .log-row:hover {
        background-color: #f8f9fa;
    }
    
    .action-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .module-badge {
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 8px;
        background: #e9ecef;
        color: #495057;
    }
    
    .pagination-info {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-top: 20px;
    }
    
    .export-btn {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        border-radius: 50px;
        padding: 15px 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .filter-summary {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 15px;
    }
    
    .filter-tag {
        display: inline-block;
        background: #2196f3;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        margin: 2px;
    }
    
    .log-detail-preview {
        font-size: 0.85rem;
        color: #6c757d;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    @media (max-width: 768px) {
        .table-responsive {
            font-size: 0.85rem;
        }
        
        .export-btn {
            bottom: 10px;
            right: 10px;
            padding: 10px 15px;
        }
        
        .filters-card {
            padding: 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-list-alt"></i> Logs de Auditoría</h2>
            <p class="text-muted">Historial completo de cambios y actividad del sistema</p>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('audit.dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-line"></i> Dashboard
            </a>
            <a href="{{ url_for('audit.export_logs', **filters) }}" class="btn btn-success">
                <i class="fas fa-download"></i> Exportar
            </a>
        </div>
    </div>
    
    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> {{ error }}
    </div>
    {% else %}
    
    <!-- Filtros -->
    <div class="filters-card">
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-2">
                    <label for="user">Usuario:</label>
                    <select name="user" id="user" class="form-control form-control-sm">
                        <option value="">Todos</option>
                        {% for user in users %}
                        <option value="{{ user }}" {% if filters.user == user %}selected{% endif %}>
                            {{ user }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="action">Acción:</label>
                    <select name="action" id="action" class="form-control form-control-sm">
                        <option value="">Todas</option>
                        {% for action in actions %}
                        <option value="{{ action }}" {% if filters.action == action %}selected{% endif %}>
                            {{ action }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="table">Tabla:</label>
                    <select name="table" id="table" class="form-control form-control-sm">
                        <option value="">Todas</option>
                        {% for table in tables %}
                        <option value="{{ table }}" {% if filters.table == table %}selected{% endif %}>
                            {{ table }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="module">Módulo:</label>
                    <select name="module" id="module" class="form-control form-control-sm">
                        <option value="">Todos</option>
                        {% for module in modules %}
                        <option value="{{ module }}" {% if filters.module == module %}selected{% endif %}>
                            {{ module }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="date_from">Desde:</label>
                    <input type="date" name="date_from" id="date_from" 
                           class="form-control form-control-sm" 
                           value="{{ filters.date_from }}">
                </div>
                
                <div class="col-md-2">
                    <label for="date_to">Hasta:</label>
                    <input type="date" name="date_to" id="date_to" 
                           class="form-control form-control-sm" 
                           value="{{ filters.date_to }}">
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                    <a href="{{ url_for('audit.logs') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-times"></i> Limpiar
                    </a>
                    <span class="ml-3 text-muted">
                        <i class="fas fa-info-circle"></i>
                        Mostrando {{ logs.total }} registro(s)
                    </span>
                </div>
            </div>
        </form>
        
        <!-- Resumen de Filtros Activos -->
        {% if filters.user or filters.action or filters.table or filters.module or filters.date_from or filters.date_to %}
        <div class="filter-summary mt-3">
            <strong>Filtros activos:</strong>
            {% if filters.user %}
            <span class="filter-tag">Usuario: {{ filters.user }}</span>
            {% endif %}
            {% if filters.action %}
            <span class="filter-tag">Acción: {{ filters.action }}</span>
            {% endif %}
            {% if filters.table %}
            <span class="filter-tag">Tabla: {{ filters.table }}</span>
            {% endif %}
            {% if filters.module %}
            <span class="filter-tag">Módulo: {{ filters.module }}</span>
            {% endif %}
            {% if filters.date_from %}
            <span class="filter-tag">Desde: {{ filters.date_from }}</span>
            {% endif %}
            {% if filters.date_to %}
            <span class="filter-tag">Hasta: {{ filters.date_to }}</span>
            {% endif %}
        </div>
        {% endif %}
    </div>
    
    <!-- Tabla de Logs -->
    <div class="log-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th width="5%">ID</th>
                        <th width="15%">Fecha/Hora</th>
                        <th width="12%">Usuario</th>
                        <th width="10%">Acción</th>
                        <th width="10%">Tabla</th>
                        <th width="8%">Módulo</th>
                        <th width="25%">Descripción</th>
                        <th width="10%">IP</th>
                        <th width="5%">Detalle</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs.items %}
                    <tr class="log-row" onclick="showLogDetail({{ log.id }})">
                        <td>
                            <small class="text-muted">#{{ log.id }}</small>
                        </td>
                        <td>
                            <small>
                                {{ log.timestamp.strftime('%d/%m/%Y') }}<br>
                                {{ log.timestamp.strftime('%H:%M:%S') }}
                            </small>
                        </td>
                        <td>
                            <i class="fas fa-user"></i>
                            <strong>{{ log.username }}</strong>
                        </td>
                        <td>
                            <span class="action-badge bg-{{ log.get_action_color() }} text-white">
                                {{ log.action }}
                            </span>
                        </td>
                        <td>
                            <code>{{ log.table_name }}</code>
                            {% if log.record_id %}
                            <br><small class="text-muted">ID: {{ log.record_id }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.module %}
                            <span class="module-badge">{{ log.module }}</span>
                            {% else %}
                            <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="log-detail-preview">
                                {{ log.get_readable_description() }}
                            </div>
                            {% if log.get_changed_fields_list() %}
                            <small class="text-info">
                                Campos: {{ log.get_changed_fields_list()|join(', ') }}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.user_ip %}
                            <small class="text-muted">{{ log.user_ip }}</small>
                            {% else %}
                            <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); showLogDetail({{ log.id }})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Paginación -->
    {% if logs.pages > 1 %}
    <div class="pagination-info">
        <div class="col-md-6">
            <small class="text-muted">
                Mostrando {{ logs.per_page * (logs.page - 1) + 1 }} - 
                {{ logs.per_page * logs.page if logs.page < logs.pages else logs.total }} 
                de {{ logs.total }} registros
            </small>
        </div>
        <div class="col-md-6">
            <nav aria-label="Paginación de logs">
                <ul class="pagination pagination-sm justify-content-end">
                    {% if logs.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('audit.logs', page=logs.prev_num, **filters) }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in logs.iter_pages() %}
                    {% if page_num %}
                    {% if page_num != logs.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('audit.logs', page=page_num, **filters) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if logs.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('audit.logs', page=logs.next_num, **filters) }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
    
    {% if not logs.items %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No se encontraron logs</h4>
        <p class="text-muted">Ajusta los filtros para ver más resultados</p>
    </div>
    {% endif %}
    
    {% endif %}
</div>

<!-- Botón de Exportación Flotante -->
<a href="{{ url_for('audit.export_logs', **filters) }}" class="btn btn-success export-btn">
    <i class="fas fa-download"></i> Exportar
</a>

<!-- Modal para Detalle de Log -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detalle de Log de Auditoría</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="logDetailContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Cargando...
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit de filtros
document.querySelectorAll('#filterForm select').forEach(select => {
    select.addEventListener('change', function() {
        document.getElementById('filterForm').submit();
    });
});

// Función para mostrar detalle de log
function showLogDetail(logId) {
    $('#logDetailModal').modal('show');
    
    fetch(`/audit/log/${logId}`)
        .then(response => response.text())
        .then(html => {
            // Extraer solo el contenido del modal
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const content = doc.querySelector('.container-fluid') || doc.querySelector('.modal-body');
            document.getElementById('logDetailContent').innerHTML = content.innerHTML;
        })
        .catch(error => {
            document.getElementById('logDetailContent').innerHTML = 
                '<div class="alert alert-danger">Error cargando detalle del log</div>';
        });
}

// Atajos de teclado
document.addEventListener('keydown', function(e) {
    // Ctrl + E para exportar
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        window.location.href = '{{ url_for("audit.export_logs", **filters) }}';
    }
    
    // Ctrl + F para enfocar filtros
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.getElementById('user').focus();
    }
});
</script>
{% endblock %}
