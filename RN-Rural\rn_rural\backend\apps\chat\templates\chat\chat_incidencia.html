{% extends "core/base.html" %}
{% load static %}
{% load date_filters %}

{% block title %}Chat de Incidencia #{{ incidencia.id }} - RN-Rural{% endblock %}

{% block extra_head %}
<style>
    /* Contenedor principal del chat */
    .chat-container {
        height: 70vh;
        display: flex;
        flex-direction: column;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    /* <PERSON><PERSON> de mensajes */
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #f0f2f5;
        background-image: linear-gradient(rgba(255, 255, 255, 0.7) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255, 255, 255, 0.7) 1px, transparent 1px);
        background-size: 20px 20px;
        scrollbar-width: thin;
        scrollbar-color: #ccc transparent;
    }

    .chat-messages::-webkit-scrollbar {
        width: 6px;
    }

    .chat-messages::-webkit-scrollbar-thumb {
        background-color: #ccc;
        border-radius: 3px;
    }

    /* Estilos generales de mensajes */
    .message {
        margin-bottom: 16px;
        max-width: 75%;
        position: relative;
        animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Mensaje enviado por el usuario actual */
    .message-sent {
        margin-left: auto;
        background-color: #e3effd;
        border-radius: 18px 4px 18px 18px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border-left: 3px solid #0d6efd;
    }

    /* Mensaje recibido de otro usuario */
    .message-received {
        margin-right: auto;
        background-color: #ffffff;
        border-radius: 4px 18px 18px 18px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border-right: 3px solid #6c757d;
    }

    /* Mensaje del sistema */
    .message-system {
        margin: 12px auto;
        background-color: rgba(240, 240, 240, 0.8);
        border-radius: 12px;
        text-align: center;
        max-width: 85%;
        font-style: italic;
        color: #555;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        padding: 8px 15px;
    }

    /* Contenido del mensaje */
    .message-content {
        padding: 12px 16px;
        word-wrap: break-word;
    }

    /* Información del mensaje (remitente, hora) */
    .message-info {
        font-size: 0.75rem;
        color: #666;
        text-align: right;
        padding: 0 16px 8px;
        display: flex;
        justify-content: space-between;
    }

    /* Área de entrada de mensajes */
    .chat-input {
        display: flex;
        gap: 10px;
        padding: 15px;
        background-color: #fff;
        border-top: 1px solid #e0e0e0;
        align-items: center;
    }

    .chat-input textarea {
        flex: 1;
        border-radius: 24px;
        padding: 12px 18px;
        resize: none;
        border: 1px solid #ddd;
        transition: border-color 0.3s;
        font-size: 0.95rem;
    }

    .chat-input textarea:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    }

    /* Botones de enviar y grabar */
    .btn-send, .btn-record, .btn-image {
        border-radius: 50%;
        width: 46px;
        height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
    }

    .btn-send:hover, .btn-record:hover, .btn-image:hover {
        transform: scale(1.05);
    }

    .btn-record.recording {
        background-color: #dc3545;
        color: white;
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Mensajes de audio */
    .audio-message {
        display: flex;
        flex-direction: column;
        background-color: rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        padding: 8px;
        margin-top: 5px;
    }

    .audio-message audio {
        width: 100%;
        height: 40px;
        border-radius: 20px;
        margin-bottom: 5px;
    }

    .audio-message .audio-icon {
        margin-right: 10px;
        color: #0d6efd;
        font-size: 1.2rem;
    }

    .play-audio-btn {
        margin-top: 5px;
        font-size: 0.85rem;
        padding: 3px 8px;
        align-self: flex-start;
    }

    /* Información de la incidencia */
    .incidencia-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 18px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* Controles de exportación */
    .export-controls {
        margin-top: 20px;
    }

    /* Estado del mensaje (leído, enviado, exportado) */
    .message-status {
        font-size: 0.7rem;
        color: #777;
        margin-top: 4px;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .message-status i {
        margin-right: 4px;
    }

    .status-read {
        color: #0d6efd;
    }

    .status-sent {
        color: #6c757d;
    }

    .status-exported {
        color: #198754;
        margin-left: 8px;
    }

    /* Notificación de nuevo mensaje */
    .new-message-notification {
        position: absolute;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #0d6efd;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        z-index: 100;
        display: none;
    }

    /* Imagen en el chat */
    .image-message img {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        cursor: pointer;
    }

    /* Mapa en el chat */
    .map-message {
        height: 200px;
        width: 100%;
        border-radius: 8px;
        margin-top: 5px;
    }

    /* Indicador de escritura */
    .typing-indicator {
        display: none;
        padding: 8px 15px;
        background-color: rgba(240, 240, 240, 0.8);
        border-radius: 12px;
        font-style: italic;
        color: #555;
        margin: 0 auto 10px auto;
        width: fit-content;
    }

    .typing-indicator span {
        animation: typingDot 1.4s infinite;
        display: inline-block;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #555;
        margin: 0 2px;
    }

    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes typingDot {
        0%, 60%, 100% { transform: translateY(0); }
        30% { transform: translateY(-4px); }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .message {
            max-width: 85%;
        }

        .chat-container {
            height: 60vh;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Formulario oculto con token CSRF -->
<form id="csrf-form" style="display: none;">
    {% csrf_token %}
</form>

<div class="container mt-4">
    <div class="row mb-3">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    {% if user.role == 'CIUDADANO' %}
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_ciudadano' %}">Mi Portal</a></li>
                    {% elif user.role == 'OPERADOR' %}
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_operador' %}">Panel de Operador</a></li>
                    {% elif user.role == 'BRIGADA' %}
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_brigada' %}">Panel de Brigada</a></li>
                    {% endif %}
                    <li class="breadcrumb-item"><a href="{% url 'incidents:detalle_incidencia_ciudadano' incidencia.id %}">Incidencia #{{ incidencia.id }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Chat</li>
                </ol>
            </nav>
            <h2 class="mb-3">Chat de Incidencia #{{ incidencia.id }}</h2>
            <hr>
        </div>
    </div>

    <div class="row">
        <!-- Columna izquierda: Chat -->
        <div class="col-lg-8">
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <!-- Los mensajes se cargarán dinámicamente aquí -->
                    <div class="message message-system">
                        <div class="message-content">
                            Conectando al chat...
                        </div>
                    </div>
                </div>

                <!-- Indicador de escritura -->
                <div class="typing-indicator" id="typingIndicator">
                    <span></span><span></span><span></span>
                </div>

                <!-- Notificación de nuevos mensajes -->
                <div class="new-message-notification" id="newMessageNotification">
                    <i class="fas fa-arrow-down"></i> Nuevos mensajes
                </div>

                <div class="chat-input">
                    <button id="imageButton" class="btn btn-outline-secondary btn-image">
                        <i class="fas fa-image"></i>
                    </button>
                    <textarea id="messageInput" class="form-control" placeholder="Escribe un mensaje..." rows="1"></textarea>
                    <button id="sendButton" class="btn btn-primary btn-send">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button id="recordButton" class="btn btn-outline-danger btn-record">
                        <i class="fas fa-microphone"></i>
                    </button>
                </div>

                <!-- Input oculto para subir imágenes -->
                <input type="file" id="imageInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Columna derecha: Información de la incidencia -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Información de la Incidencia</h5>
                </div>
                <div class="card-body">
                    <div class="incidencia-info">
                        <p><strong>Estado:</strong> {{ incidencia.get_estado_display }}</p>
                        <p><strong>Reportada por:</strong> {{ incidencia.usuario_reporta.username }}</p>
                        <p><strong>Fecha:</strong> {{ incidencia.fecha_creacion|date:"d/m/Y H:i" }}</p>

                        {% if incidencia.operador_asignado %}
                        <p><strong>Operador asignado:</strong> {{ incidencia.operador_asignado.username }}</p>
                        {% endif %}

                        {% if incidencia.brigada_asignada %}
                        <p><strong>Brigada asignada:</strong> {{ incidencia.brigada_asignada.username }}</p>
                        {% endif %}
                    </div>

                    {% if user.role == 'OPERADOR' and incidencia.brigada_asignada %}
                    <div class="export-controls">
                        <button id="exportChatButton" class="btn btn-warning btn-block">
                            <i class="fas fa-share-square"></i> Exportar Chat a Brigada
                        </button>
                        <small class="text-muted d-block mt-2">
                            Esto permitirá a la brigada ver todo el historial de comunicación con el ciudadano.
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Datos para JavaScript -->
<input type="hidden" id="incidenciaId" value="{{ incidencia.id }}">
<input type="hidden" id="userId" value="{{ user.id }}">
<input type="hidden" id="userRole" value="{{ user.role }}">
<input type="hidden" id="userName" value="{{ user.username }}">

<!-- Precargar sonido de notificación -->
<audio id="notificationSound" preload="auto" style="display: none;">
    <source src="{% static 'sounds/notification.mp3' %}" type="audio/mpeg">
</audio>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elementos del DOM
    const chatMessages = document.getElementById('chatMessages');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const recordButton = document.getElementById('recordButton');
    const imageButton = document.getElementById('imageButton');
    const imageInput = document.getElementById('imageInput');
    const exportChatButton = document.getElementById('exportChatButton');
    const typingIndicator = document.getElementById('typingIndicator');
    const newMessageNotification = document.getElementById('newMessageNotification');

    // Datos de la incidencia y el usuario
    const incidenciaId = document.getElementById('incidenciaId').value;
    const userId = document.getElementById('userId').value;
    const userRole = document.getElementById('userRole').value;
    const userName = document.getElementById('userName').value;

    // Variables para la grabación de audio
    let mediaRecorder;
    let audioChunks = [];
    let isRecording = false;

    // Variables para el control de notificaciones
    let isScrolledToBottom = true;
    let unreadMessages = 0;

    // Conexión WebSocket
    let socket;

    // Inicializar WebSocket
    function initWebSocket() {
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}/ws/chat/incidencia/${incidenciaId}/`;

        console.log('Iniciando conexión WebSocket a:', wsUrl);

        try {
            socket = new WebSocket(wsUrl);

            // Establecer un timeout para la conexión
            const connectionTimeout = setTimeout(() => {
                if (socket.readyState !== WebSocket.OPEN) {
                    console.error('Timeout de conexión WebSocket');
                    socket.close();
                    addSystemMessage('Error de conexión: Timeout. Reintentando...', true);

                    // Reintentar después de un tiempo
                    setTimeout(initWebSocket, 5000);
                }
            }, 10000); // 10 segundos de timeout

            socket.onopen = function(e) {
                console.log('Conexión WebSocket establecida');
                clearTimeout(connectionTimeout);
                addSystemMessage('Conectado al chat');

                // Enviar un ping periódico para mantener la conexión activa
                startPingInterval();
            };

            // Función para mantener la conexión activa
            function startPingInterval() {
                const pingInterval = setInterval(() => {
                    if (socket.readyState === WebSocket.OPEN) {
                        // Enviar un ping cada 30 segundos
                        socket.send(JSON.stringify({ tipo: 'PING' }));
                        console.log('Ping enviado para mantener conexión');
                    } else {
                        clearInterval(pingInterval);
                    }
                }, 30000);

                // Limpiar el intervalo si la página se cierra
                window.addEventListener('beforeunload', () => {
                    clearInterval(pingInterval);
                });
            }
        } catch (error) {
            console.error('Error al inicializar WebSocket:', error);
            addSystemMessage('Error al conectar al chat. Reintentando...', true);
            setTimeout(initWebSocket, 5000);
        }

        socket.onmessage = function(e) {
            const data = JSON.parse(e.data);
            console.log('Mensaje recibido:', data);

            if (data.type === 'connection_established') {
                console.log(data.message);
            } else if (data.type === 'historial_mensajes') {
                // Limpiar mensajes existentes
                chatMessages.innerHTML = '';

                // Mostrar historial de mensajes
                if (data.mensajes && data.mensajes.length > 0) {
                    data.mensajes.forEach(mensaje => {
                        renderMessage(mensaje);
                    });

                    // Scroll al final
                    scrollToBottom();
                } else {
                    addSystemMessage('No hay mensajes anteriores');
                }
            } else if (data.type === 'chat_message') {
                // Mostrar indicador de escritura
                if (typingIndicator) {
                    typingIndicator.style.display = 'none';
                }

                // Renderizar el mensaje como nuevo
                renderMessage(data, true);

                // Reproducir sonido de notificación si el mensaje no es del usuario actual
                if (data.remitente_id != userId) {
                    // Reproducir sonido de notificación
                    playNotificationSound();

                    // Parpadear el título de la página
                    flashPageTitle('Nuevo mensaje');

                    // Mostrar notificación del navegador si está permitido
                    showBrowserNotification(data.remitente_nombre, data.texto || 'Nuevo mensaje');
                }

                // Scroll al final o mostrar notificación
                if (isScrolledToBottom) {
                    scrollToBottom();
                } else {
                    showNewMessageNotification();
                }
            } else if (data.type === 'typing') {
                // Mostrar indicador de escritura
                if (typingIndicator && data.remitente_id != userId) {
                    typingIndicator.style.display = 'block';

                    // Ocultar después de 3 segundos
                    setTimeout(() => {
                        typingIndicator.style.display = 'none';
                    }, 3000);
                }
            } else if (data.type === 'error') {
                addSystemMessage(`Error: ${data.message}`, true);
            }
        };

        // Función para reproducir sonido de notificación
        function playNotificationSound() {
            try {
                console.log('Intentando reproducir sonido de notificación');

                // Usar una URL absoluta para el sonido
                const soundUrl = '{% static "sounds/notification.mp3" %}';
                console.log('URL del sonido:', soundUrl);

                // Precargar el sonido
                const preloadLink = document.createElement('link');
                preloadLink.rel = 'preload';
                preloadLink.href = soundUrl;
                preloadLink.as = 'audio';
                document.head.appendChild(preloadLink);

                // Usar el elemento de audio precargado
                const audioElement = document.getElementById('notificationSound');

                if (audioElement) {
                    console.log('Usando elemento de audio precargado');
                    audioElement.volume = 0.5;

                    // Intentar reproducir el audio
                    try {
                        // Reiniciar el audio si ya se ha reproducido
                        audioElement.currentTime = 0;

                        console.log('Audio cargado, intentando reproducir');
                        const playPromise = audioElement.play();

                        if (playPromise !== undefined) {
                            playPromise
                                .then(() => {
                                    console.log('Reproducción de sonido iniciada correctamente');
                                })
                                .catch(e => {
                                    console.error('No se pudo reproducir el sonido precargado:', e);
                                    // Mostrar una notificación visual como alternativa
                                    showVisualNotification();

                                    // Intentar reproducir con un clic simulado como alternativa
                                    document.addEventListener('click', function playOnClick() {
                                        audioElement.play().catch(err => console.error('Fallo en reproducción con clic:', err));
                                        document.removeEventListener('click', playOnClick);
                                    }, { once: true });
                                });
                        }
                    } catch (e) {
                        console.error('Error al reproducir el audio precargado:', e);
                        showVisualNotification();
                    }
                } else {
                    console.error('No se encontró el elemento de audio precargado');

                    // Crear un elemento de audio dinámicamente como respaldo
                    const dynamicAudio = new Audio(soundUrl);
                    dynamicAudio.volume = 0.5;
                    dynamicAudio.play().catch(e => {
                        console.error('No se pudo reproducir el audio dinámico:', e);
                        showVisualNotification();
                    });
                }

                // Manejar errores de carga del audio
                if (audioElement) {
                    audioElement.onerror = function(e) {
                        console.error('Error al cargar el archivo de audio:', e);
                        console.error('Código de error:', audioElement.error ? audioElement.error.code : 'desconocido');
                        showVisualNotification();

                        // Intentar con una URL alternativa
                        const alternativeUrl = '/static/sounds/notification.mp3';
                        console.log('Intentando con URL alternativa:', alternativeUrl);
                        const backupAudio = new Audio(alternativeUrl);
                        backupAudio.play().catch(err => console.error('Fallo en reproducción alternativa:', err));
                    };
                }
            } catch (e) {
                console.error('Error al reproducir sonido:', e);
                showVisualNotification();
            }
        }

        // Mostrar notificación visual como alternativa al sonido
        function showVisualNotification() {
            // Parpadeo del título de la página
            flashPageTitle('🔔 Nuevo mensaje');

            // Mostrar notificación en la página
            if (newMessageNotification) {
                newMessageNotification.style.display = 'block';
                newMessageNotification.style.animation = 'pulse 1s infinite';

                // Detener la animación después de 5 segundos
                setTimeout(() => {
                    newMessageNotification.style.animation = '';
                }, 5000);
            }
        }

        // Función para hacer parpadear el título de la página
        let titleFlashInterval;
        function flashPageTitle(message) {
            // Guardar el título original
            const originalTitle = document.title;

            // Limpiar cualquier intervalo existente
            if (titleFlashInterval) {
                clearInterval(titleFlashInterval);
            }

            // Crear nuevo intervalo para parpadeo
            titleFlashInterval = setInterval(function() {
                document.title = document.title === originalTitle ? message : originalTitle;
            }, 1000);

            // Detener el parpadeo cuando el usuario vuelve a la página
            const stopTitleFlash = function() {
                clearInterval(titleFlashInterval);
                document.title = originalTitle;
                window.removeEventListener('focus', stopTitleFlash);
            };

            window.addEventListener('focus', stopTitleFlash);

            // También detener después de 30 segundos por seguridad
            setTimeout(stopTitleFlash, 30000);
        }

        // Función para mostrar notificaciones del navegador
        function showBrowserNotification(title, message) {
            // Verificar si las notificaciones están soportadas
            if (!("Notification" in window)) {
                console.log("Este navegador no soporta notificaciones de escritorio");
                return;
            }

            // Verificar si ya tenemos permiso
            if (Notification.permission === "granted") {
                // Crear y mostrar la notificación
                createNotification(title, message);
            }
            // Si no hemos pedido permiso aún
            else if (Notification.permission !== "denied") {
                Notification.requestPermission().then(function (permission) {
                    if (permission === "granted") {
                        createNotification(title, message);
                    }
                });
            }

            // Función para crear la notificación
            function createNotification(title, message) {
                try {
                    const notification = new Notification(title, {
                        body: message,
                        icon: '{% static "img/logo.png" %}',
                        badge: '{% static "img/logo.png" %}',
                        vibrate: [200, 100, 200]
                    });

                    // Cerrar la notificación después de 5 segundos
                    setTimeout(() => {
                        notification.close();
                    }, 5000);

                    // Enfocar la ventana al hacer clic en la notificación
                    notification.onclick = function() {
                        window.focus();
                        this.close();
                    };
                } catch (e) {
                    console.error('Error al crear notificación:', e);
                }
            }
        }

        socket.onclose = function(e) {
            console.log('Conexión WebSocket cerrada');
            addSystemMessage('Desconectado del chat. Reconectando...', true);

            // Intentar reconectar después de 5 segundos
            setTimeout(initWebSocket, 5000);
        };

        socket.onerror = function(e) {
            console.error('Error en la conexión WebSocket:', e);
            addSystemMessage('Error en la conexión', true);
        };
    }

    // Renderizar un mensaje en el chat
    function renderMessage(mensaje, isNewMessage = false) {
        const isCurrentUser = mensaje.remitente_id == userId;
        const messageDiv = document.createElement('div');

        if (mensaje.tipo === 'SISTEMA') {
            messageDiv.className = 'message message-system';
            messageDiv.innerHTML = `
                <div class="message-content">${mensaje.texto}</div>
            `;
        } else {
            messageDiv.className = `message ${isCurrentUser ? 'message-sent' : 'message-received'}`;

            if (mensaje.tipo === 'TEXTO') {
                messageDiv.innerHTML = `
                    <div class="message-content">${mensaje.texto}</div>
                    <div class="message-info">
                        ${isCurrentUser ? 'Tú' : mensaje.remitente_nombre}
                        <span class="ms-2">${formatDateTime(mensaje.fecha_envio)}</span>
                    </div>
                    ${renderMessageStatus(mensaje, isCurrentUser)}
                `;
            } else if (mensaje.tipo === 'AUDIO') {
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <div class="audio-message">
                            <div class="audio-icon">
                                <i class="fas fa-headphones"></i>
                            </div>
                            <audio controls preload="auto" class="chat-audio" data-message-id="${mensaje.id}">
                                <source src="${mensaje.audio_url}" type="audio/webm">
                                <source src="${mensaje.audio_url}" type="audio/mpeg">
                                <source src="${mensaje.audio_url}" type="audio/mp4">
                                <source src="${mensaje.audio_url}" type="audio/ogg">
                                Tu navegador no soporta el elemento de audio.
                            </audio>
                            <button class="btn btn-sm btn-primary play-audio-btn" data-audio-id="${mensaje.id}">
                                <i class="fas fa-play"></i> Reproducir
                            </button>
                        </div>
                    </div>
                    <div class="message-info">
                        ${isCurrentUser ? 'Tú' : mensaje.remitente_nombre}
                        <span class="ms-2">${formatDateTime(mensaje.fecha_envio)}</span>
                    </div>
                    ${renderMessageStatus(mensaje, isCurrentUser)}
                `;

                // Añadir el mensaje al DOM antes de intentar reproducir el audio
                chatMessages.appendChild(messageDiv);

                // Intentar reproducir automáticamente si es un mensaje nuevo y no es del usuario actual
                if (isNewMessage && !isCurrentUser) {
                    const audioElement = messageDiv.querySelector('audio');
                    if (audioElement) {
                        // Esperar a que el audio esté cargado
                        audioElement.oncanplaythrough = function() {
                            console.log('Audio cargado, intentando reproducción automática');
                            // Intentar reproducir
                            const playPromise = audioElement.play();

                            if (playPromise !== undefined) {
                                playPromise.catch(e => {
                                    console.log('Reproducción automática bloqueada por el navegador:', e);
                                    // No hacer nada, el usuario puede reproducir manualmente
                                });
                            }
                        };

                        // Manejar errores de carga
                        audioElement.onerror = function(e) {
                            console.error('Error al cargar el audio:', e);
                            console.error('URL del audio:', mensaje.audio_url);
                        };
                    }
                }

                // No añadir el mensaje al DOM aquí, ya lo hemos hecho arriba
                return;
            } else if (mensaje.tipo === 'IMAGEN') {
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <div class="image-message">
                            <img src="${mensaje.imagen_url}" alt="Imagen compartida" onclick="window.open('${mensaje.imagen_url}', '_blank')">
                        </div>
                    </div>
                    <div class="message-info">
                        ${isCurrentUser ? 'Tú' : mensaje.remitente_nombre}
                        <span class="ms-2">${formatDateTime(mensaje.fecha_envio)}</span>
                    </div>
                    ${renderMessageStatus(mensaje, isCurrentUser)}
                `;
            } else if (mensaje.tipo === 'UBICACION') {
                const mapId = `map-${mensaje.id || Date.now()}`;
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <div>Ubicación compartida:</div>
                        <div id="${mapId}" class="map-message"></div>
                    </div>
                    <div class="message-info">
                        ${isCurrentUser ? 'Tú' : mensaje.remitente_nombre}
                        <span class="ms-2">${formatDateTime(mensaje.fecha_envio)}</span>
                    </div>
                    ${renderMessageStatus(mensaje, isCurrentUser)}
                `;

                // Inicializar el mapa después de que el elemento se haya añadido al DOM
                setTimeout(() => {
                    if (typeof L !== 'undefined') {
                        const map = L.map(mapId).setView([mensaje.ubicacion_lat, mensaje.ubicacion_lng], 15);
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            maxZoom: 19,
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                        }).addTo(map);

                        L.marker([mensaje.ubicacion_lat, mensaje.ubicacion_lng]).addTo(map);
                    }
                }, 100);
            }
        }

        // Solo añadir al DOM si no es un mensaje de audio (los mensajes de audio se añaden dentro de su propio bloque)
        if (mensaje.tipo !== 'AUDIO') {
            chatMessages.appendChild(messageDiv);
        }
    }

    // Renderizar el estado del mensaje (leído, exportado)
    function renderMessageStatus(mensaje, isCurrentUser) {
        if (!isCurrentUser) return '';

        let statusHtml = '<div class="message-status">';

        if (mensaje.leido) {
            statusHtml += '<span class="status-read"><i class="fas fa-check-double"></i> Leído</span>';
        } else {
            statusHtml += '<span class="status-sent"><i class="fas fa-check"></i> Enviado</span>';
        }

        if (mensaje.exportado_brigada) {
            statusHtml += ' <span class="status-exported"><i class="fas fa-share"></i> Exportado</span>';
        }

        statusHtml += '</div>';
        return statusHtml;
    }

    // Añadir un mensaje del sistema
    function addSystemMessage(message, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message message-system';
        if (isError) {
            messageDiv.style.backgroundColor = '#f8d7da';
            messageDiv.style.color = '#721c24';
        }
        messageDiv.innerHTML = `<div class="message-content">${message}</div>`;
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }

    // Formatear fecha y hora en formato dd-mm-yyyy HH:MM
    function formatDateTime(dateTimeStr) {
        const date = new Date(dateTimeStr);

        // Obtener componentes de la fecha
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Los meses en JS son 0-11
        const year = date.getFullYear();

        // Obtener componentes de la hora
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        // Formatear como dd-mm-yyyy HH:MM
        return `${day}-${month}-${year} ${hours}:${minutes}`;
    }

    // Scroll al final del chat
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
        isScrolledToBottom = true;

        // Ocultar notificación de nuevos mensajes
        if (newMessageNotification) {
            newMessageNotification.style.display = 'none';
            unreadMessages = 0;
        }
    }

    // Comprobar si el usuario está en la parte inferior del chat
    function checkScrollPosition() {
        const scrollPosition = chatMessages.scrollTop + chatMessages.clientHeight;
        const scrollHeight = chatMessages.scrollHeight;

        // Consideramos que está en la parte inferior si está a menos de 100px del final
        const wasAtBottom = isScrolledToBottom;
        isScrolledToBottom = (scrollHeight - scrollPosition) < 100;

        console.log('Posición de scroll:', {
            scrollTop: chatMessages.scrollTop,
            clientHeight: chatMessages.clientHeight,
            scrollHeight: scrollHeight,
            difference: scrollHeight - scrollPosition,
            isScrolledToBottom: isScrolledToBottom
        });

        // Si está en la parte inferior, ocultar notificación
        if (isScrolledToBottom && newMessageNotification) {
            newMessageNotification.style.display = 'none';
            unreadMessages = 0;
        }

        // Si cambió de estado (no estaba abajo y ahora sí), registrarlo
        if (!wasAtBottom && isScrolledToBottom) {
            console.log('Usuario ha scrolleado al final del chat');
        }
    }

    // Mostrar notificación de nuevos mensajes
    function showNewMessageNotification() {
        console.log('Mostrando notificación de nuevos mensajes, isScrolledToBottom:', isScrolledToBottom);

        if (!isScrolledToBottom && newMessageNotification) {
            unreadMessages++;

            // Actualizar el texto de la notificación
            newMessageNotification.innerHTML = `
                <i class="fas fa-arrow-down"></i>
                ${unreadMessages} ${unreadMessages === 1 ? 'nuevo mensaje' : 'nuevos mensajes'}
            `;

            // Mostrar la notificación con animación
            newMessageNotification.style.display = 'block';
            newMessageNotification.style.animation = 'pulse 1s';

            // Reproducir un sonido de notificación
            playNotificationSound();

            console.log('Notificación mostrada, mensajes no leídos:', unreadMessages);
        }
    }

    // Enviar mensaje de texto
    function sendTextMessage() {
        const text = messageInput.value.trim();
        if (!text) return;

        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({
                tipo: 'TEXTO',
                texto: text
            }));

            messageInput.value = '';
            messageInput.focus();
        } else {
            addSystemMessage('No se pudo enviar el mensaje. Conexión no disponible.', true);
        }
    }

    // Iniciar grabación de audio
    function startRecording() {
        // Verificar soporte para grabación de audio
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('Tu navegador no soporta la grabación de audio');
            addSystemMessage('Tu navegador no soporta la grabación de audio', true);
            return;
        }

        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(stream => {
                // Determinar el formato de audio más compatible
                let mimeType = 'audio/webm';

                // Verificar compatibilidad con diferentes formatos
                if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
                    mimeType = 'audio/webm;codecs=opus';
                } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
                    mimeType = 'audio/mp4';
                } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
                    mimeType = 'audio/ogg';
                }

                console.log('Usando formato de audio:', mimeType);

                // Crear el MediaRecorder con el formato adecuado
                mediaRecorder = new MediaRecorder(stream, { mimeType });
                audioChunks = [];

                mediaRecorder.ondataavailable = event => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                        console.log('Chunk de audio recibido:', event.data.size, 'bytes');
                    }
                };

                mediaRecorder.onstop = () => {
                    console.log('Grabación detenida, chunks:', audioChunks.length);

                    if (audioChunks.length === 0) {
                        addSystemMessage('No se grabó ningún audio', true);
                        return;
                    }

                    const audioBlob = new Blob(audioChunks, { type: mimeType });
                    console.log('Blob de audio creado:', audioBlob.size, 'bytes');

                    if (audioBlob.size < 100) {
                        addSystemMessage('El audio grabado está vacío o es demasiado corto', true);
                        return;
                    }

                    sendAudioMessage(audioBlob);
                };

                mediaRecorder.onerror = (event) => {
                    console.error('Error en MediaRecorder:', event.error);
                    addSystemMessage(`Error al grabar audio: ${event.error}`, true);
                };

                // Comenzar a grabar, solicitando datos cada 1 segundo
                mediaRecorder.start(1000);
                isRecording = true;
                recordButton.classList.add('recording');
                recordButton.innerHTML = '<i class="fas fa-stop"></i>';

                addSystemMessage('Grabando audio...');
            })
            .catch(error => {
                console.error('Error al acceder al micrófono:', error);
                addSystemMessage(`No se pudo acceder al micrófono: ${error.message}`, true);
            });
    }

    // Detener grabación de audio
    function stopRecording() {
        if (mediaRecorder && isRecording) {
            mediaRecorder.stop();
            isRecording = false;
            recordButton.classList.remove('recording');
            recordButton.innerHTML = '<i class="fas fa-microphone"></i>';

            // Detener todos los tracks del stream
            mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }
    }

    // Enviar mensaje de audio
    function sendAudioMessage(audioBlob) {
        addSystemMessage('Enviando audio...');

        console.log('Tamaño del audio:', audioBlob.size, 'bytes');
        console.log('Tipo del audio:', audioBlob.type);

        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = () => {
            const base64Audio = reader.result;
            console.log('Audio convertido a base64, longitud:', base64Audio.length);

            // Verificar que el audio tenga contenido
            if (base64Audio.length < 100) {
                addSystemMessage('El audio grabado está vacío o es demasiado corto', true);
                return;
            }

            // Obtener el token CSRF de forma segura
            let csrfToken = '';

            // Intentar obtener del formulario oculto
            const csrfForm = document.getElementById('csrf-form');
            if (csrfForm) {
                const csrfElement = csrfForm.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfElement) {
                    csrfToken = csrfElement.value;
                    console.log('Token CSRF obtenido del formulario oculto');
                }
            }

            // Si no se encontró en el formulario, buscar en cualquier otro lugar
            if (!csrfToken) {
                const csrfElement = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfElement) {
                    csrfToken = csrfElement.value;
                    console.log('Token CSRF obtenido de otro elemento del DOM');
                }
            }

            // Si aún no se encontró, intentar obtener de las cookies
            if (!csrfToken) {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'csrftoken') {
                        csrfToken = value;
                        console.log('Token CSRF obtenido de las cookies');
                        break;
                    }
                }
            }

            console.log('Token CSRF obtenido:', csrfToken ? 'Sí' : 'No');

            // Asegurarse de que la URL sea correcta
            const audioUrl = "{% url 'chat:guardar_audio' %}";
            console.log('URL para guardar audio:', audioUrl);

            // Incluir credenciales en la solicitud
            fetch(audioUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest' // Ayuda a Django a identificar solicitudes AJAX
                },
                credentials: 'include', // Incluir cookies de sesión en solicitudes cross-origin
                body: JSON.stringify({
                    incidencia_id: incidenciaId,
                    audio_data: base64Audio
                })
            })
            .then(response => {
                console.log('Respuesta del servidor:', response.status);
                if (!response.ok) {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    console.log('Audio enviado correctamente:', data);
                    addSystemMessage('Audio enviado correctamente');
                } else {
                    console.error('Error en la respuesta:', data);
                    addSystemMessage(`Error al enviar audio: ${data.error || 'Error desconocido'}`, true);
                }
            })
            .catch(error => {
                console.error('Error al enviar audio:', error);
                addSystemMessage(`Error al enviar el audio: ${error.message}`, true);
            });
        };

        reader.onerror = (error) => {
            console.error('Error al leer el archivo de audio:', error);
            addSystemMessage('Error al procesar el archivo de audio', true);
        };
    }

    // Exportar chat a brigada
    function exportChatToBrigade() {
        if (!confirm('¿Estás seguro de que deseas exportar todo el chat a la brigada?')) {
            return;
        }

        // Obtener el token CSRF de forma segura
        let csrfToken = '';

        // Intentar obtener del formulario oculto
        const csrfForm = document.getElementById('csrf-form');
        if (csrfForm) {
            const csrfElement = csrfForm.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfElement) {
                csrfToken = csrfElement.value;
                console.log('Token CSRF obtenido del formulario oculto');
            }
        }

        // Si no se encontró en el formulario, buscar en cualquier otro lugar
        if (!csrfToken) {
            const csrfElement = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfElement) {
                csrfToken = csrfElement.value;
                console.log('Token CSRF obtenido de otro elemento del DOM');
            }
        }

        // Si aún no se encontró, intentar obtener de las cookies
        if (!csrfToken) {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    csrfToken = value;
                    console.log('Token CSRF obtenido de las cookies');
                    break;
                }
            }
        }

        console.log('Token CSRF obtenido para exportar chat:', csrfToken ? 'Sí' : 'No');

        // Asegurarse de que la URL sea correcta
        const exportUrl = `{% url 'chat:exportar_chat_brigada' incidencia.id %}`;
        console.log('URL para exportar chat:', exportUrl);

        // Incluir credenciales en la solicitud
        fetch(exportUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest' // Ayuda a Django a identificar solicitudes AJAX
            },
            credentials: 'include' // Incluir cookies de sesión en solicitudes cross-origin
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addSystemMessage(`Chat exportado a la brigada. ${data.mensajes_exportados} mensajes compartidos.`);
            } else {
                addSystemMessage(`Error al exportar chat: ${data.error}`, true);
            }
        })
        .catch(error => {
            console.error('Error al exportar chat:', error);
            addSystemMessage('Error al exportar el chat', true);
        });
    }

    // Función para enviar imagen
    function sendImageMessage() {
        const file = imageInput.files[0];
        if (!file) return;

        // Verificar que sea una imagen
        if (!file.type.startsWith('image/')) {
            addSystemMessage('El archivo seleccionado no es una imagen válida.', true);
            return;
        }

        // Mostrar mensaje de carga
        addSystemMessage('Enviando imagen...');

        // Leer la imagen como base64
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onloadend = function() {
            const base64Image = reader.result;

            // Enviar la imagen al servidor
            // Obtener el token CSRF de forma segura
            let csrfToken = '';

            // Intentar obtener del formulario oculto
            const csrfForm = document.getElementById('csrf-form');
            if (csrfForm) {
                const csrfElement = csrfForm.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfElement) {
                    csrfToken = csrfElement.value;
                    console.log('Token CSRF obtenido del formulario oculto');
                }
            }

            // Si no se encontró en el formulario, buscar en cualquier otro lugar
            if (!csrfToken) {
                const csrfElement = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfElement) {
                    csrfToken = csrfElement.value;
                    console.log('Token CSRF obtenido de otro elemento del DOM');
                }
            }

            // Si aún no se encontró, intentar obtener de las cookies
            if (!csrfToken) {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'csrftoken') {
                        csrfToken = value;
                        console.log('Token CSRF obtenido de las cookies');
                        break;
                    }
                }
            }

            console.log('Token CSRF obtenido para imagen:', csrfToken ? 'Sí' : 'No');

            // Asegurarse de que la URL sea correcta
            const imagenUrl = "{% url 'chat:guardar_imagen' %}";
            console.log('URL para guardar imagen:', imagenUrl);

            // Incluir credenciales en la solicitud
            fetch(imagenUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest' // Ayuda a Django a identificar solicitudes AJAX
                },
                credentials: 'include', // Incluir cookies de sesión en solicitudes cross-origin
                body: JSON.stringify({
                    incidencia_id: incidenciaId,
                    imagen_data: base64Image
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Imagen enviada correctamente:', data);
                    // Limpiar el input de imagen
                    imageInput.value = '';
                } else {
                    addSystemMessage(`Error al enviar imagen: ${data.error}`, true);
                }
            })
            .catch(error => {
                console.error('Error al enviar imagen:', error);
                addSystemMessage('Error al enviar la imagen', true);
            });
        };
    }

    // Función para compartir ubicación
    function shareLocation() {
        if (!navigator.geolocation) {
            addSystemMessage('Tu navegador no soporta geolocalización.', true);
            return;
        }

        addSystemMessage('Obteniendo tu ubicación...');

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                // Enviar la ubicación al servidor
                // Obtener el token CSRF de forma segura
                let csrfToken = '';

                // Intentar obtener del formulario oculto
                const csrfForm = document.getElementById('csrf-form');
                if (csrfForm) {
                    const csrfElement = csrfForm.querySelector('[name=csrfmiddlewaretoken]');
                    if (csrfElement) {
                        csrfToken = csrfElement.value;
                        console.log('Token CSRF obtenido del formulario oculto');
                    }
                }

                // Si no se encontró en el formulario, buscar en cualquier otro lugar
                if (!csrfToken) {
                    const csrfElement = document.querySelector('[name=csrfmiddlewaretoken]');
                    if (csrfElement) {
                        csrfToken = csrfElement.value;
                        console.log('Token CSRF obtenido de otro elemento del DOM');
                    }
                }

                // Si aún no se encontró, intentar obtener de las cookies
                if (!csrfToken) {
                    const cookies = document.cookie.split(';');
                    for (let cookie of cookies) {
                        const [name, value] = cookie.trim().split('=');
                        if (name === 'csrftoken') {
                            csrfToken = value;
                            console.log('Token CSRF obtenido de las cookies');
                            break;
                        }
                    }
                }

                console.log('Token CSRF obtenido para ubicación:', csrfToken ? 'Sí' : 'No');

                // Asegurarse de que la URL sea correcta
                const ubicacionUrl = "{% url 'chat:guardar_ubicacion' %}";
                console.log('URL para guardar ubicación:', ubicacionUrl);

                // Incluir credenciales en la solicitud
                fetch(ubicacionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest' // Ayuda a Django a identificar solicitudes AJAX
                    },
                    credentials: 'include', // Incluir cookies de sesión en solicitudes cross-origin
                    body: JSON.stringify({
                        incidencia_id: incidenciaId,
                        lat: lat,
                        lng: lng
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Ubicación enviada correctamente:', data);
                    } else {
                        addSystemMessage(`Error al enviar ubicación: ${data.error}`, true);
                    }
                })
                .catch(error => {
                    console.error('Error al enviar ubicación:', error);
                    addSystemMessage('Error al enviar la ubicación', true);
                });
            },
            function(error) {
                console.error('Error al obtener la ubicación:', error);
                let errorMsg = 'No se pudo obtener tu ubicación.';

                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMsg += ' Has denegado el permiso de ubicación.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMsg += ' La información de ubicación no está disponible.';
                        break;
                    case error.TIMEOUT:
                        errorMsg += ' Se agotó el tiempo de espera para obtener la ubicación.';
                        break;
                }

                addSystemMessage(errorMsg, true);
            }
        );
    }

    // Event listeners
    sendButton.addEventListener('click', sendTextMessage);

    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendTextMessage();
        } else {
            // Enviar notificación de escritura
            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    tipo: 'TYPING'
                }));
            }
        }
    });

    recordButton.addEventListener('click', function() {
        if (isRecording) {
            stopRecording();
        } else {
            startRecording();
        }
    });

    imageButton.addEventListener('click', function() {
        imageInput.click();
    });

    imageInput.addEventListener('change', sendImageMessage);

    chatMessages.addEventListener('scroll', checkScrollPosition);

    if (newMessageNotification) {
        newMessageNotification.addEventListener('click', scrollToBottom);
    }

    // Añadir botón de compartir ubicación al menú contextual
    messageInput.addEventListener('contextmenu', function(e) {
        const shareLocationOption = document.createElement('div');
        shareLocationOption.textContent = 'Compartir ubicación';
        shareLocationOption.style.position = 'absolute';
        shareLocationOption.style.left = e.pageX + 'px';
        shareLocationOption.style.top = e.pageY + 'px';
        shareLocationOption.style.backgroundColor = '#fff';
        shareLocationOption.style.padding = '10px';
        shareLocationOption.style.border = '1px solid #ddd';
        shareLocationOption.style.borderRadius = '5px';
        shareLocationOption.style.cursor = 'pointer';
        shareLocationOption.style.zIndex = '1000';

        shareLocationOption.addEventListener('click', function() {
            document.body.removeChild(shareLocationOption);
            shareLocation();
        });

        document.body.appendChild(shareLocationOption);

        document.addEventListener('click', function removeOption() {
            if (document.body.contains(shareLocationOption)) {
                document.body.removeChild(shareLocationOption);
            }
            document.removeEventListener('click', removeOption);
        });

        e.preventDefault();
    });

    if (exportChatButton) {
        exportChatButton.addEventListener('click', exportChatToBrigade);
    }

    // Inicializar WebSocket al cargar la página
    initWebSocket();
});
</script>
{% endblock %}
