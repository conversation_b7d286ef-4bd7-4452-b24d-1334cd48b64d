# File: backend/rn_rural_project/asgi.py
# -----------------------------------------------

import os
import django

# Configurar Django antes de importar modelos
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rn_rural_project.settings')
django.setup()

from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack

# Importa tus archivos de routing de las apps aquí
import apps.incidents.routing
import apps.locations.routing
import apps.chat.routing
import apps.notifications.routing  # Añadir esta línea

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            apps.incidents.routing.websocket_urlpatterns +
            apps.locations.routing.websocket_urlpatterns +
            apps.chat.routing.websocket_urlpatterns +
            apps.notifications.routing.websocket_urlpatterns  # Añadir esta línea
        )
    ),
})