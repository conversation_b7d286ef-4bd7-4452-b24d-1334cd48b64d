#!/usr/bin/env python3
# --- Archivo: recover_instance_specific.py ---
# Script para recuperar específicamente en /home/<USER>/instance

import os
import subprocess
import sqlite3
from datetime import datetime

def create_photorec_config():
    """Crear configuración específica para PhotoRec."""
    print("📝 Creando configuración específica para PhotoRec...")
    
    # Configuración de PhotoRec para buscar solo SQLite
    config_content = """# PhotoRec configuration for SQLite recovery
fileopt,everything,disable
fileopt,sqlite,enable
fileopt,db,enable
"""
    
    with open('/tmp/photorec.cfg', 'w') as f:
        f.write(config_content)
    
    print("✅ Configuración creada en /tmp/photorec.cfg")

def get_partition_info():
    """Obtener información de la partición donde está /home."""
    print("🔍 Detectando partición de /home/<USER>/instance...")
    
    try:
        # Obtener información de la partición
        result = subprocess.run(['df', '/home/<USER>/instance'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:
                partition = lines[1].split()[0]
                print(f"💾 Partición detectada: {partition}")
                return partition
        
        # Fallback: usar df /home
        result = subprocess.run(['df', '/home'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:
                partition = lines[1].split()[0]
                print(f"💾 Partición detectada (fallback): {partition}")
                return partition
    
    except Exception as e:
        print(f"❌ Error detectando partición: {e}")
    
    return "/dev/sda3"  # Default

def calculate_sector_range():
    """Calcular el rango de sectores para /home/<USER>/instance."""
    print("📊 Calculando rango de sectores...")
    
    try:
        # Obtener información del directorio
        stat_result = os.stat('/home/<USER>/instance')
        print(f"📁 Directorio existe: /home/<USER>/instance")
        
        # Obtener información del sistema de archivos
        statvfs = os.statvfs('/home/<USER>/instance')
        block_size = statvfs.f_frsize
        
        print(f"📊 Tamaño de bloque: {block_size} bytes")
        
        # Para PhotoRec, es mejor buscar en toda la partición
        # pero filtrar por tipo de archivo
        return None
        
    except Exception as e:
        print(f"⚠️ No se pudo calcular rango específico: {e}")
        return None

def create_recovery_script():
    """Crear script específico de recuperación."""
    print("📝 Creando script de recuperación específico...")
    
    partition = get_partition_info()
    
    script_content = f'''#!/bin/bash
# Script de recuperación específico para /home/<USER>/instance

echo "🔍 Recuperación Específica de app.db"
echo "===================================="
echo "📁 Buscando en: /home/<USER>/instance"
echo "💾 Partición: {partition}"
echo ""

# Crear directorio de recuperación
RECOVERY_DIR="$HOME/recovery_instance_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RECOVERY_DIR"
echo "📁 Directorio de recuperación: $RECOVERY_DIR"

# Crear configuración de PhotoRec
cat > /tmp/photorec_instance.cfg << 'EOF'
# PhotoRec configuration for SQLite recovery in instance directory
fileopt,everything,disable
fileopt,sqlite,enable
fileopt,db,enable
EOF

echo ""
echo "⚠️  CONFIGURACIÓN RECOMENDADA:"
echo "   1. Selecciona la partición {partition}"
echo "   2. Elige [File Opt] y habilita solo 'sqlite' y 'db'"
echo "   3. En [Search] elige [Free] para buscar en espacio libre"
echo "   4. Confirma el directorio de destino"
echo ""

read -p "¿Continuar con PhotoRec? (s/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Ss]$ ]]; then
    echo "🚀 Iniciando PhotoRec..."
    
    # Ejecutar PhotoRec con configuración específica
    sudo photorec /log /d "$RECOVERY_DIR" {partition}
    
    echo ""
    echo "🔍 Analizando archivos recuperados..."
    
    # Buscar archivos SQLite recuperados
    find "$RECOVERY_DIR" -type f \\( -name "*.db" -o -name "*.sqlite*" \\) | while read file; do
        echo ""
        echo "📄 Analizando: $file"
        
        # Verificar tamaño
        size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
        echo "  📊 Tamaño: $size bytes"
        
        # Verificar si es una base de datos SQLite válida
        if sqlite3 "$file" "SELECT name FROM sqlite_master WHERE type='table';" 2>/dev/null | grep -q "point"; then
            echo "  ✅ ¡Base de datos con tabla 'point' encontrada!"
            
            # Contar registros
            points=$(sqlite3 "$file" "SELECT COUNT(*) FROM point;" 2>/dev/null)
            images=$(sqlite3 "$file" "SELECT COUNT(*) FROM image;" 2>/dev/null)
            cameras=$(sqlite3 "$file" "SELECT COUNT(*) FROM camera;" 2>/dev/null)
            users=$(sqlite3 "$file" "SELECT COUNT(*) FROM user;" 2>/dev/null)
            
            echo "  📊 Puntos: $points"
            echo "  📊 Imágenes: $images"
            echo "  📊 Cámaras: $cameras"
            echo "  📊 Usuarios: $users"
            
            # Si tiene datos significativos, copiarlo con nombre descriptivo
            if [ "$points" -gt 100 ]; then
                recovered_name="$RECOVERY_DIR/RECOVERED_app_${{points}}points_$(date +%Y%m%d_%H%M%S).db"
                cp "$file" "$recovered_name"
                echo "  💾 ¡COPIADO COMO: $recovered_name"
                echo "  🎉 ¡POSIBLE ARCHIVO ORIGINAL ENCONTRADO!"
            fi
        elif sqlite3 "$file" "SELECT name FROM sqlite_master WHERE type='table';" 2>/dev/null | grep -q "user"; then
            echo "  ⚠️  Base de datos con tabla 'user' (posible fragmento)"
        else
            echo "  ❌ No es una base de datos SQLite válida o está corrupta"
        fi
    done
    
    echo ""
    echo "✅ Recuperación completada"
    echo "📁 Revisa los archivos en: $RECOVERY_DIR"
    echo ""
    echo "🔍 Archivos importantes encontrados:"
    find "$RECOVERY_DIR" -name "RECOVERED_*" -type f
    
else
    echo "❌ Recuperación cancelada"
fi
'''
    
    with open('recover_instance.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('recover_instance.sh', 0o755)
    print("✅ Script creado: recover_instance.sh")

def create_manual_search():
    """Crear búsqueda manual en el directorio específico."""
    print("📝 Creando búsqueda manual...")
    
    script_content = '''#!/bin/bash
# Búsqueda manual en /home/<USER>/instance

echo "🔍 Búsqueda Manual en /home/<USER>/instance"
echo "=============================================="

# Buscar archivos ocultos o temporales
echo "📁 Buscando archivos ocultos..."
find /home/<USER>/instance -name ".*" -type f 2>/dev/null

echo ""
echo "📁 Buscando archivos de respaldo..."
find /home/<USER>/instance -name "*backup*" -type f 2>/dev/null
find /home/<USER>/instance -name "*.bak" -type f 2>/dev/null
find /home/<USER>/instance -name "*.old" -type f 2>/dev/null

echo ""
echo "📁 Buscando archivos SQLite..."
find /home/<USER>/instance -name "*.db*" -type f 2>/dev/null
find /home/<USER>/instance -name "*.sqlite*" -type f 2>/dev/null

echo ""
echo "📁 Listando todos los archivos con fechas..."
ls -la /home/<USER>/instance/

echo ""
echo "📁 Buscando en directorios padre..."
find /home/<USER>"app.db*" -type f 2>/dev/null

echo ""
echo "🔍 Verificando archivos de journal SQLite..."
ls -la /home/<USER>/instance/app.db-* 2>/dev/null || echo "No hay archivos de journal"
'''
    
    with open('manual_search.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('manual_search.sh', 0o755)
    print("✅ Script creado: manual_search.sh")

def main():
    """Función principal."""
    print("🎯 Recuperador Específico para /home/<USER>/instance")
    print("=" * 55)
    
    print("📁 Directorio objetivo: /home/<USER>/instance")
    print("🎯 Archivo objetivo: app.db")
    print("")
    
    # Verificar si el directorio existe
    if not os.path.exists('/home/<USER>/instance'):
        print("❌ El directorio /home/<USER>/instance no existe")
        return
    
    print("🔧 Opciones disponibles:")
    print("1. 🔍 Búsqueda manual rápida")
    print("2. 📝 Crear script de PhotoRec específico")
    print("3. 🚀 Ejecutar PhotoRec directamente")
    print("4. ❌ Salir")
    
    choice = input("\n¿Qué opción prefieres? (1-4): ").strip()
    
    if choice == '1':
        create_manual_search()
        print("\n🚀 Ejecuta: ./manual_search.sh")
        
    elif choice == '2':
        create_photorec_config()
        create_recovery_script()
        print("\n🚀 Ejecuta: ./recover_instance.sh")
        
    elif choice == '3':
        partition = get_partition_info()
        recovery_dir = f"~/recovery_instance_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"\n🚀 Comando directo:")
        print(f"mkdir -p {recovery_dir}")
        print(f"sudo photorec /log /d {recovery_dir} {partition}")
        print("\nEn PhotoRec:")
        print("- Selecciona [File Opt] → habilita solo 'sqlite' y 'db'")
        print("- Selecciona [Search] → [Free] para buscar en espacio libre")
        
    else:
        print("❌ Saliendo")

if __name__ == "__main__":
    main()
