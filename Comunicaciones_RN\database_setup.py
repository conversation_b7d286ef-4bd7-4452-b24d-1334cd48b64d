from app import create_app
from extensions import db
from models import User, Node
from werkzeug.security import generate_password_hash
import logging

# Configurar logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

app = create_app()

with app.app_context():
    logger.debug(f"App: {app}")
    logger.debug(f"DB: {db}")

    logger.debug("Creating all tables in the database.")
    db.create_all()

    logger.debug("Checking if the User table is empty.")
    try:
        if User.query.first() is None:
            logger.debug("User table is empty, adding initial data.")
            
            # Añadir datos iniciales solo si la tabla User está vacía
            admin = User(username='admin', password=generate_password_hash('admin', method='pbkdf2:sha256'), role='admin', node_access='all',
                         first_name='Admin', last_name='User', badge_number='0001', organization='Admin')
            db.session.add(admin)

            # Ejemplo de nodos
            root_node = Node(name='Comunicaciones RN')
            db.session.add(root_node)
            agency_node = Node(name='Agencia', parent=root_node)
            db.session.add(agency_node)
            police_node = Node(name='Policia', parent=root_node)
            db.session.add(police_node)
            
            # Unidades Regionales
            ur1_node = Node(name='Unidad Regional 1', parent=police_node)
            db.session.add(ur1_node)
            ur2_node = Node(name='Unidad Regional 2', parent=police_node)
            db.session.add(ur2_node)
            ur3_node = Node(name='Unidad Regional 3', parent=police_node)
            db.session.add(ur3_node)
            ur4_node = Node(name='Unidad Regional 4', parent=police_node)
            db.session.add(ur4_node)
            ur5_node = Node(name='Unidad Regional 5', parent=police_node)
            db.session.add(ur5_node)
            ur6_node = Node(name='Unidad Regional 6', parent=police_node)
            db.session.add(ur6_node)

            # Rojo UR5
            cities_ur5 = ['Catriel', 'Campo Grande', 'Cinco Saltos', 'Cipolletti', 'Fernández Oro']
            for city in cities_ur5:
                db.session.add(Node(name=city, parent=ur5_node))

            # Celeste UR2
            cities_ur2 = ['General Roca', 'Allen', 'Cervantes', 'Ingeniero Huergo', 'Mainqué', 'General Enrique Godoy', 'Villa Regina', 'Chichinales']
            for city in cities_ur2:
                db.session.add(Node(name=city, parent=ur2_node))

            # Amarillo UR4
            cities_ur4 = ['Choele Choel', 'Pomona', 'Luis Beltrán', 'Lamarque', 'Belisle', 'Chimpay']
            for city in cities_ur4:
                db.session.add(Node(name=city, parent=ur4_node))

            # Violeta UR3
            cities_ur3 = ['San Carlos de Bariloche', 'Villa Mascardi', 'Villa Llanquín', 'Dina Huapi', 'Pilcaniyeu', 'Comallo']
            for city in cities_ur3:
                db.session.add(Node(name=city, parent=ur3_node))

            # Naranja UR6
            cities_ur6 = ['Ramos Mexía', 'Valcheta', 'Sierra Colorada', 'Los Menucos', 'Maquinchao', 'Ingeniero Jacobacci']
            for city in cities_ur6:
                db.session.add(Node(name=city, parent=ur6_node))

            # Verde UR1
            cities_ur1 = ['Viedma', 'General Conesa', 'San Antonio Oeste', 'Las Grutas', 'Puerto San Antonio Este', 'Guardia Mitre']
            for city in cities_ur1:
                db.session.add(Node(name=city, parent=ur1_node))

            db.session.commit()
            logger.debug("Initial data added.")
        else:
            logger.debug("Initial data already exists.")
    except Exception as e:
        logger.error(f"Error querying User table: {e}")

    # Verificación para listar todos los nodos
    try:
        nodes = Node.query.all()
        if nodes:
            logger.debug("Listing all nodes:")
            for node in nodes:
                logger.debug(f"Node: {node.name}, Parent: {node.parent.name if node.parent else 'None'}")
        else:
            logger.debug("No nodes found in the database.")
    except Exception as e:
        logger.error(f"Error querying Node table: {e}")
