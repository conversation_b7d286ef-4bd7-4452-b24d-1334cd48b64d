#!/usr/bin/env python3
# --- Archivo: complete_migration.py ---
# Script para completar la migración con todos los datos

import sqlite3
import shutil
from datetime import datetime

def complete_missing_data():
    """Completar los datos que faltan en la migración."""
    print("🔧 Completando datos faltantes...")
    
    backup_path = "instance/app.db.bkp"
    current_path = "instance/app.db"
    
    # Hacer backup de seguridad
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    safety_backup = f"safety_backup_complete_{timestamp}.db"
    shutil.copy2(current_path, safety_backup)
    print(f"✅ Backup de seguridad: {safety_backup}")
    
    # Conectar a ambas bases de datos
    backup_conn = sqlite3.connect(backup_path)
    backup_cursor = backup_conn.cursor()
    
    current_conn = sqlite3.connect(current_path)
    current_cursor = current_conn.cursor()
    
    # 1. Agregar columnas faltantes a la tabla image
    print("\n🖼️ Actualizando tabla image...")
    
    # Verificar si las columnas existen
    current_cursor.execute("PRAGMA table_info(image);")
    current_columns = [col[1] for col in current_cursor.fetchall()]
    
    if 'original_filename' not in current_columns:
        current_cursor.execute("ALTER TABLE image ADD COLUMN original_filename VARCHAR(256);")
        print("  ✅ Columna original_filename agregada")
    
    if 'upload_timestamp' not in current_columns:
        current_cursor.execute("ALTER TABLE image ADD COLUMN upload_timestamp DATETIME;")
        print("  ✅ Columna upload_timestamp agregada")
    
    if 'notes' not in current_columns:
        current_cursor.execute("ALTER TABLE image ADD COLUMN notes TEXT;")
        print("  ✅ Columna notes agregada")
    
    # 2. Actualizar datos de imágenes
    print("\n📥 Actualizando datos de imágenes...")
    
    backup_cursor.execute("""
        SELECT id, original_filename, upload_timestamp, notes 
        FROM image 
        WHERE original_filename IS NOT NULL OR upload_timestamp IS NOT NULL OR notes IS NOT NULL
    """)
    
    image_updates = backup_cursor.fetchall()
    
    for image_id, original_filename, upload_timestamp, notes in image_updates:
        current_cursor.execute("""
            UPDATE image 
            SET original_filename = ?, upload_timestamp = ?, notes = ?
            WHERE id = ?
        """, (original_filename, upload_timestamp, notes, image_id))
    
    print(f"  ✅ {len(image_updates)} imágenes actualizadas con datos adicionales")
    
    # 3. Agregar columnas faltantes a la tabla camera
    print("\n📷 Actualizando tabla camera...")
    
    current_cursor.execute("PRAGMA table_info(camera);")
    current_columns = [col[1] for col in current_cursor.fetchall()]
    
    if 'annotations_json' not in current_columns:
        current_cursor.execute("ALTER TABLE camera ADD COLUMN annotations_json TEXT;")
        print("  ✅ Columna annotations_json agregada")
    
    # 4. Actualizar datos de cámaras
    print("\n📥 Actualizando datos de cámaras...")
    
    backup_cursor.execute("""
        SELECT id, annotations_json 
        FROM camera 
        WHERE annotations_json IS NOT NULL
    """)
    
    camera_updates = backup_cursor.fetchall()
    
    for camera_id, annotations_json in camera_updates:
        current_cursor.execute("""
            UPDATE camera 
            SET annotations_json = ?
            WHERE id = ?
        """, (annotations_json, camera_id))
    
    print(f"  ✅ {len(camera_updates)} cámaras actualizadas con anotaciones")
    
    # 5. Crear índices importantes del backup original
    print("\n📇 Creando índices importantes...")
    
    indexes_to_create = [
        ("ix_user_username", "user", "username"),
        ("ix_user_email", "user", "email"),
        ("ix_point_city", "point", "city"),
        ("ix_point_source", "point", "source"),
        ("ix_point_status", "point", "status"),
        ("ix_image_point_id", "image", "point_id"),
        ("ix_image_user_id", "image", "user_id"),
        ("ix_camera_point_id", "camera", "point_id")
    ]
    
    for index_name, table_name, column_name in indexes_to_create:
        try:
            current_cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name});")
            print(f"  ✅ Índice {index_name} creado")
        except Exception as e:
            print(f"  ⚠️ Error creando índice {index_name}: {e}")
    
    # 6. Commit cambios
    current_conn.commit()
    
    # 7. Verificar resultado
    print("\n📊 Verificando resultado...")
    
    # Verificar imágenes con datos adicionales
    current_cursor.execute("SELECT COUNT(*) FROM image WHERE original_filename IS NOT NULL;")
    original_filename_count = current_cursor.fetchone()[0]
    
    current_cursor.execute("SELECT COUNT(*) FROM image WHERE notes IS NOT NULL;")
    notes_count = current_cursor.fetchone()[0]
    
    current_cursor.execute("SELECT COUNT(*) FROM camera WHERE annotations_json IS NOT NULL;")
    camera_annotations_count = current_cursor.fetchone()[0]
    
    print(f"  📄 Imágenes con original_filename: {original_filename_count}")
    print(f"  📄 Imágenes con notes: {notes_count}")
    print(f"  📄 Cámaras con annotations: {camera_annotations_count}")
    
    # Verificar índices
    current_cursor.execute("SELECT name FROM sqlite_master WHERE type='index';")
    current_indexes = [i[0] for i in current_cursor.fetchall()]
    print(f"  📇 Índices totales: {len(current_indexes)}")
    
    # Verificar tamaño final
    current_conn.close()
    backup_conn.close()
    
    import os
    final_size = os.path.getsize(current_path)
    original_size = os.path.getsize(backup_path)
    
    print(f"\n📊 Tamaños finales:")
    print(f"  📄 Original: {original_size:,} bytes")
    print(f"  📄 Migrado: {final_size:,} bytes")
    print(f"  📉 Diferencia: {original_size - final_size:,} bytes")
    
    return True

def verify_complete_migration():
    """Verificar que la migración esté completa."""
    print("\n🔍 Verificando migración completa...")
    
    current_path = "instance/app.db"
    conn = sqlite3.connect(current_path)
    cursor = conn.cursor()
    
    # Verificar conteos
    cursor.execute("SELECT COUNT(*) FROM user;")
    user_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM point;")
    point_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM image;")
    image_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM camera;")
    camera_count = cursor.fetchone()[0]
    
    print(f"📊 Datos migrados:")
    print(f"  👥 Usuarios: {user_count}")
    print(f"  📍 Puntos: {point_count}")
    print(f"  🖼️ Imágenes: {image_count}")
    print(f"  📷 Cámaras: {camera_count}")
    
    # Verificar usuarios
    cursor.execute("SELECT username, role FROM user ORDER BY username;")
    users = cursor.fetchall()
    print(f"\n👥 Usuarios disponibles:")
    for user in users:
        print(f"  - {user[0]} ({user[1]})")
    
    # Verificar algunas estadísticas
    cursor.execute("SELECT COUNT(DISTINCT city) FROM point WHERE city IS NOT NULL;")
    city_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(DISTINCT source) FROM point WHERE source IS NOT NULL;")
    source_count = cursor.fetchone()[0]
    
    print(f"\n📊 Estadísticas:")
    print(f"  🏙️ Ciudades únicas: {city_count}")
    print(f"  📡 Fuentes únicas: {source_count}")
    
    # Verificar datos adicionales
    cursor.execute("SELECT COUNT(*) FROM image WHERE original_filename IS NOT NULL;")
    original_filename_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM image WHERE notes IS NOT NULL;")
    notes_count = cursor.fetchone()[0]
    
    print(f"  📄 Imágenes con nombre original: {original_filename_count}")
    print(f"  📝 Imágenes con notas: {notes_count}")
    
    conn.close()
    
    return True

def main():
    """Función principal."""
    print("🔧 Completador de Migración")
    print("=" * 35)
    
    print("⚠️ Este proceso va a:")
    print("  1. Agregar columnas faltantes (original_filename, notes, etc.)")
    print("  2. Migrar todos los datos adicionales del backup")
    print("  3. Crear índices importantes para rendimiento")
    print("  4. Verificar que todo esté completo")
    
    response = input("\n¿Continuar? (s/n): ").strip().lower()
    
    if response in ['s', 'si', 'y', 'yes']:
        try:
            if complete_missing_data():
                if verify_complete_migration():
                    print("\n🎉 ¡Migración completada al 100%!")
                    print("\n🚀 Próximos pasos:")
                    print("   1. Reiniciar aplicación: systemctl restart relevamiento")
                    print("   2. Verificar funcionamiento: python3 test_login.py")
                    print("   3. Probar aplicación web")
                    print("   4. Crear templates HTML para gestión de usuarios")
                    print("\n✅ Ahora tienes TODOS los datos originales + sistema de permisos")
                else:
                    print("\n⚠️ Migración completada pero con advertencias")
            else:
                print("\n❌ Error completando migración")
        except Exception as e:
            print(f"\n❌ Error: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ Operación cancelada")

if __name__ == "__main__":
    main()
