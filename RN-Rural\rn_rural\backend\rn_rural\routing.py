# File: backend/rn_rural/routing.py
# -----------------------------------------------

from django.urls import path
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from apps.incidents.consumers import IncidentsConsumer, IncidenciaConsumer

websocket_urlpatterns = [
    path('ws/incidents/', IncidentsConsumer.as_asgi()),
    path('ws/incidencia/<int:incidencia_id>/', IncidenciaConsumer.as_asgi()),
]

application = ProtocolTypeRouter({
    'websocket': AuthMiddlewareStack(
        URLRouter(
            websocket_urlpatterns
        )
    ),
})
