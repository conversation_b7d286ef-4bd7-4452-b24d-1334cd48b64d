<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redireccionando al Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .redirect-container {
            max-width: 500px;
            width: 100%;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Cargando...</span>
        </div>
        <h2>Redireccionando al Dashboard</h2>
        <p class="lead">Por favor espere mientras lo redirigimos al panel de administración...</p>
        <div id="token-info" class="mt-4 text-muted small"></div>
        <div class="mt-4">
            <a href="/web/dashboard" class="btn btn-primary">Ir al Dashboard</a>
        </div>
    </div>

    <script>
        // Obtener el token de la URL (si está en formato JSON)
        const urlParams = new URLSearchParams(window.location.search);
        const tokenParam = urlParams.get('token');
        
        // Función para extraer el token del texto JSON
        function extractToken(jsonText) {
            try {
                const data = JSON.parse(jsonText);
                return data.access_token;
            } catch (e) {
                return null;
            }
        }
        
        // Función para guardar el token en localStorage
        function saveToken(token) {
            if (token) {
                localStorage.setItem('admin_access_token', token);
                document.getElementById('token-info').textContent = 'Token guardado correctamente';
                
                // Redirigir al dashboard después de 2 segundos
                setTimeout(() => {
                    window.location.href = '/web/dashboard';
                }, 2000);
            } else {
                document.getElementById('token-info').textContent = 'No se pudo obtener el token';
            }
        }
        
        // Procesar el contenido de la página si parece ser JSON
        document.addEventListener('DOMContentLoaded', () => {
            const bodyText = document.body.textContent.trim();
            if (bodyText.startsWith('{') && bodyText.endsWith('}')) {
                try {
                    const jsonData = JSON.parse(bodyText);
                    if (jsonData.access_token) {
                        saveToken(jsonData.access_token);
                        
                        // Limpiar el contenido JSON de la página
                        document.body.innerHTML = document.querySelector('.redirect-container').outerHTML;
                    }
                } catch (e) {
                    console.error('Error al parsear JSON:', e);
                }
            } else if (tokenParam) {
                // Si hay un token en la URL, usarlo
                saveToken(tokenParam);
            } else {
                // Verificar si ya hay un token en localStorage
                const storedToken = localStorage.getItem('admin_access_token');
                if (storedToken) {
                    document.getElementById('token-info').textContent = 'Usando token existente';
                    setTimeout(() => {
                        window.location.href = '/web/dashboard';
                    }, 2000);
                } else {
                    document.getElementById('token-info').textContent = 'No se encontró ningún token';
                }
            }
        });
    </script>
</body>
</html>
