<!-- /app/templates/acta.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import format_participant %}
{% block title %}Acta Nº {{ acta_number }}{% endblock %}

{% block content %}
<div style="text-align: center; border: 3px solid #000; padding: 40px; width: 80%; margin: auto;">
  <h1>Acta Nº {{ acta_number }}</h1>
  <p>
    En la ciudad de {{ city }}, provincia de {{ province }}, a los {{ day }} del mes de {{ month }} del año {{ year }}, 
    siendo la hora {{ time }}, se hacen presentes:
  </p>
  <ul style="list-style: none; padding: 0; font-size: 20px;">
    {% for participant in participants %}
      <li>{{ format_participant(participant, current_user) }}</li>
    {% endfor %}
  </ul>
  <p style="font-size: 20px; margin-top: 20px;">
    Quienes se reúnen para realizar: <strong>{{ act_description }}</strong>
  </p>
  <p style="font-size: 20px;">
    No habiendo más asuntos, se da por finalizada la presente acta, firmando los concurrentes como constancia:
  </p>
  <br>
  <div style="display: flex; justify-content: space-around; margin-top: 40px;">
    {% for participant in participants %}
      <div style="text-align: center;">
        <hr style="width: 200px;">
        <p>{{ format_participant(participant, current_user) }}</p>
      </div>
    {% endfor %}
  </div>
</div>

<div class="container my-4 text-center">
  {% set pdf_url = url_for('routes.acta',
      acta_number=acta_number,
      city=city,
      province=province,
      day=day,
      month=month,
      year=year,
      time=time,
      act_description=act_description,
      participants=participants|map(attribute='id')|join(',')
  ) %}
  <a href="{{ pdf_url }}" class="btn btn-primary">Descargar Acta en PDF</a>
</div>
{% endblock %}