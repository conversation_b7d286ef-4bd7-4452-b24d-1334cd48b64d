<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Panel de Taxis{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        header {
            background-color: #343a40;
            color: white;
            padding: 1rem;
        }
        header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        header nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header nav a {
            color: #ced4da;
            text-decoration: none;
            margin-left: 15px;
        }
        header nav a:hover {
            color: white;
        }
        main {
            flex: 1;
            padding: 2rem;
        }
        footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 1rem;
            margin-top: auto;
        }
    </style>
    {% block head_extra %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#">Panel de Administración de Taxis</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        {% if current_user %}
                            <li class="nav-item">
                                <span class="nav-link">Bienvenido, {{ current_user.email }}
                                    {% if request.state.user_roles_from_token %}
                                        ({{ request.state.user_roles_from_token[0] }})
                                    {% else %}
                                        (Sin rol)
                                    {% endif %}
                                </span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('admin_dashboard_route') }}">
                                    <i class="bi bi-speedometer2"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('users_management_route') }}">
                                    <i class="bi bi-people"></i> Usuarios
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('vehicles_management_route') }}">
                                    <i class="bi bi-taxi-front"></i> Vehículos
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('trips_management_route') }}">
                                    <i class="bi bi-geo-alt"></i> Viajes
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('fare_settings_route') }}">
                                    <i class="bi bi-cash-coin"></i> Tarifas
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('realtime_map_route') }}">
                                    <i class="bi bi-map"></i> Mapa
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('web_logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> Cerrar Sesión
                                </a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('login_page') }}">Iniciar Sesión</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container">
        {% block content %}{% endblock %}
    </main>

    <footer>
        <p>&copy; 2023 Sistema de Taxis</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Guardar token en localStorage si está presente en la respuesta de login
        // Esto es un ejemplo muy básico, en una app real usarías un manejo más robusto.
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        if (token) {
            localStorage.setItem('taxi_admin_token', token);
            // Opcional: limpiar el token de la URL
            // window.history.replaceState({}, document.title, window.location.pathname);
        }

        async function fetchWithAuth(url, options = {}) {
            const authToken = localStorage.getItem('taxi_admin_token');
            const headers = {
                ...options.headers,
                'Authorization': `Bearer ${authToken}`
            };
            const response = await fetch(url, { ...options, headers });
            if (response.status === 401) { // No autorizado o token expirado
                localStorage.removeItem('taxi_admin_token');
                window.location.href = "{{ url_for('login_page') }}"; // Redirigir a login
            }
            return response;
        }
    </script>
    {% block scripts_extra %}{% endblock %}
</body>
</html>
