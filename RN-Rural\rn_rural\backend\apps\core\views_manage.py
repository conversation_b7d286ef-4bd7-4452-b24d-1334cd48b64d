# /backend/apps/core/views_manage.py  (nuevo archivo)
# ---------------------------------------------------
from django.contrib.auth.mixins import UserPassesTestMixin, LoginRequiredMixin
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from apps.users.models import User
from .forms import UserManageCreateForm, UserManageUpdateForm

class SuperuserRequired(UserPassesTestMixin):
    def test_func(self):
        return self.request.user.is_superuser

class UserListView(LoginRequiredMixin, SuperuserRequired, ListView):
    model = User
    template_name = "core/user_list.html"
    context_object_name = "users"
    paginate_by = 20

class UserCreateView(LoginRequiredMixin, SuperuserRequired, CreateView):
    model = User
    form_class = UserManageCreateForm
    template_name = "core/user_form.html"
    success_url = reverse_lazy("user_list")

class UserUpdateView(LoginRequiredMixin, SuperuserRequired, UpdateView):
    model = User
    form_class = UserManageUpdateForm
    template_name = "core/user_form.html"
    success_url = reverse_lazy("user_list")

class UserDeleteView(LoginRequiredMixin, SuperuserRequired, DeleteView):
    model = User
    template_name = "core/user_confirm_delete.html"
    success_url = reverse_lazy("user_list")
