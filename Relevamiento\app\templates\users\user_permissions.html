<!-- app/templates/users/user_permissions.html -->
{% extends "users/base_users.html" %}

{% block title %}Permisos de {{ user.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-key"></i>
                    Permisos de {{ user.username }}
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('users.edit_user', user_id=user.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i>
                        Editar Usuario
                    </a>
                    <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Volver a Lista
                    </a>
                </div>
            </div>
            
            <!-- Información del Usuario -->
            <div class="user-summary-card mb-4">
                <div class="row">
                    <div class="col-md-2">
                        <div class="user-avatar-large">
                            {{ user.username[0].upper() }}
                        </div>
                    </div>
                    <div class="col-md-10">
                        <h4>{{ user.username }}</h4>
                        <p class="mb-1">
                            <span class="role-badge role-{{ user.role }}">{{ user.role }}</span>
                            <span class="ms-3 {{ 'status-active' if user.is_active else 'status-inactive' }}">
                                <i class="fas fa-{{ 'check-circle' if user.is_active else 'times-circle' }}"></i>
                                {{ 'Activo' if user.is_active else 'Inactivo' }}
                            </span>
                        </p>
                        <p class="text-muted mb-0">
                            <i class="fas fa-envelope"></i> {{ user.email or 'Sin email' }} |
                            <i class="fas fa-calendar"></i> Creado: {{ user.created_at.strftime('%d/%m/%Y') if user.created_at else 'N/A' }}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Resumen de Permisos -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="permission-summary-card">
                        <div class="permission-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="permission-info">
                            <h5>Rol</h5>
                            <p class="role-badge role-{{ user.role }}">{{ user.role }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="permission-summary-card">
                        <div class="permission-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="permission-info">
                            <h5>Ciudades</h5>
                            <p>{{ user.city_permissions|length if user.city_permissions else 'Todas' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="permission-summary-card">
                        <div class="permission-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="permission-info">
                            <h5>Fuentes</h5>
                            <p>{{ user.source_permissions|length if user.source_permissions else 'Todas' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="permission-summary-card">
                        <div class="permission-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="permission-info">
                            <h5>Permisos Especiales</h5>
                            <p>{{ user.permissions|length if user.permissions else '0' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Permisos por Rol -->
            <div class="permissions-section mb-4">
                <h4><i class="fas fa-shield-alt"></i> Permisos por Rol</h4>
                <div class="role-permissions-card">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Puntos</h6>
                            <div class="permission-list">
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor', 'operador', 'visualizador'] else 'inactive' }}">
                                    <i class="fas fa-eye"></i>
                                    <span>Ver puntos</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor', 'operador', 'visualizador'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor', 'operador'] else 'inactive' }}">
                                    <i class="fas fa-edit"></i>
                                    <span>Editar puntos</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor', 'operador'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor'] else 'inactive' }}">
                                    <i class="fas fa-plus"></i>
                                    <span>Crear puntos</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador'] else 'inactive' }}">
                                    <i class="fas fa-trash"></i>
                                    <span>Eliminar puntos</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador'] else '✗' }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Cámaras</h6>
                            <div class="permission-list">
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor', 'operador', 'visualizador'] else 'inactive' }}">
                                    <i class="fas fa-eye"></i>
                                    <span>Ver cámaras</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor', 'operador', 'visualizador'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor', 'operador'] else 'inactive' }}">
                                    <i class="fas fa-edit"></i>
                                    <span>Editar cámaras</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor', 'operador'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor'] else 'inactive' }}">
                                    <i class="fas fa-plus"></i>
                                    <span>Crear cámaras</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador'] else 'inactive' }}">
                                    <i class="fas fa-trash"></i>
                                    <span>Eliminar cámaras</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador'] else '✗' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>Reportes</h6>
                            <div class="permission-list">
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor', 'visualizador'] else 'inactive' }}">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Ver reportes</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor', 'visualizador'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador', 'supervisor'] else 'inactive' }}">
                                    <i class="fas fa-download"></i>
                                    <span>Exportar datos</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador', 'supervisor'] else '✗' }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Administración</h6>
                            <div class="permission-list">
                                <div class="permission-item {{ 'active' if user.role in ['administrador'] else 'inactive' }}">
                                    <i class="fas fa-users"></i>
                                    <span>Gestionar usuarios</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador'] else '✗' }}</span>
                                </div>
                                <div class="permission-item {{ 'active' if user.role in ['administrador'] else 'inactive' }}">
                                    <i class="fas fa-cogs"></i>
                                    <span>Configuración del sistema</span>
                                    <span class="permission-status">{{ '✓' if user.role in ['administrador'] else '✗' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Restricciones Geográficas -->
            <div class="row">
                <div class="col-md-6">
                    <div class="permissions-section">
                        <h4><i class="fas fa-map-marker-alt"></i> Ciudades Permitidas</h4>
                        <div class="geographic-permissions-card">
                            {% if user.city_permissions %}
                            <div class="permission-chips">
                                {% for perm in user.city_permissions %}
                                <span class="permission-chip city-chip">
                                    <i class="fas fa-map-marker-alt"></i>
                                    {{ perm.city }}
                                    <small class="text-muted">desde {{ perm.created_at.strftime('%d/%m/%Y') if perm.created_at else 'N/A' }}</small>
                                </span>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="no-restrictions">
                                <i class="fas fa-globe"></i>
                                <p>Sin restricciones geográficas</p>
                                <small class="text-muted">El usuario puede acceder a datos de todas las ciudades</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="permissions-section">
                        <h4><i class="fas fa-database"></i> Fuentes Permitidas</h4>
                        <div class="geographic-permissions-card">
                            {% if user.source_permissions %}
                            <div class="permission-chips">
                                {% for perm in user.source_permissions %}
                                <span class="permission-chip source-chip">
                                    <i class="fas fa-database"></i>
                                    {{ perm.source }}
                                    <small class="text-muted">desde {{ perm.created_at.strftime('%d/%m/%Y') if perm.created_at else 'N/A' }}</small>
                                </span>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="no-restrictions">
                                <i class="fas fa-database"></i>
                                <p>Sin restricciones de fuente</p>
                                <small class="text-muted">El usuario puede acceder a datos de todas las fuentes</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Permisos Especiales -->
            {% if user.permissions %}
            <div class="permissions-section mt-4">
                <h4><i class="fas fa-star"></i> Permisos Especiales</h4>
                <div class="special-permissions-card">
                    <div class="row">
                        {% for permission in user.permissions %}
                        <div class="col-md-6 mb-3">
                            <div class="special-permission-item">
                                <div class="permission-header">
                                    <i class="fas fa-{{ 'check' if permission.permission_value else 'times' }}"></i>
                                    <strong>{{ permission.permission_type }}</strong>
                                </div>
                                <div class="permission-details">
                                    <span class="permission-value {{ 'active' if permission.permission_value else 'inactive' }}">
                                        {{ 'Habilitado' if permission.permission_value else 'Deshabilitado' }}
                                    </span>
                                    <small class="text-muted d-block">
                                        Actualizado: {{ permission.updated_at.strftime('%d/%m/%Y %H:%M') if permission.updated_at else 'N/A' }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Estadísticas de Acceso -->
            <div class="permissions-section mt-4">
                <h4><i class="fas fa-chart-line"></i> Estadísticas de Acceso</h4>
                <div class="access-stats-card">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <h5>Puntos Accesibles</h5>
                                    <p class="stat-number">
                                        {% if user.city_permissions %}
                                        {{ accessible_points_count or 'Calculando...' }}
                                        {% else %}
                                        Todos
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="stat-info">
                                    <h5>Cámaras Accesibles</h5>
                                    <p class="stat-number">
                                        {% if user.city_permissions or user.source_permissions %}
                                        {{ accessible_cameras_count or 'Calculando...' }}
                                        {% else %}
                                        Todas
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-city"></i>
                                </div>
                                <div class="stat-info">
                                    <h5>Ciudades</h5>
                                    <p class="stat-number">
                                        {{ user.city_permissions|length if user.city_permissions else 'Todas' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="stat-info">
                                    <h5>Fuentes</h5>
                                    <p class="stat-number">
                                        {{ user.source_permissions|length if user.source_permissions else 'Todas' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Acciones -->
            <div class="permissions-actions mt-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            Volver a Lista
                        </a>
                    </div>
                    
                    <div>
                        {% if current_user.can_manage_users() %}
                        <a href="{{ url_for('users.edit_user', user_id=user.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i>
                            Editar Permisos
                        </a>
                        
                        <button type="button" class="btn btn-info" onclick="exportPermissions()">
                            <i class="fas fa-download"></i>
                            Exportar Permisos
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Exportar permisos del usuario
function exportPermissions() {
    const userData = {
        username: '{{ user.username }}',
        role: '{{ user.role }}',
        is_active: {{ 'true' if user.is_active else 'false' }},
        city_permissions: [
            {% for perm in user.city_permissions %}
            '{{ perm.city }}',
            {% endfor %}
        ],
        source_permissions: [
            {% for perm in user.source_permissions %}
            '{{ perm.source }}',
            {% endfor %}
        ],
        special_permissions: [
            {% for perm in user.permissions %}
            {
                type: '{{ perm.permission_type }}',
                value: {{ 'true' if perm.permission_value else 'false' }}
            },
            {% endfor %}
        ]
    };
    
    const dataStr = JSON.stringify(userData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `permisos_${userData.username}_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}
</script>
{% endblock %}
