// main.js

import { socket, safeEmit, logToFile } from './socketConnection.js';
import * as chat from './chat.js';
import * as audio from './audio.js';
import * as fileTransfer from './fileTransfer.js';
import * as userList from './userList.js';

document.addEventListener('DOMContentLoaded', function() {

  // Cualquier inicialización que *absolutamente* necesite acceso a múltiples
  // módulos podría ir aquí, PERO es mejor evitarlo si es posible.  Si un módulo
  // necesita inicializarse, es mejor que lo haga él mismo al importarse, o que
  // exponga una función init() que se llame desde aquí.  En este caso, todos
  // los módulos se auto-inicializan (registran sus event listeners, etc.)
  // al ser importados, así que no necesitamos hacer nada especial aquí.

  // Ejemplo de inicialización explícita (si fuera necesario):
  // chat.init();
  // audio.init();
  // ...

  // La lógica que *realmente* no pertenece a ningún otro módulo podría ir aquí.
  // Por ejemplo, si tuvieras un botón de "desconexión global" que no
  // perteneciera a un módulo específico, podrías poner el listener aquí:
  //
  // const disconnectButton = document.getElementById('disconnectButton');
  // disconnectButton.addEventListener('click', () => {
  //   socket.disconnect(); // O safeEmit('disconnect'); si quieres usar safeEmit
  // });

    // --- Salir de la sala al cerrar la ventana ---.  Esto es global a la aplicacion, no a un modulo en particular
    window.addEventListener('beforeunload', function () {
      const nodeId = parseInt(document.body.dataset.nodeId);
      const username = document.body.dataset.username;
      safeEmit('leave', { username: username, node_id: nodeId });
  });

});