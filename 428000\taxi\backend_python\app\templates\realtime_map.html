{% extends "base_layout.html" %}

{% block title %}Mapa en Tiempo Real - Panel de Taxis{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" integrity="sha256-kLaT2GOSpHechhsozzB+flnD+zUyjE2LlfWPgU04xyI=" crossorigin=""/>
<style>
    #map {
        height: 600px;
        width: 100%;
        border-radius: 5px;
    }
    .map-container {
        position: relative;
    }
    .map-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        background-color: white;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .vehicle-info {
        max-height: 600px;
        overflow-y: auto;
    }
    .vehicle-item {
        cursor: pointer;
        transition: background-color 0.3s;
    }
    .vehicle-item:hover {
        background-color: #f8f9fa;
    }
    .vehicle-item.active {
        background-color: #e9ecef;
        border-left: 3px solid #0d6efd;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .status-available {
        background-color: #28a745;
    }
    .status-busy {
        background-color: #dc3545;
    }
    .status-idle {
        background-color: #ffc107;
    }
    .filter-buttons {
        margin-bottom: 15px;
    }
    .filter-buttons .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }
    .stats-card {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Mapa en Tiempo Real</h2>
                <div>
                    <button type="button" class="btn btn-light me-2" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise"></i> Actualizar
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-light dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-gear"></i> Opciones
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="centerMapBtn">Centrar Mapa</a></li>
                            <li><a class="dropdown-item" href="#" id="trackAllBtn">Seguir Todos</a></li>
                            <li><a class="dropdown-item" href="#" id="showRoutesBtn">Mostrar Rutas</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="exportDataBtn">Exportar Datos</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white stats-card">
                            <div class="card-body text-center">
                                <h5>Vehículos Activos</h5>
                                <h2 id="activeVehiclesCount">{{ active_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white stats-card">
                            <div class="card-body text-center">
                                <h5>Disponibles</h5>
                                <h2 id="availableVehiclesCount">{{ available_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white stats-card">
                            <div class="card-body text-center">
                                <h5>En Viaje</h5>
                                <h2 id="busyVehiclesCount">{{ busy_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark stats-card">
                            <div class="card-body text-center">
                                <h5>Inactivos</h5>
                                <h2 id="idleVehiclesCount">{{ idle_vehicles }}</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-9">
                        <div class="map-container">
                            <div id="map"></div>
                            <div class="map-controls">
                                <div class="filter-buttons">
                                    <button class="btn btn-sm btn-outline-primary filter-btn active" data-filter="all">Todos</button>
                                    <button class="btn btn-sm btn-outline-success filter-btn" data-filter="available">Disponibles</button>
                                    <button class="btn btn-sm btn-outline-danger filter-btn" data-filter="busy">En Viaje</button>
                                    <button class="btn btn-sm btn-outline-warning filter-btn" data-filter="idle">Inactivos</button>
                                </div>
                                <div class="input-group input-group-sm">
                                    <input type="text" class="form-control" id="searchVehicle" placeholder="Buscar vehículo...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Vehículos</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush vehicle-info">
                                    {% for vehicle in vehicles %}
                                    <a href="#" class="list-group-item list-group-item-action vehicle-item" data-vehicle-id="{{ vehicle.id }}" data-status="{{ vehicle.status }}">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">
                                                {% if vehicle.status == 'available' %}
                                                <span class="status-indicator status-available"></span>
                                                {% elif vehicle.status == 'busy' %}
                                                <span class="status-indicator status-busy"></span>
                                                {% else %}
                                                <span class="status-indicator status-idle"></span>
                                                {% endif %}
                                                {{ vehicle.license_plate }}
                                            </h6>
                                            <small>{{ vehicle.last_update }}</small>
                                        </div>
                                        <p class="mb-1">{{ vehicle.driver_name }}</p>
                                        <small>{{ vehicle.brand }} {{ vehicle.model }}</small>
                                    </a>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para detalles del vehículo -->
<div class="modal fade" id="vehicleDetailsModal" tabindex="-1" aria-labelledby="vehicleDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="vehicleDetailsModalLabel">Detalles del Vehículo</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <span id="vehicleStatusBadge" class="badge bg-success">Disponible</span>
                </div>
                
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Placa:</span>
                        <span id="vehiclePlate">-</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Conductor:</span>
                        <span id="vehicleDriver">-</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Vehículo:</span>
                        <span id="vehicleModel">-</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Última actualización:</span>
                        <span id="vehicleLastUpdate">-</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Velocidad:</span>
                        <span id="vehicleSpeed">-</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Ubicación:</span>
                        <span id="vehicleLocation">-</span>
                    </li>
                </ul>
                
                <div id="vehicleTripInfo" class="mt-3" style="display: none;">
                    <h6>Información del Viaje Actual</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>ID del Viaje:</span>
                            <span id="tripId">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Pasajero:</span>
                            <span id="tripPassenger">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Origen:</span>
                            <span id="tripOrigin">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Destino:</span>
                            <span id="tripDestination">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Tiempo estimado:</span>
                            <span id="tripEta">-</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                <button type="button" class="btn btn-primary" id="trackVehicleBtn">Seguir</button>
                <button type="button" class="btn btn-success" id="contactDriverBtn">Contactar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js" integrity="sha256-WBkoXOwTeyKclOHuWtc+i2uENFpDZ9YPdf5Hf+D7ewM=" crossorigin=""></script>
<script>
    // Inicializar mapa
    var map = L.map('map').setView([-34.603722, -58.381592], 13); // Buenos Aires como ejemplo
    
    // Añadir capa de mapa base
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Marcadores de vehículos (simulados)
    var vehicles = [
        { id: 1, plate: 'ABC123', driver: 'Juan Pérez', model: 'Toyota Corolla', status: 'available', lat: -34.603722, lng: -58.381592, speed: 0, lastUpdate: '2 min' },
        { id: 2, plate: 'DEF456', driver: 'María López', model: 'Honda Civic', status: 'busy', lat: -34.608722, lng: -58.371592, speed: 35, lastUpdate: '1 min' },
        { id: 3, plate: 'GHI789', driver: 'Carlos Gómez', model: 'Ford Focus', status: 'idle', lat: -34.598722, lng: -58.391592, speed: 0, lastUpdate: '5 min' },
        { id: 4, plate: 'JKL012', driver: 'Ana Martínez', model: 'Chevrolet Cruze', status: 'available', lat: -34.613722, lng: -58.361592, speed: 0, lastUpdate: '3 min' },
        { id: 5, plate: 'MNO345', driver: 'Roberto Sánchez', model: 'Volkswagen Gol', status: 'busy', lat: -34.593722, lng: -58.401592, speed: 28, lastUpdate: '30 seg' }
    ];
    
    // Iconos para diferentes estados
    var vehicleIcons = {
        available: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),
        busy: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),
        idle: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-yellow.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        })
    };
    
    // Añadir marcadores al mapa
    var markers = {};
    vehicles.forEach(function(vehicle) {
        var marker = L.marker([vehicle.lat, vehicle.lng], {icon: vehicleIcons[vehicle.status]})
            .addTo(map)
            .bindPopup(`<b>${vehicle.plate}</b><br>${vehicle.driver}<br>${vehicle.model}`);
        
        marker.on('click', function() {
            showVehicleDetails(vehicle.id);
        });
        
        markers[vehicle.id] = marker;
    });
    
    // Mostrar detalles del vehículo
    function showVehicleDetails(vehicleId) {
        // Buscar el vehículo por ID
        var vehicle = vehicles.find(v => v.id === vehicleId);
        if (!vehicle) return;
        
        // Actualizar la información en el modal
        document.getElementById('vehiclePlate').textContent = vehicle.plate;
        document.getElementById('vehicleDriver').textContent = vehicle.driver;
        document.getElementById('vehicleModel').textContent = vehicle.model;
        document.getElementById('vehicleLastUpdate').textContent = vehicle.lastUpdate;
        document.getElementById('vehicleSpeed').textContent = vehicle.speed + ' km/h';
        document.getElementById('vehicleLocation').textContent = vehicle.lat.toFixed(6) + ', ' + vehicle.lng.toFixed(6);
        
        // Actualizar el estado
        var statusBadge = document.getElementById('vehicleStatusBadge');
        statusBadge.className = 'badge';
        if (vehicle.status === 'available') {
            statusBadge.classList.add('bg-success');
            statusBadge.textContent = 'Disponible';
            document.getElementById('vehicleTripInfo').style.display = 'none';
        } else if (vehicle.status === 'busy') {
            statusBadge.classList.add('bg-danger');
            statusBadge.textContent = 'En Viaje';
            document.getElementById('vehicleTripInfo').style.display = 'block';
            
            // Simular información del viaje
            document.getElementById('tripId').textContent = 'T-' + Math.floor(Math.random() * 10000);
            document.getElementById('tripPassenger').textContent = 'Cliente ' + Math.floor(Math.random() * 100);
            document.getElementById('tripOrigin').textContent = 'Av. Corrientes 1234';
            document.getElementById('tripDestination').textContent = 'Av. Santa Fe 4321';
            document.getElementById('tripEta').textContent = Math.floor(Math.random() * 30) + ' min';
        } else {
            statusBadge.classList.add('bg-warning');
            statusBadge.textContent = 'Inactivo';
            document.getElementById('vehicleTripInfo').style.display = 'none';
        }
        
        // Mostrar el modal
        var myModal = new bootstrap.Modal(document.getElementById('vehicleDetailsModal'));
        myModal.show();
        
        // Resaltar el vehículo en la lista
        document.querySelectorAll('.vehicle-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`.vehicle-item[data-vehicle-id="${vehicleId}"]`).classList.add('active');
    }
    
    // Filtrar vehículos por estado
    document.querySelectorAll('.filter-btn').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Resaltar botón activo
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
            
            // Filtrar marcadores en el mapa
            vehicles.forEach(vehicle => {
                if (filter === 'all' || vehicle.status === filter) {
                    markers[vehicle.id].addTo(map);
                } else {
                    map.removeLayer(markers[vehicle.id]);
                }
            });
            
            // Filtrar lista de vehículos
            document.querySelectorAll('.vehicle-item').forEach(item => {
                if (filter === 'all' || item.getAttribute('data-status') === filter) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
    
    // Buscar vehículo
    document.getElementById('searchButton').addEventListener('click', function() {
        const searchTerm = document.getElementById('searchVehicle').value.toLowerCase();
        
        if (!searchTerm) return;
        
        // Buscar vehículo que coincida
        const vehicle = vehicles.find(v => 
            v.plate.toLowerCase().includes(searchTerm) || 
            v.driver.toLowerCase().includes(searchTerm) ||
            v.model.toLowerCase().includes(searchTerm)
        );
        
        if (vehicle) {
            // Centrar mapa en el vehículo
            map.setView([vehicle.lat, vehicle.lng], 15);
            // Mostrar popup
            markers[vehicle.id].openPopup();
            // Mostrar detalles
            showVehicleDetails(vehicle.id);
        } else {
            alert('No se encontró ningún vehículo que coincida con la búsqueda');
        }
    });
    
    // También buscar al presionar Enter
    document.getElementById('searchVehicle').addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            document.getElementById('searchButton').click();
        }
    });
    
    // Hacer clic en un vehículo de la lista
    document.querySelectorAll('.vehicle-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const vehicleId = parseInt(this.getAttribute('data-vehicle-id'));
            const vehicle = vehicles.find(v => v.id === vehicleId);
            
            if (vehicle) {
                // Centrar mapa en el vehículo
                map.setView([vehicle.lat, vehicle.lng], 15);
                // Mostrar popup
                markers[vehicleId].openPopup();
                // Mostrar detalles
                showVehicleDetails(vehicleId);
            }
        });
    });
    
    // Actualizar datos
    document.getElementById('refreshBtn').addEventListener('click', function() {
        // Aquí implementar la lógica para actualizar los datos en tiempo real
        alert('Actualizando datos...');
    });
    
    // Centrar mapa
    document.getElementById('centerMapBtn').addEventListener('click', function() {
        map.setView([-34.603722, -58.381592], 13);
    });
    
    // Seguir vehículo
    document.getElementById('trackVehicleBtn').addEventListener('click', function() {
        alert('Siguiendo vehículo...');
        // Implementar lógica para seguir vehículo
    });
    
    // Contactar conductor
    document.getElementById('contactDriverBtn').addEventListener('click', function() {
        alert('Contactando conductor...');
        // Implementar lógica para contactar conductor
    });
</script>
{% endblock %}
