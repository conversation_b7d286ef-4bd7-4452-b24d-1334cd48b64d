{% extends "base_layout.html" %}

{% block title %}Detalles de Usuario - Panel de Taxis{% endblock %}

{% block head_extra %}
<style>
    .user-profile {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .profile-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }
    .profile-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        color: white;
        font-size: 2.5rem;
    }
    .profile-info h3 {
        margin-bottom: 5px;
    }
    .profile-status {
        display: inline-block;
        margin-top: 5px;
    }
    .detail-card {
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .detail-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .detail-icon {
        font-size: 2rem;
        margin-bottom: 15px;
    }
    .activity-item {
        padding: 15px;
        border-left: 3px solid #0d6efd;
        margin-bottom: 15px;
        background-color: #f8f9fa;
    }
    .activity-date {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .tab-content {
        padding: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Detalles de Usuario</h2>
                <div>
                    <a href="{{ url_for('users_management_route') }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Volver
                    </a>
                    <button type="button" class="btn btn-warning" id="editUserBtn">
                        <i class="bi bi-pencil"></i> Editar
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="user-profile">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="bi bi-person"></i>
                        </div>
                        <div class="profile-info">
                            <h3 id="userName">{{ user.full_name }}</h3>
                            <p id="userEmail">{{ user.email }}</p>
                            <div id="userStatus" class="profile-status">
                                {% if user.is_active %}
                                <span class="badge bg-success">Activo</span>
                                {% else %}
                                <span class="badge bg-danger">Inactivo</span>
                                {% endif %}
                            </div>
                            <div id="userRole" class="profile-status ms-2">
                                {% if user_role %}
                                    {% if user_role == 'administrador' %}
                                    <span class="badge bg-secondary">Administrador</span>
                                    {% elif user_role == 'operador' %}
                                    <span class="badge bg-info">Operador</span>
                                    {% elif user_role == 'taxi' %}
                                    <span class="badge bg-success">Conductor</span>
                                    {% elif user_role == 'usuario' %}
                                    <span class="badge bg-warning text-dark">Pasajero</span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">{{ user_role }}</span>
                                    {% endif %}
                                {% else %}
                                <span class="badge bg-light text-dark">Sin rol</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <ul class="nav nav-tabs" id="userDetailsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab" aria-controls="info" aria-selected="true">
                            <i class="bi bi-info-circle"></i> Información
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab" aria-controls="activity" aria-selected="false">
                            <i class="bi bi-clock-history"></i> Actividad
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                            <i class="bi bi-shield-lock"></i> Seguridad
                        </button>
                    </li>
                </ul>
                <div class="tab-content" id="userDetailsTabsContent">
                    <div class="tab-pane fade show active" id="info" role="tabpanel" aria-labelledby="info-tab">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card detail-card">
                                    <div class="card-body text-center">
                                        <div class="detail-icon text-primary">
                                            <i class="bi bi-person-vcard"></i>
                                        </div>
                                        <h5 class="card-title">Información Personal</h5>
                                        <ul class="list-group list-group-flush text-start">
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>ID:</span>
                                                <span id="userId">{{ user.id }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Nombre completo:</span>
                                                <span>{{ user.full_name }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Email:</span>
                                                <span>{{ user.email }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Teléfono:</span>
                                                <span>{{ user.phone_number or 'No especificado' }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Fecha de registro:</span>
                                                <span>{{ user.created_at or '01/01/2023' }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card detail-card">
                                    <div class="card-body text-center">
                                        <div class="detail-icon text-success">
                                            <i class="bi bi-graph-up"></i>
                                        </div>
                                        <h5 class="card-title">Estadísticas</h5>
                                        <ul class="list-group list-group-flush text-start">
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Total de viajes:</span>
                                                <span>{{ user_stats.total_trips or 0 }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Viajes completados:</span>
                                                <span>{{ user_stats.completed_trips or 0 }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Viajes cancelados:</span>
                                                <span>{{ user_stats.cancelled_trips or 0 }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Calificación promedio:</span>
                                                <span>{{ user_stats.average_rating or '0.0' }} ⭐</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <span>Último acceso:</span>
                                                <span>{{ user_stats.last_login or 'Nunca' }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="activity" role="tabpanel" aria-labelledby="activity-tab">
                        <h4>Actividad Reciente</h4>
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <h5>Inicio de sesión</h5>
                                    <span class="activity-date">22/05/2023 14:30</span>
                                </div>
                                <p>El usuario inició sesión desde la aplicación móvil.</p>
                            </div>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <h5>Viaje completado</h5>
                                    <span class="activity-date">21/05/2023 18:45</span>
                                </div>
                                <p>Viaje desde Av. Corrientes 1234 hasta Av. Santa Fe 4321.</p>
                            </div>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <h5>Actualización de perfil</h5>
                                    <span class="activity-date">20/05/2023 10:15</span>
                                </div>
                                <p>El usuario actualizó su número de teléfono.</p>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card detail-card">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bi bi-key"></i> Cambiar Contraseña</h5>
                                        <form id="passwordForm">
                                            <div class="mb-3">
                                                <label for="newPassword" class="form-label">Nueva Contraseña</label>
                                                <input type="password" class="form-control" id="newPassword" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="confirmPassword" class="form-label">Confirmar Contraseña</label>
                                                <input type="password" class="form-control" id="confirmPassword" required>
                                            </div>
                                            <button type="button" class="btn btn-primary" id="changePasswordBtn">Cambiar Contraseña</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card detail-card">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bi bi-shield-check"></i> Acciones de Seguridad</h5>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-warning" id="lockAccountBtn">
                                                <i class="bi bi-lock"></i> Bloquear Cuenta
                                            </button>
                                            <button class="btn btn-danger" id="deleteAccountBtn">
                                                <i class="bi bi-trash"></i> Eliminar Cuenta
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para editar usuario -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editUserModalLabel">Editar Usuario</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <div class="mb-3">
                        <label for="editFullName" class="form-label">Nombre Completo</label>
                        <input type="text" class="form-control" id="editFullName" value="{{ user.full_name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" value="{{ user.email }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="editPhone" class="form-label">Teléfono</label>
                        <input type="tel" class="form-control" id="editPhone" value="{{ user.phone_number }}">
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Rol</label>
                        <select class="form-select" id="editRole">
                            <option value="usuario" {% if user_role == 'usuario' %}selected{% endif %}>Pasajero</option>
                            <option value="taxi" {% if user_role == 'taxi' %}selected{% endif %}>Conductor</option>
                            <option value="operador" {% if user_role == 'operador' %}selected{% endif %}>Operador</option>
                            <option value="administrador" {% if user_role == 'administrador' %}selected{% endif %}>Administrador</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="editIsActive" {% if user.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="editIsActive">Usuario Activo</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="saveUserChangesBtn">Guardar Cambios</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
    // Función para obtener el token de autenticación
    function getAuthToken() {
        const cookieValue = document.cookie.split('; ')
            .find(row => row.startsWith('admin_access_token='))
            ?.split('=')[1];

        if (cookieValue) {
            // Decodificar el valor de la cookie si está codificado
            try {
                return decodeURIComponent(cookieValue);
            } catch (e) {
                return cookieValue;
            }
        }
        return null;
    }

    // Función para realizar peticiones a la API con autenticación
    async function fetchWithAuth(url, options = {}) {
        const token = getAuthToken();
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
            console.log('Token enviado:', token);
        } else {
            console.error('No se encontró token de autenticación');
        }

        return fetch(url, {
            ...options,
            headers,
            credentials: 'include' // Incluir cookies en la solicitud
        });
    }

    // Abrir modal de edición
    document.getElementById('editUserBtn').addEventListener('click', function() {
        new bootstrap.Modal(document.getElementById('editUserModal')).show();
    });

    // Guardar cambios del usuario
    document.getElementById('saveUserChangesBtn').addEventListener('click', async function() {
        // Validar formulario
        const form = document.getElementById('editUserForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Recopilar datos del formulario
        const userData = {
            full_name: document.getElementById('editFullName').value,
            email: document.getElementById('editEmail').value,
            phone_number: document.getElementById('editPhone').value,
            is_active: document.getElementById('editIsActive').checked,
            roles: [document.getElementById('editRole').value.toUpperCase()]
        };

        try {
            const userId = document.getElementById('userId').textContent;
            const response = await fetchWithAuth(`/api/v1/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error al actualizar usuario');
            }

            alert('Usuario actualizado correctamente');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();

            // Recargar la página para ver los cambios
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            alert('Error al actualizar usuario: ' + error.message);
        }
    });

    // Cambiar contraseña
    document.getElementById('changePasswordBtn').addEventListener('click', async function() {
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!newPassword || !confirmPassword) {
            alert('Por favor complete ambos campos de contraseña');
            return;
        }

        if (newPassword !== confirmPassword) {
            alert('Las contraseñas no coinciden');
            return;
        }

        try {
            const userId = document.getElementById('userId').textContent;
            const response = await fetchWithAuth(`/api/v1/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify({
                    password: newPassword
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error al cambiar contraseña');
            }

            alert('Contraseña cambiada correctamente');
            document.getElementById('passwordForm').reset();
        } catch (error) {
            console.error('Error:', error);
            alert('Error al cambiar contraseña: ' + error.message);
        }
    });

    // Bloquear cuenta
    document.getElementById('lockAccountBtn').addEventListener('click', async function() {
        if (!confirm('¿Está seguro de que desea bloquear esta cuenta?')) {
            return;
        }

        try {
            const userId = document.getElementById('userId').textContent;
            const response = await fetchWithAuth(`/api/v1/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify({
                    is_active: false
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error al bloquear cuenta');
            }

            alert('Cuenta bloqueada correctamente');
            window.location.reload();
        } catch (error) {
            console.error('Error:', error);
            alert('Error al bloquear cuenta: ' + error.message);
        }
    });

    // Eliminar cuenta
    document.getElementById('deleteAccountBtn').addEventListener('click', async function() {
        if (!confirm('¿Está seguro de que desea eliminar esta cuenta? Esta acción no se puede deshacer.')) {
            return;
        }

        try {
            const userId = document.getElementById('userId').textContent;
            const response = await fetchWithAuth(`/api/v1/users/${userId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error al eliminar cuenta');
            }

            alert('Cuenta eliminada correctamente');
            window.location.href = '/web/users';
        } catch (error) {
            console.error('Error:', error);
            alert('Error al eliminar cuenta: ' + error.message);
        }
    });
</script>
{% endblock %}
