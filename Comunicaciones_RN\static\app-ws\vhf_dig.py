# vhf_dig.py

import json
import pyaudio
import wave
import io
import base64
import numpy as np
import time
import socketio
import threading
import os

# Inicializar Socket.IO para la conexión con el servidor
sio = socketio.Client()

# Función para cargar la configuración del usuario
def load_config():
    try:
        with open('ptt_client_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: No se encontró el archivo de configuración.")
        return {}

# Función para monitorear cambios en el archivo de configuración
def monitor_config_file(config, config_lock):
    last_modified = os.path.getmtime('ptt_client_config.json')

    while True:
        time.sleep(2)  # Monitorea cada 2 segundos
        current_modified = os.path.getmtime('ptt_client_config.json')
        
        if current_modified != last_modified:
            print("Cambios detectados en el archivo de configuración. Recargando...")
            with config_lock:
                new_config = load_config()
                config.update(new_config)
            last_modified = current_modified

# Función para capturar y enviar audio del micrófono al servidor mediante Socket.IO
def capture_and_send_audio(config, config_lock):
    p = pyaudio.PyAudio()

    # Configuración del stream de audio
    stream = p.open(format=pyaudio.paInt16,
                    channels=1,
                    rate=8000,  # Frecuencia de muestreo ajustada para voz
                    input=True,
                    input_device_index=config.get('input_device_index', 0),
                    frames_per_buffer=512)  # Tamaño de buffer ajustado

    volume_level_threshold = config.get('volume_level', 5)
    is_transmitting = False

    def send_audio(data):
        # Crear archivo WAV en memoria
        wav_buffer = io.BytesIO()
        wf = wave.open(wav_buffer, 'wb')
        wf.setnchannels(1)
        wf.setsampwidth(p.get_sample_size(pyaudio.paInt16))
        wf.setframerate(8000)
        wf.writeframes(data)
        wf.close()

        # Convertir el archivo WAV a base64
        wav_buffer.seek(0)
        audio_base64 = base64.b64encode(wav_buffer.read()).decode('utf-8')

        # Enviar el audio al servidor usando Socket.IO
        print(f"Enviando audio desde {config['username']} al nodo {config['node_id']}")
        sio.emit('transmit_audio', {
            'audio': audio_base64,
            'user': config['username'],
            'node_id': config['node_id']
        })

    print("Monitoreando el audio...")

    try:
        while True:
            data = stream.read(2048)
            audio_data = np.frombuffer(data, dtype=np.int16)
            volume = np.linalg.norm(audio_data) / len(audio_data)

            with config_lock:
                volume_level_threshold = config.get('volume_level', 5)

            if volume > volume_level_threshold and not is_transmitting:
                print(f"Volumen {volume} excede el umbral {volume_level_threshold}. Iniciando transmisión...")
                is_transmitting = True

            if is_transmitting:
                send_audio(data)

            if is_transmitting and volume <= volume_level_threshold:
                print(f"Volumen {volume} por debajo del umbral {volume_level_threshold}. Deteniendo transmisión...")
                is_transmitting = False

            time.sleep(0.1)  # Pausa para evitar sobrecargar el servidor
    except KeyboardInterrupt:
        print("Interrumpido por el usuario, deteniendo el envío de audio.")
    finally:
        stream.stop_stream()
        stream.close()
        p.terminate()
        print("Transmisión de audio detenida.")

# Conexión al servidor WebSocket mediante Socket.IO
def connect_to_socket(config):
    @sio.event
    def connect():
        print(f"Conectado al servidor en el nodo {config['node_id']}")

    @sio.event
    def disconnect():
        print("Desconectado del servidor")

    @sio.event
    def connect_error(data):
        print("Error al conectar con el servidor WebSocket:", data)

    sio.connect(f"wss://{config['node_url']}", 
                headers={'node_id': config['node_id'], 'username': config['username']},
                namespaces=['/node'])

# Ejecutar el cliente
if __name__ == '__main__':
    config = load_config()
    config_lock = threading.Lock()

    if 'username' not in config or 'password' not in config or 'node_url' not in config or 'node_id' not in config:
        print("Error: Configuración incompleta. Por favor, asegúrate de tener usuario, contraseña y nodo configurados.")
        exit(1)

    # Iniciar un hilo para monitorear el archivo de configuración
    threading.Thread(target=monitor_config_file, args=(config, config_lock), daemon=True).start()

    # Conectar al servidor WebSocket para transmitir audio
    connect_to_socket(config)

    # Iniciar la captura y transmisión de audio
    capture_and_send_audio(config, config_lock)
