<!-- /app/templates/admin/edit_pastor.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}

{% block title %}Editar Información del Pastor{% endblock %}

{% block content %}
    <h1>Editar Información del Pastor: {{ user.first_name }} {{ user.last_name }}</h1>
    <form method="POST" action="">
        {{ form.hidden_tag() }}
        <div class="form-group">
            {{ render_field(form.grado, class="form-control", placeholder="Grado") }}
          </div>
        <div class="form-group">
            {{ render_field(form.address, class="form-control", placeholder="Dirección (Casa Pastoral)") }}
        </div>
         <div class="form-group">
            {{ form.latitude.label }}
            {{ form.latitude(class="form-control", type="hidden") }}
            {{ form.longitude.label }}
            {{ form.longitude(class="form-control", type="hidden") }}
        </div>
        <!-- Botón para mostrar/ocultar el mapa -->
        <button type="button" class="btn btn-info mb-3" id="toggle-map">Mostrar/Ocultar Mapa</button>
        <div id="map" style="height: 400px; display:none;"></div>

        {{ form.submit(class="btn btn-primary") }}
    </form>
{% endblock %}

{% block scripts %}
    <script>
     var map = L.map('map'); 
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    var drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);
    var drawControl = new L.Control.Draw({
        draw: {
            polygon: false,
            polyline: false,
            rectangle: false,
            circle: false,
            circlemarker: false,
            marker: true
        },
        edit: {
            featureGroup: drawnItems,
            remove: false
        }
    });
    map.addControl(drawControl);
    map.on(L.Draw.Event.CREATED, function (e) {
        var layer = e.layer;
        if (e.layerType === 'marker') {
            var lat = layer.getLatLng().lat;
            var lng = layer.getLatLng().lng;
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;
            drawnItems.clearLayers();
            drawnItems.addLayer(layer);
        }
    });
    {% if user.pastor.latitude and user.pastor.longitude %}
        var existingMarker = L.marker([{{ user.pastor.latitude }}, {{ user.pastor.longitude }}], {
            icon: L.icon({
                iconUrl: "{{ url_for('static', filename='img/casa.png') }}",
                iconSize: [38, 38],
                iconAnchor: [19, 37],
                popupAnchor: [0, -37]
            })
        }).addTo(map);
        drawnItems.addLayer(existingMarker);
    {% endif %}
    document.getElementById('toggle-map').addEventListener('click', function() {
        var mapDiv = document.getElementById('map');
        if (mapDiv.style.display === 'none') {
            mapDiv.style.display = 'block';
            map.invalidateSize();
            {% if user.pastor.latitude and user.pastor.longitude %}
                map.setView([{{ user.pastor.latitude }}, {{ user.pastor.longitude }}], 15);
            {% else %}
                map.setView([-40.8110, -62.9972], 13);
            {% endif %}
        } else {
            mapDiv.style.display = 'none';
        }
    });
    </script>
{% endblock %}
