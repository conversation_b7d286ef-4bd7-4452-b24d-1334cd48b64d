// /static/js/dig-vhf.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;

    console.log(`Conectado como ${username} al nodo ${nodeId} para Digital a VHF.`);

    // Conectar al servidor usando Socket.IO al nodo específico
    const socket = io.connect('https://www.patagoniaservers.com.ar:5000/node', {
        transports: ['websocket'],
        query: {
            node_id: nodeId,
            username: username
        }
    });

    let isTransmittingAudio = false;  // Variable para verificar si el usuario está transmitiendo audio

    socket.on('connect', () => {
        console.log('Conexión exitosa con Socket.IO para Digital a VHF.');
    });

    // Evento cuando se recibe audio del servidor
    socket.on('receive_audio', (data) => {
        console.log('Recibiendo audio, activando PTT...', data);

        // Verificar si el audio proviene del mismo usuario, ignorar si es propio
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);
            return;  // Ignorar si es el mismo usuario que envió el audio
        }

        // Verificar si el campo 'audio' está vacío
        if (!data.audio || data.audio.trim() === "") {
            console.error("Error: Audio vacío recibido.");
            return;  // No intentar reproducir audio vacío
        }

        // Activa el PTT un segundo antes de reproducir el audio
        console.log('Enviando evento ptt_control: Activando PTT en 1 segundo...');
        activarPTT();

        setTimeout(() => {
            try {
                // Decodifica el audio base64
                const audioBlob = new Blob([new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))], { type: 'audio/wav' });

                // Crea un objeto URL para el audio
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);

                audio.play().then(() => {
                    console.log('Reproduciendo audio de otro usuario...');
                }).catch((error) => {
                    console.error('Error al reproducir el audio:', error);
                });

                // Desactiva el PTT un segundo después de finalizar la reproducción
                audio.addEventListener('ended', () => {
                    console.log('Audio finalizado. Desactivando PTT en 1 segundo...');
                    setTimeout(() => {
                        desactivarPTT();
                    }, 1000);  // Desactivar PTT un segundo después de terminar el audio
                });
            } catch (error) {
                console.error('Error al decodificar o reproducir el audio:', error);
            }
        }, 1000);  // Retraso de 1 segundo antes de reproducir el audio
    });

    // Evento desde el servidor que indica que un usuario está transmitiendo audio
    socket.on('audio_start', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha comenzado a transmitir audio. Evitando activación de PTT...`);
            isTransmittingAudio = true;  // Si el usuario está transmitiendo, evitamos la activación de PTT
        }
    });

    // Evento desde el servidor que indica que la transmisión de audio ha finalizado
    socket.on('audio_end', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha terminado la transmisión de audio.`);
            isTransmittingAudio = false;  // El usuario ya no está transmitiendo
        }
    });

    // Función para activar el PTT enviando un POST al servidor
    function activarPTT() {
        if (!isTransmittingAudio) {
            console.log("Activando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ptt_state: true  // Activar PTT
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error en la solicitud de activación del PTT');
                }
                return response.json();
            })
            .then(data => {
                console.log('Respuesta del servidor:', data);
            })
            .catch((error) => {
                console.error('Error al activar el PTT:', error);
            });
        } else {
            console.log('PTT no activado ya que el usuario está transmitiendo audio.');
        }
    }

    // Función para desactivar el PTT enviando un POST al servidor
    function desactivarPTT() {
        if (!isTransmittingAudio) {
            console.log("Desactivando PTT...");
            fetch('/ptt_event', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ptt_state: false  // Desactivar PTT
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error en la solicitud de desactivación del PTT');
                }
                return response.json();
            })
            .then(data => {
                console.log('Respuesta del servidor:', data);
            })
            .catch((error) => {
                console.error('Error al desactivar el PTT:', error);
            });
        } else {
            console.log('PTT no desactivado ya que el usuario está transmitiendo audio.');
        }
    }
});
