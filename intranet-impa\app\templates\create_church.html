{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}
{% block title %}Crear <PERSON>{% endblock %}
{% include '_search_form.html' %}

{% block content %}
    <h1>Crear <PERSON></h1>
    <form method="POST" action="">
        {{ form.hidden_tag() }}
        <div class="form-group">
            {{ render_field(form.name, class="form-control", placeholder="Nombre de la Iglesia") }}
        </div>
        <div class="form-group">
            {{ render_field(form.address, class="form-control", placeholder="Dirección Completa") }}
        </div>
        {# --- AÑADIDOS CAMPOS CIUDAD Y PROVINCIA --- #}
        <div class="form-group">
            {{ render_field(form.city, class="form-control", placeholder="Ciudad") }}
        </div>
        <div class="form-group">
            {{ render_field(form.province, class="form-control", placeholder="Provincia") }}
        </div>
        {# --- FIN AÑADIDOS --- #}
        <div class="form-group">
            {{ render_field(form.district, class="form-control", placeholder="Distrito/Zona (Opcional)") }}
        </div>
        <div class="form-group">
            {{ render_field(form.pastor, class="form-control") }} {# Pastor Gobernante #}
        </div>

        {# --- Campos Latitud/Longitud y Mapa --- #}
        <div class="form-group" style="display: none;"> {# Ocultar labels si los campos están ocultos #}
            {{ form.latitude.label }}
            {{ form.latitude(class="form-control", type="hidden") }}
            {{ form.longitude.label }}
            {{ form.longitude(class="form-control", type="hidden") }}
        </div>
        <!-- Botón para mostrar/ocultar el mapa -->
        <button type="button" class="btn btn-info mb-3" id="toggle-map">Mostrar/Ocultar Mapa para Ubicación</button>
        <!-- Contenedor del mapa (inicialmente oculto) -->
        <div id="map" style="height: 400px; display: none;"></div>
        {# --- Fin Campos Mapa --- #}

        {{ form.submit(class="btn btn-primary") }}
        <a href="{{ url_for('routes.list_churches') }}" class="btn btn-secondary">Cancelar</a> {# Añadido botón Cancelar #}
    </form>
{% endblock %}

{% block scripts %}
    {{ super() }} {# Importante heredar scripts de base.html #}
    <script>
    // Inicialización del mapa
    var map = L.map('map');
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Capa para los elementos dibujados
    var drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);

    // Configuración de Leaflet.draw
    var drawControl = new L.Control.Draw({
        draw: {
            polygon: false, polyline: false, rectangle: false, circle: false, circlemarker: false, marker: true
        },
        edit: { featureGroup: drawnItems, remove: false }
    });
    map.addControl(drawControl);

    // Evento: Cuando se crea un marcador manualmente
    map.on(L.Draw.Event.CREATED, function (e) {
        var layer = e.layer;
        var lat = layer.getLatLng().lat;
        var lng = layer.getLatLng().lng;
        document.getElementById('latitude').value = lat.toFixed(6); // Guardar con precisión
        document.getElementById('longitude').value = lng.toFixed(6);
        drawnItems.clearLayers();
        drawnItems.addLayer(layer);
    });

    // Botón para mostrar/ocultar el mapa
    document.getElementById('toggle-map').addEventListener('click', function() {
        var mapDiv = document.getElementById('map');
        if (mapDiv.style.display === 'none') {
            mapDiv.style.display = 'block';
            map.invalidateSize(); // Necesario si el mapa estaba oculto
            map.setView([-40.8110, -62.9972], 13); // Centrar en Viedma/Patagones por defecto
        } else {
            mapDiv.style.display = 'none';
        }
    });

    // Buscar dirección al perder foco en el campo Address
    var addressInput = document.getElementById('address');
    var cityInput = document.getElementById('city'); // Input de ciudad
    var provinceInput = document.getElementById('province'); // Input de provincia

    function searchAddress() {
         // Construir query más específica si hay ciudad/provincia
        var addressQuery = addressInput.value;
        var cityQuery = cityInput ? cityInput.value : '';
        var provinceQuery = provinceInput ? provinceInput.value : '';
        var fullQuery = addressQuery;
        if (cityQuery) fullQuery += ', ' + cityQuery;
        if (provinceQuery) fullQuery += ', ' + provinceQuery;

        if(addressQuery) { // Buscar solo si hay algo en la dirección
            console.log("Buscando:", fullQuery);
            // Usar Nominatim con viewbox para sesgar resultados a Argentina (opcional)
            // bbox ~ -73.5,-55.2,-53.6,-21.8 (min Long, min Lat, max Long, max Lat)
            fetch("https://nominatim.openstreetmap.org/search?format=json&limit=1&countrycodes=ar&q=" + encodeURIComponent(fullQuery) /*+ "&viewbox=-73.5,-55.2,-53.6,-21.8&bounded=1"*/)
            .then(response => response.json())
            .then(data => {
                if(data.length > 0) {
                    var result = data[0];
                    var lat = parseFloat(result.lat);
                    var lon = parseFloat(result.lon);
                    console.log("Encontrado:", result.display_name, lat, lon);

                    drawnItems.clearLayers();
                    var marker = L.marker([lat, lon]).addTo(map);
                    drawnItems.addLayer(marker);
                    document.getElementById('latitude').value = lat.toFixed(6);
                    document.getElementById('longitude').value = lon.toFixed(6);

                    var mapDiv = document.getElementById('map');
                    if(mapDiv.style.display === 'none') {
                        mapDiv.style.display = 'block';
                        map.invalidateSize();
                    }
                    map.setView([lat, lon], 15); // Centrar en resultado

                    // Opcional: intentar autocompletar ciudad/provincia si están vacíos
                    // if (cityInput && !cityInput.value && result.address) {
                    //    cityInput.value = result.address.city || result.address.town || result.address.village || '';
                    // }
                    // if (provinceInput && !provinceInput.value && result.address) {
                    //     provinceInput.value = result.address.state || '';
                    // }

                } else {
                    console.log("No se encontró la dirección:", fullQuery);
                }
            })
            .catch(error => {
                console.error("Error al buscar la dirección:", error);
            });
        }
    }
    // Añadir listeners a address, city y province para buscar al perder foco
    if(addressInput) addressInput.addEventListener('blur', searchAddress);
    if(cityInput) cityInput.addEventListener('blur', searchAddress);
    if(provinceInput) provinceInput.addEventListener('blur', searchAddress);

    </script>
{% endblock %}