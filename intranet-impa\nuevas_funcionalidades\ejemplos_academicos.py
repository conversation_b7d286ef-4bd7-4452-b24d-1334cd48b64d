# ejemplos_academicos.py
# Ejemplos prácticos del Sistema Académico

"""
EJEMPLOS PRÁCTICOS DEL SISTEMA ACADÉMICO
========================================

Este archivo contiene ejemplos reales de cómo funcionaría el sistema académico
en tu aplicación Intranet IMPA, con casos de uso específicos para iglesias.
"""

from datetime import datetime, date, timedelta

# ============================================================================
# 1. EJEMPLOS DE PENSUMS PREDEFINIDOS
# ============================================================================

PENSUMS_PREDEFINIDOS = {
    'escuela_biblica_basica': {
        'name': 'Escuela Bíblica Básica',
        'description': 'Fundamentos de la fe cristiana para nuevos creyentes',
        'duration_months': 6,
        'target_audience': 'Nuevos miembros y creyentes recientes',
        'subjects': [
            {
                'name': 'Fundamentos de la Fe',
                'code': 'FUN-001',
                'description': 'Doctrinas básicas del cristianismo',
                'credits': 3,
                'level': 1,
                'is_mandatory': True,
                'estimated_hours': 40,
                'topics': [
                    'La Salvación por Gracia',
                    'La Trinidad',
                    'La Autoridad de las Escrituras',
                    'La Vida Eterna',
                    'El Bautismo y la Santa Cena'
                ]
            },
            {
                'name': 'Historia Bíblica',
                'code': 'HIS-001', 
                'description': 'Panorama del Antiguo y Nuevo Testamento',
                'credits': 2,
                'level': 1,
                'is_mandatory': True,
                'estimated_hours': 30,
                'topics': [
                    'Creación y Caída',
                    'Los Patriarcas',
                    'Éxodo y Ley',
                    'Reyes y Profetas',
                    'Vida de Jesús',
                    'La Iglesia Primitiva'
                ]
            },
            {
                'name': 'Vida Cristiana Práctica',
                'code': 'VCP-001',
                'description': 'Aplicación práctica de la fe en la vida diaria',
                'credits': 2,
                'level': 2,
                'is_mandatory': True,
                'estimated_hours': 25,
                'prerequisites': ['FUN-001'],
                'topics': [
                    'La Oración',
                    'El Estudio Bíblico Personal',
                    'El Testimonio Cristiano',
                    'La Mayordomía',
                    'Las Relaciones Familiares'
                ]
            },
            {
                'name': 'Evangelismo Personal',
                'code': 'EVA-001',
                'description': 'Métodos y técnicas para compartir el evangelio',
                'credits': 2,
                'level': 2,
                'is_mandatory': False,
                'estimated_hours': 20,
                'prerequisites': ['FUN-001'],
                'topics': [
                    'El Plan de Salvación',
                    'Cómo Iniciar una Conversación',
                    'Respondiendo Objeciones',
                    'El Seguimiento de Nuevos Convertidos'
                ]
            }
        ]
    },
    
    'escuela_liderazgo': {
        'name': 'Escuela de Liderazgo',
        'description': 'Formación para líderes y servidores de la iglesia',
        'duration_months': 12,
        'target_audience': 'Miembros con llamado al liderazgo',
        'subjects': [
            {
                'name': 'Principios de Liderazgo Cristiano',
                'code': 'LID-001',
                'description': 'Fundamentos bíblicos del liderazgo',
                'credits': 4,
                'level': 1,
                'is_mandatory': True,
                'estimated_hours': 50,
                'topics': [
                    'El Líder según el Corazón de Dios',
                    'Características del Líder Cristiano',
                    'Liderazgo Servicial',
                    'La Autoridad Espiritual',
                    'Formación del Carácter'
                ]
            },
            {
                'name': 'Administración Eclesiástica',
                'code': 'ADM-001',
                'description': 'Gestión y organización de la iglesia local',
                'credits': 3,
                'level': 2,
                'is_mandatory': True,
                'estimated_hours': 40,
                'prerequisites': ['LID-001'],
                'topics': [
                    'Estructura Organizacional',
                    'Planificación Estratégica',
                    'Gestión de Recursos',
                    'Desarrollo de Ministerios',
                    'Evaluación y Crecimiento'
                ]
            },
            {
                'name': 'Consejería Pastoral',
                'code': 'CON-001',
                'description': 'Principios básicos de consejería bíblica',
                'credits': 3,
                'level': 3,
                'is_mandatory': True,
                'estimated_hours': 35,
                'prerequisites': ['LID-001'],
                'topics': [
                    'Fundamentos de la Consejería Bíblica',
                    'Escucha Activa',
                    'Problemas Comunes en la Iglesia',
                    'Consejería Matrimonial Básica',
                    'Límites y Referencias'
                ]
            },
            {
                'name': 'Homilética',
                'code': 'HOM-001',
                'description': 'Arte y ciencia de la predicación',
                'credits': 4,
                'level': 3,
                'is_mandatory': True,
                'estimated_hours': 45,
                'prerequisites': ['LID-001'],
                'topics': [
                    'Preparación del Sermón',
                    'Exégesis Básica',
                    'Estructura del Mensaje',
                    'Técnicas de Comunicación',
                    'Práctica de Predicación'
                ]
            }
        ]
    },
    
    'escuela_maestros': {
        'name': 'Escuela de Maestros',
        'description': 'Capacitación para maestros de escuela dominical y grupos',
        'duration_months': 9,
        'target_audience': 'Maestros actuales y futuros',
        'subjects': [
            {
                'name': 'Pedagogía Cristiana',
                'code': 'PED-001',
                'description': 'Principios de enseñanza desde una perspectiva cristiana',
                'credits': 3,
                'level': 1,
                'is_mandatory': True,
                'estimated_hours': 35,
                'topics': [
                    'Filosofía de la Educación Cristiana',
                    'El Maestro como Discipulador',
                    'Estilos de Aprendizaje',
                    'Motivación en el Aula',
                    'Evaluación del Aprendizaje'
                ]
            },
            {
                'name': 'Métodos de Enseñanza',
                'code': 'MET-001',
                'description': 'Técnicas y estrategias didácticas',
                'credits': 3,
                'level': 2,
                'is_mandatory': True,
                'estimated_hours': 40,
                'prerequisites': ['PED-001'],
                'topics': [
                    'Métodos Participativos',
                    'Uso de Recursos Audiovisuales',
                    'Dinámicas Grupales',
                    'Enseñanza por Edades',
                    'Tecnología en el Aula'
                ]
            },
            {
                'name': 'Desarrollo Curricular',
                'code': 'CUR-001',
                'description': 'Diseño y planificación de programas educativos',
                'credits': 2,
                'level': 3,
                'is_mandatory': True,
                'estimated_hours': 25,
                'prerequisites': ['PED-001', 'MET-001'],
                'topics': [
                    'Diseño de Lecciones',
                    'Secuenciación de Contenidos',
                    'Objetivos de Aprendizaje',
                    'Materiales Didácticos',
                    'Evaluación Curricular'
                ]
            }
        ]
    }
}

# ============================================================================
# 2. FUNCIONES DE INICIALIZACIÓN
# ============================================================================

def create_standard_schools_for_church(church_id, church_name):
    """Crear escuelas estándar para una nueva iglesia"""
    from nuevas_funcionalidades.sistema_academico import AcademicService
    
    schools_created = []
    
    # 1. Escuela Bíblica Básica (obligatoria para toda iglesia)
    basic_school = AcademicService.create_school(
        church_id=church_id,
        name=f"Escuela Bíblica Básica - {church_name}",
        description="Fundamentos de la fe para nuevos miembros",
        start_date=date.today(),
        max_students=50,
        auto_enrollment=True  # Matrícula automática para nuevos miembros
    )
    
    # Crear pensum básico
    basic_curriculum = AcademicService.create_curriculum(
        school_id=basic_school.id,
        name="Fundamentos de Fe",
        description="Curso básico de 6 meses",
        duration_months=6,
        subjects_data=PENSUMS_PREDEFINIDOS['escuela_biblica_basica']['subjects']
    )
    
    schools_created.append({
        'school': basic_school,
        'curriculum': basic_curriculum,
        'type': 'basic'
    })
    
    # 2. Escuela de Liderazgo (opcional, para iglesias con más de 50 miembros)
    leadership_school = AcademicService.create_school(
        church_id=church_id,
        name=f"Escuela de Liderazgo - {church_name}",
        description="Formación para líderes y servidores",
        start_date=date.today() + timedelta(days=30),  # Inicia en un mes
        max_students=20,
        auto_enrollment=False  # Matrícula por invitación
    )
    
    leadership_curriculum = AcademicService.create_curriculum(
        school_id=leadership_school.id,
        name="Formación de Líderes",
        description="Curso avanzado de 12 meses",
        duration_months=12,
        subjects_data=PENSUMS_PREDEFINIDOS['escuela_liderazgo']['subjects']
    )
    
    schools_created.append({
        'school': leadership_school,
        'curriculum': leadership_curriculum,
        'type': 'leadership'
    })
    
    return schools_created

def setup_academic_system_for_corporation():
    """Configurar sistema académico para toda la corporación"""
    from app.models import Church
    
    churches = Church.query.all()
    total_schools = 0
    
    for church in churches:
        # Verificar si ya tiene escuelas
        existing_schools = church.academic_schools
        
        if not existing_schools:
            schools = create_standard_schools_for_church(church.id, church.name)
            total_schools += len(schools)
            print(f"✅ Creadas {len(schools)} escuelas para {church.name}")
        else:
            print(f"⚠️ {church.name} ya tiene {len(existing_schools)} escuelas")
    
    print(f"\n🎓 Sistema académico configurado: {total_schools} escuelas creadas")
    return total_schools

# ============================================================================
# 3. EJEMPLOS DE REPORTES ESPECÍFICOS
# ============================================================================

def generate_monthly_academic_report(church_id=None):
    """Generar reporte académico mensual"""
    from nuevas_funcionalidades.sistema_academico import AcademicSchool, AcademicEnrollment, AcademicGrade
    from app import db
    
    # Período del reporte
    today = date.today()
    start_month = today.replace(day=1)
    
    # Base query
    schools_query = AcademicSchool.query.filter_by(is_active=True)
    if church_id:
        schools_query = schools_query.filter_by(church_id=church_id)
    
    schools = schools_query.all()
    
    report = {
        'period': start_month.strftime('%B %Y'),
        'generated_at': datetime.now(),
        'schools': []
    }
    
    for school in schools:
        # Estadísticas de la escuela
        total_enrolled = AcademicEnrollment.query.filter_by(
            school_id=school.id, 
            status='active'
        ).count()
        
        completed_this_month = AcademicEnrollment.query.filter(
            AcademicEnrollment.school_id == school.id,
            AcademicEnrollment.status == 'completed',
            AcademicEnrollment.completion_date >= start_month
        ).count()
        
        new_enrollments = AcademicEnrollment.query.filter(
            AcademicEnrollment.school_id == school.id,
            AcademicEnrollment.enrollment_date >= start_month
        ).count()
        
        # Promedio de calificaciones del mes
        avg_grade = db.session.query(db.func.avg(AcademicGrade.grade_value)).join(
            AcademicEnrollment
        ).filter(
            AcademicEnrollment.school_id == school.id,
            AcademicGrade.evaluation_date >= start_month
        ).scalar()
        
        school_data = {
            'name': school.name,
            'church': school.church.name,
            'director': f"{school.director.first_name} {school.director.last_name}" if school.director else 'Sin director',
            'total_enrolled': total_enrolled,
            'new_enrollments': new_enrollments,
            'completed_this_month': completed_this_month,
            'average_grade': round(avg_grade, 2) if avg_grade else 0,
            'capacity_usage': f"{(total_enrolled/school.max_students)*100:.1f}%" if school.max_students > 0 else "N/A"
        }
        
        report['schools'].append(school_data)
    
    # Totales corporativos
    report['totals'] = {
        'total_schools': len(schools),
        'total_students': sum(s['total_enrolled'] for s in report['schools']),
        'total_new_enrollments': sum(s['new_enrollments'] for s in report['schools']),
        'total_completions': sum(s['completed_this_month'] for s in report['schools']),
        'overall_average': sum(s['average_grade'] for s in report['schools']) / len(report['schools']) if report['schools'] else 0
    }
    
    return report

def generate_student_transcript(enrollment_id):
    """Generar transcripción académica de un estudiante"""
    from nuevas_funcionalidades.sistema_academico import AcademicEnrollment, AcademicGrade
    
    enrollment = AcademicEnrollment.query.get(enrollment_id)
    if not enrollment:
        return None
    
    # Obtener todas las calificaciones
    grades = AcademicGrade.query.filter_by(enrollment_id=enrollment_id).order_by(
        AcademicGrade.evaluation_date
    ).all()
    
    # Agrupar por materia
    subjects_grades = {}
    for grade in grades:
        subject_id = grade.subject_id
        if subject_id not in subjects_grades:
            subjects_grades[subject_id] = {
                'subject': grade.subject,
                'grades': [],
                'final_grade': None
            }
        
        subjects_grades[subject_id]['grades'].append(grade)
        
        if grade.grade_type == 'final':
            subjects_grades[subject_id]['final_grade'] = grade.grade_value
    
    # Calcular estadísticas
    final_grades = [s['final_grade'] for s in subjects_grades.values() if s['final_grade'] is not None]
    gpa = sum(final_grades) / len(final_grades) if final_grades else 0
    
    transcript = {
        'student': {
            'name': f"{enrollment.student.first_name} {enrollment.student.last_name}",
            'email': enrollment.student.email,
            'church': enrollment.school.church.name
        },
        'school': {
            'name': enrollment.school.name,
            'director': f"{enrollment.school.director.first_name} {enrollment.school.director.last_name}" if enrollment.school.director else 'Sin director'
        },
        'curriculum': {
            'name': enrollment.curriculum.name,
            'duration': f"{enrollment.curriculum.duration_months} meses",
            'total_credits': enrollment.curriculum.total_credits
        },
        'enrollment': {
            'date': enrollment.enrollment_date,
            'status': enrollment.status,
            'completion_date': enrollment.completion_date,
            'final_grade': enrollment.final_grade
        },
        'subjects': subjects_grades,
        'statistics': {
            'subjects_completed': len([s for s in subjects_grades.values() if s['final_grade'] is not None]),
            'subjects_total': len(subjects_grades),
            'gpa': round(gpa, 2),
            'credits_earned': sum(s['subject'].credits for s in subjects_grades.values() if s['final_grade'] and s['final_grade'] >= 3.0)
        },
        'certificate': {
            'issued': enrollment.certificate_issued,
            'date': enrollment.certificate_date
        }
    }
    
    return transcript

# ============================================================================
# 4. INTEGRACIÓN CON SISTEMA EXISTENTE
# ============================================================================

def integrate_with_existing_members():
    """Integrar miembros existentes al sistema académico"""
    from app.models import Member, User, Church
    from nuevas_funcionalidades.sistema_academico import AcademicService
    
    # Obtener todas las iglesias con escuelas básicas
    churches_with_schools = Church.query.join(AcademicSchool).filter(
        AcademicSchool.auto_enrollment == True
    ).all()
    
    total_enrolled = 0
    
    for church in churches_with_schools:
        # Obtener escuela básica de la iglesia
        basic_school = church.academic_schools.filter_by(auto_enrollment=True).first()
        
        if basic_school:
            # Auto-matricular miembros elegibles
            enrolled = AcademicService.auto_enroll_eligible_students(basic_school.id)
            total_enrolled += len(enrolled)
            
            print(f"✅ {church.name}: {len(enrolled)} miembros matriculados automáticamente")
    
    print(f"\n🎓 Total de miembros matriculados: {total_enrolled}")
    return total_enrolled

def create_academic_dashboard_data(user):
    """Crear datos para dashboard académico según rol del usuario"""
    from nuevas_funcionalidades.sistema_academico import AcademicSchool, AcademicEnrollment
    
    dashboard_data = {
        'user_role': user.role,
        'user_name': f"{user.first_name} {user.last_name}",
        'schools': [],
        'enrollments': [],
        'statistics': {}
    }
    
    if user.role in ['administrador', 'secretaria']:
        # Vista corporativa
        schools = AcademicSchool.query.all()
        dashboard_data['schools'] = schools
        
        dashboard_data['statistics'] = {
            'total_schools': len(schools),
            'total_students': AcademicEnrollment.query.filter_by(status='active').count(),
            'total_completed': AcademicEnrollment.query.filter_by(status='completed').count()
        }
        
    elif user.role == 'pastorado':
        # Vista de iglesia
        if user.church_id:
            schools = AcademicSchool.query.filter_by(church_id=user.church_id).all()
            dashboard_data['schools'] = schools
            
            total_students = sum(
                AcademicEnrollment.query.filter_by(school_id=school.id, status='active').count()
                for school in schools
            )
            
            dashboard_data['statistics'] = {
                'church_schools': len(schools),
                'church_students': total_students,
                'directed_schools': len([s for s in schools if s.director_id == user.id])
            }
    
    else:
        # Vista de estudiante
        enrollments = AcademicEnrollment.query.filter_by(student_id=user.id).all()
        dashboard_data['enrollments'] = enrollments
        
        dashboard_data['statistics'] = {
            'active_enrollments': len([e for e in enrollments if e.status == 'active']),
            'completed_courses': len([e for e in enrollments if e.status == 'completed']),
            'certificates_earned': len([e for e in enrollments if e.certificate_issued])
        }
    
    return dashboard_data

# ============================================================================
# 5. EJEMPLO DE TEMPLATE HTML
# ============================================================================

ACADEMIC_DASHBOARD_TEMPLATE = """
<!-- templates/academic/dashboard.html -->
{% extends "base.html" %}

{% block title %}Sistema Académico - Intranet IMPA{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-graduation-cap"></i> 
                Sistema Académico
            </h1>
        </div>
    </div>
    
    <!-- Estadísticas principales -->
    <div class="row mb-4">
        {% if current_user.role in ['administrador', 'secretaria'] %}
        <div class="col-md-3">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Escuelas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ dashboard_data.statistics.total_schools }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-school fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Estudiantes Activos
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ dashboard_data.statistics.total_students }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-graduate fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Cursos Completados
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ dashboard_data.statistics.total_completed }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-certificate fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Lista de escuelas -->
    {% if dashboard_data.schools %}
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                Escuelas Bíblicas
                {% if current_user.role in ['administrador', 'secretaria'] %}
                <a href="{{ url_for('routes.create_academic_school') }}" class="btn btn-primary btn-sm float-right">
                    <i class="fas fa-plus"></i> Nueva Escuela
                </a>
                {% endif %}
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Escuela</th>
                            <th>Iglesia</th>
                            <th>Director</th>
                            <th>Estudiantes</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for school in dashboard_data.schools %}
                        <tr>
                            <td>
                                <strong>{{ school.name }}</strong>
                                <br>
                                <small class="text-muted">{{ school.description }}</small>
                            </td>
                            <td>{{ school.church.name }}</td>
                            <td>
                                {% if school.director %}
                                {{ school.director.first_name }} {{ school.director.last_name }}
                                {% else %}
                                <span class="text-muted">Sin asignar</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-info">
                                    {{ school.enrollments|selectattr("status", "equalto", "active")|list|length }} activos
                                </span>
                            </td>
                            <td>
                                {% if school.is_active %}
                                <span class="badge badge-success">Activa</span>
                                {% else %}
                                <span class="badge badge-secondary">Inactiva</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('routes.academic_school_detail', school_id=school.id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> Ver
                                </a>
                                {% if current_user.role in ['administrador', 'secretaria'] %}
                                <a href="{{ url_for('routes.edit_academic_school', school_id=school.id) }}" 
                                   class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i> Editar
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Matrículas del usuario (si es estudiante) -->
    {% if dashboard_data.enrollments %}
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">Mis Cursos</h6>
        </div>
        <div class="card-body">
            {% for enrollment in dashboard_data.enrollments %}
            <div class="card mb-3">
                <div class="card-body">
                    <h5 class="card-title">{{ enrollment.school.name }}</h5>
                    <p class="card-text">{{ enrollment.curriculum.name }}</p>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Estado:</strong> 
                                {% if enrollment.status == 'active' %}
                                <span class="badge badge-primary">En curso</span>
                                {% elif enrollment.status == 'completed' %}
                                <span class="badge badge-success">Completado</span>
                                {% endif %}
                            </small>
                        </div>
                        <div class="col-md-6">
                            {% if enrollment.final_grade %}
                            <small class="text-muted">
                                <strong>Calificación Final:</strong> {{ enrollment.final_grade }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                    {% if enrollment.certificate_issued %}
                    <div class="mt-2">
                        <a href="{{ url_for('routes.download_certificate', enrollment_id=enrollment.id) }}" 
                           class="btn btn-success btn-sm">
                            <i class="fas fa-download"></i> Descargar Certificado
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
"""

print("🎓 Ejemplos del Sistema Académico creados")
print("Incluye:")
print("- Pensums predefinidos para diferentes tipos de escuelas")
print("- Funciones de inicialización automática")
print("- Generadores de reportes específicos")
print("- Integración con sistema existente")
print("- Template HTML de ejemplo")
