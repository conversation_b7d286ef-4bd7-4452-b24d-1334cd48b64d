<!-- /app/templates/admin/map.html -->
{% extends "base.html" %}
{% block title %}Mapa de Iglesias y Pastores{% endblock %}

{% block head %}
  {{ super() }}
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
        integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
          integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
  <style>
    #map { height: 600px; }
  </style>
{% endblock %}

{% block content %}
  <h1>Mapa de Iglesias y Pastores</h1>
  <div id="map"></div>
{% endblock %}

{% block scripts %}
<script>
  // Centrar el mapa en Argentina (zoom 4 para abarcar gran parte del país)
  var map = L.map('map').setView([-38.4161, -63.6167], 4);
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
  }).addTo(map);

  // Marcadores para Iglesias
  {% for church in churches %}
    {% if church.latitude and church.longitude %}
      L.marker([{{ church.latitude }}, {{ church.longitude }}], {
        icon: L.icon({
          iconUrl: "{{ url_for('static', filename='img/edif.png') }}",
          iconSize: [38, 38],
          iconAnchor: [19, 37],
          popupAnchor: [0, -37]
        })
      }).addTo(map).bindPopup("<b>{{ church.name }}</b><br>{{ church.address }}");
    {% endif %}
  {% endfor %}

  // Marcadores para Pastores
  {% for pastor in pastores %}
    {% if pastor.latitude and pastor.longitude %}
      L.marker([{{ pastor.latitude }}, {{ pastor.longitude }}], {
        icon: L.icon({
          iconUrl: "{{ url_for('static', filename='img/casa.png') }}",
          iconSize: [30, 30],
          iconAnchor: [19, 37],
          popupAnchor: [0, -37]
        })
      }).addTo(map)
      .bindPopup("<b>{{ pastor.user.first_name }} {{ pastor.user.last_name }}</b><br>Casa del Pastor");
    {% endif %}
  {% endfor %}
</script>
{% endblock %}
