# /app/routes.py
import json
from datetime import datetime, timedelta
from flask import (Blueprint, render_template, redirect, url_for, flash,
                   request, jsonify, current_app, send_from_directory, make_response,
                     send_file, abort)
from flask_login import login_user, logout_user, login_required, current_user
from app import db, bcrypt
from app.models import (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Pastor, Member, UserRelationship,
                         ChurchInventory, MemberFunction, Message, UserReview,
                           CalendarEvent, Document, Account, Transaction, Announcement,
                             JerarquiaPastores, JerarquiaSuperintendentes, db, MemberTransferRequest,
                             AcademicSchool, AcademicCurriculum, AcademicSubject, AcademicEnrollment, AcademicGrade,
                             PastoralInstitute, PastoralProgram, PastoralSubject, PastoralEnrollment, PastoralGrade )
from app.forms import (LoginForm, RegistrationForm, ChurchForm, EditUserForm,
                        PastorForm, MemberRegistrationForm, EditMemberForm,
                          FamilyRelationshipForm, InventoryItemForm, UserReviewForm,
                            CalendarEventForm, TransactionForm, ActaForm,
                              MessageForm, SelectPastorCredencialForm,
                                ProfileEditForm, AnnouncementForm,
                                  AsignarSuperintendenteForm, JerarquiaSuperintForm,
                                    JerarquiaPastorForm, RequestMemberTransferForm,ApproveRejectTransferForm )
from sqlalchemy import or_, func, asc, desc, extract
from app.utils import (admin_or_secretary_required, get_user_for_view, describe_relationship,
                        pastor_required, calculate_age, get_iglesias_para_superintendente,
                          get_iglesias_para_jefe_sector)
from werkzeug.utils import secure_filename
import os, pdfkit
from wtforms import SelectField, SubmitField

routes_bp = Blueprint('routes', __name__)

# Decorador para verificar si es pastor
@pastor_required
# ... (definición del decorador si no existe)

def allowed_file(filename):
    allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', set())
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

@routes_bp.route('/')
def index():
    return redirect(url_for('routes.login'))

@routes_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('routes.dashboard'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        # --- MODIFICACIÓN AQUÍ ---
        # Verificar si el usuario existe, la contraseña es correcta Y está activo
        if user and bcrypt.check_password_hash(user.password, form.password.data) and user.is_active:
            login_user(user, remember=form.remember.data)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('routes.dashboard'))
        # --- FIN MODIFICACIÓN ---
        elif user and not user.is_active:
             flash('Tu cuenta ha sido desactivada. Por favor, contacta al administrador.', 'warning')
        else:
            flash('Login incorrecto. Verifica tu usuario y contraseña.', 'danger')
    return render_template('login.html', title='Login', form=form)

@routes_bp.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('routes.index'))

@routes_bp.route('/dashboard')
@login_required
def dashboard():
    view_user = get_user_for_view() # Usuario cuya vista estamos mostrando

    # Verificar si el usuario tiene un rol válido antes de proceder
    if not hasattr(view_user, 'role') or not view_user.role:
         flash('El usuario no tiene un rol asignado. Contacta al administrador.', 'danger')
         # Considera desloguear o redirigir a una página específica
         logout_user()
         return redirect(url_for('routes.login'))

    # --- DATOS PARA WIDGETS COMUNES ---
    unread_count = Message.query.filter_by(receiver_id=view_user.id, is_read=False).count() # Mensajes SIN LEER
    now_utc = datetime.utcnow()
    now_local = datetime.now() # Usar hora local para comparar cumpleaños si las fechas de nacimiento son locales
    today = now_local.date()
    next_week_date = today + timedelta(days=7)

    # Próximos cumpleaños (optimizado para buscar por mes/día)
    upcoming_birthdays_query = User.query.filter(
        User.date_of_birth != None,
        extract('month', User.date_of_birth) >= today.month, # Mes >= mes actual
        extract('day', User.date_of_birth) >= today.day      # Día >= día actual
    ).order_by(
        extract('month', User.date_of_birth),
        extract('day', User.date_of_birth)
    )

    # Añadir cumpleaños de la próxima semana que caen en el próximo año
    if next_week_date.year > today.year:
        # Añadir los que cumplen entre el 1 de Enero y la fecha límite de la próxima semana
        upcoming_birthdays_query_next_year = User.query.filter(
            User.date_of_birth != None,
            extract('month', User.date_of_birth) <= next_week_date.month,
            extract('day', User.date_of_birth) <= next_week_date.day
        ).order_by(
            extract('month', User.date_of_birth),
            extract('day', User.date_of_birth)
        )
        # Esta unión puede ser compleja, una alternativa es hacer 2 queries y combinar/ordenar en Python
        # Por simplicidad, haremos la combinación en Python

    # Filtrar por rol/iglesia DESPUÉS de la query base de fechas
    allowed_user_ids_for_birthdays = []
    if view_user.role in ['administrador', 'secretaria']:
        # Admin/Sec ven todos
        upcoming_birthdays = upcoming_birthdays_query.all()
        if next_week_date.year > today.year:
            upcoming_birthdays.extend(upcoming_birthdays_query_next_year.all())
    elif view_user.role == 'pastorado':
        # Pastor ve solo su iglesia
        user_ids_in_church = [u.id for u in User.query.with_entities(User.id).filter(User.church_id == view_user.church_id).all()]
        upcoming_birthdays = upcoming_birthdays_query.filter(User.id.in_(user_ids_in_church)).all()
        if next_week_date.year > today.year:
            upcoming_birthdays.extend(upcoming_birthdays_query_next_year.filter(User.id.in_(user_ids_in_church)).all())
    elif view_user.role == 'miembro':
         # Miembro ve solo el suyo? O ninguno en el widget? (Decidimos ninguno en widget por ahora)
         upcoming_birthdays = [] # Simplificado para miembro
         # upcoming_birthdays = upcoming_birthdays_query.filter(User.id == view_user.id).all() # Si quisiera ver el suyo
    else:
        upcoming_birthdays = []

    # Ordenar final y limitar (si se combinaron listas de Python)
    # Usar una clave que maneje el cruce de año (ej. días desde hoy)
    def days_until_birthday(user):
        bday = user.date_of_birth.replace(year=today.year)
        if bday < today:
            bday = bday.replace(year=today.year + 1)
        return (bday - today).days

    upcoming_birthdays.sort(key=days_until_birthday)
    upcoming_birthdays = [u for u in upcoming_birthdays if days_until_birthday(u) <= 7][:3] # Tomar los próximos 3 dentro de 7 días

    # --- OBTENER ANUNCIOS VISIBLES (Revisión Ligera) ---
    visible_announcements_query = Announcement.query.order_by(Announcement.created_at.desc())

    allowed_visibility = ['todos'] # Siempre ve 'todos'
    # Añadir visibilidad específica del rol actual
    if view_user.role:
        allowed_visibility.append(view_user.role)

    # Expandir visibilidad para roles superiores
    if view_user.role == 'administrador':
        allowed_visibility.extend(['secretaria', 'pastorado', 'miembros'])
    elif view_user.role == 'secretaria':
         allowed_visibility.extend(['pastorado', 'miembros']) # Asumiendo que ven roles 'inferiores'
    elif view_user.role == 'pastorado':
         allowed_visibility.append('miembros')

    # Construir el filtro OR
    visibility_filter = or_(
        Announcement.visibility.in_(allowed_visibility)
        # Añadir condición para iglesia específica si el usuario tiene iglesia
        # (El `False` explícito maneja el caso donde `view_user.church_id` es None)
        , (Announcement.visibility == 'iglesia_especifica') & (Announcement.target_church_id == view_user.church_id) if view_user.church_id else False
    )

    visible_announcements_query = visible_announcements_query.filter(visibility_filter)
    visible_announcements = visible_announcements_query.limit(5).all() # Limitar a 5 en dashboard

    # --- DATOS PARA WIDGETS ---
    widget_data = {
        'unread_count': unread_count,
        'upcoming_birthdays': upcoming_birthdays,
        'announcements': visible_announcements,
        # ... otras variables comunes si las hay ...
    }

    # ... (cálculos comunes como birthdays, announcements) ...

    # Renderizar la plantilla correspondiente
    if view_user.role == 'administrador':
        # ... (datos específicos admin) ...
        widget_data['new_users_count'] = ...
        widget_data['pending_docs_count'] = ...
        return render_template('admin/dashboard.html', view_user=view_user, **widget_data)

    elif view_user.role == 'secretaria':
         # ... (datos específicos secretaria) ...
         widget_data['pending_docs_count'] = ...
         widget_data['total_users_count'] = ...
         return render_template('secretaria/dashboard.html', view_user=view_user, **widget_data)

    elif view_user.role == 'pastorado':
        # --- VERIFICA ESTA SECCIÓN ---
        account = None
        if view_user.church_id: # Solo buscar si tiene iglesia
            account = Account.query.filter_by(church_id=view_user.church_id).first()
        # >>> ESTA LÍNEA ES CRUCIAL <<<
        widget_data['church_balance'] = account.balance if account else None
        widget_data['members_count'] = User.query.filter_by(church_id=view_user.church_id, role='miembro').count() if view_user.church_id else 0
        # >>> Y ESTA FORMA DE PASAR LAS VARIABLES <<<
        return render_template('pastorado/dashboard.html', view_user=view_user, **widget_data)
        # --- FIN VERIFICACIÓN ---

    elif view_user.role == 'miembro':
        # ... (datos específicos miembro) ...
        return render_template('miembros/dashboard.html', view_user=view_user, **widget_data)

    else:
        # Rol desconocido ya manejado al inicio
        # Este bloque else es por si acaso, pero no debería alcanzarse si la verificación inicial funciona
        flash("Rol de usuario inesperado.", "danger")
        logout_user()
        return redirect(url_for('routes.login'))

@routes_bp.route('/crear_admin', methods=['GET', 'POST'])
def crear_admin():
    admin_exists = User.query.filter_by(role='administrador').first()
    if admin_exists:
        return "Ya existe un administrador. Esta ruta está desactivada.", 403

    form = RegistrationForm()
    if form.validate_on_submit():
        hashed_password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
        # Usar first_name y last_name en lugar de full_name
        user = User(username=form.username.data,
                    email=form.email.data,
                    password=hashed_password,
                    role='administrador',
                    first_name=form.first_name.data,
                    last_name=form.last_name.data)
        db.session.add(user)
        db.session.commit()
        flash('Administrador creado exitosamente! Ahora puedes iniciar sesión.', 'success')
        return redirect(url_for('routes.login'))
    return render_template('register.html', title='Crear Administrador', form=form)

@routes_bp.route('/register', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def register():
    form = RegistrationForm()
    form.pastor_roles.choices = [(role.id, role.role_name) for role in PastorRole.query.all()]
    form.church.choices = [(0, 'Ninguna')] + [(church.id, church.name) for church in Church.query.all()]

    if form.validate_on_submit():
        hashed_password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
        if form.role.data in ['miembro', 'pastorado'] and current_user.role in ('administrador', 'secretaria'):
            church_id = form.church.data if form.church.data != 0 else None
        else:
            church_id = None
        user = User(
            username=form.username.data,
            email=form.email.data,
            password=hashed_password,
            role=form.role.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            church_id=church_id
        )
        db.session.add(user)
        db.session.commit()

        if form.role.data == 'pastorado':
            for role_id in form.pastor_roles.data:
                role = PastorRole.query.get(role_id)
                if role:
                    user.pastor_roles.append(role)
            db.session.commit()
            pastor = Pastor(user_id=user.id)
            db.session.add(pastor)
            db.session.commit()
        elif form.role.data == 'miembro':
            member = Member(user_id=user.id, church_id=form.church.data)
            db.session.add(member)
            db.session.commit()

        # Agregar reseña para el histórico de cambios
        review_text = "Usuario creado"
        reviewer_id = current_user.id if current_user.is_authenticated else user.id
        new_review = UserReview(user_id=user.id, reviewer_id=reviewer_id, review_text=review_text)
        db.session.add(new_review)
        db.session.commit()

        flash('Usuario creado exitosamente!', 'success')
        return redirect(url_for('routes.dashboard'))

    return render_template('register.html', title='Registrar Usuario', form=form)

@routes_bp.route('/create_church', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def create_church():
    form = ChurchForm()
    form.pastor.choices = [(0, 'Ninguno')] + [(user.id, f"{user.first_name} {user.last_name}") for user in User.query.filter_by(role='pastorado').all()]

    if form.validate_on_submit():
        pastor_id = form.pastor.data if form.pastor.data != 0 else None
        church = Church(
            name=form.name.data,
            address=form.address.data,
            district=form.district.data,
            pastor_id=pastor_id,
            latitude=form.latitude.data,
            longitude=form.longitude.data
        )
        db.session.add(church)
        db.session.commit()
        if pastor_id:
            pastor_user = User.query.get(pastor_id)
            if pastor_user:
                pastor_user.church_id = church.id
                db.session.commit()
        flash('Iglesia creada exitosamente!', 'success')
        return redirect(url_for('routes.dashboard'))

    return render_template('create_church.html', title='Crear Iglesia', form=form)

@routes_bp.route('/edit_church/<int:church_id>', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def edit_church(church_id):
    church = Church.query.get_or_404(church_id)
    # Instanciar el formulario.
    # - Si es GET, usar obj=church para poblar desde la BD.
    # - Si es POST, WTForms usará request.form automáticamente.
    form = ChurchForm(obj=church if request.method == 'GET' else None)

    # Poblar choices del pastor gobernante (siempre)
    form.pastor.choices = [(0, 'Ninguno')] + [(user.id, f"{user.first_name} {user.last_name}") for user in User.query.filter_by(role='pastorado').order_by('last_name', 'first_name').all()]

    # Asegurar que el pastor actual esté seleccionado en GET
    if request.method == 'GET':
        form.pastor.data = church.pastor_id if church.pastor_id else 0

    # --- Procesar POST ---
    if form.validate_on_submit():
        # --- CORRECCIÓN: Asignar explícitamente TODOS los campos del form al objeto church ---
        church.name = form.name.data
        church.address = form.address.data
        church.city = form.city.data          # <-- ASEGURARSE QUE ESTÉ
        church.province = form.province.data  # <-- ASEGURARSE QUE ESTÉ
        church.district = form.district.data
        church.latitude = form.latitude.data
        church.longitude = form.longitude.data
        new_pastor_id = form.pastor.data if form.pastor.data != 0 else None

        # --- Lógica para actualizar pastor_id (sin cambios necesarios aquí) ---
        # No necesitas quitar el pastor anterior aquí, SQLAlchemy maneja la FK.
        # Simplemente asigna el nuevo pastor_id.
        church.pastor_id = new_pastor_id
        # --- FIN Lógica pastor_id ---

        try:
            db.session.commit() # Guardar los cambios en la iglesia
            flash('Iglesia actualizada exitosamente!', 'success')
            # MODIFICADO: Redirigir a la lista de iglesias para ver el cambio
            return redirect(url_for('routes.list_churches'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error al actualizar iglesia {church_id}: {e}", exc_info=True)
            flash(f"Error al guardar los cambios de la iglesia: {e}", "danger")
            # No redirigir, renderizar de nuevo con errores (si validate_on_submit falló)
            # o mostrar error si falló el commit

    # --- Renderizar si es GET o la validación falló ---
    # Si falló la validación, form ya contiene los datos enviados y los errores.
    # Si es GET, form se pobló con obj=church.
    return render_template('edit_church.html', title='Editar Iglesia', form=form, church=church)

@routes_bp.route('/users')
@login_required
@admin_or_secretary_required
def list_users():
    # --- Obtener filtros y paginación ---
    search_term = request.args.get('search', '').strip()
    status_filter = request.args.get('status', 'all') # 'all', 'active', 'inactive'
    page = request.args.get('page', 1, type=int) # Página actual
    per_page = current_app.config.get('USERS_PER_PAGE', 15) # Usuarios por página

    # --- Query Base ---
    query = User.query.options(db.joinedload(User.church)) # Carga ansiosa de iglesia

    # --- Aplicar Filtro de Estado ---
    if status_filter == 'active':
        query = query.filter(User.is_active == True)
    elif status_filter == 'inactive':
        query = query.filter(User.is_active == False)
    # Si es 'all', no se aplica filtro de estado

    # --- Aplicar Filtro de Búsqueda ---
    if search_term:
        query = query.filter(
            or_(
                func.concat(User.first_name, ' ', User.last_name).ilike(f'%{search_term}%'),
                User.username.ilike(f'%{search_term}%'),
                User.email.ilike(f'%{search_term}%'),
                User.dni.ilike(f'%{search_term}%') # Buscar por DNI también
            )
        )

    # --- Ordenación (opcional, ejemplo por apellido) ---
    query = query.order_by(User.last_name, User.first_name)

    # --- Ejecutar Paginación ---
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    users_list = pagination.items

    # --- Pasar filtros a la plantilla ---
    filters = {
        'search': search_term,
        'status': status_filter
    }

    return render_template(
        'admin/list_users.html',
        users=users_list,
        pagination=pagination,
        filters=filters # Pasar filtros para pre-rellenar form y paginación
    )

@routes_bp.route('/users/<int:user_id>')
@login_required
@admin_or_secretary_required
def user_detail(user_id):
    user = User.query.options(
    db.joinedload(User.church),
    # Verifica que la siguiente línea esté comentada o eliminada:
    # db.joinedload(User.pastor_roles),
    db.joinedload(User.member).joinedload(Member.functions) # Otros joinedload están bien
    ).get_or_404(user_id)
    user = db.session.get(User, user.id)
    if not user:
        abort(404)
    relationships = db.session.query(UserRelationship).filter(
        or_(UserRelationship.user_id_1 == user_id, UserRelationship.user_id_2 == user_id)
    ).all()

    member_record = Member.query.filter_by(user_id=user.id).first()
    reviews = user.reviews
    full_name = f"{user.first_name} {user.last_name}"
    return render_template('admin/user_detail.html',
                           user=user,
                           full_name=full_name,
                           relationships=relationships,
                           describe_relationship=describe_relationship,
                           member=member_record,
                           reviews=reviews)

@routes_bp.route('/edit_user/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def edit_user(user_id):
    user = User.query.get_or_404(user_id)
    form = EditUserForm(obj=user)
    form.pastor_roles.choices = [(role.id, role.role_name) for role in PastorRole.query.all()]
    form.church.choices = [(0, 'Ninguna')] + [(church.id, church.name) for church in Church.query.all()]
    form.church.data = user.church_id if user.church_id else 0

    pastor_form = None
    member_form = None
    member_obj = None

    if user.role == 'pastorado':
        pastor = Pastor.query.filter_by(user_id=user.id).first()
        if not pastor:
            pastor = Pastor(user_id=user.id)
            db.session.add(pastor)
            db.session.commit()
        pastor_form = PastorForm(obj=pastor)
    elif user.role == 'miembro':
        member_obj = Member.query.filter_by(user_id=user.id).first()
        member_form = EditMemberForm(obj=user)
        member_form.member_functions.choices = [(func.id, func.name) for func in MemberFunction.query.all()]
        if request.method == 'GET' and member_obj:
            member_form.member_functions.data = [func.id for func in member_obj.functions]

    if form.validate_on_submit():
        old_data = {
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone_number": user.phone_number,
            "address": user.address,
            "date_of_birth": user.date_of_birth,
        }
        if user.role == 'miembro' and member_obj:
            old_data["alergies"] = member_obj.alergies
            old_data["emergency_contact"] = member_obj.emergency_contact

        user.username = form.username.data
        user.first_name = form.first_name.data
        user.last_name = form.last_name.data
        user.email = form.email.data
        user.role = form.role.data
        user.phone_number = form.phone_number.data
        user.address = form.address.data
        user.date_of_birth = form.date_of_birth.data
        user.church_id = form.church.data if form.church.data != 0 else None
        if form.password.data:
            user.password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')

        if user.role == 'pastorado' and pastor_form:
            pastor = Pastor.query.filter_by(user_id=user.id).first()
            if pastor:
                pastor.address = pastor_form.address.data
                pastor.latitude = pastor_form.latitude.data
                pastor.longitude = pastor_form.longitude.data
            allowed_grades = ['probando', 'diacono', 'presbistero']
            user.pastor_roles = [r for r in user.pastor_roles if r.role_name not in allowed_grades]
            new_grade = PastorRole.query.filter_by(role_name=pastor_form.grado.data).first()
            if new_grade:
                user.pastor_roles.append(new_grade)
        elif user.role == 'miembro' and member_form and member_obj:
            selected_functions = MemberFunction.query.filter(
                MemberFunction.id.in_(member_form.member_functions.data)
            ).all()
            member_obj.functions = selected_functions
            member_obj.alergies = form.alergies.data
            member_obj.emergency_contact = form.emergency_contact.data

        try:
            db.session.commit()
            changes = []
            for field, old_value in old_data.items():
                new_value = getattr(user, field)
                if str(new_value) != str(old_value):
                    changes.append(f"{field}: '{old_value}' -> '{new_value}'")
            detailed_review = "Perfil actualizado"
            if changes:
                detailed_review += " - " + ", ".join(changes)
            else:
                detailed_review += " sin cambios significativos"
            new_review = UserReview(user_id=user.id, reviewer_id=current_user.id, review_text=detailed_review)
            db.session.add(new_review)
            db.session.commit()

            flash("Usuario actualizado exitosamente!", "success")
            return redirect(url_for('routes.user_detail', user_id=user.id))
        except Exception as e:
            db.session.rollback()
            flash(f"Error al guardar los cambios: {e}", "danger")
            return redirect(url_for('routes.admin_edit_profile', user_id=user.id))

    return render_template('admin/edit_profile.html', form=form, pastor_form=pastor_form, member_form=member_form, user=user)

@routes_bp.route('/member_relationships/<int:user_id>')
@login_required
def member_relationships(user_id):
    user = User.query.get_or_404(user_id)
    if current_user.role not in ['administrador', 'secretaria']:
        if current_user.role == 'pastorado' and user.church_id != current_user.church_id:
            flash("No tienes permiso para ver las relaciones de este usuario.", "danger")
            return redirect(url_for('routes.dashboard'))
    relationships = db.session.query(UserRelationship).filter(
        or_(UserRelationship.user_id_1 == user_id, UserRelationship.user_id_2 == user_id)
    ).all()
    return render_template('admin/member_relationships.html', user=user, relationships=relationships, describe_relationship=describe_relationship)

@routes_bp.route('/send_message', methods=['GET', 'POST'])
@login_required
def send_message():
    form = MessageForm()

    if current_user.role == 'miembro':
        if current_user.church and current_user.church.pastor:
            form.receiver.choices = [(current_user.church.pastor.id, f"{current_user.church.pastor.first_name} {current_user.church.pastor.last_name}")]
        else:
            flash("No tienes un pastor asignado.", "danger")
            return redirect(url_for('routes.dashboard'))
    else:
        users = User.query.filter(User.id != current_user.id).all()
        form.receiver.choices = [(u.id, f"{u.first_name} {u.last_name}") for u in users]

    if form.validate_on_submit():
        if current_user.role == 'miembro' and form.receiver.data != current_user.church.pastor.id:
            flash("Como miembro, solo puedes enviar mensajes a tu pastor.", "danger")
            return redirect(url_for('routes.send_message'))

        message = Message(
            sender_id=current_user.id,
            receiver_id=form.receiver.data,
            subject=form.subject.data,
            message_text=form.message_text.data
        )
        db.session.add(message)
        db.session.commit()
        flash("Mensaje enviado correctamente.", "success")
        return redirect(url_for('routes.dashboard'))

    return render_template('admin/send_message.html', form=form)

@routes_bp.route('/inbox')
@login_required
def inbox():
    messages = db.session.query(Message).filter_by(receiver_id=current_user.id).order_by(Message.sent_at.desc()).all()
    return render_template('admin/inbox.html', messages=messages)

@routes_bp.route('/forward_message/<int:message_id>', methods=['GET', 'POST'])
@login_required
def forward_message(message_id):
    original = Message.query.get_or_404(message_id)
    form = MessageForm()
    if not form.is_submitted():
        form.subject.data = "Fwd: " + (original.subject or "")
        form.message_text.data = "\n\n---- Mensaje Original ----\n" + original.message_text

    if current_user.role == 'miembro':
        form.receiver.choices = [(current_user.church.pastor.id, f"{current_user.church.pastor.first_name} {current_user.church.pastor.last_name}")]
    else:
        users = User.query.filter(User.id != current_user.id).all()
        form.receiver.choices = [(u.id, f"{u.first_name} {u.last_name}") for u in users]

    if form.validate_on_submit():
        forwarded = Message(
            sender_id=current_user.id,
            receiver_id=form.receiver.data,
            subject=form.subject.data,
            message_text=form.message_text.data,
            forwarded_from=original.id
        )
        db.session.add(forwarded)
        db.session.commit()
        flash("Mensaje reenviado correctamente.", "success")
        return redirect(url_for('routes.inbox'))

    return render_template('admin/forward_message.html', form=form, original=original)

@routes_bp.route('/reply_message/<int:message_id>', methods=['GET', 'POST'])
@login_required
def reply_message(message_id):
    original = Message.query.get_or_404(message_id)
    form = MessageForm()
    if not form.is_submitted():
        form.subject.data = "Re: " + (original.subject or "")
        form.message_text.data = "\n\n---- Mensaje Original ----\n" + original.message_text
    form.receiver.choices = [(original.sender.id, f"{original.sender.first_name} {original.sender.last_name}")]

    if form.validate_on_submit():
        reply = Message(
            sender_id=current_user.id,
            receiver_id=form.receiver.data,
            subject=form.subject.data,
            message_text=form.message_text.data
        )
        db.session.add(reply)
        db.session.commit()
        flash("Respuesta enviada correctamente.", "success")
        return redirect(url_for('routes.inbox'))

    return render_template('admin/reply_message.html', form=form, original=original)

@routes_bp.route('/delete_message/<int:message_id>', methods=['POST'])
@login_required
def delete_message(message_id):
    message = Message.query.get_or_404(message_id)
    if current_user.id not in [message.sender_id, message.receiver_id]:
        flash("No tienes permiso para eliminar este mensaje.", "danger")
        return redirect(url_for('routes.inbox'))
    db.session.delete(message)
    db.session.commit()
    flash("Mensaje eliminado correctamente.", "success")
    return redirect(url_for('routes.inbox'))

@routes_bp.route('/add_review', methods=['GET', 'POST'])
@login_required
def add_review():
    user_id = request.args.get('user_id', type=int)
    if not user_id:
        flash("Debes seleccionar un usuario a revisar.", "warning")
        return redirect(url_for('routes.choose_review_user'))

    user_to_review = User.query.get_or_404(user_id)
    if current_user.role == 'pastorado':
        if user_to_review.role != 'miembro' or user_to_review.church_id != current_user.church_id:
            flash("No tienes permiso para escribir reseñas para este usuario.", "danger")
            return redirect(url_for('routes.dashboard'))

    form = UserReviewForm()
    if form.validate_on_submit():
        review = UserReview(user_id=user_to_review.id, reviewer_id=current_user.id, review_text=form.review_text.data)
        db.session.add(review)
        db.session.commit()
        flash("Reseña guardada correctamente.", "success")
        return redirect(url_for('routes.user_detail', user_id=user_to_review.id))
    return render_template('admin/add_review.html', form=form, user=user_to_review)

@routes_bp.route('/list_reviews')
@login_required
def list_reviews():
    if current_user.role in ['administrador', 'secretaria']:
        reviews = UserReview.query.order_by(UserReview.created_at.desc()).all()
    elif current_user.role == 'pastorado':
        reviews = UserReview.query.join(User, UserReview.user_id == User.id)\
                  .filter(User.church_id == current_user.church_id)\
                  .order_by(UserReview.created_at.desc()).all()
    else:
        reviews = []
    return render_template('admin/list_reviews.html', reviews=reviews)

@routes_bp.route('/edit_review/<int:review_id>', methods=['GET', 'POST'])
@login_required
def edit_review(review_id):
    review = UserReview.query.get_or_404(review_id)
    reviewed_user = User.query.get(review.user_id)
    if current_user.role == 'pastorado':
        if reviewed_user.role != 'miembro' or reviewed_user.church_id != current_user.church_id:
            flash("No tienes permiso para editar esta reseña.", "danger")
            return redirect(url_for('routes.dashboard'))
    elif current_user.role not in ['administrador', 'secretaria']:
        flash("No tienes permiso para editar reseñas.", "danger")
        return redirect(url_for('routes.dashboard'))
    form = UserReviewForm(obj=review)
    if form.validate_on_submit():
        review.review_text = form.review_text.data
        db.session.commit()
        flash("Reseña actualizada correctamente.", "success")
        return redirect(url_for('routes.user_detail', user_id=review.user_id))
    return render_template('admin/edit_review.html', form=form, review=review)

@routes_bp.route('/delete_review/<int:review_id>', methods=['POST'])
@login_required
def delete_review(review_id):
    review = UserReview.query.get_or_404(review_id)
    reviewed_user = User.query.get(review.user_id)
    if current_user.role == 'pastorado':
        if reviewed_user.role != 'miembro' or reviewed_user.church_id != current_user.church_id:
            flash("No tienes permiso para eliminar esta reseña.", "danger")
            return redirect(url_for('routes.dashboard'))
    elif current_user.role not in ['administrador', 'secretaria']:
        flash("No tienes permiso para eliminar reseñas.", "danger")
        return redirect(url_for('routes.dashboard'))
    db.session.delete(review)
    db.session.commit()
    flash("Reseña eliminada correctamente.", "success")
    return redirect(url_for('routes.user_detail', user_id=review.user_id))

@routes_bp.route('/choose_review_user', methods=['GET', 'POST'])
@login_required
def choose_review_user():
    from app.forms import SelectReviewUserForm
    form = SelectReviewUserForm()
    if current_user.role in ['administrador', 'secretaria']:
        users = User.query.order_by(func.concat(User.first_name, ' ', User.last_name)).all()
    elif current_user.role == 'pastorado':
        users = User.query.filter_by(church_id=current_user.church_id, role='miembro').order_by(func.concat(User.first_name, ' ', User.last_name)).all()
    else:
        flash("No tienes permiso para escribir reseñas.", "danger")
        return redirect(url_for('routes.dashboard'))
    form.user.choices = [(u.id, f"{u.first_name} {u.last_name}") for u in users]
    if form.validate_on_submit():
        return redirect(url_for('routes.add_review', user_id=form.user.data))
    return render_template('admin/choose_review_user.html', form=form)

@routes_bp.route('/admin/edit_profile/<int:user_id>', methods=['GET', 'POST'])
@login_required
#@admin_or_secretary_required # O usa la lógica de permisos abajo
def admin_edit_profile(user_id):
    user_to_edit = User.query.get_or_404(user_id) # Renombrar variable para claridad

    # Lógica de permisos: Solo Admin/Secretaria pueden usar esta ruta
    if current_user.role not in ['administrador', 'secretaria']:
        flash("No tienes permiso para acceder a esta función administrativa.", "danger")
        return redirect(url_for('routes.dashboard'))
    # Opcional: Impedir que editen a otros admins/secretarias?
    # if user_to_edit.role in ['administrador', 'secretaria'] and current_user.id != user_to_edit.id:
    #    flash("No puedes editar otros administradores o secretarias desde aquí.", "danger")
    #    return redirect(url_for('routes.list_users'))


    # --- CORRECCIÓN: Pasar argumentos originales al crear el form ---
    form = EditUserForm(
        original_username=user_to_edit.username,
        original_email=user_to_edit.email,
        original_dni=user_to_edit.dni,
        obj=user_to_edit # Usar user_to_edit aquí
    )
    # --- FIN CORRECCIÓN ---

    # Poblar choices para selects
    form.pastor_roles.choices = [(role.id, role.role_name) for role in PastorRole.query.order_by('role_name').all()]
    form.church.choices = [(0, 'Ninguna')] + [(c.id, c.name) for c in Church.query.order_by('name').all()]

    # --- Lógica para PastorForm ---
    pastor_form = None
    if user_to_edit.role == 'pastorado': # Usar user_to_edit
        pastor = user_to_edit.pastor
        if not pastor:
            print(f"WARN: Usuario {user_id} (admin edit) es pastorado pero no tenía registro en Pastor. Creando uno.")
            pastor = Pastor(user_id=user_to_edit.id)
            db.session.add(pastor)
        if pastor:
            pastor_form = PastorForm(formdata=request.form if request.method == 'POST' else None, obj=pastor)
        else:
            pastor_form = PastorForm(formdata=request.form if request.method == 'POST' else None)

    # --- Lógica al enviar el formulario (POST) ---
    if form.validate_on_submit():
        pastor_form_is_valid = True
        if user_to_edit.role == 'pastorado' and pastor_form:
             pastor_form_is_valid = pastor_form.validate() # Validar si es necesario

        if pastor_form_is_valid:
            # Capturar datos ANTES de modificar (opcional, para reseña)
            old_data = { "username": user_to_edit.username, "email": user_to_edit.email, "first_name": user_to_edit.first_name,
                         "last_name": user_to_edit.last_name, "phone_number": user_to_edit.phone_number, "address": user_to_edit.address,
                         "date_of_birth": user_to_edit.date_of_birth, "dni": user_to_edit.dni, "ciudad": user_to_edit.ciudad,
                         "estado_civil": user_to_edit.estado_civil, "role": user_to_edit.role, "church_id": user_to_edit.church_id }
            if user_to_edit.pastor:
                 old_data.update({"pastor_grado": user_to_edit.pastor.grado, "pastor_matricula": user_to_edit.pastor.matricula,
                                 "pastor_address": user_to_edit.pastor.address})

            # --- Asignar datos de EditUserForm al objeto User ---
            user = user_to_edit # Usar user_to_edit para modificar
            user.username = form.username.data
            user.first_name = form.first_name.data
            user.last_name = form.last_name.data
            user.email = form.email.data
            user.role = form.role.data
            user.phone_number = form.phone_number.data
            user.address = form.address.data # Dirección PERSONAL
            user.date_of_birth = form.date_of_birth.data
            user.church_id = form.church.data if form.church.data != 0 else None # Iglesia a la que PERTENECE
            user.dni = form.dni.data
            user.ciudad = form.ciudad.data # Ciudad PERSONAL
            user.estado_civil = form.estado_civil.data

            if form.password.data:
                user.password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')

            # --- Lógica específica para Pastor (actualizar tabla 'pastores') ---
            if user.role == 'pastorado' and pastor_form:
                pastor = user.pastor
                if pastor:
                    pastor.grado = pastor_form.grado.data
                    pastor.matricula = pastor_form.matricula.data
                    pastor.address = pastor_form.address.data # Casa pastoral
                    pastor.latitude = pastor_form.latitude.data
                    pastor.longitude = pastor_form.longitude.data

                # Actualizar roles adicionales
                selected_roles = PastorRole.query.filter(PastorRole.id.in_(form.pastor_roles.data)).all()
                user.pastor_roles = selected_roles

            # --- Lógica si el rol CAMBIA (idéntica a edit_profile) ---
            elif old_data.get("role") == 'pastorado' and user.role != 'pastorado':
                 if user.pastor: db.session.delete(user.pastor)
                 user.pastor_roles = []
            elif old_data.get("role") == 'miembro' and user.role != 'miembro':
                 if user.member: db.session.delete(user.member)
            elif user.role == 'miembro' and not user.member:
                 if not user.church_id:
                      flash("Para asignar el rol 'Miembro', debe seleccionar una iglesia.", "warning")
                 else:
                      new_member = Member(user_id=user.id, church_id=user.church_id)
                      db.session.add(new_member)

            try:
                db.session.commit()
                # --- Lógica de Reseña/Historial (idéntica a edit_profile) ---
                changes = []
                for field, old_value in old_data.items():
                    new_value = None
                    if field.startswith("pastor_"):
                        pastor_field = field.split("pastor_")[1]
                        if user.pastor: new_value = getattr(user.pastor, pastor_field, None)
                    else:
                        new_value = getattr(user, field, None)
                    if str(old_value or '') != str(new_value or ''):
                        changes.append(f"{field}: '{old_value or 'Vacío'}' -> '{new_value or 'Vacío'}'")
                if changes: detailed_review = "Perfil actualizado (Admin) - " + ", ".join(changes)
                else: detailed_review = "Perfil actualizado (Admin - sin cambios detectados)"
                new_review = UserReview(user_id=user.id, reviewer_id=current_user.id, review_text=detailed_review)
                db.session.add(new_review)
                db.session.commit()
                # --- Fin Lógica Reseña ---

                flash("Usuario actualizado exitosamente!", "success")
                return redirect(url_for('routes.user_detail', user_id=user.id))
            except Exception as e:
                db.session.rollback()
                flash(f"Error al guardar los cambios: {e}", "danger")

        else:
             flash("Por favor, corrige los errores en la sección de Información del Pastor.", "warning")


    # --- Lógica para GET (poblar selects/checkboxes si no es POST o falló validación) ---
    if request.method != 'POST' or not form.is_submitted():
        form.church.data = user_to_edit.church_id if user_to_edit.church_id else 0
        if user_to_edit.role == 'pastorado':
            form.pastor_roles.data = [role.id for role in user_to_edit.pastor_roles]

    # Renderizar la plantilla, pasando el usuario que se está editando
    return render_template('admin/edit_profile.html', form=form, pastor_form=pastor_form, user=user_to_edit)

@routes_bp.route('/churches')
@login_required
@admin_or_secretary_required
def list_churches():
    search_term = request.args.get('search')
    if search_term:
        churches = Church.query.filter(or_(
            Church.name.ilike(f'%{search_term}%'),
            Church.address.ilike(f'%{search_term}%'),
            Church.district.ilike(f'%{search_term}%')
            )).all()
    else:
        churches = Church.query.all()
    return render_template('admin/list_churches.html', churches=churches, search_term=search_term)

@routes_bp.route('/delete_church/<int:church_id>', methods=['POST'])
@login_required
@admin_or_secretary_required
def delete_church(church_id):
    church = Church.query.get_or_404(church_id)
    db.session.delete(church)
    db.session.commit()
    flash('Iglesia eliminada exitosamente!', 'success')
    return redirect(url_for('routes.list_churches'))

@routes_bp.route('/edit_pastor/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def edit_pastor(user_id):
    user = User.query.get_or_404(user_id)
    if user.role != 'pastorado':
        flash('Este usuario no es un pastor.', 'danger')
        return redirect(url_for('routes.list_users'))
    pastor = Pastor.query.filter_by(user_id=user.id).first()
    if pastor is None:
        pastor = Pastor(user_id=user.id)
        db.session.add(pastor)
        db.session.commit()
    form = PastorForm(obj=pastor)
    if form.validate_on_submit():
        pastor.latitude = form.latitude.data
        pastor.longitude = form.longitude.data
        pastor.address = form.address.data
        pastor.grado = form.grado.data
        db.session.commit()
        db.session.expire(pastor)
        flash('Información del pastor actualizada!', 'success')
        return redirect(url_for('routes.user_detail', user_id=user.id))
    return render_template('admin/edit_pastor.html', title='Editar Pastor', form=form, user=user)

@routes_bp.route('/register_member', methods=['GET', 'POST'])
@login_required
def register_member():
    if current_user.role != 'pastorado':
        flash('No tienes permiso para acceder a esta página.', 'danger')
        return redirect(url_for('routes.dashboard'))
    form = MemberRegistrationForm()
    form.member_functions.choices = [(func.id, func.name) for func in MemberFunction.query.all()]
    if form.validate_on_submit():
        hashed_password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
        user = User(
            username=form.username.data,
            email=form.email.data,
            password=hashed_password,
            role='miembro',
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            church_id=current_user.church_id
        )
        db.session.add(user)
        db.session.commit()
        member = Member(user_id=user.id, church_id=current_user.church_id)
        selected_functions = MemberFunction.query.filter(MemberFunction.id.in_(form.member_functions.data)).all()
        member.functions = selected_functions
        db.session.add(member)
        db.session.commit()
        flash('Miembro registrado exitosamente!', 'success')
        return redirect(url_for('routes.dashboard'))
    return render_template('pastorado/register_member.html', title='Registrar Miembro', form=form)

@routes_bp.route('/pastor/members')
@login_required
def pastor_members():
    if current_user.role != 'pastorado':
        flash('No tienes permiso para acceder a esta página.', 'danger')
        return redirect(url_for('routes.dashboard'))
    search_term = request.args.get('search', '')
    query = User.query.filter_by(church_id=current_user.church_id)
    if search_term:
        query = query.filter(
            or_(
                func.concat(User.first_name, ' ', User.last_name).ilike(f'%{search_term}%'),
                User.username.ilike(f'%{search_term}%'),
                User.email.ilike(f'%{search_term}%')
            )
        )
    members_and_pastors = query.all()
    return render_template('pastorado/members_list.html', members=members_and_pastors, pastor=current_user, search_term=search_term)

@routes_bp.route('/church_members_count', methods=['GET'])
@login_required
@admin_or_secretary_required
def church_members_count():
    churches = Church.query.all()
    church_counts = []
    for church in churches:
        member_count = Member.query.filter_by(church_id=church.id).count()
        pastor_count = User.query.filter_by(church_id=church.id, role='pastorado').count()
        total_count = member_count + pastor_count
        church_counts.append((church, total_count))
    return render_template('admin/church_members_count.html', church_counts=church_counts)

@routes_bp.route('/edit_member/<int:user_id>', methods=['GET', 'POST'])
@login_required
def edit_member(user_id):
    # Obtener el registro Member y el User asociado
    member = Member.query.filter_by(user_id=user_id).first()
    if not member:
        flash('Registro de Miembro no encontrado.', 'danger')
        # Redirigir adecuadamente
        return redirect(url_for('routes.dashboard')) # O list_users

    user = member.user # Obtener el objeto User asociado
    if not user: # Control extra por si hay inconsistencia
        flash('Error: Usuario asociado al miembro no encontrado.', 'danger')
        return redirect(url_for('routes.dashboard'))

    # --- Lógica de Permisos ---
    can_edit = False
    if current_user.role in ('administrador', 'secretaria'):
        can_edit = True
    elif current_user.role == 'pastorado' and member.church_id == current_user.church_id:
        can_edit = True
    # Permitir al miembro editar sus propios datos de miembro?
    # elif current_user.id == user_id:
    #    can_edit = True # Si quieres permitir autoedición de datos de miembro

    if not can_edit:
        flash('No tienes permiso para editar este miembro.', 'danger')
        return redirect(url_for('routes.dashboard'))

    # --- CORRECCIÓN: Pasar args originales a EditMemberForm ---
    # Asumiendo que EditMemberForm ahora requiere original_email y original_dni
    form = EditMemberForm(
        original_email=user.email,
        original_dni=user.dni
        # No usar obj=user aquí, poblaremos manualmente o con obj=member si EditMemberForm solo tuviera campos de Member
    )
    # --- FIN CORRECCIÓN ---

    # Poblar choices de funciones
    form.member_functions.choices = [(func.id, func.name) for func in MemberFunction.query.order_by('name').all()]

    # --- Procesar POST ---
    if form.validate_on_submit():
        # Capturar datos antiguos (opcional para reseña)
        old_data = { "first_name": user.first_name, "last_name": user.last_name, "email": user.email,
                     "phone_number": user.phone_number, "address": user.address, "date_of_birth": user.date_of_birth,
                     "ciudad": user.ciudad, "estado_civil": user.estado_civil, "dni": user.dni,
                     "alergies": member.alergies, "emergency_contact": member.emergency_contact,
                     "functions": [f.id for f in member.functions]}

        # Actualizar datos del User
        user.first_name = form.first_name.data
        user.last_name = form.last_name.data
        user.email = form.email.data
        user.phone_number = form.phone_number.data
        user.address = form.address.data
        user.date_of_birth = form.date_of_birth.data
        user.ciudad = form.ciudad.data
        user.estado_civil = form.estado_civil.data
        user.dni = form.dni.data

        # Actualizar datos del Member
        member.alergies = form.alergies.data
        member.emergency_contact = form.emergency_contact.data
        selected_functions = MemberFunction.query.filter(MemberFunction.id.in_(form.member_functions.data)).all()
        member.functions = selected_functions # SQLAlchemy maneja la tabla de asociación

        try:
            db.session.commit() # Guardar cambios en User y Member

             # --- Lógica de Reseña/Historial ---
            changes = []
            # Compara campos de User
            for field in ["first_name", "last_name", "email", "phone_number", "address", "date_of_birth", "ciudad", "estado_civil", "dni"]:
                 if str(old_data.get(field) or '') != str(getattr(user, field) or ''):
                     changes.append(f"{field}: '{old_data.get(field) or 'Vacío'}' -> '{getattr(user, field) or 'Vacío'}'")
            # Compara campos de Member
            for field in ["alergies", "emergency_contact"]:
                 if str(old_data.get(field) or '') != str(getattr(member, field) or ''):
                     changes.append(f"{field}: '{old_data.get(field) or 'Vacío'}' -> '{getattr(member, field) or 'Vacío'}'")
            # Compara funciones (más complejo, solo detectar si cambió o no)
            old_func_ids = set(old_data.get("functions", []))
            new_func_ids = set(f.id for f in member.functions)
            if old_func_ids != new_func_ids:
                 changes.append("funciones: modificadas")

            if changes: detailed_review = f"Datos de miembro actualizados ({current_user.username}) - " + ", ".join(changes)
            else: detailed_review = f"Datos de miembro actualizados ({current_user.username} - sin cambios detectados)"
            new_review = UserReview(user_id=user.id, reviewer_id=current_user.id, review_text=detailed_review)
            db.session.add(new_review)
            db.session.commit()
             # --- Fin Lógica Reseña ---


            flash('Miembro actualizado exitosamente!', 'success')
            # Redirigir a la lista/detalle correspondiente
            if current_user.role == 'pastorado':
                return redirect(url_for('routes.pastor_members'))
            else: # admin/secretaria
                return redirect(url_for('routes.user_detail', user_id=user.id))
        except Exception as e:
             db.session.rollback()
             flash(f'Error al actualizar miembro: {e}', 'danger')
             # No redirigir

    # --- Lógica para GET o si falla validación ---
    # Poblar el formulario con los datos actuales si no es un POST exitoso
    if request.method != 'POST' or not form.is_submitted():
        form.first_name.data = user.first_name
        form.last_name.data = user.last_name
        form.email.data = user.email
        form.phone_number.data = user.phone_number
        form.address.data = user.address
        form.date_of_birth.data = user.date_of_birth
        form.ciudad.data = user.ciudad
        form.estado_civil.data = user.estado_civil
        form.dni.data = user.dni
        form.alergies.data = member.alergies
        form.emergency_contact.data = member.emergency_contact
        form.member_functions.data = [func.id for func in member.functions]


    # Renderizar la plantilla
    return render_template('admin/edit_member.html', title='Editar Miembro', form=form, user=user, member=member)

@routes_bp.route('/add_relationship', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def add_relationship():
    form = FamilyRelationshipForm()
    form.user1.choices = [(user.id, f"{user.first_name} {user.last_name} ({user.username})") for user in User.query.all()]
    form.user2.choices = [(user.id, f"{user.first_name} {user.last_name} ({user.username})") for user in User.query.all()]
    if form.validate_on_submit():
        existing_relation = db.session.query(UserRelationship).filter(
            ((UserRelationship.user_id_1 == form.user1.data) & (UserRelationship.user_id_2 == form.user2.data)) |
            ((UserRelationship.user_id_1 == form.user2.data) & (UserRelationship.user_id_2 == form.user1.data))
        ).first()
        if existing_relation:
            flash('Esta relación familiar ya existe (o su inversa).', 'warning')
            return redirect(url_for('routes.add_relationship'))
        relationship = UserRelationship(
            user_id_1=form.user1.data,
            user_id_2=form.user2.data,
            tipo_de_relacion=form.relationship_type.data
        )
        db.session.add(relationship)
        db.session.commit()
        flash('Relación familiar agregada exitosamente!', 'success')
        return redirect(url_for('routes.list_users'))
    return render_template('admin/add_relationship.html', title='Agregar Relación Familiar', form=form)

@routes_bp.route('/edit_relationship/<int:relationship_id>', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required
def edit_relationship(relationship_id):
    relationship = db.session.query(UserRelationship).get_or_404(relationship_id)
    form = FamilyRelationshipForm(obj=relationship)
    form.user1.choices = [(user.id, f"{user.first_name} {user.last_name} ({user.username})") for user in User.query.all()]
    form.user2.choices = [(user.id, f"{user.first_name} {user.last_name} ({user.username})") for user in User.query.all()]
    form.user1.data = relationship.user_id_1
    form.user2.data = relationship.user_id_2
    if form.validate_on_submit():
        existing_relation = db.session.query(UserRelationship).filter(
            ((UserRelationship.user_id_1 == form.user2.data) & (UserRelationship.user_id_2 == form.user1.data)) &
            (UserRelationship.id != relationship_id)
        ).first()
        if existing_relation:
            flash('Esta relación familiar ya existe (o su inversa).', 'warning')
            return redirect(url_for('routes.edit_relationship', relationship_id=relationship_id))
        relationship.user_id_1 = form.user1.data
        relationship.user_id_2 = form.user2.data
        relationship.tipo_de_relacion = form.relationship_type.data
        db.session.commit()
        flash('Relación familiar actualizada exitosamente!', 'success')
        return redirect(url_for('routes.list_users'))
    return render_template('admin/edit_relationship.html', title='Editar Relación Familiar', form=form, relationship=relationship)

@routes_bp.route('/delete_relationship/<int:relationship_id>', methods=['POST'])
@login_required
@admin_or_secretary_required
def delete_relationship(relationship_id):
    relationship = db.session.query(UserRelationship).get_or_404(relationship_id)
    db.session.delete(relationship)
    db.session.commit()
    flash('Relación familiar eliminada exitosamente!', 'success')
    return redirect(url_for('routes.list_users'))

@routes_bp.route('/pastor/edit_relationship/<int:relationship_id>', methods=['GET', 'POST'])
@login_required
def edit_pastor_relationship(relationship_id):
    if current_user.role != 'pastorado':
        flash('No tienes permiso para acceder a esta página.', 'danger')
        return redirect(url_for('routes.dashboard'))
    relationship = db.session.query(UserRelationship).get_or_404(relationship_id)
    if (relationship.user1.church_id != current_user.church_id and
        relationship.user2.church_id != current_user.church_id):
        flash('No tienes permiso para editar esta relación.', 'danger')
        return redirect(url_for('routes.pastor_members'))
    form = FamilyRelationshipForm(obj=relationship)
    form.user1.choices = [(user.id, f"{user.first_name} {user.last_name} ({user.username})") for user in User.query.all()]
    form.user2.choices = [(user.id, f"{user.first_name} {user.last_name} ({user.username})") for user in User.query.all()]
    form.user1.data = relationship.user_id_1
    form.user2.data = relationship.user_id_2
    if form.validate_on_submit():
        existing_relation = db.session.query(UserRelationship).filter(
            ((UserRelationship.user_id_1 == form.user2.data) & (UserRelationship.user_id_2 == form.user1.data)) &
            (UserRelationship.id != relationship_id)
        ).first()
        if existing_relation:
            flash('Esta relación familiar ya existe (o su inversa).', 'warning')
            return redirect(url_for('routes.edit_pastor_relationship', relationship_id=relationship_id))
        relationship.user_id_1 = form.user1.data
        relationship.user_id_2 = form.user2.data
        relationship.tipo_de_relacion = form.relationship_type.data
        db.session.commit()
        flash('Relación familiar actualizada exitosamente!', 'success')
        return redirect(url_for('routes.pastor_members'))
    return render_template('pastorado/edit_relationship.html', title='Editar Relación Familiar', form=form, relationship=relationship)

@routes_bp.route('/pastor/delete_relationship/<int:relationship_id>', methods=['POST'])
@login_required
def delete_pastor_relationship(relationship_id):
    if current_user.role != 'pastorado':
        flash('No tienes permiso para acceder a esta página.', 'danger')
        return redirect(url_for('routes.dashboard'))
    relationship = db.session.query(UserRelationship).get_or_404(relationship_id)
    if (relationship.user1.church_id != current_user.church_id and
        relationship.user2.church_id != current_user.church_id):
        flash('No tienes permiso para eliminar esta relación.', 'danger')
        return redirect(url_for('routes.pastor_members'))
    db.session.delete(relationship)
    db.session.commit()
    flash('Relación familiar eliminada exitosamente!', 'success')
    return redirect(url_for('routes.pastor_members'))

@routes_bp.route('/inventory/<int:church_id>', methods=['GET'])
@login_required
def inventory(church_id):
    church = Church.query.get_or_404(church_id)
    if current_user.role not in ('administrador', 'secretaria'):
        if current_user.role != 'pastorado' or current_user.church_id != church_id:
            flash('No tienes permiso para ver este inventario.', 'danger')
            return redirect(url_for('routes.dashboard'))
    inventory_items = ChurchInventory.query.filter_by(church_id=church_id).all()
    return render_template('admin/inventory.html', church=church, inventory_items=inventory_items)

@routes_bp.route('/inventory/<int:church_id>/add', methods=['GET', 'POST'])
@login_required
def add_inventory_item(church_id):
    church = Church.query.get_or_404(church_id)
    if current_user.role not in ('administrador', 'secretaria'):
        if current_user.role != 'pastorado' or current_user.church_id != church_id:
            flash('No tienes permiso para agregar elementos de inventario.', 'danger')
            return redirect(url_for('routes.inventory', church_id=church_id))
    form = InventoryItemForm()
    if form.validate_on_submit():
        item = ChurchInventory(
            church_id=church_id,
            item_name=form.item_name.data,
            item_type=form.item_type.data,
            quantity=form.quantity.data,
            description=form.description.data,
            purchase_date=form.purchase_date.data,
            purchase_price=form.purchase_price.data,
            current_value=form.current_value.data,
            serial_number=form.serial_number.data,
            notes=form.notes.data
        )
        db.session.add(item)
        db.session.commit()
        flash('Elemento de inventario agregado exitosamente!', 'success')
        return redirect(url_for('routes.inventory', church_id=church_id))
    return render_template('admin/inventory_form.html', form=form, church=church, action='add')

@routes_bp.route('/inventory/edit/<int:item_id>', methods=['GET', 'POST'])
@login_required
def edit_inventory_item(item_id):
    item = ChurchInventory.query.get_or_404(item_id)
    church = item.church
    if current_user.role not in ('administrador', 'secretaria'):
        if current_user.role != 'pastorado' or current_user.church_id != church.id:
            flash('No tienes permiso para editar este elemento de inventario.', 'danger')
            return redirect(url_for('routes.inventory', church_id=church.id))
    form = InventoryItemForm(obj=item)
    if form.validate_on_submit():
        item.item_name = form.item_name.data
        item.item_type = form.item_type.data
        item.quantity = form.quantity.data
        item.description = form.description.data
        item.purchase_date = form.purchase_date.data
        item.purchase_price = form.purchase_price.data
        item.current_value = form.current_value.data
        item.serial_number = form.serial_number.data
        item.notes = form.notes.data
        db.session.commit()
        db.session.expire(item)
        flash('Elemento de inventario actualizado exitosamente!', 'success')
        return redirect(url_for('routes.inventory', church_id=church.id))
    return render_template('admin/inventory_form.html', form=form, church=church, item=item, action='edit')

@routes_bp.route('/inventory/delete/<int:item_id>', methods=['POST'])
@login_required
def delete_inventory_item(item_id):
    item = ChurchInventory.query.get_or_404(item_id)
    church_id = item.church_id
    if current_user.role not in ('administrador', 'secretaria'):
        if current_user.role != 'pastorado' or current_user.church_id != church_id:
            flash('No tienes permiso para eliminar este elemento de inventario.', 'danger')
            return redirect(url_for('routes.inventory', church_id=church_id))
    db.session.delete(item)
    db.session.commit()
    flash('Elemento de inventario eliminado exitosamente!', 'success')
    return redirect(url_for('routes.inventory', church_id=church_id))

@routes_bp.route('/pastor/member/<int:user_id>')
@login_required
def pastor_member_detail(user_id):
    if current_user.role != 'pastorado':
        flash('No tienes permiso para acceder a esta página.', 'danger')
        return redirect(url_for('routes.dashboard'))
    member = Member.query.filter_by(user_id=user_id).first()
    if not member or member.church_id != current_user.church_id:
        flash('No tienes permiso para ver este detalle.', 'danger')
        return redirect(url_for('routes.pastor_members'))
    user = User.query.options(
        db.joinedload(User.church),
        db.joinedload(User.pastor_roles)
    ).get_or_404(user_id)
    relationships = db.session.query(UserRelationship).filter(
        or_(UserRelationship.user_id_1 == user_id, UserRelationship.user_id_2 == user_id)
    ).all()
    return render_template('admin/user_detail.html', user=user, relationships=relationships,
                           describe_relationship=describe_relationship, member=member)

@routes_bp.route('/map')
@login_required
def map_view():
    if current_user.role not in ['administrador', 'secretaria', 'pastorado']:
        flash('No tienes permiso para ver el mapa.', 'danger')
        return redirect(url_for('routes.dashboard'))
    churches = Church.query.all()
    pastores = Pastor.query.join(User).filter(User.role=='pastorado').all()
    return render_template('admin/map.html', churches=churches, pastores=pastores)

@routes_bp.route('/inventarios')
@login_required
@admin_or_secretary_required
def list_inventories():
    churches = Church.query.all()
    return render_template('admin/list_inventories.html', churches=churches)

@routes_bp.route('/pastores')
@login_required
@admin_or_secretary_required
def list_pastores():
    # --- Manejo de Ordenación ---
    sort_by = request.args.get('sort_by', 'last_name') # Columna por defecto: apellido
    sort_dir = request.args.get('sort_dir', 'asc')     # Dirección por defecto: ascendente

    if sort_dir not in ['asc', 'desc']:
        sort_dir = 'asc'

    allowed_sorts = {
        'first_name': User.first_name,
        'last_name': User.last_name,
        'grado': Pastor.grado,
        'matricula': Pastor.matricula,
        # Añadir otras si es necesario
    }

    if sort_by not in allowed_sorts:
        sort_by = 'last_name'

    sort_column = allowed_sorts[sort_by]

    query = db.session.query(User).outerjoin(Pastor, User.id == Pastor.user_id)\
                      .filter(User.role == 'pastorado')

    # --- CORRECCIÓN: Eliminar .nullslast() y .nullsfirst() ---
    if sort_dir == 'asc':
        # Simplemente usa asc()
        query = query.order_by(asc(sort_column))
    else:
        # Simplemente usa desc()
        query = query.order_by(desc(sort_column))
    # --- FIN CORRECCIÓN ---

    # --- Manejo de Búsqueda ---
    search_term = request.args.get('search')
    if search_term:
         # Asegúrate de que func esté importado: from sqlalchemy import func
         query = query.filter(
             or_(
                 func.concat(User.first_name, ' ', User.last_name).ilike(f'%{search_term}%'),
                 User.username.ilike(f'%{search_term}%'),
                 User.email.ilike(f'%{search_term}%'),
                 Pastor.grado.ilike(f'%{search_term}%'),
                 Pastor.matricula.ilike(f'%{search_term}%')
             )
         )

    pastores = query.all()

    return render_template(
        'admin/list_pastores.html',
        pastores=pastores,
        sort_by=sort_by,
        sort_dir=sort_dir,
        search_term=search_term
    )

@routes_bp.route('/edit_profile/<int:user_id>', methods=['GET', 'POST'])
@login_required
def edit_profile(user_id):
    # Verificar si el usuario actual puede editar el perfil solicitado
    user = User.query.get_or_404(user_id)
    can_edit = False
    if current_user.id == user_id: # El usuario puede editar su propio perfil
        can_edit = True
    elif current_user.role in ['administrador', 'secretaria']: # Admin/Sec pueden editar a todos (excepto a sí mismos en esta lógica básica)
        can_edit = True
    elif current_user.role == 'pastorado':
        # Pastor puede editar miembros de su iglesia
        if user.role == 'miembro' and user.church_id == current_user.church_id:
            can_edit = True

    if not can_edit:
        flash('No tienes permiso para editar este perfil.', 'danger')
        return redirect(url_for('routes.dashboard')) # O redirigir a user_detail si prefieres

    # --- CORRECCIÓN: Pasar argumentos originales al crear el form ---
    form = EditUserForm(
        original_username=user.username,
        original_email=user.email,
        original_dni=user.dni,
        obj=user # Pasar obj también para poblar campos
    )
    # --- FIN CORRECCIÓN ---

    # Poblar choices para selects (siempre necesario)
    form.pastor_roles.choices = [(role.id, role.role_name) for role in PastorRole.query.order_by('role_name').all()]
    form.church.choices = [(0, 'Ninguna')] + [(c.id, c.name) for c in Church.query.order_by('name').all()]

    # --- Lógica para PastorForm ---
    pastor_form = None
    if user.role == 'pastorado':
        pastor = user.pastor # Acceder via backref
        if not pastor:
            # Si un usuario es pastorado pero no tiene registro en Pastor, créalo
            print(f"WARN: Usuario {user_id} es pastorado pero no tenía registro en Pastor. Creando uno.")
            pastor = Pastor(user_id=user.id)
            db.session.add(pastor)
            # No hagas commit aquí, se hará al final si todo es válido
        # Crear PastorForm con datos del pastor, si existe
        if pastor:
            pastor_form = PastorForm(formdata=request.form if request.method == 'POST' else None, obj=pastor)
        else: # Si por alguna razón no se pudo crear/encontrar el pastor
            pastor_form = PastorForm(formdata=request.form if request.method == 'POST' else None) # Crear form vacío si es POST

    # --- Lógica al enviar el formulario (POST) ---
    # Validar el formulario principal (EditUserForm)
    if form.validate_on_submit():
        # Validar PastorForm SOLO si aplica y existe
        pastor_form_is_valid = True # Asumir válido si no aplica
        if user.role == 'pastorado' and pastor_form:
            # Validar el subformulario si se envió data para él
            # (WTForms usualmente maneja esto bien si se instancia con formdata)
            pastor_form_is_valid = pastor_form.validate()

        if pastor_form_is_valid: # Proceder solo si ambos forms son válidos
            # Capturar datos ANTES de modificar (opcional, para reseña)
            old_data = { "username": user.username, "email": user.email, "first_name": user.first_name,
                         "last_name": user.last_name, "phone_number": user.phone_number, "address": user.address,
                         "date_of_birth": user.date_of_birth, "dni": user.dni, "ciudad": user.ciudad,
                         "estado_civil": user.estado_civil, "role": user.role, "church_id": user.church_id }
            if user.pastor:
                 old_data.update({"pastor_grado": user.pastor.grado, "pastor_matricula": user.pastor.matricula,
                                 "pastor_address": user.pastor.address}) # Añadir datos de pastor a old_data si existen

            # --- Asignar datos de EditUserForm al objeto User ---
            user.username = form.username.data
            user.first_name = form.first_name.data
            user.last_name = form.last_name.data
            user.email = form.email.data
            user.role = form.role.data # Actualizar rol
            user.phone_number = form.phone_number.data
            user.address = form.address.data # Dirección PERSONAL
            user.date_of_birth = form.date_of_birth.data
            user.church_id = form.church.data if form.church.data != 0 else None # Iglesia a la que PERTENECE
            user.dni = form.dni.data
            user.ciudad = form.ciudad.data # Ciudad PERSONAL
            user.estado_civil = form.estado_civil.data

            # Actualizar contraseña solo si se proporcionó una nueva
            if form.password.data:
                user.password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')

            # --- Lógica específica para Pastor (actualizar tabla 'pastores') ---
            if user.role == 'pastorado' and pastor_form:
                pastor = user.pastor # Debería existir ahora
                if pastor:
                    # Asignar datos desde pastor_form
                    pastor.grado = pastor_form.grado.data
                    pastor.matricula = pastor_form.matricula.data
                    pastor.address = pastor_form.address.data # Dirección casa pastoral
                    pastor.latitude = pastor_form.latitude.data
                    pastor.longitude = pastor_form.longitude.data
                    # Asignar otras fechas si están en PastorForm:
                    # pastor.fecha_promocion = pastor_form.fecha_promocion.data
                    # pastor.fecha_graduacion = pastor_form.fecha_graduacion.data

                # Actualizar roles adicionales de pastor (desde EditUserForm)
                selected_roles = PastorRole.query.filter(PastorRole.id.in_(form.pastor_roles.data)).all()
                user.pastor_roles = selected_roles # SQLAlchemy maneja la tabla de asociación

            # --- Lógica si el rol CAMBIA ---
            # Si dejó de ser pastor, ¿eliminar registro Pastor? ¿Quitar roles?
            elif old_data.get("role") == 'pastorado' and user.role != 'pastorado':
                 if user.pastor:
                     print(f"INFO: Usuario {user.id} cambió de rol. Eliminando registro Pastor.")
                     db.session.delete(user.pastor)
                 user.pastor_roles = [] # Limpiar roles de pastor
            # Si dejó de ser miembro, ¿eliminar registro Member?
            elif old_data.get("role") == 'miembro' and user.role != 'miembro':
                 if user.member:
                     print(f"INFO: Usuario {user.id} cambió de rol. Eliminando registro Member.")
                     db.session.delete(user.member)
            # Si se convirtió en miembro, crear registro Member si no existe
            elif user.role == 'miembro' and not user.member:
                 print(f"INFO: Usuario {user.id} cambió a rol Miembro. Creando registro Member.")
                 # Asegurar que tenga iglesia asignada
                 if not user.church_id:
                      flash("Para asignar el rol 'Miembro', debe seleccionar una iglesia.", "warning")
                      # Podrías invalidar el commit o dejar que falle la FK si church_id es requerido en Member
                 else:
                      new_member = Member(user_id=user.id, church_id=user.church_id)
                      db.session.add(new_member)
                      # Los campos alergies/emergency_contact/functions se llenan en edit_member

            try:
                db.session.commit() # Guardar todos los cambios (User, Pastor, Member si aplica)

                # --- Lógica de Reseña/Historial ---
                changes = []
                for field, old_value in old_data.items():
                    new_value = None
                    if field.startswith("pastor_"): # Datos del pastor
                        pastor_field = field.split("pastor_")[1]
                        if user.pastor: new_value = getattr(user.pastor, pastor_field, None)
                    else: # Datos del usuario
                        new_value = getattr(user, field, None)

                    # Comparar con cuidado (manejar None y tipos)
                    if str(old_value or '') != str(new_value or ''):
                        changes.append(f"{field}: '{old_value or 'Vacío'}' -> '{new_value or 'Vacío'}'")

                if changes:
                    detailed_review = "Perfil actualizado - " + ", ".join(changes)
                else:
                    detailed_review = "Perfil actualizado (sin cambios detectados en campos principales)"

                new_review = UserReview(user_id=user.id, reviewer_id=current_user.id, review_text=detailed_review)
                db.session.add(new_review)
                db.session.commit()
                # --- Fin Lógica Reseña ---

                flash("Usuario actualizado exitosamente!", "success")
                return redirect(url_for('routes.user_detail', user_id=user.id))
            except Exception as e:
                db.session.rollback()
                flash(f"Error al guardar los cambios: {e}", "danger")
                # No redirigir, dejar que renderice el form de nuevo con errores
        else:
             # Si falla la validación de pastor_form (si aplica)
             flash("Por favor, corrige los errores en la sección de Información del Pastor.", "warning")


    # --- Lógica para GET (poblar selects/checkboxes si no es POST o falló validación) ---
    # Asegurar que los datos se carguen correctamente la primera vez o si falla el POST
    if request.method != 'POST' or not form.is_submitted(): # Cargar datos iniciales en GET
        form.church.data = user.church_id if user.church_id else 0
        if user.role == 'pastorado':
            form.pastor_roles.data = [role.id for role in user.pastor_roles]
        # Si hay datos en pastor_form (ej. grado), ya se cargaron con obj=pastor
        # Si hay datos en member (alergies...), se cargan en edit_member, no aquí.


    # Renderizar la plantilla
    # Pasar solo los forms necesarios: form (EditUserForm) y pastor_form (si aplica)
    return render_template('admin/edit_profile.html', form=form, pastor_form=pastor_form, user=user)

@routes_bp.route('/calendar')
@login_required
def calendar_view():
    current_year = datetime.utcnow().year
    if current_user.role in ['administrador', 'secretaria']:
        users = User.query.filter(User.date_of_birth != None).all()
    elif current_user.role == 'pastorado':
        users = User.query.filter_by(church_id=current_user.church_id)\
                          .filter(User.date_of_birth != None).all()
    else:
        users = []
    birthday_events = []
    for u in users:
        try:
            birthday_date = u.date_of_birth.replace(year=current_year)
        except Exception as e:
            continue
        birthday_events.append({
            'id': f"birthday-{u.id}",
            'title': f"Cumpleaños: {u.first_name} {u.last_name}",
            'start': birthday_date.strftime("%Y-%m-%d"),
            'allDay': True,
            'color': '#ff9f89'
        })
    if current_user.role in ['administrador', 'secretaria']:
        events_manual = CalendarEvent.query.all()
    else:
        events_manual = CalendarEvent.query.filter_by(user_id=current_user.id).all()
    manual_events = []
    for ev in events_manual:
        manual_events.append({
            'id': ev.id,
            'title': ev.title,
            'start': ev.event_date.strftime("%Y-%m-%d"),
            'allDay': True,
            'description': ev.description,
            'color': '#378006'
        })
    events = birthday_events + manual_events
    events_json = json.dumps(events)
    return render_template('calendar.html', events_json=events_json)

@routes_bp.route('/calendar/event/add', methods=['GET', 'POST'])
@login_required
def add_calendar_event():
    form = CalendarEventForm()
    if form.validate_on_submit():
        event = CalendarEvent(
            user_id=current_user.id,
            title=form.title.data,
            description=form.description.data,
            event_date=form.event_date.data
        )
        db.session.add(event)
        db.session.commit()
        flash("Evento añadido correctamente.", "success")
        return redirect(url_for('routes.calendar_view'))
    return render_template('calendar_event_form.html', form=form, action="Agregar")

@routes_bp.route('/calendar/event/edit/<int:event_id>', methods=['GET', 'POST'])
@login_required
def edit_calendar_event(event_id):
    event = CalendarEvent.query.get_or_404(event_id)
    if current_user.id != event.user_id and current_user.role not in ['administrador', 'secretaria']:
        flash("No tienes permiso para editar este evento.", "danger")
        return redirect(url_for('routes.calendar_view'))
    form = CalendarEventForm(obj=event)
    if form.validate_on_submit():
        event.title = form.title.data
        event.description = form.description.data
        event.event_date = form.event_date.data
        db.session.commit()
        flash("Evento actualizado correctamente.", "success")
        return redirect(url_for('routes.calendar_view'))
    return render_template('calendar_event_form.html', form=form, action="Editar")

@routes_bp.route('/calendar/event/delete/<int:event_id>', methods=['POST'])
@login_required
def delete_calendar_event(event_id):
    event = CalendarEvent.query.get_or_404(event_id)
    if current_user.id != event.user_id and current_user.role not in ['administrador', 'secretaria']:
        flash("No tienes permiso para eliminar este evento.", "danger")
        return redirect(url_for('routes.calendar_view'))
    db.session.delete(event)
    db.session.commit()
    flash("Evento eliminado correctamente.", "success")
    return redirect(url_for('routes.calendar_view'))

@routes_bp.route('/upload_document', methods=['GET', 'POST'])
@login_required
def upload_document():
    from app.forms import DocumentForm
    form = DocumentForm()
    if form.validate_on_submit():
        file = form.file.data
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            upload_folder = current_app.config['UPLOAD_FOLDER']
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)
            file_path = os.path.join(upload_folder, filename)
            file.save(file_path)
            status = 'aprobado' if current_user.role in ['administrador', 'secretaria'] else 'pendiente'
            document = Document(
                title=form.title.data,
                description=form.description.data,
                file_path=filename,
                category=form.category.data,
                topic=form.topic.data,
                uploaded_by=current_user.id,
                status=status
            )
            db.session.add(document)
            db.session.commit()
            flash("Documento subido correctamente.", "success")
            return redirect(url_for('routes.list_documents'))
        else:
            flash("El formato del archivo no es permitido.", "danger")
    return render_template('upload_document.html', form=form)

@routes_bp.route('/list_documents')
@login_required
def list_documents():
    if current_user.role in ['administrador', 'secretaria']:
        documents = Document.query.order_by(Document.upload_date.desc()).all()
    elif current_user.role == 'pastorado':
        documents = Document.query.filter(
            (Document.uploaded_by == current_user.id) | (Document.status == 'aprobado')
        ).order_by(Document.upload_date.desc()).all()
    else:
        documents = Document.query.filter_by(status='aprobado').order_by(Document.upload_date.desc()).all()
    return render_template('list_documents.html', documents=documents)

@routes_bp.route('/approve_document/<int:doc_id>', methods=['POST'])
@login_required
@admin_or_secretary_required
def approve_document(doc_id):
    document = Document.query.get_or_404(doc_id)
    document.status = 'aprobado'
    db.session.commit()
    flash("Documento aprobado.", "success")
    return redirect(url_for('routes.list_documents'))

@routes_bp.route('/download_document/<int:doc_id>')
@login_required
def download_document(doc_id):
    document = Document.query.get_or_404(doc_id)
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], document.file_path)
    return send_file(file_path, as_attachment=True)

@routes_bp.route('/document/diploma/<int:member_id>')
@login_required
def diploma(member_id):
    from app.models import Member
    member_obj = Member.query.filter_by(user_id=member_id).first()
    if not member_obj:
        flash("Miembro no encontrado.", "danger")
        return redirect(url_for('routes.dashboard'))
    current_date = datetime.utcnow().strftime("%d-%m-%Y")
    diploma_model = request.args.get('model', '1')
    if diploma_model == '1':
        template_name = 'diploma1.html'
    elif diploma_model == '2':
        template_name = 'diploma2.html'
    elif diploma_model == '3':
        template_name = 'diploma3.html'
    else:
        template_name = 'diploma1.html'
    rendered = render_template(template_name, member=member_obj.user, current_date=current_date, view_user=current_user)
    if request.args.get('preview') == '1':
        return rendered
    config = pdfkit.configuration(wkhtmltopdf='/usr/bin/wkhtmltopdf')
    options = {'enable-local-file-access': None, 'quiet': None, 'page-size': 'A4'}
    try:
        pdf = pdfkit.from_string(rendered, False, configuration=config, options=options)
    except Exception as e:
        flash("Error al generar el PDF: " + str(e), "danger")
        return redirect(url_for('routes.dashboard'))
    response = make_response(pdf)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=diploma_{member_obj.user.username}.pdf'
    return response

@routes_bp.route('/document/diploma/preview', methods=['GET', 'POST'])
@login_required
def diploma_preview():
    from app.models import Member
    if current_user.role == 'pastorado':
        members = Member.query.filter_by(church_id=current_user.church_id).all()
    else:
        members = Member.query.all()
    options = [(member.user.id, f"{member.user.first_name} {member.user.last_name}") for member in members]
    if request.method == 'POST':
        member_id = request.form.get('member_id')
        diploma_model = request.form.get('diploma_model')
        if member_id and diploma_model:
            return redirect(url_for('routes.diploma', member_id=int(member_id), model=diploma_model, preview=1))
        else:
            flash("Debes seleccionar un miembro y un modelo.", "warning")
    return render_template('diploma_preview.html', options=options)

@routes_bp.route('/document/acta/preview', methods=['GET', 'POST'])
@login_required
def acta_preview():
    from app.forms import ActaForm
    from app.models import Member, User
    form = ActaForm()
    if current_user.role in ('administrador', 'secretaria'):
        users = User.query.all()
        options = [(user.id, f"{user.first_name} {user.last_name}") for user in users]
    elif current_user.role == 'pastorado':
        members = Member.query.filter_by(church_id=current_user.church_id).all()
        options = [(member.user.id, f"{member.user.first_name} {member.user.last_name}") for member in members]
        if current_user.id not in [uid for uid, _ in options]:
            options.append((current_user.id, f"{current_user.first_name} {current_user.last_name} (Yo mismo)"))
    else:
        return "No tiene acceso", 403
    form.participants.choices = options
    if request.method == 'GET':
        form.participants.data = []
    if form.validate_on_submit():
        query_params = {
            'acta_number': form.acta_number.data,
            'city': form.city.data,
            'province': form.province.data,
            'day': form.day.data,
            'month': form.month.data,
            'year': form.year.data,
            'time': form.time.data,
            'act_description': form.act_description.data,
            'participants': ",".join(str(pid) for pid in form.participants.data)
        }
        return redirect(url_for('routes.acta', **query_params, preview=1))
    return render_template('acta_form.html', form=form)

@routes_bp.route('/document/acta/view')
@login_required
def acta():
    acta_number = request.args.get('acta_number')
    city = request.args.get('city')
    province = request.args.get('province')
    day = request.args.get('day')
    month = request.args.get('month')
    year = request.args.get('year')
    time_str = request.args.get('time')
    act_description = request.args.get('act_description')
    participants_ids = request.args.get('participants')
    participant_ids = [int(pid) for pid in participants_ids.split(",") if pid.strip()] if participants_ids else []
    from app.models import User
    participants = User.query.filter(User.id.in_(participant_ids)).all() if participant_ids else []
    rendered = render_template('acta.html',
                               acta_number=acta_number,
                               city=city,
                               province=province,
                               day=day,
                               month=month,
                               year=year,
                               time=time_str,
                               act_description=act_description,
                               participants=participants)
    if request.args.get('preview') == '1':
        return rendered
    filename = f"acta_nro_{acta_number}.pdf"
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
    status = 'aprobado' if current_user.role in ['administrador', 'secretaria'] else 'pendiente'
    document = Document(
        title=f"Acta Nº {acta_number}",
        description=act_description,
        file_path=filename,
        category='acta',
        topic="Acta de Miembros",
        uploaded_by=current_user.id,
        status=status
    )
    db.session.add(document)
    db.session.commit()
    config = pdfkit.configuration(wkhtmltopdf='/usr/bin/wkhtmltopdf')
    options = {'enable-local-file-access': None, 'quiet': None, 'page-size': 'A4'}
    try:
        pdfkit.from_string(rendered, file_path, configuration=config, options=options)
    except Exception as e:
        db.session.rollback()
        flash("Error al generar el PDF: " + str(e), "danger")
        return redirect(url_for('routes.dashboard'))
    return send_file(file_path, as_attachment=True)

@routes_bp.route('/delete_document/<int:doc_id>', methods=['POST'])
@login_required
@admin_or_secretary_required
def delete_document(doc_id):
    document = Document.query.get_or_404(doc_id)
    try:
        os.remove(os.path.join(current_app.config['UPLOAD_FOLDER'], document.file_path))
    except FileNotFoundError:
        flash(f"El archivo {document.file_path} no se encontró en el servidor.", "warning")
    except Exception as e:
        flash(f"Error al eliminar el archivo: {e}", "danger")
        return redirect(url_for('routes.list_documents'))
    db.session.delete(document)
    db.session.commit()
    flash("Documento eliminado exitosamente.", "success")
    return redirect(url_for('routes.list_documents'))

@routes_bp.route('/pastor/economia', methods=['GET', 'POST'])
@login_required
@pastor_required
def pastor_economia():
    if current_user.church_id is None:
        flash('No tienes una iglesia asignada. Contacta al administrador.', 'danger')
        return redirect(url_for('routes.dashboard'))
    account = Account.query.filter_by(church_id=current_user.church_id).first()
    if account is None:
        account = Account(church_id=current_user.church_id, name="Cuenta Principal")
        db.session.add(account)
        db.session.commit()
    form = TransactionForm(church_id=current_user.church_id)
    if form.validate_on_submit():
        member_id = form.member_id.data
        if member_id == 0:
            member_id = None
        transaction = Transaction(
            account_id=account.id,
            user_id=current_user.id,
            member_id=member_id,
            amount=form.amount.data,
            transaction_type=form.transaction_type.data,
            category=form.category.data,
            description=form.description.data,
            transaction_date=form.transaction_date.data,
            notes=form.notes.data
        )
        db.session.add(transaction)
        if form.transaction_type.data == 'ingreso':
            account.balance += form.amount.data
        else:
            account.balance -= form.amount.data
        db.session.commit()
        flash('Transacción registrada exitosamente!', 'success')
        return redirect(url_for('routes.pastor_economia'))
    transactions = Transaction.query.filter_by(account_id=account.id).order_by(Transaction.transaction_date.desc()).all()
    return render_template('pastorado/economia.html', form=form, account=account, transactions=transactions)

@routes_bp.route('/credencial/seleccionar', methods=['GET', 'POST'])
@login_required
@admin_or_secretary_required # O ajusta permisos si los pastores pueden generar la suya
def seleccionar_pastor_credencial():
    form = SelectPastorCredencialForm()
    if form.validate_on_submit():
        pastor_user_id = form.pastor_user.data
        # Redirige a la ruta de generación (o a una vista previa HTML si prefieres)
        return redirect(url_for('routes.generar_credencial_pdf', pastor_user_id=pastor_user_id))
    return render_template('admin/seleccionar_pastor_credencial.html', form=form)

# --- Función Helper para parsear Matrícula/Vencimiento ---
def obtener_vencimiento_de_matricula(matricula):
    if not matricula or len(matricula.split('-')) < 2:
        return None
    try:
        # Asume formato como "215-03/25" -> Queremos "30/MM/YYYY"
        parts = matricula.split('-')
        date_part = parts[-1] # Toma la última parte "03/25"
        if '/' in date_part:
            month_str, year_short_str = date_part.split('/')
            year = int("20" + year_short_str) # Asume siglo 21
            month = int(month_str)
            # Devolver en formato DD/MM/YYYY (usando el día 30 como en el ejemplo)
            return f"30/{month:02d}/{year}"
        else:
            return None # Formato no esperado
    except Exception:
        return None # Error al parsear

@routes_bp.route('/credencial/generar/<int:pastor_user_id>')
@login_required
# @admin_or_secretary_required
def generar_credencial_pdf(pastor_user_id):
    user = db.session.get(User, pastor_user_id)
    if not user or user.role != 'pastorado' or not user.pastor:
        # --- CORREGIDO Logger ---
        current_app.logger.warning(f"Intento de generar credencial para ID {pastor_user_id} fallido: Usuario no válido o sin rol/registro pastor.")
        flash('Usuario no encontrado o no es un pastor válido.', 'danger')
        return redirect(url_for('routes.seleccionar_pastor_credencial'))

    pastor = user.pastor
    vencimiento = obtener_vencimiento_de_matricula(pastor.matricula)

    # --- Buscar la iglesia gobernada ---
    governed_church = Church.query.filter_by(pastor_id=user.id).first()
    ciudad_iglesia = "N/A"
    provincia_iglesia = "N/A"

    if governed_church:
        ciudad_iglesia = governed_church.city if governed_church.city else "N/A (Iglesia sin ciudad)"
        provincia_iglesia = governed_church.province if governed_church.province else "N/A (Iglesia sin prov.)"
    else:
         # --- CORREGIDO Logger ---
        current_app.logger.info(f"Pastor {user.id} no gobierna ninguna iglesia.")


    # --- Manejo de Fotos ---
    pastor_photo_url = None
    photo_base_dir_absolute = os.path.join(current_app.static_folder, 'img', 'fotos pastores', 'PASTORES')

    if user.dni:
        photo_filename = f"{user.dni}.jpg"
        photo_path_absolute = os.path.join(photo_base_dir_absolute, photo_filename)

        if os.path.exists(photo_path_absolute):
            pastor_photo_url = 'file:///' + photo_path_absolute.replace('\\', '/')
            # --- CORREGIDO Logger ---
            current_app.logger.info(f"Foto encontrada para user {user.id}: {pastor_photo_url}")
        else:
            # --- CORREGIDO Logger ---
            current_app.logger.warning(f"Archivo de foto no encontrado para user {user.id} en: {photo_path_absolute}")
    else:
        # --- CORREGIDO Logger ---
        current_app.logger.warning(f"Usuario {user.id} no tiene DNI registrado, no se puede buscar foto.")


    # --- Logo URL ---
    logo_path_relative = os.path.join('img', 'logo.png')
    logo_path_absolute = os.path.join(current_app.static_folder, logo_path_relative)
    logo_url = None
    if os.path.exists(logo_path_absolute):
        logo_url = 'file:///' + logo_path_absolute.replace('\\', '/')
    else:
         # --- CORREGIDO Logger ---
        current_app.logger.warning(f"Logo no encontrado en {logo_path_absolute}")

    # --- Renderizar plantilla HTML ---
    html = render_template(
        'credencial.html',
        user=user,
        pastor=pastor,
        vencimiento=vencimiento,
        pastor_photo_url=pastor_photo_url,
        logo_url=logo_url,
        ciudad_iglesia=ciudad_iglesia,
        provincia_iglesia=provincia_iglesia
    )

    # --- Configuración PDFKit ---
    config_path = '/usr/bin/wkhtmltopdf'
    try:
        config = pdfkit.configuration(wkhtmltopdf=config_path)
    except OSError as e:
        current_app.logger.error(f"Error al configurar pdfkit con path '{config_path}': {e}", exc_info=True)
        flash(f"Error de configuración del generador PDF: {e}", "danger")
        abort(500, description=f"Error de configuración del generador PDF: wkhtmltopdf no encontrado o no ejecutable en '{config_path}'.")

    options = {
        'page-width': '85.6mm', 'page-height': '53.98mm',
        'margin-top': '3mm', 'margin-right': '3mm', 'margin-bottom': '3mm', 'margin-left': '3mm',
        'encoding': "UTF-8", 'custom-header' : [('Accept-Language', 'es')],
        'no-outline': None, 'enable-local-file-access': None
    }

    # --- Generar y devolver PDF ---
    try:
        pdf = pdfkit.from_string(html, False, configuration=config, options=options)

        response = make_response(pdf)
        response.headers['Content-Type'] = 'application/pdf'
        filename = f"credencial_{user.last_name}_{user.first_name}.pdf"
        response.headers['Content-Disposition'] = f'inline; filename="{filename}"'
        return response

    except Exception as e:
        # --- CORREGIDO Logger ---
        current_app.logger.error(f"Error generando PDF para user {user.id}: {e}", exc_info=True)
        flash(f"Error interno al generar la credencial PDF. Contacte al administrador.", "danger")
        abort(500, description=f"Error al generar el PDF: {e}")

@routes_bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_own_profile():
    # El usuario a editar es siempre current_user
    user = current_user
    member = user.member # Acceder al registro Member asociado (puede ser None si no es miembro)

    # Instanciar el formulario. En GET, poblar con datos actuales.
    # En POST, WTForms usará request.form.
    form = ProfileEditForm(obj=user if request.method == 'GET' else None)

    # Poblar campos específicos de Member en GET, si el usuario es miembro
    if request.method == 'GET' and member:
        form.alergies.data = member.alergies
        form.emergency_contact.data = member.emergency_contact

    if form.validate_on_submit():
        # Guardar datos antiguos para posible reseña (opcional)
        old_data_user = {f: getattr(user, f) for f in ['first_name', 'last_name', 'phone_number', 'address', 'ciudad', 'estado_civil', 'date_of_birth']}
        old_data_member = {}
        if member:
            old_data_member = {f: getattr(member, f) for f in ['alergies', 'emergency_contact']}

        # Actualizar datos del objeto User
        user.first_name = form.first_name.data
        user.last_name = form.last_name.data
        user.phone_number = form.phone_number.data
        user.address = form.address.data
        user.ciudad = form.ciudad.data
        user.estado_civil = form.estado_civil.data
        user.date_of_birth = form.date_of_birth.data
        # No actualizamos email, dni, role, church_id aquí

        # Actualizar contraseña si se proporcionó una nueva
        if form.password.data:
            user.password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
            flash('Contraseña actualizada exitosamente.', 'info')

        # Actualizar datos del objeto Member (si existe)
        if member:
            member.alergies = form.alergies.data
            member.emergency_contact = form.emergency_contact.data

        try:
            db.session.commit() # Guardar cambios en User y Member

            # Opcional: Registrar cambio en UserReview
            changes = []
            # Comparar campos user
            for field, old_value in old_data_user.items():
                 new_value = getattr(user, field)
                 if str(old_value or '') != str(new_value or ''):
                     changes.append(f"{field}: '{old_value or 'Vacío'}' -> '{new_value or 'Vacío'}'")
             # Comparar campos member
            if member:
                for field, old_value in old_data_member.items():
                    new_value = getattr(member, field)
                    if str(old_value or '') != str(new_value or ''):
                         changes.append(f"{field}: '{old_value or 'Vacío'}' -> '{new_value or 'Vacío'}'")

            if changes:
                review_text = "Perfil propio actualizado - " + ", ".join(changes)
                new_review = UserReview(user_id=user.id, reviewer_id=user.id, review_text=review_text) # El usuario se reseña a sí mismo
                db.session.add(new_review)
                db.session.commit()

            flash('Tu perfil ha sido actualizado exitosamente.', 'success')
            return redirect(url_for('routes.dashboard')) # Redirigir al dashboard o a una vista de perfil
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error al actualizar perfil propio para user {user.id}: {e}", exc_info=True)
            flash(f"Error al guardar los cambios: {e}", "danger")
            # Renderizar de nuevo el formulario con errores

    # Renderizar plantilla si es GET o falla la validación
    return render_template('profile_edit.html', title='Editar Mi Perfil', form=form, user_is_member=(member is not None))

# --- Ruta para crear/editar anuncios ---
@routes_bp.route('/announcements/new', methods=['GET', 'POST'])
@routes_bp.route('/announcements/edit/<int:announcement_id>', methods=['GET', 'POST'])
@login_required
# @admin_or_secretary_required # O permitir a pastores? Definir permisos
def manage_announcement(announcement_id=None):
    # Definir quién puede crear/editar
    if current_user.role not in ['administrador', 'secretaria', 'pastorado']: # Ejemplo: Permitir a pastores
         flash('No tienes permiso para gestionar anuncios.', 'danger')
         return redirect(url_for('routes.dashboard'))

    announcement = None
    if announcement_id:
        announcement = Announcement.query.get_or_404(announcement_id)
        # Verificar si el usuario actual puede editar ESTE anuncio (ej. solo el autor o admin/sec)
        if announcement.author_id != current_user.id and current_user.role not in ['administrador', 'secretaria']:
             flash('No tienes permiso para editar este anuncio.', 'danger')
             return redirect(url_for('routes.dashboard')) # O a una lista de anuncios

    form = AnnouncementForm(obj=announcement if request.method == 'GET' else None)

    if form.validate_on_submit():
        if announcement is None: # Creando nuevo
            announcement = Announcement(author_id=current_user.id)
            db.session.add(announcement)
            action_msg = "Anuncio publicado"
        else: # Editando existente
            action_msg = "Anuncio actualizado"

        announcement.title = form.title.data
        announcement.content = form.content.data
        announcement.visibility = form.visibility.data
        # Asignar target_church_id solo si la visibilidad lo requiere
        if form.visibility.data == 'iglesia_especifica':
            announcement.target_church_id = form.target_church.data if form.target_church.data != 0 else None
        else:
            announcement.target_church_id = None # Limpiar si no aplica

        try:
            db.session.commit()
            flash(f'{action_msg} exitosamente.', 'success')
            return redirect(url_for('routes.dashboard')) # O a lista de anuncios
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error al guardar anuncio: {e}", exc_info=True)
            flash(f"Error al guardar el anuncio: {e}", "danger")

    return render_template('announcement_form.html',
                           title='Crear Anuncio' if announcement_id is None else 'Editar Anuncio',
                           form=form,
                           announcement=announcement)

@routes_bp.route('/iglesias-visibles')
@login_required
def iglesias_visibles():
    if current_user.role == 'pastorado':
        iglesias = Church.query.filter_by(pastor_id=current_user.id).all()
    elif current_user.role == 'superintendente':
        iglesias = get_iglesias_para_superintendente(current_user.id)
    elif current_user.role == 'jefe_sector':
        iglesias = get_iglesias_para_jefe_sector(current_user.id)
    elif current_user.role == 'administrador':
        iglesias = Church.query.all()
    else:
        iglesias = []  # secretaria y otros no tienen acceso a miembros

    return render_template('iglesias/visibles.html', iglesias=iglesias)

@routes_bp.route('/asignar-jerarquias', methods=['GET', 'POST'])
@login_required
def asignar_jerarquias():
    if current_user.role not in ('administrador', 'secretaria'):
        flash('No tienes permiso para acceder a esta sección.', 'danger')
        return redirect(url_for('routes.dashboard'))

    form_pastor = JerarquiaPastorForm()
    form_superint = JerarquiaSuperintForm()

    pastores = User.query.filter_by(role='pastorado').all()
    pastor_choices = [(u.id, f"{u.first_name} {u.last_name}") for u in pastores]

    form_pastor.pastor_comun_id.choices = pastor_choices
    form_pastor.superintendente_id.choices = pastor_choices
    form_superint.jefe_sector_id.choices = pastor_choices
    form_superint.superintendente_sec_id.choices = pastor_choices

    # Procesar asignación de pastor a superintendente
    if form_pastor.submit_pastor.data and form_pastor.validate_on_submit():
        existe = JerarquiaPastores.query.filter_by(
            pastor_id=form_pastor.pastor_comun_id.data,
            supervisor_id=form_pastor.superintendente_id.data
        ).first()
        if not existe:
            jer = JerarquiaPastores(
                pastor_id=form_pastor.pastor_comun_id.data,
                supervisor_id=form_pastor.superintendente_id.data
            )
            db.session.add(jer)
            db.session.commit()
            flash('Pastor asignado al superintendente.', 'success')
        else:
            flash('Esta relación ya existe.', 'warning')
        return redirect(url_for('routes.asignar_jerarquias'))

    # Procesar asignación de superintendente a jefe de sector
    if form_superint.submit_superintendente.data and form_superint.validate_on_submit():
        existe = JerarquiaSuperintendentes.query.filter_by(
            superintendente_id=form_superint.superintendente_sec_id.data,
            jefe_sector_id=form_superint.jefe_sector_id.data
        ).first()
        if not existe:
            jer = JerarquiaSuperintendentes(
                superintendente_id=form_superint.superintendente_sec_id.data,
                jefe_sector_id=form_superint.jefe_sector_id.data
            )
            db.session.add(jer)
            db.session.commit()
            flash('Superintendente asignado a jefe de sector.', 'success')
        else:
            flash('Esta relación ya existe.', 'warning')
        return redirect(url_for('routes.asignar_jerarquias'))

    # Listar jerarquías
    jerarquias_pastores = JerarquiaPastores.query.all()
    jerarquias_superintendentes = JerarquiaSuperintendentes.query.all()

    return render_template(
        'jerarquia/asignar_jerarquias.html',
        form_pastor=form_pastor,
        form_superint=form_superint,
        jerarquias_pastores=jerarquias_pastores,
        jerarquias_superintendentes=jerarquias_superintendentes
    )

@routes_bp.route('/eliminar-jerarquia-superintendente/<int:id>', methods=['POST'])
@login_required
@admin_or_secretary_required
def eliminar_jerarquia_superintendente(id):
    relacion = JerarquiaSuperintendentes.query.get_or_404(id)
    db.session.delete(relacion)
    db.session.commit()
    flash("Relación eliminada correctamente.", "success")
    return redirect(url_for('routes.asignar_jerarquias'))

@routes_bp.route('/eliminar-jerarquia-pastor/<int:id>', methods=['POST'])
@login_required
@admin_or_secretary_required
def eliminar_jerarquia_pastor(id):
    relacion = JerarquiaPastores.query.get_or_404(id)
    db.session.delete(relacion)
    db.session.commit()
    flash("Relación eliminada correctamente.", "success")
    return redirect(url_for('routes.asignar_jerarquias'))

@routes_bp.route('/announcements')
@login_required
def list_announcements():
    view_user = get_user_for_view()

    # --- Obtener parámetros de filtro/búsqueda/página ---
    search_term = request.args.get('search', '').strip()
    page = request.args.get('page', 1, type=int) # <-- LÍNEA CORREGIDA: Obtener 'page' directamente
    per_page = current_app.config.get('ANNOUNCEMENTS_PER_PAGE', 10) # Configurable

    # --- Query Base ---
    # Unimos con User para poder buscar/mostrar el nombre del autor fácilmente
    query = db.session.query(Announcement).options(
        db.joinedload(Announcement.author),
        db.joinedload(Announcement.target_church) # Cargar iglesia si es específica
    ).order_by(Announcement.created_at.desc())

    # --- Aplicar Filtro de Visibilidad (exactamente como en el dashboard) ---
    allowed_visibility = ['todos']
    if view_user.role:
        allowed_visibility.append(view_user.role)
    if view_user.role == 'administrador':
        allowed_visibility.extend(['secretaria', 'pastorado', 'miembros'])
    elif view_user.role == 'secretaria':
         allowed_visibility.extend(['pastorado', 'miembros'])
    elif view_user.role == 'pastorado':
         allowed_visibility.append('miembros')

    visibility_filter = or_(
        Announcement.visibility.in_(allowed_visibility),
        (Announcement.visibility == 'iglesia_especifica') & (Announcement.target_church_id == view_user.church_id) if view_user.church_id else False
    )
    query = query.filter(visibility_filter)

    # --- Aplicar Filtro de Búsqueda ---
    if search_term:
        search_filter = or_(
            Announcement.title.ilike(f'%{search_term}%'),
            Announcement.content.ilike(f'%{search_term}%'),
            # Buscar por nombre de autor (requiere join implícito o explícito - joinedload ayuda)
            func.concat(User.first_name, ' ', User.last_name).ilike(f'%{search_term}%')
        )
        # Necesitamos unir explícitamente si la relación no se cargó con joinedload o si no está implícita
        # Pero como hicimos joinedload(Announcement.author), SQLAlchemy podría manejarlo.
        # Para estar seguros o si no usas joinedload:
        query = query.join(User, Announcement.author_id == User.id, isouter=True) # isouter=True por si author es NULL
        query = query.filter(search_filter)

    # --- Paginación (usando Flask-SQLAlchemy paginate) ---
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    announcements_list = pagination.items

    # --- Pasar filtros de vuelta a la plantilla ---
    filters = {
        'search': search_term,
    }

    return render_template(
        'announcements_list.html', # Nueva plantilla
        title='Anuncios',
        announcements=announcements_list,
        pagination=pagination,
        filters=filters # Para pre-rellenar el formulario de búsqueda
    )

@routes_bp.route('/user/toggle_activation/<int:user_id>', methods=['POST'])
@login_required
@admin_or_secretary_required # Solo Admin/Secretaria pueden activar/desactivar
def toggle_user_activation(user_id):
    user_to_toggle = db.session.get(User, user_id) # Usar db.session.get

    if not user_to_toggle:
        flash('Usuario no encontrado.', 'danger')
        return redirect(url_for('routes.list_users'))

    # Prevenir desactivación de administradores/secretarias (importante!)
    if user_to_toggle.role in ('administrador', 'secretaria'):
        flash('No se pueden desactivar cuentas de Administrador o Secretaria.', 'danger')
        return redirect(url_for('routes.list_users'))

    # Prevenir autodesactivación (aunque el rol ya lo impide)
    if user_to_toggle.id == current_user.id:
        flash('No puedes desactivar tu propia cuenta.', 'danger')
        return redirect(url_for('routes.list_users'))

    # Cambiar el estado
    user_to_toggle.is_active = not user_to_toggle.is_active
    action = "activado" if user_to_toggle.is_active else "desactivado"

    # Añadir entrada al historial (UserReview)
    review_text = f"Usuario {action} por {current_user.username}."
    new_review = UserReview(user_id=user_to_toggle.id, reviewer_id=current_user.id, review_text=review_text)
    db.session.add(new_review)

    try:
        db.session.commit()
        flash(f'Usuario {user_to_toggle.username} ha sido {action}.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al actualizar el estado del usuario: {e}', 'danger')
        current_app.logger.error(f"Error toggling activation for user {user_id}: {e}", exc_info=True)

    # Redirigir de vuelta a la lista, manteniendo los filtros si es posible
    # (Pasar search y page como argumentos si existen en la request actual)
    redirect_url = url_for('routes.list_users',
                           search=request.args.get('search', ''),
                           page=request.args.get('page', 1, type=int),
                           status=request.args.get('status', 'all') # Mantener filtro de estado si existe
                           )
    return redirect(redirect_url)


# ... (Modificar delete_user si se desea) ...

@routes_bp.route('/delete_user/<int:user_id>', methods=['POST'])
@login_required
@admin_or_secretary_required # O solo admin? Decidir política
def delete_user(user_id):
    user = db.session.get(User, user_id) # Usar db.session.get

    if not user:
         flash('Usuario no encontrado.', 'danger')
         return redirect(url_for('routes.list_users'))

    # REAFIRMAR: No eliminar Admin/Secretaria
    if user.role in ('administrador', 'secretaria'):
        flash('No se pueden eliminar usuarios administradores o secretarias.', 'danger')
        return redirect(url_for('routes.list_users'))

    # REAFIRMAR: No autoeliminación
    if user.id == current_user.id:
         flash('No puedes eliminar tu propia cuenta.', 'danger')
         return redirect(url_for('routes.list_users'))

    # --- CONSIDERACIÓN IMPORTANTE SOBRE CASCADAS ---
    # La eliminación actual podría borrar muchos datos (mensajes, documentos, etc.)
    # debido a ondelete='CASCADE'. Si se quiere conservar, NO se debe eliminar.
    # Se podría añadir una confirmación extra muy clara aquí.
    # O directamente comentar/eliminar esta ruta y usar solo desactivación.

    # Ejemplo de confirmación extra (requiere JS en la plantilla):
    # if request.form.get('confirm_delete') != 'CONFIRMAR':
    #    flash('Confirmación de eliminación incorrecta. Escribe CONFIRMAR.', 'danger')
    #    return redirect(url_for('routes.user_detail', user_id=user_id))

    username_deleted = user.username # Guardar nombre para el mensaje flash

    # Eliminar registros relacionados manualmente si NO se usa CASCADE o se quiere más control
    # Ejemplo: db.session.delete(user.pastor) if user.pastor else None
    # Ejemplo: db.session.delete(user.member) if user.member else None
    # CUIDADO: Borrar manualmente puede ser complejo si hay muchas relaciones.
    # La desactivación es más segura si quieres mantener el historial.

    db.session.delete(user)
    try:
        db.session.commit()
        flash(f'Usuario "{username_deleted}" ELIMINADO PERMANENTEMENTE.', 'warning') # Mensaje más fuerte
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar el usuario: {e}', 'danger')
        current_app.logger.error(f"Error deleting user {user_id}: {e}", exc_info=True)

    return redirect(url_for('routes.list_users'))

@routes_bp.route('/member/<int:user_id>/request_transfer', methods=['GET', 'POST'])
@login_required
@pastor_required # Solo pastores pueden iniciar
def request_member_transfer(user_id):
    member_user = db.session.get(User, user_id)

    # --- Validaciones ---
    if not member_user or member_user.role != 'miembro':
        flash('Usuario no encontrado o no es un miembro.', 'danger')
        return redirect(url_for('routes.pastor_members')) # Redirigir a su lista

    # Verificar que el pastor actual sea el pastor del miembro
    if member_user.church_id != current_user.church_id:
        flash('No tienes permiso para iniciar la transferencia de este miembro.', 'danger')
        return redirect(url_for('routes.pastor_members'))

    current_church = current_user.church # Iglesia del pastor actual (origen)
    if not current_church:
         flash('Error: No tienes una iglesia asignada.', 'danger')
         return redirect(url_for('routes.dashboard'))

    # Verificar si ya existe una solicitud PENDIENTE para este miembro
    existing_pending_request = MemberTransferRequest.query.filter_by(
        user_id=user_id,
        status='pendiente'
    ).first()
    if existing_pending_request:
        flash(f'Ya existe una solicitud de transferencia pendiente para {member_user.full_name} hacia la iglesia {existing_pending_request.target_church.name}.', 'warning')
        # Podría redirigir a una vista de 'mis solicitudes pendientes'
        return redirect(url_for('routes.pastor_members'))

    form = RequestMemberTransferForm(current_church_id=current_church.id)

    if form.validate_on_submit():
        target_church_id = form.target_church.data
        target_church = db.session.get(Church, target_church_id) # Ya validado que existe y tiene pastor en el form

        # Crear la solicitud de transferencia
        new_request = MemberTransferRequest(
            user_id=user_id,
            origin_church_id=current_church.id,
            target_church_id=target_church_id,
            requesting_pastor_id=current_user.id,
            approving_pastor_id=target_church.pastor_id, # Pastor destino
            status='pendiente',
            request_notes=form.request_notes.data
        )
        db.session.add(new_request)

        # Notificar al Pastor Destino (Approving Pastor)
        subject_notif = f"Solicitud de Transferencia para {member_user.full_name}"
        message_to_approver = (
            f"Estimado Pastor {target_church.pastor.full_name},\n\n"
            f"El Pastor {current_user.full_name} de la iglesia '{current_church.name}' "
            f"ha solicitado transferir al miembro {member_user.full_name} a su iglesia ('{target_church.name}').\n\n"
            f"Notas de la solicitud: {form.request_notes.data or 'N/A'}\n\n"
            f"Puede revisar y aprobar/rechazar esta solicitud en la sección 'Transferencias Pendientes' de la intranet.\n\n"
            f"Saludos,\nSistema Intranet IMPA"
        )
        msg_approver = Message(sender_id=current_user.id, # O un ID de sistema
                              receiver_id=target_church.pastor_id,
                              subject=subject_notif,
                              message_text=message_to_approver)
        db.session.add(msg_approver)

        try:
            db.session.commit()
            flash(f'Solicitud de transferencia para {member_user.full_name} enviada a {target_church.pastor.full_name}.', 'success')
             # Redirigir a la lista de miembros o a una vista de solicitudes
            return redirect(url_for('routes.pastor_members'))
        except Exception as e:
            db.session.rollback()
            # Manejar error de constraint único si no se usó postgresql_where
            if 'uq_pending_transfer' in str(e):
                 flash(f'Error: Ya existe una solicitud pendiente para este miembro y esa iglesia destino.', 'danger')
            else:
                flash(f'Error al crear la solicitud: {e}', 'danger')
            current_app.logger.error(f"Error requesting transfer for user {user_id}: {e}", exc_info=True)

    return render_template('pastorado/request_transfer_form.html',
                           title=f"Solicitar Transferencia para {member_user.full_name}",
                           form=form,
                           user=member_user,
                           current_church=current_church)

@routes_bp.route('/transfers/pending')
@login_required
@pastor_required # O permitir a Admin/Sec ver todas?
def list_pending_transfers():
    # Solicitudes PARA APROBAR por el pastor actual
    requests_to_approve = MemberTransferRequest.query.filter_by(
        approving_pastor_id=current_user.id,
        status='pendiente'
    ).options(
        db.joinedload(MemberTransferRequest.user),
        db.joinedload(MemberTransferRequest.origin_church),
        db.joinedload(MemberTransferRequest.requesting_pastor)
    ).order_by(MemberTransferRequest.created_at.asc()).all()

    # Solicitudes INICIADAS por el pastor actual y aún pendientes
    requests_initiated = MemberTransferRequest.query.filter_by(
        requesting_pastor_id=current_user.id,
        status='pendiente'
    ).options(
        db.joinedload(MemberTransferRequest.user),
        db.joinedload(MemberTransferRequest.target_church),
        db.joinedload(MemberTransferRequest.approving_pastor)
    ).order_by(MemberTransferRequest.created_at.asc()).all()

    return render_template('pastorado/pending_transfers.html',
                           title="Transferencias Pendientes",
                           requests_to_approve=requests_to_approve,
                           requests_initiated=requests_initiated)

@routes_bp.route('/transfer/<int:request_id>/review', methods=['GET', 'POST'])
@login_required
@pastor_required
def review_transfer_request(request_id):
    transfer_request = db.session.get(MemberTransferRequest, request_id)

    # --- Validaciones ---
    if not transfer_request:
        flash("Solicitud de transferencia no encontrada.", "danger")
        return redirect(url_for('routes.list_pending_transfers'))

    # Verificar que el pastor actual sea el que debe aprobar
    if transfer_request.approving_pastor_id != current_user.id:
         flash("No tienes permiso para revisar esta solicitud.", "danger")
         return redirect(url_for('routes.list_pending_transfers'))

    # Verificar que siga pendiente
    if transfer_request.status != 'pendiente':
        flash(f"Esta solicitud ya ha sido {transfer_request.status}.", "warning")
        return redirect(url_for('routes.list_pending_transfers'))

    member_user = transfer_request.user # Obtener el usuario a transferir
    if not member_user or not member_user.member: # Verificar consistencia
        flash("Error: No se puede encontrar el miembro asociado a esta solicitud.", "danger")
        # ¿Quizás cambiar estado a 'cancelada'?
        transfer_request.status = 'cancelada'
        transfer_request.approval_notes = "Cancelada automáticamente: Miembro no encontrado."
        db.session.commit()
        return redirect(url_for('routes.list_pending_transfers'))

    form = ApproveRejectTransferForm()

    if form.validate_on_submit(): # POST request
        new_status = None
        action_msg = ""
        notification_msg_template = ""

        if form.submit_approve.data:
            new_status = 'aprobada'
            action_msg = "aprobada"
            notification_msg_template = ("La solicitud de transferencia para {member_name} a su iglesia "
                                         "ha sido APROBADA.\n\nNotas: {notes}")

            # --- REALIZAR LA TRANSFERENCIA ---
            member_user.church_id = transfer_request.target_church_id
            member_user.member.church_id = transfer_request.target_church_id
            # --- FIN TRANSFERENCIA ---

        elif form.submit_reject.data:
            new_status = 'rechazada'
            action_msg = "rechazada"
            notification_msg_template = ("La solicitud de transferencia para {member_name} a la iglesia '{target_church_name}' "
                                         "ha sido RECHAZADA.\n\nNotas: {notes}")

        if new_status:
            transfer_request.status = new_status
            transfer_request.approval_notes = form.approval_notes.data
            transfer_request.updated_at = datetime.utcnow() # Asegurar actualización

            # Notificar al Pastor Origen
            if transfer_request.requesting_pastor_id:
                 notification_text = notification_msg_template.format(
                     member_name=member_user.full_name,
                     target_church_name=transfer_request.target_church.name, # Nombre explícito
                     notes=form.approval_notes.data or "N/A"
                 )
                 msg_origin = Message(
                     sender_id=current_user.id, # O ID sistema
                     receiver_id=transfer_request.requesting_pastor_id,
                     subject=f"Transferencia {action_msg}: {member_user.full_name}",
                     message_text=notification_text
                 )
                 db.session.add(msg_origin)

            # Registrar en historial del miembro
            review_text = (f"Solicitud de transferencia (ID: {transfer_request.id}) hacia '{transfer_request.target_church.name}' "
                           f"fue {action_msg} por Pastor {current_user.full_name}. "
                           f"Notas: {form.approval_notes.data or 'N/A'}")
            new_review = UserReview(user_id=member_user.id, reviewer_id=current_user.id, review_text=review_text)
            db.session.add(new_review)

            try:
                db.session.commit()
                flash(f'Solicitud de transferencia {action_msg} exitosamente.', 'success')
                return redirect(url_for('routes.list_pending_transfers'))
            except Exception as e:
                db.session.rollback()
                flash(f'Error al procesar la solicitud: {e}', 'danger')
                current_app.logger.error(f"Error processing transfer request {request_id}: {e}", exc_info=True)
        else:
             flash('Acción no reconocida.', 'danger')


    # --- Renderizar GET ---
    return render_template('pastorado/review_transfer_form.html',
                           title="Revisar Solicitud de Transferencia",
                           form=form,
                           transfer_request=transfer_request,
                           member_user=member_user)

@routes_bp.route('/transfer/<int:request_id>/cancel', methods=['POST'])
@login_required
@pastor_required
def cancel_transfer_request(request_id):
    transfer_request = db.session.get(MemberTransferRequest, request_id)

    # --- Validaciones ---
    if not transfer_request:
        flash("Solicitud no encontrada.", "danger")
        return redirect(url_for('routes.list_pending_transfers'))

    # Solo el pastor que la inició puede cancelarla
    if transfer_request.requesting_pastor_id != current_user.id:
         flash("No tienes permiso para cancelar esta solicitud.", "danger")
         return redirect(url_for('routes.list_pending_transfers'))

    # Solo cancelar si está pendiente
    if transfer_request.status != 'pendiente':
        flash(f"Esta solicitud ya no está pendiente (estado: {transfer_request.status}).", "warning")
        return redirect(url_for('routes.list_pending_transfers'))

    transfer_request.status = 'cancelada'
    transfer_request.updated_at = datetime.utcnow()
    # Opcional: añadir nota de cancelación

    # Notificar (opcionalmente) al pastor que iba a aprobar
    if transfer_request.approving_pastor_id:
         # ... (código para enviar mensaje Message) ...
         pass

    try:
        db.session.commit()
        flash('Solicitud de transferencia cancelada.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al cancelar la solicitud: {e}', 'danger')
        current_app.logger.error(f"Error cancelling transfer request {request_id}: {e}", exc_info=True)

    return redirect(url_for('routes.list_pending_transfers'))

# ============================================================================
# RUTAS ACADÉMICAS
# ============================================================================

from app.academic_services import AcademicService, PastoralAcademicService
from app.academic_forms import (AcademicSchoolForm, PastoralProgramForm, AcademicGradeForm,
                               PastoralGradeForm, AcademicEnrollmentForm, PastoralEnrollmentForm)
from functools import wraps

# Decorador para roles académicos
def academic_role_required(*roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if current_user.role not in roles:
                flash('No tienes permisos para acceder a esta página.', 'danger')
                return redirect(url_for('routes.dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# DASHBOARD ACADÉMICO PRINCIPAL
@routes_bp.route('/academic')
@login_required
def academic_dashboard():
    """Dashboard principal del sistema académico"""
    if current_user.role in ['administrador', 'secretaria', 'instituto', 'rector']:
        # Vista administrativa
        schools = AcademicSchool.query.filter_by(is_active=True).all()
        pastoral_programs = PastoralProgram.query.filter_by(is_active=True).all()

        stats = {
            'total_schools': len(schools),
            'total_programs': len(pastoral_programs),
            'active_students': AcademicEnrollment.query.filter_by(status='active').count(),
            'pastoral_students': PastoralEnrollment.query.filter_by(status='active').count(),
            'pending_approvals': PastoralEnrollment.query.filter_by(status='pending').count(),
            'completed_this_month': AcademicEnrollment.query.filter(
                AcademicEnrollment.status == 'completed',
                AcademicEnrollment.completion_date >= datetime.now().replace(day=1).date()
            ).count()
        }

        return render_template('academic/dashboard.html',
                             schools=schools,
                             pastoral_programs=pastoral_programs,
                             stats=stats)

    elif current_user.role == 'pastorado':
        # Vista de pastor
        my_schools = AcademicSchool.query.filter_by(church_id=current_user.church_id).all()
        my_pastoral_enrollments = PastoralEnrollment.query.filter_by(student_id=current_user.id).all()

        return render_template('academic/pastor_dashboard.html',
                             schools=my_schools,
                             enrollments=my_pastoral_enrollments)

    else:
        # Vista de miembro/estudiante
        my_enrollments = AcademicEnrollment.query.filter_by(student_id=current_user.id).all()
        return render_template('academic/student_dashboard.html', enrollments=my_enrollments)

# GESTIÓN DE ESCUELAS BÍBLICAS
@routes_bp.route('/academic/schools')
@login_required
@academic_role_required('administrador', 'secretaria', 'instituto')
def academic_schools():
    """Lista de escuelas bíblicas"""
    schools = AcademicSchool.query.filter_by(is_active=True).all()
    return render_template('academic/schools.html', schools=schools)

@routes_bp.route('/academic/schools/create', methods=['GET', 'POST'])
@login_required
@academic_role_required('administrador', 'secretaria')
def create_academic_school():
    """Crear nueva escuela bíblica"""
    form = AcademicSchoolForm()
    form.church_id.choices = [(c.id, c.name) for c in Church.query.all()]
    form.director_id.choices = [(0, 'Sin Director')] + [
        (u.id, f"{u.first_name} {u.last_name}")
        for u in User.query.filter_by(role='pastorado').all()
    ]

    if form.validate_on_submit():
        try:
            school = AcademicService.create_school(
                church_id=form.church_id.data,
                name=form.name.data,
                description=form.description.data,
                director_id=form.director_id.data if form.director_id.data != 0 else None,
                start_date=form.start_date.data,
                end_date=form.end_date.data,
                max_students=form.max_students.data,
                auto_enrollment=form.auto_enrollment.data
            )
            flash('Escuela creada exitosamente', 'success')
            return redirect(url_for('routes.academic_schools'))
        except Exception as e:
            flash(f'Error al crear escuela: {e}', 'danger')

    return render_template('academic/create_school.html', form=form)

@routes_bp.route('/academic/schools/<int:school_id>')
@login_required
def academic_school_detail(school_id):
    """Detalle de escuela bíblica"""
    school = AcademicSchool.query.get_or_404(school_id)

    # Verificar permisos
    if current_user.role not in ['administrador', 'secretaria', 'instituto']:
        if current_user.role == 'pastorado' and school.church_id != current_user.church_id:
            flash('No tienes permisos para ver esta escuela', 'danger')
            return redirect(url_for('routes.academic_dashboard'))

    enrollments = AcademicEnrollment.query.filter_by(school_id=school_id).all()

    return render_template('academic/school_detail.html',
                         school=school,
                         enrollments=enrollments)

# GESTIÓN DE PROGRAMAS PASTORALES
@routes_bp.route('/academic/pastoral')
@login_required
@academic_role_required('administrador', 'instituto', 'rector')
def pastoral_programs():
    """Lista de programas pastorales"""
    programs = PastoralProgram.query.filter_by(is_active=True).all()
    pending_enrollments = PastoralEnrollment.query.filter_by(status='pending').all()

    return render_template('academic/pastoral_programs.html',
                         programs=programs,
                         pending_enrollments=pending_enrollments)

@routes_bp.route('/academic/pastoral/create', methods=['GET', 'POST'])
@login_required
@academic_role_required('administrador', 'instituto', 'rector')
def create_pastoral_program():
    """Crear nuevo programa pastoral"""
    form = PastoralProgramForm()

    # Obtener institutos disponibles
    institutes = PastoralInstitute.query.filter_by(is_active=True).all()
    form.institute_id.choices = [(i.id, i.name) for i in institutes]

    if form.validate_on_submit():
        try:
            program = PastoralAcademicService.create_pastoral_program(
                institute_id=form.institute_id.data,
                name=form.name.data,
                program_type=form.program_type.data,
                duration_months=form.duration_months.data,
                total_credits=form.total_credits.data,
                min_ministry_years=form.min_ministry_years.data,
                max_students=form.max_students.data,
                requires_approval=form.requires_approval.data,
                created_by_id=current_user.id,
                description=form.description.data
            )
            flash('Programa pastoral creado exitosamente', 'success')
            return redirect(url_for('routes.pastoral_programs'))
        except Exception as e:
            flash(f'Error al crear programa: {e}', 'danger')

    return render_template('academic/create_pastoral_program.html', form=form)

@routes_bp.route('/academic/pastoral/enroll/<int:program_id>')
@login_required
def enroll_in_pastoral_program(program_id):
    """Matricular pastor en programa pastoral"""
    if current_user.role not in ['pastorado', 'superintendente']:
        flash('Solo pastores pueden matricularse en programas pastorales', 'danger')
        return redirect(url_for('routes.academic_dashboard'))

    try:
        enrollment = PastoralAcademicService.enroll_pastor(current_user.id, program_id)
        flash('Solicitud de matrícula enviada. Pendiente de aprobación.', 'info')
    except ValueError as e:
        flash(str(e), 'danger')
    except Exception as e:
        flash(f'Error al procesar matrícula: {e}', 'danger')

    return redirect(url_for('routes.academic_dashboard'))

# APROBACIÓN DE MATRÍCULAS PASTORALES
@routes_bp.route('/academic/pastoral/approve/<int:enrollment_id>')
@login_required
@academic_role_required('instituto', 'rector')
def approve_pastoral_enrollment(enrollment_id):
    """Aprobar matrícula pastoral"""
    try:
        success = PastoralAcademicService.approve_enrollment(
            enrollment_id,
            current_user.id,
            "Aprobado por sistema académico"
        )
        if success:
            flash('Matrícula aprobada exitosamente', 'success')
        else:
            flash('No se pudo aprobar la matrícula', 'danger')
    except Exception as e:
        flash(f'Error al aprobar matrícula: {e}', 'danger')

    return redirect(url_for('routes.pastoral_programs'))

@routes_bp.route('/academic/pastoral/reject/<int:enrollment_id>')
@login_required
@academic_role_required('instituto', 'rector')
def reject_pastoral_enrollment(enrollment_id):
    """Rechazar matrícula pastoral"""
    try:
        enrollment = PastoralEnrollment.query.get_or_404(enrollment_id)
        enrollment.status = 'dropped'
        enrollment.approved_by_id = current_user.id
        enrollment.approval_notes = "Rechazado por sistema académico"
        db.session.commit()

        flash('Matrícula rechazada', 'info')
    except Exception as e:
        flash(f'Error al rechazar matrícula: {e}', 'danger')

    return redirect(url_for('routes.pastoral_programs'))

# GESTIÓN DE CALIFICACIONES
@routes_bp.route('/academic/grades/assign/<int:enrollment_id>', methods=['GET', 'POST'])
@login_required
@academic_role_required('administrador', 'secretaria', 'instituto', 'profesor_corporativo')
def assign_grade(enrollment_id):
    """Asignar calificación a estudiante"""
    enrollment = AcademicEnrollment.query.get_or_404(enrollment_id)
    form = AcademicGradeForm()

    # Configurar choices
    form.enrollment_id.choices = [(enrollment.id, f"{enrollment.student.first_name} {enrollment.student.last_name}")]
    form.subject_id.choices = [(s.id, s.name) for s in enrollment.curriculum.subjects]

    if form.validate_on_submit():
        try:
            grade = AcademicService.assign_grade(
                enrollment_id=form.enrollment_id.data,
                subject_id=form.subject_id.data,
                grade_value=form.grade_value.data,
                teacher_id=current_user.id,
                grade_type=form.grade_type.data,
                comments=form.comments.data
            )
            flash('Calificación asignada exitosamente', 'success')
            return redirect(url_for('routes.academic_school_detail', school_id=enrollment.school_id))
        except Exception as e:
            flash(f'Error al asignar calificación: {e}', 'danger')

    return render_template('academic/assign_grade.html', form=form, enrollment=enrollment)

# REPORTES ACADÉMICOS
@routes_bp.route('/academic/reports')
@login_required
@academic_role_required('administrador', 'secretaria', 'instituto', 'rector')
def academic_reports():
    """Reportes del sistema académico"""
    stats = PastoralAcademicService.get_dashboard_stats()

    # Estadísticas adicionales
    stats.update({
        'certificates_issued': AcademicEnrollment.query.filter_by(certificate_issued=True).count(),
        'pastoral_certificates': PastoralEnrollment.query.filter_by(certificate_issued=True).count(),
        'dropout_rate': 0,  # Calcular tasa de deserción
        'completion_rate': 0  # Calcular tasa de finalización
    })

    return render_template('academic/reports.html', stats=stats)

# API ENDPOINTS PARA AJAX
@routes_bp.route('/api/academic/schools/<int:church_id>')
@login_required
def api_schools_by_church(church_id):
    """API: Obtener escuelas por iglesia"""
    schools = AcademicSchool.query.filter_by(church_id=church_id, is_active=True).all()
    return jsonify([{'id': s.id, 'name': s.name} for s in schools])

@routes_bp.route('/api/academic/curriculums/<int:school_id>')
@login_required
def api_curriculums_by_school(school_id):
    """API: Obtener pensums por escuela"""
    curriculums = AcademicCurriculum.query.filter_by(school_id=school_id, is_active=True).all()
    return jsonify([{'id': c.id, 'name': c.name} for c in curriculums])

@routes_bp.route('/api/academic/subjects/<int:curriculum_id>')
@login_required
def api_subjects_by_curriculum(curriculum_id):
    """API: Obtener materias por pensum"""
    subjects = AcademicSubject.query.filter_by(curriculum_id=curriculum_id).all()
    return jsonify([{'id': s.id, 'name': s.name, 'code': s.code} for s in subjects])