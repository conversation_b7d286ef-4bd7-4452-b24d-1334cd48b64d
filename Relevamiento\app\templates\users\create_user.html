<!-- app/templates/users/create_user.html -->
{% extends "users/base_users.html" %}

{% block title %}Crear Usuario{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-user-plus"></i>
                    Crear Nuevo Usuario
                </h2>
                <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Volver a Lista
                </a>
            </div>

            <!-- Formulario -->
            <form method="POST" id="create-user-form">
                {{ form.hidden_tag() }}

                <!-- Información Básica -->
                <div class="form-section">
                    <h4><i class="fas fa-user"></i> Información Básica</h4>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                                {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Nombre único para identificar al usuario en el sistema.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Email para notificaciones (opcional).
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Mínimo 6 caracteres.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password_confirm.label(class="form-label") }}
                                {{ form.password_confirm(class="form-control" + (" is-invalid" if form.password_confirm.errors else "")) }}
                                {% if form.password_confirm.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password_confirm.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rol y Estado -->
                <div class="form-section">
                    <h4><i class="fas fa-shield-alt"></i> Rol y Estado</h4>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.role.label(class="form-label") }}
                                {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                                {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    <small>
                                        <strong>Administrador:</strong> Acceso completo<br>
                                        <strong>Supervisor:</strong> Gestión de datos y reportes<br>
                                        <strong>Operador:</strong> Edición de puntos y cámaras<br>
                                        <strong>Visualizador:</strong> Solo lectura
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Estado</label>
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>
                                <div class="form-text">
                                    Los usuarios inactivos no pueden iniciar sesión.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <button type="button" class="btn btn-outline-info btn-sm" id="apply-role-defaults">
                                <i class="fas fa-magic"></i>
                                Aplicar Permisos por Defecto del Rol
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Permisos Específicos -->
                <div class="form-section">
                    <h4><i class="fas fa-key"></i> Permisos Específicos</h4>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>Puntos</h6>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" name="points_view" id="points_view" class="form-check-input" data-group="points">
                                    <label for="points_view" class="form-check-label">Ver puntos</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="points_edit" id="points_edit" class="form-check-input" data-group="points">
                                    <label for="points_edit" class="form-check-label">Editar puntos</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="points_create" id="points_create" class="form-check-input" data-group="points">
                                    <label for="points_create" class="form-check-label">Crear puntos</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="points_delete" id="points_delete" class="form-check-input" data-group="points">
                                    <label for="points_delete" class="form-check-label">Eliminar puntos</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6>Cámaras</h6>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" name="cameras_view" id="cameras_view" class="form-check-input" data-group="cameras">
                                    <label for="cameras_view" class="form-check-label">Ver cámaras</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="cameras_edit" id="cameras_edit" class="form-check-input" data-group="cameras">
                                    <label for="cameras_edit" class="form-check-label">Editar cámaras</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="cameras_create" id="cameras_create" class="form-check-input" data-group="cameras">
                                    <label for="cameras_create" class="form-check-label">Crear cámaras</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="cameras_delete" id="cameras_delete" class="form-check-input" data-group="cameras">
                                    <label for="cameras_delete" class="form-check-label">Eliminar cámaras</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>Reportes</h6>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" name="reports_view" id="reports_view" class="form-check-input" data-group="reports">
                                    <label for="reports_view" class="form-check-label">Ver reportes</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="reports_export" id="reports_export" class="form-check-input" data-group="reports">
                                    <label for="reports_export" class="form-check-label">Exportar datos</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6>Administración</h6>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" name="users_manage" id="users_manage" class="form-check-input" data-group="admin">
                                    <label for="users_manage" class="form-check-label">Gestionar usuarios</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Restricciones Geográficas -->
                <div class="form-section">
                    <h4><i class="fas fa-map-marker-alt"></i> Restricciones Geográficas</h4>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.cities.label(class="form-label") }}
                                {{ form.cities(class="form-select multi-select" + (" is-invalid" if form.cities.errors else ""), multiple=True) }}
                                {% if form.cities.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.cities.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Mantén Ctrl presionado para seleccionar múltiples ciudades. Vacío = todas las ciudades.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.sources.label(class="form-label") }}
                                {{ form.sources(class="form-select multi-select" + (" is-invalid" if form.sources.errors else ""), multiple=True) }}
                                {% if form.sources.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.sources.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Mantén Ctrl presionado para seleccionar múltiples fuentes. Vacío = todas las fuentes.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Botones -->
                <div class="form-section">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Cancelar
                        </a>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Crear Usuario
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
