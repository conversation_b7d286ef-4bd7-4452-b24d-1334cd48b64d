<!-- dashboard.html -->

<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Dashboard</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
  <link rel="icon" href="'{{ url_for('static', filename='RNCom.ico') }}'" type="image/x-icon">
  <style>
    .body-background::before {
      background: url('{{ url_for('static', filename='RNCom.png') }}') no-repeat center center;
      background-size: contain; /* Ajusta la imagen sin estirarla */
      opacity: 0.3; /* Reduce la opacidad para que no interfiera */

    }
    .flash-error {
      color: red;
      font-weight: bold;
    }
    /* Contenedor para la lista de nodos */
    .node-list-container {
        overflow-y: auto; /* Scroll vertical si es necesario */
        max-height: 50vh;  /* Altura máxima (ajusta según necesites) */
        margin-bottom: 20px; /* Espacio debajo de la lista */
        padding: 10px;
        border-radius: 5px;
    }
    .node-list-container ul{
        margin: 0; /*Reseteamos margenes*/
        padding: 0; /*Reseteamos padding*/
        list-style: none; /* Quitamos los puntos de la lista*/
    }
    .node-list-container li{
        margin-bottom: 5px;
    }
    .download-link {
        margin-top: auto;  /*  Empuja el enlace hacia abajo */
        text-align: center; /* Centra el enlace */
        padding-bottom: 1em;
    }
    footer{
        text-align: center;
        padding: 1em;
        margin-top: auto;
    }
    /* Ajustes adicionales para evitar scroll horizontal en general*/

    *, *::before, *::after{
        box-sizing: border-box; /*  Modelo de caja */
    }
  </style>
</head>
<body class="body-background">
  <header>
    <h1>Bienvenido, {{ current_user.username }}</h1>
    <nav class="navigation-links">
        <!--  Enlaces directos, NO dropdown -->
        <a href="{{ url_for('views.private_chats') }}">Chats Privados</a>
        {% if current_user.role == 'admin' %}
            <a href="{{ url_for('admin.list_users') }}">Administrar Usuarios</a>
            <a href="{{ url_for('admin.list_nodes') }}">Administrar Nodos</a>
            <a href="{{ url_for('admin.view_connected_users') }}">Ver Usuarios Conectados</a>
        {% endif %}
        <a href="{{ url_for('auth.logout') }}">Logout</a>
    </nav>
  </header>

  <main>
    <!-- ... (El resto de tu contenido principal, flashes, lista de nodos, etc.) ... -->
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          <ul class="flashes">
            {% for category, message in messages %}
              <li class="flash-{{ category }}">{{ message }}</li>
            {% endfor %}
          </ul>
        {% endif %}
      {% endwith %}
      <div class="node-list-container">
          <ul>
            {% for node in nodes %}
              <li><a href="{{ url_for('views.node_view', node_id=node.id) }}">{{ node.name }}</a></li>
            {% endfor %}
          </ul>
      </div>

    </main>
    <div class="download-link">
      {% if current_user.role == 'admin' %}
        <a href="{{ url_for('static', filename='install_ptt_client.zip') }}" download>Descargar Script de Instalación PTT</a>
      {% endif %}
    </div>
    <footer>
      <p>© 2024 Tu Sitio Web</p>
    </footer>
      <!--  Elimina el script anterior, ya no es necesario -->
</body>
</html>