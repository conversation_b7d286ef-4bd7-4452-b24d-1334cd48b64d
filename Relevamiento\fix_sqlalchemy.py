#!/usr/bin/env python3
# --- Archivo: fix_sqlalchemy.py ---
# Script para arreglar el problema de SQLAlchemy sin tocar los datos

import os
import sys
import sqlite3

def clear_python_cache():
    """Limpiar cache de Python."""
    print("🧹 Limpiando cache de Python...")
    
    import subprocess
    
    try:
        # Limpiar archivos .pyc
        result = subprocess.run(['find', '.', '-name', '*.pyc', '-delete'], 
                              capture_output=True, text=True)
        
        # Limpiar directorios __pycache__
        result = subprocess.run(['find', '.', '-name', '__pycache__', '-type', 'd', '-exec', 'rm', '-rf', '{}', '+'], 
                              capture_output=True, text=True)
        
        print("✅ Cache de Python limpiado")
        return True
        
    except Exception as e:
        print(f"❌ Error limpiando cache: {e}")
        return False

def stop_flask_app():
    """Detener la aplicación Flask."""
    print("🛑 Deteniendo aplicación Flask...")
    
    import subprocess
    
    try:
        # Buscar procesos de Python que contengan run.py
        result = subprocess.run(['pkill', '-f', 'python.*run.py'], 
                              capture_output=True, text=True)
        
        print("✅ Aplicación Flask detenida")
        return True
        
    except Exception as e:
        print(f"⚠️  Error deteniendo aplicación: {e}")
        return False

def verify_database_structure():
    """Verificar que la estructura de la base de datos sea correcta."""
    print("🔍 Verificando estructura de base de datos...")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Verificar tabla user
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        
        required_columns = ['id', 'username', 'email', 'password_hash', 'role', 'is_active', 'created_by', 'created_at', 'updated_at']
        actual_columns = [col[1] for col in columns]
        
        missing_columns = set(required_columns) - set(actual_columns)
        
        if missing_columns:
            print(f"❌ Columnas faltantes en tabla user: {missing_columns}")
            return False
        
        print("✅ Estructura de tabla user correcta")
        
        # Verificar tablas de permisos
        permission_tables = ['user_city_permissions', 'user_source_permissions', 'user_permissions']
        
        for table in permission_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?;", (table,))
            if not cursor.fetchone():
                print(f"❌ Tabla {table} no existe")
                return False
        
        print("✅ Tablas de permisos existen")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verificando estructura: {e}")
        return False

def test_sqlalchemy_fresh():
    """Probar SQLAlchemy con importación fresca."""
    print("🧪 Probando SQLAlchemy con importación fresca...")
    
    try:
        # Limpiar módulos importados relacionados con la app
        modules_to_remove = []
        for module_name in sys.modules:
            if module_name.startswith('app'):
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            del sys.modules[module_name]
        
        print("🧹 Módulos de app limpiados del cache")
        
        # Importar de nuevo
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            # Probar consulta básica
            users = User.query.all()
            print(f"✅ SQLAlchemy funciona: {len(users)} usuarios encontrados")
            
            for user in users:
                print(f"  👤 {user.username} ({user.role}) - Activo: {user.is_active}")
                print(f"    🔐 Es admin: {user.is_admin()}")
                print(f"    🔐 Puede gestionar usuarios: {user.can_manage_users()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error probando SQLAlchemy: {e}")
        print(f"Detalles del error: {str(e)}")
        return False

def recreate_sqlalchemy_metadata():
    """Recrear metadata de SQLAlchemy forzando la detección de la estructura."""
    print("🔄 Recreando metadata de SQLAlchemy...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        
        app = create_app()
        
        with app.app_context():
            # Forzar reflexión de la base de datos
            db.engine.dispose()  # Cerrar conexiones existentes
            
            # Recrear metadata
            db.metadata.clear()
            db.metadata.reflect(bind=db.engine)
            
            print("✅ Metadata recreada")
            
            # Probar consulta directa con SQL
            result = db.engine.execute("SELECT username, role FROM user LIMIT 1;")
            row = result.fetchone()
            if row:
                print(f"✅ Consulta SQL directa funciona: {row[0]} ({row[1]})")
            
            return True
            
    except Exception as e:
        print(f"❌ Error recreando metadata: {e}")
        return False

def main():
    """Función principal."""
    print("🔧 Reparador de SQLAlchemy (Sin Tocar Datos)")
    print("=" * 50)
    
    # Verificar estructura de base de datos
    if not verify_database_structure():
        print("\n❌ La estructura de la base de datos no es correcta")
        print("💡 Ejecuta primero: python migrate_permissions.py")
        sys.exit(1)
    
    # Detener aplicación Flask
    stop_flask_app()
    
    # Limpiar cache de Python
    clear_python_cache()
    
    # Probar SQLAlchemy con importación fresca
    if test_sqlalchemy_fresh():
        print("\n🎉 ¡SQLAlchemy funciona correctamente!")
        print("\n🚀 Puedes iniciar la aplicación:")
        print("   python run.py &")
    else:
        print("\n⚠️  SQLAlchemy aún tiene problemas, probando recreación de metadata...")
        
        if recreate_sqlalchemy_metadata():
            print("\n🎉 ¡Metadata recreada exitosamente!")
            print("\n🚀 Puedes iniciar la aplicación:")
            print("   python run.py &")
        else:
            print("\n❌ No se pudo arreglar SQLAlchemy")
            print("\n💡 Opciones:")
            print("   1. Hacer backup: python backup_data.py")
            print("   2. Reconstruir: python clean_rebuild.py")

if __name__ == "__main__":
    main()
