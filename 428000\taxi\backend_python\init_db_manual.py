#!/usr/bin/env python3
"""
Script para inicializar manualmente la base de datos.
Este script crea las tablas y los roles iniciales.
"""

from sqlalchemy import create_engine, text
from app.core.config import settings
from app.models.user import RoleEnum

# Obtener la URL de la base de datos desde las variables de entorno
def get_db_url():
    return settings.DATABASE_URL

# Crear las tablas manualmente
def create_tables():
    engine = create_engine(get_db_url())
    conn = engine.connect()

    try:
        # Crear tipo enum para roles
        conn.execute(text("""
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'roleenum') THEN
                CREATE TYPE roleenum AS ENUM ('usuario', 'taxi', 'operador', 'titular', 'base', 'administrador');
            END IF;
        END
        $$;
        """))

        # Crear tabla roles
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS roles (
            id SERIAL PRIMARY KEY,
            name roleenum NOT NULL,
            description VARCHAR(255)
        );
        CREATE INDEX IF NOT EXISTS ix_roles_id ON roles (id);
        """))

        # Crear tabla users
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            email VARCHAR(255) NOT NULL UNIQUE,
            full_name VARCHAR(255),
            hashed_password VARCHAR(255) NOT NULL,
            phone_number VARCHAR(50) UNIQUE,
            is_active BOOLEAN DEFAULT TRUE,
            is_superuser BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE
        );
        CREATE INDEX IF NOT EXISTS ix_users_id ON users (id);
        CREATE INDEX IF NOT EXISTS ix_users_email ON users (email);
        CREATE INDEX IF NOT EXISTS ix_users_full_name ON users (full_name);
        CREATE INDEX IF NOT EXISTS ix_users_phone_number ON users (phone_number);
        """))

        # Crear tabla user_roles_association
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS user_roles_association (
            user_id INTEGER NOT NULL,
            role_id INTEGER NOT NULL,
            PRIMARY KEY (user_id, role_id),
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (role_id) REFERENCES roles (id)
        );
        """))

        # Crear tabla basestations
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS basestations (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL UNIQUE,
            address VARCHAR(255),
            phone_number VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            latitude VARCHAR(50),
            longitude VARCHAR(50),
            manager_id INTEGER UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE,
            FOREIGN KEY (manager_id) REFERENCES users (id)
        );
        CREATE INDEX IF NOT EXISTS ix_basestations_id ON basestations (id);
        CREATE INDEX IF NOT EXISTS ix_basestations_name ON basestations (name);
        """))

        # Crear tabla vehicles
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS vehicles (
            id SERIAL PRIMARY KEY,
            plate_number VARCHAR(50) NOT NULL UNIQUE,
            brand VARCHAR(100),
            model VARCHAR(100),
            year INTEGER,
            color VARCHAR(50),
            status VARCHAR(50),
            latitude VARCHAR(50),
            longitude VARCHAR(50),
            owner_id INTEGER,
            driver_id INTEGER,
            base_station_id INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE,
            FOREIGN KEY (owner_id) REFERENCES users (id),
            FOREIGN KEY (driver_id) REFERENCES users (id),
            FOREIGN KEY (base_station_id) REFERENCES basestations (id)
        );
        CREATE INDEX IF NOT EXISTS ix_vehicles_id ON vehicles (id);
        CREATE INDEX IF NOT EXISTS ix_vehicles_plate_number ON vehicles (plate_number);
        """))

        # Crear tabla trips
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS trips (
            id SERIAL PRIMARY KEY,
            passenger_id INTEGER NOT NULL,
            driver_id INTEGER,
            vehicle_id INTEGER,
            origin_latitude VARCHAR(50) NOT NULL,
            origin_longitude VARCHAR(50) NOT NULL,
            origin_address VARCHAR(255),
            destination_latitude VARCHAR(50) NOT NULL,
            destination_longitude VARCHAR(50) NOT NULL,
            destination_address VARCHAR(255),
            status VARCHAR(50),
            estimated_fare FLOAT,
            actual_fare FLOAT,
            passenger_rating FLOAT,
            driver_rating FLOAT,
            requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            accepted_at TIMESTAMP WITH TIME ZONE,
            started_at TIMESTAMP WITH TIME ZONE,
            completed_at TIMESTAMP WITH TIME ZONE,
            FOREIGN KEY (passenger_id) REFERENCES users (id),
            FOREIGN KEY (driver_id) REFERENCES users (id),
            FOREIGN KEY (vehicle_id) REFERENCES vehicles (id)
        );
        CREATE INDEX IF NOT EXISTS ix_trips_id ON trips (id);
        """))

        conn.commit()
        print("Tablas creadas correctamente.")
    except Exception as e:
        conn.rollback()
        print(f"Error al crear las tablas: {e}")
    finally:
        conn.close()

# Crear roles iniciales
def create_initial_roles():
    engine = create_engine(get_db_url())
    with engine.connect() as conn:
        try:
            # Insertar roles
            for role_enum_member in RoleEnum: # Renombrado para claridad
                role_value = role_enum_member.value
                description = f"Rol de {role_value.capitalize()}"
                
                # Verificar si el rol ya existe usando parámetros
                stmt_select = text("SELECT id FROM roles WHERE name = :name")
                result = conn.execute(stmt_select, {"name": role_value})
                
                if not result.fetchone():
                    # Insertar rol usando parámetros
                    stmt_insert = text("INSERT INTO roles (name, description) VALUES (:name, :description)")
                    conn.execute(stmt_insert, {"name": role_value, "description": description})
                    print(f"Rol {role_enum_member.name} ('{role_value}') creado.")
                else:
                    print(f"Rol {role_enum_member.name} ('{role_value}') ya existe.")

            conn.commit()
            print("Roles iniciales procesados correctamente.")
        except Exception as e:
            # conn.rollback() # El rollback es automático con el context manager si ocurre una excepción antes del commit
            print(f"Error al crear los roles iniciales: {e}")

if __name__ == "__main__":
    print("Inicializando la base de datos...")
    create_tables()
    create_initial_roles()
    print("Base de datos inicializada correctamente.")
