from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
from datetime import datetime
import logging

from app.api import deps
from app.schemas import vehicle_schema
from app.services import vehicle_service
from app.models.user import User
from app.models.vehicle import Vehicle, VehicleStatus, VehicleCategory
from app.websockets.connection import connection_manager

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", status_code=status.HTTP_201_CREATED)
def create_vehicle(
    vehicle_in: vehicle_schema.VehicleCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_or_operator_user)
):

    # Verificar si ya existe un vehículo con la misma placa
    db_vehicle = vehicle_service.get_vehicle_by_plate(db, plate_number=vehicle_in.plate_number)
    if db_vehicle:
        raise HTTPException(
            status_code=400,
            detail="Ya existe un vehículo con esta placa"
        )

    # Crear el vehículo
    vehicle = vehicle_service.create_vehicle(db=db, vehicle_in=vehicle_in)
    return vehicle

@router.get("/")
def read_vehicles(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    category: Optional[str] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_or_operator_user)
):
    # Filtrar por estado y/o categoría si se proporcionan
    if status and category:
        try:
            status_enum = VehicleStatus(status)
            category_enum = VehicleCategory(category)
            # Obtener vehículos por estado y categoría
            vehicles_by_status = vehicle_service.get_vehicles_by_status(db, status=status_enum, skip=skip, limit=limit)
            # Filtrar los vehículos que coincidan con la categoría
            vehicles = [v for v in vehicles_by_status if v.category == category_enum]
        except ValueError as e:
            if "VehicleStatus" in str(e):
                raise HTTPException(
                    status_code=400,
                    detail=f"Estado no válido. Valores permitidos: {[e.value for e in VehicleStatus]}"
                )
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Categoría no válida. Valores permitidos: {[e.value for e in VehicleCategory]}"
                )
    elif status:
        try:
            status_enum = VehicleStatus(status)
            vehicles = vehicle_service.get_vehicles_by_status(db, status=status_enum, skip=skip, limit=limit)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Estado no válido. Valores permitidos: {[e.value for e in VehicleStatus]}"
            )
    elif category:
        try:
            category_enum = VehicleCategory(category)
            vehicles = vehicle_service.get_vehicles_by_category(db, category=category_enum, skip=skip, limit=limit)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Categoría no válida. Valores permitidos: {[e.value for e in VehicleCategory]}"
            )
    else:
        vehicles = vehicle_service.get_vehicles(db, skip=skip, limit=limit)

    return vehicles

@router.get("/{vehicle_id}")
def read_vehicle(
    vehicle_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    # Obtener el vehículo
    vehicle = vehicle_service.get_vehicle(db, vehicle_id=vehicle_id)
    if vehicle is None:
        raise HTTPException(status_code=404, detail="Vehículo no encontrado")

    # Verificar si el usuario actual es administrador u operador
    is_admin_or_operator = False
    try:
        # Verificar si el usuario tiene rol de administrador u operador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) IN ('administrador', 'operador')
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})

        is_admin_or_operator = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol: {e}")

    # Verificar si el usuario actual es el conductor o el propietario del vehículo
    is_driver_or_owner = (vehicle.driver_id == current_user.id) or (vehicle.owner_id == current_user.id)

    if not (is_admin_or_operator or is_driver_or_owner):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos suficientes para acceder a esta información"
        )

    return vehicle

@router.put("/{vehicle_id}")
def update_vehicle(
    vehicle_id: int,
    vehicle_in: vehicle_schema.VehicleUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    # Verificar si el usuario actual es administrador u operador
    is_admin = False
    is_operator = False
    try:
        # Verificar si el usuario tiene rol de administrador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'administrador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_admin = role_query.first() is not None

        # Verificar si el usuario tiene rol de operador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'operador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_operator = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol: {e}")

    # Obtener el vehículo
    vehicle = vehicle_service.get_vehicle(db, vehicle_id=vehicle_id)
    if vehicle is None:
        raise HTTPException(status_code=404, detail="Vehículo no encontrado")

    # Verificar si el usuario actual es el propietario del vehículo
    is_owner = vehicle.owner_id == current_user.id

    # Los operadores solo pueden actualizar ciertos campos
    if is_operator and not is_admin:
        # Crear una copia del objeto con solo los campos permitidos para operadores
        allowed_fields = ["status", "category", "last_latitude", "last_longitude"]
        update_data = {}

        for field in allowed_fields:
            if field in vehicle_in.__dict__ and vehicle_in.__dict__[field] is not None:
                update_data[field] = vehicle_in.__dict__[field]

        vehicle_in = vehicle_schema.VehicleUpdate(**update_data)

    if not (is_admin or is_operator or is_owner):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos suficientes para modificar este vehículo"
        )

    # Actualizar el vehículo
    updated_vehicle = vehicle_service.update_vehicle(db, db_vehicle=vehicle, vehicle_in=vehicle_in)
    return updated_vehicle

@router.delete("/{vehicle_id}")
def delete_vehicle(
    vehicle_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user)
):

    # Obtener el vehículo
    vehicle = vehicle_service.get_vehicle(db, vehicle_id=vehicle_id)
    if vehicle is None:
        raise HTTPException(status_code=404, detail="Vehículo no encontrado")

    # Eliminar el vehículo
    deleted_vehicle = vehicle_service.delete_vehicle(db, vehicle_id=vehicle_id)
    return deleted_vehicle

@router.post("/{vehicle_id}/assign-driver/{driver_id}")
def assign_driver_to_vehicle(
    vehicle_id: int,
    driver_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user)
):

    # Verificar si el vehículo existe
    vehicle = vehicle_service.get_vehicle(db, vehicle_id=vehicle_id)
    if vehicle is None:
        raise HTTPException(status_code=404, detail="Vehículo no encontrado")

    # Verificar si el conductor existe
    from app.services import user_service
    driver = user_service.get_user(db, user_id=driver_id)
    if driver is None:
        raise HTTPException(status_code=404, detail="Conductor no encontrado")

    # Verificar si el conductor tiene el rol de taxi
    is_taxi = False
    try:
        # Verificar si el usuario tiene rol de taxi
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'taxi'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": driver_id})

        is_taxi = role_query.first() is not None
    except Exception as e:
        print(f"Error al verificar rol de taxi: {e}")

    if not is_taxi:
        raise HTTPException(
            status_code=400,
            detail="El usuario asignado debe tener el rol de taxi"
        )

    # Asignar el conductor al vehículo
    updated_vehicle = vehicle_service.assign_driver(db, vehicle_id=vehicle_id, driver_id=driver_id)
    return updated_vehicle

@router.post("/{vehicle_id}/location", status_code=status.HTTP_200_OK)
async def update_vehicle_location(
    vehicle_id: int,
    location: vehicle_schema.VehicleLocationUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Actualiza la ubicación de un vehículo y notifica a los clientes conectados.
    """
    # Verificar si el vehículo existe
    vehicle = vehicle_service.get_vehicle(db, vehicle_id=vehicle_id)
    if vehicle is None:
        raise HTTPException(status_code=404, detail="Vehículo no encontrado")

    # Verificar permisos (solo el conductor asignado o un administrador puede actualizar la ubicación)
    is_admin = False
    is_driver = False

    try:
        # Verificar si el usuario tiene rol de administrador
        sql_query = text("""
            SELECT r.name
            FROM roles r
            JOIN user_roles_association ura ON r.id = ura.role_id
            WHERE ura.user_id = :user_id AND LOWER(r.name::text) = 'administrador'
            LIMIT 1
        """)
        role_query = db.execute(sql_query, {"user_id": current_user.id})
        is_admin = role_query.first() is not None

        # Verificar si el usuario es el conductor asignado
        is_driver = vehicle.driver_id == current_user.id
    except Exception as e:
        logger.error(f"Error al verificar roles: {e}")

    if not (is_admin or is_driver):
        raise HTTPException(
            status_code=403,
            detail="No tienes permisos para actualizar la ubicación de este vehículo"
        )

    # Actualizar la ubicación del vehículo
    updated_vehicle = vehicle_service.update_vehicle_location(
        db,
        vehicle_id=vehicle_id,
        latitude=location.latitude,
        longitude=location.longitude
    )

    if not updated_vehicle:
        raise HTTPException(
            status_code=400,
            detail="No se pudo actualizar la ubicación del vehículo"
        )

    # Enviar notificación a los clientes conectados (en segundo plano)
    background_tasks.add_task(
        notify_location_update,
        vehicle_id=vehicle_id,
        latitude=location.latitude,
        longitude=location.longitude
    )

    return {"message": "Ubicación actualizada correctamente"}

async def notify_location_update(vehicle_id: int, latitude: str, longitude: str):
    """
    Notifica a los clientes conectados sobre la actualización de ubicación.
    """
    try:
        # Preparar mensaje para broadcast
        message = {
            "type": "location_update",
            "vehicle_id": vehicle_id,
            "latitude": latitude,
            "longitude": longitude,
            "timestamp": str(datetime.now())
        }

        # Enviar a todos los clientes en el grupo del vehículo
        group_name = f"vehicle_{vehicle_id}"
        await connection_manager.broadcast_to_group(group_name, message)

        logger.info(f"Notificación de ubicación enviada para vehículo {vehicle_id}")
    except Exception as e:
        logger.error(f"Error al enviar notificación de ubicación: {e}")
