 <!-- app/templates/register.html -->
 {% extends "base.html" %}

 {% block title %}Registro - {{ super() }}{% endblock %}
 
 {% block content %}
 <div class="row justify-content-center">
     <div class="col-md-6 col-lg-5">
          <div class="card mt-5">
              <div class="card-body">
                 <h2 class="card-title text-center mb-4">Crear Nueva Cuenta</h2>
                 <form action="{{ url_for('auth.register') }}" method="post" novalidate>
                     {{ form.hidden_tag() }} {# CSRF Protection #}
 
                     <div class="mb-3">
                         {{ form.username.label(class="form-label") }}
                         {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""), size=32, placeholder="Elige un nombre de usuario") }}
                         {% for error in form.username.errors %}
                             <div class="invalid-feedback">{{ error }}</div>
                         {% endfor %}
                     </div>
 
                     <div class="mb-3">
                         {{ form.email.label(class="form-label") }}
                         {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), size=64, placeholder="<EMAIL> (opcional)") }}
                         {% for error in form.email.errors %}
                             <div class="invalid-feedback">{{ error }}</div>
                         {% endfor %}
                     </div>
 
                     <div class="mb-3">
                         {{ form.password.label(class="form-label") }}
                         {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), size=32, placeholder="Crea una contraseña segura") }}
                          {% if form.password.errors %}
                              {% for error in form.password.errors %}
                                 <div class="invalid-feedback d-block">{{ error }}</div>
                              {% endfor %}
                          {% else %}
                              <div class="form-text">Mínimo 6 caracteres.</div>
                          {% endif %}
                     </div>
 
                     <div class="mb-3">
                         {{ form.password2.label(class="form-label") }}
                         {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else ""), size=32, placeholder="Repite la contraseña") }}
                         {% for error in form.password2.errors %}
                             <div class="invalid-feedback">{{ error }}</div>
                         {% endfor %}
                     </div>
 
                     <div class="d-grid mt-4">
                         {{ form.submit(class="btn btn-success btn-block") }}
                     </div>
                 </form>
             </div>
             <div class="card-footer text-center">
                 <small>¿Ya tienes cuenta? <a href="{{ url_for('auth.login') }}">Inicia sesión</a></small>
             </div>
         </div>
     </div>
 </div>
 {% endblock %}