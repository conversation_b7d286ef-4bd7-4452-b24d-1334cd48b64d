<!-- app/templates/users/edit_user.html -->
{% extends "users/base_users.html" %}

{% block title %}Editar Usuario - {{ user.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-user-edit"></i>
                    Editar Usuario: {{ user.username }}
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('users.user_permissions', user_id=user.id) }}" class="btn btn-info">
                        <i class="fas fa-key"></i>
                        Ver Permisos
                    </a>
                    <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Volver a Lista
                    </a>
                </div>
            </div>

            <!-- Información del Usuario -->
            <div class="user-info-card mb-4">
                <div class="row">
                    <div class="col-md-8">
                        <h5><i class="fas fa-info-circle"></i> Información del Usuario</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>ID:</strong> {{ user.id }}</p>
                                <p><strong>Usuario:</strong> {{ user.username }}</p>
                                <p><strong>Email:</strong> {{ user.email or 'No especificado' }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Rol:</strong> <span class="role-badge role-{{ user.role }}">{{ user.role }}</span></p>
                                <p><strong>Estado:</strong>
                                    <span class="{{ 'status-active' if user.is_active else 'status-inactive' }}">
                                        <i class="fas fa-{{ 'check-circle' if user.is_active else 'times-circle' }}"></i>
                                        {{ 'Activo' if user.is_active else 'Inactivo' }}
                                    </span>
                                </p>
                                <p><strong>Creado:</strong> {{ user.created_at.strftime('%d/%m/%Y %H:%M') if user.created_at else 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="user-avatar-large">
                            {{ user.username[0].upper() }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Formulario de Edición -->
            <form method="POST" id="edit-user-form">
                {{ form.hidden_tag() }}

                <!-- Información Básica -->
                <div class="form-section">
                    <h4><i class="fas fa-user"></i> Información Básica</h4>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                                {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Cambiar el nombre de usuario puede afectar el acceso del usuario.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Email para notificaciones (opcional).
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cambio de Contraseña -->
                <div class="form-section">
                    <h4><i class="fas fa-lock"></i> Cambiar Contraseña</h4>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Deja estos campos vacíos si no deseas cambiar la contraseña.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Mínimo 6 caracteres. Vacío = no cambiar.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password_confirm.label(class="form-label") }}
                                {{ form.password_confirm(class="form-control" + (" is-invalid" if form.password_confirm.errors else "")) }}
                                {% if form.password_confirm.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password_confirm.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rol y Estado -->
                <div class="form-section">
                    <h4><i class="fas fa-shield-alt"></i> Rol y Estado</h4>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.role.label(class="form-label") }}
                                {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                                {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    <small>
                                        <strong>Administrador:</strong> Acceso completo<br>
                                        <strong>Supervisor:</strong> Gestión de datos y reportes<br>
                                        <strong>Operador:</strong> Edición de puntos y cámaras<br>
                                        <strong>Visualizador:</strong> Solo lectura
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Estado</label>
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>
                                {% if user.id == current_user.id %}
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    No puedes desactivar tu propia cuenta.
                                </div>
                                {% else %}
                                <div class="form-text">
                                    Los usuarios inactivos no pueden iniciar sesión.
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <button type="button" class="btn btn-outline-info btn-sm" id="apply-role-defaults">
                                <i class="fas fa-magic"></i>
                                Aplicar Permisos por Defecto del Rol
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Restricciones Geográficas -->
                <div class="form-section">
                    <h4><i class="fas fa-map-marker-alt"></i> Restricciones Geográficas</h4>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.cities.label(class="form-label") }}
                                {{ form.cities(class="form-select multi-select" + (" is-invalid" if form.cities.errors else ""), multiple=True) }}
                                {% if form.cities.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.cities.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Mantén Ctrl presionado para seleccionar múltiples ciudades. Vacío = todas las ciudades.
                                </div>
                                <div class="current-permissions">
                                    <small class="text-muted">
                                        <strong>Actual:</strong>
                                        {% if user.city_permissions %}
                                            {% for perm in user.city_permissions %}
                                            <span class="permission-chip">{{ perm.city }}</span>
                                            {% endfor %}
                                        {% else %}
                                        <span class="text-muted">Todas las ciudades</span>
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.sources.label(class="form-label") }}
                                {{ form.sources(class="form-select multi-select" + (" is-invalid" if form.sources.errors else ""), multiple=True) }}
                                {% if form.sources.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.sources.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    Mantén Ctrl presionado para seleccionar múltiples fuentes. Vacío = todas las fuentes.
                                </div>
                                <div class="current-permissions">
                                    <small class="text-muted">
                                        <strong>Actual:</strong>
                                        {% if user.source_permissions %}
                                            {% for perm in user.source_permissions %}
                                            <span class="permission-chip">{{ perm.source }}</span>
                                            {% endfor %}
                                        {% else %}
                                        <span class="text-muted">Todas las fuentes</span>
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Botones -->
                <div class="form-section">
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                Cancelar
                            </a>

                            <a href="{{ url_for('users.user_permissions', user_id=user.id) }}" class="btn btn-info">
                                <i class="fas fa-key"></i>
                                Ver Permisos Detallados
                            </a>
                        </div>

                        <div>
                            {% if user.id != current_user.id %}
                            <button type="button" class="btn btn-warning" id="reset-password-btn">
                                <i class="fas fa-key"></i>
                                Resetear Contraseña
                            </button>
                            {% endif %}

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Guardar Cambios
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para resetear contraseña -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resetear Contraseña</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>¿Estás seguro de que deseas resetear la contraseña de <strong>{{ user.username }}</strong>?</p>
                <p class="text-info">Se generará una contraseña temporal que deberás comunicar al usuario.</p>
                <div id="new-password-display" class="alert alert-success" style="display: none;">
                    <strong>Nueva contraseña temporal:</strong>
                    <code id="temp-password"></code>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="copyPassword()">
                        <i class="fas fa-copy"></i> Copiar
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-warning" id="confirm-reset-password">
                    <i class="fas fa-key"></i>
                    Resetear Contraseña
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Aplicar permisos por defecto según el rol
document.getElementById('apply-role-defaults').addEventListener('click', function() {
    const role = document.getElementById('role').value;

    if (confirm('¿Aplicar los permisos por defecto para el rol ' + role + '?')) {
        // Aquí puedes implementar la lógica para aplicar permisos por defecto
        // según el rol seleccionado
        alert('Funcionalidad en desarrollo');
    }
});

// Resetear contraseña
document.getElementById('reset-password-btn').addEventListener('click', function() {
    $('#resetPasswordModal').modal('show');
});

document.getElementById('confirm-reset-password').addEventListener('click', function() {
    fetch(`/users/{{ user.id }}/reset-password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('temp-password').textContent = data.new_password;
            document.getElementById('new-password-display').style.display = 'block';
            document.getElementById('confirm-reset-password').style.display = 'none';
        } else {
            alert('Error al resetear la contraseña: ' + (data.message || 'Error desconocido'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al resetear la contraseña');
    });
});

// Copiar contraseña al portapapeles
function copyPassword() {
    const password = document.getElementById('temp-password').textContent;
    navigator.clipboard.writeText(password).then(function() {
        alert('Contraseña copiada al portapapeles');
    });
}

// Prevenir desactivar cuenta propia
{% if user.id == current_user.id %}
document.getElementById('is_active').addEventListener('change', function() {
    if (!this.checked) {
        this.checked = true;
        alert('No puedes desactivar tu propia cuenta.');
    }
});
{% endif %}
</script>
{% endblock %}
