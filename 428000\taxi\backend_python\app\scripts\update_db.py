"""
Script para actualizar la base de datos con las columnas faltantes.
"""
import os
import sys
from pathlib import Path

# Agregar el directorio raíz al path para poder importar los módulos
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy import create_engine, text
from app.core.config import settings

def get_db_url():
    """Obtener la URL de la base de datos."""
    return settings.DATABASE_URL

def create_connection():
    """Crear una conexión a la base de datos."""
    engine = create_engine(get_db_url())
    return engine.connect()

def check_column_exists(conn, table, column):
    """Verificar si una columna existe en una tabla."""
    query = text(f"""
    SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = '{table}'
        AND column_name = '{column}'
    );
    """)
    result = conn.execute(query).scalar()
    return result

def add_column_if_not_exists(conn, table, column, data_type):
    """Agregar una columna a una tabla si no existe."""
    if not check_column_exists(conn, table, column):
        query = text(f"ALTER TABLE {table} ADD COLUMN {column} {data_type};")
        conn.execute(query)
        print(f"Columna {column} agregada a la tabla {table}")
    else:
        print(f"La columna {column} ya existe en la tabla {table}")

def update_database():
    """Actualizar la base de datos con las columnas faltantes."""
    conn = create_connection()
    
    try:
        # Iniciar transacción
        trans = conn.begin()
        
        # Agregar columnas faltantes a la tabla vehicles
        add_column_if_not_exists(conn, "vehicles", "last_latitude", "VARCHAR")
        add_column_if_not_exists(conn, "vehicles", "last_longitude", "VARCHAR")
        add_column_if_not_exists(conn, "vehicles", "last_location_update", "TIMESTAMP WITH TIME ZONE")
        
        # Agregar columnas faltantes a la tabla trips
        add_column_if_not_exists(conn, "trips", "estimated_distance_meters", "INTEGER")
        add_column_if_not_exists(conn, "trips", "actual_distance_meters", "INTEGER")
        add_column_if_not_exists(conn, "trips", "estimated_duration_seconds", "INTEGER")
        add_column_if_not_exists(conn, "trips", "actual_duration_seconds", "INTEGER")
        
        # Confirmar transacción
        trans.commit()
        print("Base de datos actualizada correctamente")
    except Exception as e:
        # Revertir transacción en caso de error
        trans.rollback()
        print(f"Error al actualizar la base de datos: {e}")
    finally:
        # Cerrar conexión
        conn.close()

if __name__ == "__main__":
    update_database()
