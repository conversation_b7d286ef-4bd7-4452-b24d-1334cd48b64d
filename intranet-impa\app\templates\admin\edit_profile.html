<!-- /app/templates/admin/edit_profile.html -->
{% extends "base.html" %}
{% from "_formhelpers.html" import render_field %}
{% block title %}Editar Perfil{% endblock %}
{% include '_search_form.html' %}

{% block content %}
<h1>Editar Perfil: {{ user.first_name }} {{ user.last_name }}</h1>
<form method="POST" action="">
  {{ form.hidden_tag() }}

  {# --- CAMPOS DEL USER (EditUserForm) --- #}
  <fieldset>
      <legend>Información de Usuario</legend>
      <div class="form-group">
        {{ render_field(form.username, class="form-control") }}
      </div>
      <div class="form-group">
        {{ render_field(form.first_name, class="form-control", placeholder="Nombre") }}
      </div>
      <div class="form-group">
        {{ render_field(form.last_name, class="form-control", placeholder="Apellido") }}
      </div>
      <div class="form-group">
        {{ render_field(form.email, class="form-control", placeholder="Correo Electrónico") }}
      </div>
      <div class="form-group">
        {{ render_field(form.password, class="form-control", placeholder="Nueva Contraseña (dejar vacío para no cambiar)") }}
      </div>
      <div class="form-group">
        {{ render_field(form.confirm_password, class="form-control", placeholder="Confirmar Nueva Contraseña") }}
      </div>
      <div class="form-group">
        {{ render_field(form.role, class="form-control", id="role") }} {# Rol Principal #}
      </div>
      <div class="form-group">
        {{ render_field(form.church, class="form-control") }} {# Iglesia a la que PERTENECE #}
      </div>
      <div class="form-group">
        {{ render_field(form.phone_number, class="form-control", placeholder="Teléfono") }}
      </div>
      <div class="form-group">
        {{ render_field(form.date_of_birth, class="form-control") }}
      </div>
      <div class="form-group">
        {{ render_field(form.dni, class="form-control", placeholder="DNI") }} {# <-- ASEGURADO #}
      </div>
      <div class="form-group">
        {{ render_field(form.ciudad, class="form-control", placeholder="Ciudad Personal") }} {# <-- ASEGURADO #}
      </div>
      <div class="form-group">
        {{ render_field(form.estado_civil, class="form-control", placeholder="Estado Civil") }} {# <-- ASEGURADO #}
      </div>
       <div class="form-group">
        {{ render_field(form.address, class="form-control", placeholder="Dirección Personal") }} {# Dirección PERSONAL #}
      </div>
  </fieldset>
  {# --- FIN CAMPOS USER --- #}


  {# --- CAMPOS ESPECÍFICOS PASTOR (PastorForm) --- #}
  {% if user.role == 'pastorado' and pastor_form %}
    <hr>
    <fieldset>
        <legend>Información Adicional del Pastor (Tabla Pastores)</legend>
        {# Nota: pastor_form NO tiene CSRF token, no es necesario llamarlo de nuevo #}
        {# {{ pastor_form.hidden_tag() }} #}
        <div class="form-group">
          {{ render_field(pastor_form.grado, class="form-control") }}
        </div>
        <div class="form-group">
           {# Asumiendo que PastorForm tiene 'matricula' #}
           {% if pastor_form.matricula %}
             {{ render_field(pastor_form.matricula, class="form-control", placeholder="Matrícula") }}
           {% endif %}
        </div>
        {# Dirección específica de CASA PASTORAL (opcional si difiere de la personal) #}
        {% if pastor_form.address %}
        <div class="form-group">
          {{ render_field(pastor_form.address, class="form-control", placeholder="Dirección Casa Pastoral (opcional)") }}
        </div>
        {# Mapa para Casa Pastoral #}
        <div class="form-group" style="display: none;">
            {{ pastor_form.latitude.label }}
            {{ pastor_form.latitude(class="form-control", type="hidden", id="pastor_latitude") }} {# ID único #}
            {{ pastor_form.longitude.label }}
            {{ pastor_form.longitude(class="form-control", type="hidden", id="pastor_longitude") }} {# ID único #}
        </div>
        <button type="button" class="btn btn-info mb-3" id="toggle-pastor-map">Mostrar/Ocultar Mapa Casa Pastoral</button>
        <div id="pastor-map" style="height: 300px; display: none;"></div>
        {% endif %}

         {# Roles Adicionales (vienen de EditUserForm, no PastorForm) #}
        <div class="form-group">
            <label>Roles Adicionales</label>
            {% for subfield in form.pastor_roles %}
              <div class="form-check">
                {{ subfield(class="form-check-input") }}
                {{ subfield.label(class="form-check-label") }}
              </div>
            {% endfor %}
            {% if form.pastor_roles.errors %}
                <div class="invalid-feedback d-block">
                    {{ form.pastor_roles.errors|join(', ') }}
                </div>
            {% endif %}
        </div>
    </fieldset>
  {% endif %}
  {# --- FIN CAMPOS PASTOR --- #}


  {# --- CAMPOS ESPECÍFICOS MIEMBRO (EditMemberForm) --- #}
  {# Estos campos solo deberían mostrarse si la ruta es edit_member, no edit_profile #}
  {# Si necesitas editar todo junto, habría que refactorizar rutas/formularios #}
  {# COMENTADO TEMPORALMENTE para evitar confusión, ya que EditUserForm no los tiene #}
  {#
  {% elif user.role == 'miembro' and member_form %}
    <hr>
    <fieldset>
        <legend>Información Adicional del Miembro</legend>
        {# {{ member_form.hidden_tag() }} <- No necesario si el form principal ya lo tiene #}
        {# Los campos dni, ciudad, estado_civil, etc., ya están arriba en el form principal #}
        {# Campos específicos de la tabla 'members' #}
        {#
        <div class="form-group">
          {{ render_field(member_form.alergies, class="form-control", placeholder="Alergias") }}
        </div>
        <div class="form-group">
          {{ render_field(member_form.emergency_contact, class="form-control", placeholder="Contacto de Emergencia") }}
        </div>
        <div class="form-group">
          <label>Funciones Asignadas</label>
          {% for subfield in member_form.member_functions %}
            <div class="form-check">
              {{ subfield(class="form-check-input") }}
              {{ subfield.label(class="form-check-label") }}
            </div>
          {% endfor %}
           {% if member_form.member_functions.errors %}
                <div class="invalid-feedback d-block">
                    {{ member_form.member_functions.errors|join(', ') }}
                </div>
            {% endif %}
        </div>
        #}
    {# </fieldset> #}
  {# endif #}
  {# --- FIN CAMPOS MIEMBRO --- #}


  <button type="submit" class="btn btn-primary mt-3">{{ form.submit.label }}</button>
  <a href="{{ url_for('routes.user_detail', user_id=user.id) }}" class="btn btn-secondary mt-3">Cancelar</a> {# Botón Cancelar #}

</form>
{% endblock %}

{% block scripts %}
 {{ super() }} {# Heredar scripts de base.html (jQuery, Bootstrap, Leaflet base) #}

 {# Script para mapa de Casa Pastoral (solo si es pastor) #}
 {% if user.role == 'pastorado' and pastor_form and pastor_form.address %}
 <script>
    // Mapa específico para la casa pastoral
    var pastorMap = L.map('pastor-map');
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(pastorMap);

    var pastorDrawnItems = new L.FeatureGroup();
    pastorMap.addLayer(pastorDrawnItems);

    var pastorDrawControl = new L.Control.Draw({
        draw: { marker: true, polyline: false, polygon: false, rectangle: false, circle: false, circlemarker: false },
        edit: { featureGroup: pastorDrawnItems, remove: true } // Permitir editar/borrar marcador casa pastoral
    });
    pastorMap.addControl(pastorDrawControl);

    // Evento CREATED para mapa pastoral
    pastorMap.on(L.Draw.Event.CREATED, function (e) {
        var layer = e.layer;
        var lat = layer.getLatLng().lat;
        var lng = layer.getLatLng().lng;
        document.getElementById('pastor_latitude').value = lat.toFixed(6); // Usar ID único
        document.getElementById('pastor_longitude').value = lng.toFixed(6); // Usar ID único
        pastorDrawnItems.clearLayers();
        pastorDrawnItems.addLayer(layer);
    });

     // Evento EDITED para mapa pastoral
    pastorMap.on(L.Draw.Event.EDITED, function (e) {
         e.layers.eachLayer(function (layer) {
            if (layer instanceof L.Marker) {
                var lat = layer.getLatLng().lat;
                var lng = layer.getLatLng().lng;
                document.getElementById('pastor_latitude').value = lat.toFixed(6);
                document.getElementById('pastor_longitude').value = lng.toFixed(6);
            }
         });
    });

     // Evento DELETED para mapa pastoral
     pastorMap.on(L.Draw.Event.DELETED, function(e){
         document.getElementById('pastor_latitude').value = ''; // Limpiar campos si se borra marcador
         document.getElementById('pastor_longitude').value = '';
     });


    // Mostrar marcador existente casa pastoral
    var pastorInitialLat = {{ user.pastor.latitude or 'null' }};
    var pastorInitialLng = {{ user.pastor.longitude or 'null' }};
    var pastorExistingMarker = null;
    if(pastorInitialLat !== null && pastorInitialLng !== null) {
        pastorExistingMarker = L.marker([pastorInitialLat, pastorInitialLng], {draggable: true}).addTo(pastorMap);
        pastorDrawnItems.addLayer(pastorExistingMarker);
        pastorExistingMarker.on('dragend', function(event){
             var marker = event.target;
             var position = marker.getLatLng();
             document.getElementById('pastor_latitude').value = position.lat.toFixed(6);
             document.getElementById('pastor_longitude').value = position.lng.toFixed(6);
         });
    }

    // Botón para mapa pastoral
    document.getElementById('toggle-pastor-map').addEventListener('click', function() {
        var mapDiv = document.getElementById('pastor-map');
        if (mapDiv.style.display === 'none') {
            mapDiv.style.display = 'block';
            pastorMap.invalidateSize();
            if (pastorExistingMarker) {
                pastorMap.setView(pastorExistingMarker.getLatLng(), 15);
            } else {
                 // Centrar en dirección personal del usuario si existe, sino en Viedma
                 var userLat = {{ user.latitude or 'null' }}; // Asumiendo que user podría tener lat/lng personal
                 var userLng = {{ user.longitude or 'null' }};
                 if(userLat && userLng) pastorMap.setView([userLat, userLng], 13);
                 else pastorMap.setView([-40.8110, -62.9972], 13);
            }
        } else {
            mapDiv.style.display = 'none';
        }
    });

     // Buscar dirección de casa pastoral
    var pastorAddressInput = document.getElementById('address'); // Asumiendo ID 'address' para pastor_form.address
    if(pastorAddressInput){
        pastorAddressInput.addEventListener('blur', function(){
            var query = pastorAddressInput.value;
            if(query){
                 // Búsqueda similar a la de iglesia... (adaptar si es necesario)
            }
        });
    }

 </script>
 {% endif %}

 {# Script para mostrar/ocultar campos de pastor según rol principal #}
 <script>
    const roleSelect = document.getElementById('role');
    // Seleccionar el contenedor correcto basado en la estructura HTML
    const pastorFieldset = document.querySelector('fieldset legend:contains("Información Adicional del Pastor")')?.closest('fieldset');

    function togglePastorFields() {
      if (roleSelect.value === 'pastorado' && pastorFieldset) {
        pastorFieldset.style.display = 'block';
      } else if (pastorFieldset) {
        pastorFieldset.style.display = 'none';
      }
    }
    // Añadir un selector :contains (requiere jQuery o polyfill) o buscar por ID si le pones uno al fieldset
    // Solución sin jQuery:
    function findFieldsetByLegend(text) {
        const legends = document.querySelectorAll('fieldset legend');
        for (let legend of legends) {
            if (legend.textContent.includes(text)) {
                return legend.closest('fieldset');
            }
        }
        return null;
    }
    const pastorFieldsetJS = findFieldsetByLegend("Información Adicional del Pastor");

     function togglePastorFieldsJS() {
      if (roleSelect.value === 'pastorado' && pastorFieldsetJS) {
        pastorFieldsetJS.style.display = 'block';
      } else if (pastorFieldsetJS) {
        pastorFieldsetJS.style.display = 'none';
      }
    }

    if (roleSelect) {
        roleSelect.addEventListener('change', togglePastorFieldsJS);
        // Llamar al inicio para establecer el estado correcto
        togglePastorFieldsJS();
    }
 </script>
{% endblock %}