import os
import requests
import polyline
import logging
from typing import Dict, List, Any, Optional, Tuple
import math
from datetime import datetime

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Obtener API key desde variables de entorno o usar un valor predeterminado para desarrollo
OPENROUTE_API_KEY = os.getenv("OPENROUTE_API_KEY", "")
OPENROUTE_API_URL = "https://api.openrouteservice.org/v2/directions/driving-car"

def calculate_route(
    origin_lat: str, 
    origin_lng: str, 
    destination_lat: str, 
    destination_lng: str,
    waypoints: Optional[List[Tuple[str, str]]] = None
) -> Dict[str, Any]:
    """
    Calcula una ruta entre dos puntos utilizando OpenRouteService.
    
    Args:
        origin_lat: Latitud del origen
        origin_lng: Longitud del origen
        destination_lat: Latitud del destino
        destination_lng: Longitud del destino
        waypoints: Lista opcional de puntos intermedios (lat, lng)
        
    Returns:
        Dict con información de la ruta (distancia, duración, geometría)
    """
    try:
        # Convertir coordenadas a float
        origin_lat_float = float(origin_lat)
        origin_lng_float = float(origin_lng)
        destination_lat_float = float(destination_lat)
        destination_lng_float = float(destination_lng)
        
        # Preparar coordenadas para la API
        coordinates = [[origin_lng_float, origin_lat_float]]
        
        # Agregar waypoints si existen
        if waypoints:
            for wp_lat, wp_lng in waypoints:
                coordinates.append([float(wp_lng), float(wp_lat)])
        
        # Agregar destino
        coordinates.append([destination_lng_float, destination_lat_float])
        
        # Si no hay API key, usar una implementación simplificada
        if not OPENROUTE_API_KEY:
            logger.warning("No se encontró API key para OpenRouteService, usando cálculo simplificado")
            return calculate_route_simplified(
                origin_lat, origin_lng, destination_lat, destination_lng, waypoints
            )
        
        # Preparar headers y parámetros
        headers = {
            'Accept': 'application/json, application/geo+json, application/gpx+xml',
            'Authorization': OPENROUTE_API_KEY,
            'Content-Type': 'application/json'
        }
        
        params = {
            'coordinates': coordinates,
            'format': 'geojson',
            'instructions': True,
            'language': 'es',
            'units': 'km',
            'geometry': True
        }
        
        # Realizar la solicitud a la API
        response = requests.post(OPENROUTE_API_URL, json=params, headers=headers)
        
        if response.status_code != 200:
            logger.error(f"Error en la API de OpenRouteService: {response.status_code} - {response.text}")
            return calculate_route_simplified(
                origin_lat, origin_lng, destination_lat, destination_lng, waypoints
            )
        
        # Procesar la respuesta
        route_data = response.json()
        
        # Extraer información relevante
        route = route_data['features'][0]
        geometry = route['geometry']
        properties = route['properties']
        
        segments = properties['segments'][0]
        distance = segments['distance']  # en metros
        duration = segments['duration']  # en segundos
        
        # Decodificar la geometría si está en formato polyline
        decoded_geometry = None
        if 'coordinates' in geometry:
            decoded_geometry = geometry['coordinates']
        
        # Preparar respuesta
        result = {
            'distance_meters': distance,
            'duration_seconds': duration,
            'geometry': decoded_geometry,
            'steps': segments.get('steps', []),
            'source': 'openrouteservice'
        }
        
        return result
    
    except Exception as e:
        logger.error(f"Error al calcular ruta con OpenRouteService: {e}")
        # En caso de error, usar el cálculo simplificado
        return calculate_route_simplified(
            origin_lat, origin_lng, destination_lat, destination_lng, waypoints
        )

def calculate_route_simplified(
    origin_lat: str, 
    origin_lng: str, 
    destination_lat: str, 
    destination_lng: str,
    waypoints: Optional[List[Tuple[str, str]]] = None
) -> Dict[str, Any]:
    """
    Implementación simplificada para calcular una ruta cuando no hay API disponible.
    Usa la fórmula de Haversine para calcular distancias en línea recta.
    
    Args:
        origin_lat: Latitud del origen
        origin_lng: Longitud del origen
        destination_lat: Latitud del destino
        destination_lng: Longitud del destino
        waypoints: Lista opcional de puntos intermedios (lat, lng)
        
    Returns:
        Dict con información de la ruta (distancia, duración, geometría)
    """
    try:
        # Convertir coordenadas a float
        origin_lat_float = float(origin_lat)
        origin_lng_float = float(origin_lng)
        destination_lat_float = float(destination_lat)
        destination_lng_float = float(destination_lng)
        
        # Calcular distancia en línea recta
        from app.services.trip_service import calculate_distance
        
        # Inicializar distancia total
        total_distance = 0
        
        # Crear lista de puntos para la geometría
        geometry = [[origin_lng_float, origin_lat_float]]
        
        # Punto actual para calcular distancias
        current_lat, current_lng = origin_lat, origin_lng
        
        # Agregar waypoints si existen
        if waypoints:
            for wp_lat, wp_lng in waypoints:
                # Calcular distancia al waypoint
                segment_distance = calculate_distance(current_lat, current_lng, wp_lat, wp_lng)
                total_distance += segment_distance
                
                # Actualizar punto actual
                current_lat, current_lng = wp_lat, wp_lng
                
                # Agregar a la geometría
                geometry.append([float(wp_lng), float(wp_lat)])
        
        # Calcular distancia al destino
        segment_distance = calculate_distance(current_lat, current_lng, destination_lat, destination_lng)
        total_distance += segment_distance
        
        # Agregar destino a la geometría
        geometry.append([destination_lng_float, destination_lat_float])
        
        # Estimar duración (asumiendo velocidad promedio de 30 km/h = 8.33 m/s)
        average_speed_mps = 8.33
        estimated_duration = total_distance / average_speed_mps
        
        # Preparar respuesta
        result = {
            'distance_meters': total_distance,
            'duration_seconds': estimated_duration,
            'geometry': geometry,
            'steps': [],
            'source': 'simplified'
        }
        
        return result
    
    except Exception as e:
        logger.error(f"Error en cálculo simplificado de ruta: {e}")
        # En caso de error, devolver valores predeterminados
        return {
            'distance_meters': 5000,  # 5 km
            'duration_seconds': 600,  # 10 minutos
            'geometry': [
                [float(origin_lng), float(origin_lat)],
                [float(destination_lng), float(destination_lat)]
            ],
            'steps': [],
            'source': 'default'
        }
