/* Estilos para el dashboard de brigada y ciudadano */

/* Estilos del mapa */
#mapBrigada, #mapCiudadano {
  height: 60vh;
  width: 100%;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Estilos de las tarjetas */
.card-dashboard {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.card-dashboard:hover {
  transform: translateY(-5px);
}

.card-header-custom {
  border-radius: 10px 10px 0 0;
  padding: 15px;
  font-weight: bold;
}

/* Colores de estado */
.bg-nueva { background-color: #0dcaf0; color: white; }
.bg-proceso { background-color: #ffc107; color: black; }
.bg-derivada { background-color: #fd7e14; color: white; }
.bg-resuelta { background-color: #198754; color: white; }
.bg-cerrada { background-color: #6c757d; color: white; }

/* Estilos de las tarjetas de incidencia */
.incidencia-card {
  margin-bottom: 15px;
  border-left-width: 5px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  transition: transform 0.2s ease;
}

.incidencia-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.incidencia-nueva { border-left-color: #0dcaf0; /* Bootstrap info */ }
.incidencia-en-proceso { border-left-color: #ffc107; /* Bootstrap warning */ }
.incidencia-derivada { border-left-color: #fd7e14; /* Bootstrap orange */ }
.incidencia-resuelta { border-left-color: #198754; /* Bootstrap success */ }
.incidencia-cerrada { border-left-color: #6c757d; /* Bootstrap secondary */ }

/* Estilos del mapa */
.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255,255,255,0.8);
  padding: 10px;
  border-radius: 5px;
  z-index: 1000;
}

.map-error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  display: none;
}

.distance-badge {
  font-size: 1.2rem;
  margin-bottom: 15px;
  border-radius: 8px;
}

.status-controls {
  margin-top: 15px;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.route-line {
  stroke: #3388ff;
  stroke-width: 4;
  stroke-opacity: 0.7;
}

/* Estilos de los iconos */
.brigada-icon img {
  filter: hue-rotate(240deg) saturate(1.5); /* Azul */
}

.incidencia-icon img {
  filter: hue-rotate(0deg) saturate(1.5); /* Rojo */
}

.usuario-icon img {
  filter: hue-rotate(120deg) saturate(1.5); /* Verde */
}

/* Controles del mapa */
.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: white;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Leyenda del mapa */
.map-legend {
  position: absolute;
  bottom: 30px;
  right: 10px;
  z-index: 1000;
  background: white;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  font-size: 0.8rem;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 50%;
}

.legend-brigada { background-color: #3388ff; }
.legend-incidencia { background-color: #ff3333; }
.legend-usuario { background-color: #33cc33; }

/* Mejoras de accesibilidad */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Compatibilidad con navegadores */
.user-select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.text-size-adjust {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Mejoras para dispositivos móviles */
@media (max-width: 768px) {
  #mapBrigada, #mapCiudadano {
    height: 50vh;
  }
  
  .map-legend {
    bottom: 10px;
    right: 10px;
    font-size: 0.7rem;
    padding: 5px;
  }
  
  .legend-color {
    width: 15px;
    height: 15px;
  }
}
