-- <PERSON><PERSON>r la base de datos (si no existe)
CREATE DATABASE IF NOT EXISTS intranet CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE intranet;

-- 1. <PERSON><PERSON>r las tablas SIN claves foráneas

-- <PERSON>bla de iglesias (congregaciones)
CREATE TABLE IF NOT EXISTS churches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address VARCHAR(255),
    pastor_id INT,  -- Sin FOREIGN KEY por ahora
    district VARCHAR(255)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- <PERSON>bla de usuarios
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role ENUM('administrador', 'secretaria', 'pastorado', 'miembro') NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    phone_number VARCHAR(255),
    address VARCHAR(255),
    date_of_birth DATE,
    is_active BOOLEAN DEFAULT TRUE,
    church_id INT  -- Sin FOREIGN KEY por ahora
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de miembros
CREATE TABLE IF NOT EXISTS members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    alergies VARCHAR(255),
    emergency_contact VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE  -- Esta FK sí se puede crear
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Roles de pastor
CREATE TABLE IF NOT EXISTS pastor_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(255) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla intermedia user_pastor_roles
CREATE TABLE IF NOT EXISTS user_pastor_roles (
    user_id INT,
    pastor_role_id INT,
    PRIMARY KEY (user_id, pastor_role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pastor_role_id) REFERENCES pastor_roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de documentos
CREATE TABLE IF NOT EXISTS documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_path VARCHAR(255) NOT NULL,
    upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT,
    category ENUM('acta', 'informe_financiero', 'circular', 'informe_miembros', 'diploma') NOT NULL,
    topic VARCHAR(255),
    version INT DEFAULT 1,
    previous_version_id INT,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (previous_version_id) REFERENCES documents(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de mensajes
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT,
    receiver_id INT,
    message_text TEXT NOT NULL,
    sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de mapas
CREATE TABLE IF NOT EXISTS maps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    geodata JSON,
    description TEXT,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla jerarquia_pastores (sin FKs por ahora)
CREATE TABLE IF NOT EXISTS jerarquia_pastores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pastor_id INT,
    supervisor_id INT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla usuario_categoria
CREATE TABLE IF NOT EXISTS usuario_categoria (
    usuario_id INT,
    categoria_id INT,
    FOREIGN KEY (usuario_id) REFERENCES users(id),
    FOREIGN KEY (categoria_id) REFERENCES pastor_roles(id),
    PRIMARY KEY (usuario_id, categoria_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabla de categorias
CREATE TABLE IF NOT EXISTS categorias (
   id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- 2. Agregar las claves foráneas (ALTER TABLE)

ALTER TABLE churches
ADD FOREIGN KEY (pastor_id) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE users
ADD FOREIGN KEY (church_id) REFERENCES churches(id) ON DELETE SET NULL;

-- 3. Inserts (roles de pastor y categorías)

INSERT INTO pastor_roles (role_name) VALUES
('Pastor Gobernante'),
('Pastor de Jóvenes'),
('Pastor de Música'),
('Superintendente'),
('Jefe de Sector'),
('Director de Jóvenes'),
('Obispo Presidente'),
('Miembro del Presbiterio Mayor'),
('Miembro del Tribunal de Disciplina');

INSERT INTO categorias (nombre) VALUES ('Pastor'), ('Miembro'), ('Presbitero'), ('Diacono'),
('Superintendente'), ('Probando'), ('Plena Comunion'), ('Obispo Presidente'),
('Administracion'), ('Tribunal Eclesiastico'), ('Oficial'), ('Presbisterio Mayor');