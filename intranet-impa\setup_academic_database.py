# setup_academic_database.py
# Script para configurar la base de datos del sistema académico

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import *
from app.academic_services import AcademicService, PastoralAcademicService
from datetime import date, datetime

def setup_database():
    """Configurar base de datos para sistema académico"""
    app = create_app()
    
    with app.app_context():
        print("🎓 Configurando Sistema Académico...")
        
        try:
            # 1. Crear todas las tablas
            print("📊 Creando tablas...")
            db.create_all()
            print("✅ Tablas creadas correctamente")
            
            # 2. Verificar si ya existe instituto
            existing_institute = PastoralInstitute.query.first()
            if not existing_institute:
                # 3. Crear Instituto Pastoral Principal
                print("🏛️ Creando Instituto Pastoral...")
                institute = PastoralAcademicService.create_institute(
                    name="Instituto de Formación Pastoral IMPA",
                    description="Instituto corporativo para la formación continua de pastores y líderes de la Corporación IMPA"
                )
                print(f"✅ Instituto creado: {institute.name}")
            else:
                print("ℹ️ Instituto pastoral ya existe")
                institute = existing_institute
            
            # 4. Crear escuelas básicas para iglesias existentes
            print("🏫 Creando escuelas bíblicas...")
            churches = Church.query.all()
            schools_created = 0
            
            for church in churches:
                # Verificar si ya tiene escuela
                existing_school = AcademicSchool.query.filter_by(church_id=church.id).first()
                
                if not existing_school:
                    try:
                        school = AcademicService.create_school(
                            church_id=church.id,
                            name=f"Escuela Bíblica Básica - {church.name}",
                            description="Fundamentos de la fe cristiana para miembros",
                            start_date=date.today(),
                            max_students=50,
                            auto_enrollment=True
                        )
                        schools_created += 1
                        print(f"✅ Escuela creada para: {church.name}")
                    except Exception as e:
                        print(f"❌ Error creando escuela para {church.name}: {e}")
                else:
                    print(f"ℹ️ {church.name} ya tiene escuela bíblica")
            
            # 5. Mostrar estadísticas finales
            total_schools = AcademicSchool.query.count()
            total_institutes = PastoralInstitute.query.count()
            
            print(f"\n🎓 Sistema Académico configurado exitosamente:")
            print(f"   - {total_institutes} Instituto(s) Pastoral(es)")
            print(f"   - {total_schools} Escuela(s) Bíblica(s)")
            print(f"   - {schools_created} Escuela(s) nueva(s) creada(s)")
            print(f"   - Sistema listo para usar")
            
            return True
            
        except Exception as e:
            print(f"❌ Error durante la configuración: {e}")
            return False

def create_sample_data():
    """Crear datos de ejemplo para testing"""
    app = create_app()
    
    with app.app_context():
        print("\n📝 Creando datos de ejemplo...")
        
        try:
            # Obtener primera escuela
            school = AcademicSchool.query.first()
            if not school:
                print("❌ No hay escuelas disponibles")
                return False
            
            # Crear pensum de ejemplo
            existing_curriculum = AcademicCurriculum.query.filter_by(school_id=school.id).first()
            if not existing_curriculum:
                curriculum = AcademicService.create_curriculum(
                    school_id=school.id,
                    name="Fundamentos de Fe Cristiana",
                    description="Curso básico de 6 meses para nuevos creyentes",
                    duration_months=6,
                    subjects_data=[
                        {
                            'name': 'Fundamentos de la Fe',
                            'code': 'FUN-001',
                            'description': 'Doctrinas básicas del cristianismo',
                            'credits': 3,
                            'level': 1,
                            'is_mandatory': True
                        },
                        {
                            'name': 'Historia Bíblica',
                            'code': 'HIS-001',
                            'description': 'Panorama del Antiguo y Nuevo Testamento',
                            'credits': 2,
                            'level': 1,
                            'is_mandatory': True
                        },
                        {
                            'name': 'Vida Cristiana Práctica',
                            'code': 'VCP-001',
                            'description': 'Aplicación práctica de la fe',
                            'credits': 2,
                            'level': 2,
                            'is_mandatory': True
                        }
                    ]
                )
                print(f"✅ Pensum creado: {curriculum.name}")
            else:
                print("ℹ️ Ya existe pensum para esta escuela")
            
            # Crear programa pastoral de ejemplo
            institute = PastoralInstitute.query.first()
            if institute:
                existing_program = PastoralProgram.query.filter_by(institute_id=institute.id).first()
                if not existing_program:
                    # Obtener un usuario administrador para created_by
                    admin_user = User.query.filter_by(role='administrador').first()
                    if admin_user:
                        program = PastoralAcademicService.create_pastoral_program(
                            institute_id=institute.id,
                            name="Diploma en Ministerio Pastoral",
                            program_type="diploma",
                            duration_months=18,
                            target_roles=['pastorado', 'superintendente'],
                            min_ministry_years=2,
                            created_by_id=admin_user.id,
                            total_credits=45,
                            max_students=20
                        )
                        print(f"✅ Programa pastoral creado: {program.name}")
                    else:
                        print("⚠️ No se encontró usuario administrador para crear programa")
                else:
                    print("ℹ️ Ya existe programa pastoral")
            
            print("✅ Datos de ejemplo creados")
            return True
            
        except Exception as e:
            print(f"❌ Error creando datos de ejemplo: {e}")
            return False

def show_system_status():
    """Mostrar estado actual del sistema académico"""
    app = create_app()
    
    with app.app_context():
        print("\n📊 ESTADO DEL SISTEMA ACADÉMICO")
        print("=" * 50)
        
        # Estadísticas básicas
        total_schools = AcademicSchool.query.count()
        total_institutes = PastoralInstitute.query.count()
        total_programs = PastoralProgram.query.count()
        total_enrollments = AcademicEnrollment.query.count()
        total_pastoral_enrollments = PastoralEnrollment.query.count()
        
        print(f"🏫 Escuelas Bíblicas: {total_schools}")
        print(f"🏛️ Institutos Pastorales: {total_institutes}")
        print(f"📚 Programas Pastorales: {total_programs}")
        print(f"👥 Matrículas Básicas: {total_enrollments}")
        print(f"🎓 Matrículas Pastorales: {total_pastoral_enrollments}")
        
        # Detalles por iglesia
        print(f"\n📋 ESCUELAS POR IGLESIA:")
        churches = Church.query.all()
        for church in churches:
            school_count = AcademicSchool.query.filter_by(church_id=church.id).count()
            print(f"   - {church.name}: {school_count} escuela(s)")
        
        # Usuarios por rol
        print(f"\n👤 USUARIOS POR ROL:")
        roles = ['administrador', 'secretaria', 'pastorado', 'miembro', 'instituto', 'rector', 'profesor_corporativo']
        for role in roles:
            count = User.query.filter_by(role=role).count()
            print(f"   - {role}: {count} usuario(s)")

if __name__ == '__main__':
    print("🚀 CONFIGURACIÓN DEL SISTEMA ACADÉMICO")
    print("=" * 50)
    
    # Ejecutar configuración
    success = setup_database()
    
    if success:
        # Crear datos de ejemplo
        create_sample_data()
        
        # Mostrar estado final
        show_system_status()
        
        print(f"\n🎉 ¡Sistema Académico configurado exitosamente!")
        print(f"📝 Próximos pasos:")
        print(f"   1. Asignar rol 'instituto' a un usuario")
        print(f"   2. Configurar programas pastorales")
        print(f"   3. Comenzar matrículas")
    else:
        print(f"\n❌ Error en la configuración. Revisa los logs arriba.")
