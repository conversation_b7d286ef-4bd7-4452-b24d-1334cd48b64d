#!/usr/bin/env python3
# --- Archivo: verify_final.py ---
# Script para verificación final del sistema

import os
import sys
import requests
import time

def test_database():
    """Probar que la base de datos funcione."""
    print("🗃️  Probando base de datos...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            # Probar consulta básica
            users = User.query.all()
            print(f"✅ Base de datos funciona: {len(users)} usuarios")
            
            for user in users:
                print(f"  👤 {user.username} ({user.role}) - Activo: {user.is_active}")
                
                # Probar métodos de permisos
                print(f"    🔐 Es admin: {user.is_admin()}")
                print(f"    🔐 Puede gestionar usuarios: {user.can_manage_users()}")
                print(f"    🔐 Puede ver puntos: {user.can_view_points()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error en base de datos: {e}")
        return False

def test_web_app():
    """Probar que la aplicación web funcione."""
    print("\n🌐 Probando aplicación web...")
    
    base_url = "https://patagoniaservers.com.ar:5006"
    
    # Esperar un poco para que la app inicie
    print("⏳ Esperando que la aplicación inicie...")
    time.sleep(3)
    
    try:
        # Probar página principal
        response = requests.get(f"{base_url}/", verify=False, timeout=10)
        if response.status_code == 200:
            print("✅ Página principal responde")
        else:
            print(f"❌ Página principal error: {response.status_code}")
            return False
        
        # Probar página de login
        response = requests.get(f"{base_url}/auth/login", verify=False, timeout=10)
        if response.status_code == 200:
            print("✅ Página de login responde")
        else:
            print(f"❌ Página de login error: {response.status_code}")
            return False
        
        # Probar login
        session = requests.Session()
        login_data = {
            'username': 'admin',
            'password': 'isaias52',
            'submit': 'Iniciar Sesión'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data, verify=False, allow_redirects=False)
        
        if response.status_code in [302, 303]:
            print("✅ Login funciona correctamente")
            
            # Probar acceso a página protegida
            protected_response = session.get(f"{base_url}/points/list", verify=False)
            if protected_response.status_code == 200:
                print("✅ Acceso a páginas protegidas funciona")
            else:
                print(f"⚠️  Páginas protegidas responden con: {protected_response.status_code}")
            
            return True
        else:
            print(f"❌ Login falló: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error conectando a la aplicación: {e}")
        return False

def check_app_running():
    """Verificar si la aplicación está corriendo."""
    try:
        response = requests.get("https://patagoniaservers.com.ar:5006/", verify=False, timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """Función principal."""
    print("✅ Verificación Final del Sistema")
    print("=" * 40)
    
    # Probar base de datos
    if not test_database():
        print("\n❌ La base de datos no funciona correctamente")
        sys.exit(1)
    
    # Verificar si la aplicación está corriendo
    if not check_app_running():
        print("\n⚠️  La aplicación no está corriendo")
        print("💡 Iniciando aplicación...")
        
        import subprocess
        try:
            # Detener procesos existentes
            subprocess.run(['pkill', '-f', 'python.*run.py'], capture_output=True)
            time.sleep(2)
            
            # Iniciar aplicación en background
            subprocess.Popen(['python', 'run.py'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
            
            print("⏳ Esperando que la aplicación inicie...")
            time.sleep(5)
            
        except Exception as e:
            print(f"❌ Error iniciando aplicación: {e}")
    
    # Probar aplicación web
    if test_web_app():
        print("\n🎉 ¡Sistema funcionando perfectamente!")
        print("\n🚀 Información de acceso:")
        print("   URL: https://patagoniaservers.com.ar:5006/")
        print("   Usuario: admin")
        print("   Contraseña: isaias52")
        print("   Rol: administrador")
        print("\n📋 Próximos pasos:")
        print("   1. Crear templates para gestión de usuarios")
        print("   2. Implementar filtros en vistas existentes")
        print("   3. Agregar enlaces en navegación")
    else:
        print("\n❌ La aplicación web tiene problemas")
        print("\n💡 Verifica manualmente:")
        print("   ps aux | grep python")
        print("   tail -f logs/app.log")

if __name__ == "__main__":
    main()
