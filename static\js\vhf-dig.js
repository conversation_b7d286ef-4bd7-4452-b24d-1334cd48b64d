// /static/js/vhf-dig.js

document.addEventListener('DOMContentLoaded', function () {
    // const nodeId = window.nodeId; // Ya están globales
    // const username = window.username; // Ya están globales
    // const nodeName = window.nodeName; // Ya está global

    console.log(`(index.js) Conectando como ${window.username} al nodo ${window.nodeId} en el servidor: ${window.socketIoServerUrl}`);

    // Usa la URL del servidor Socket.IO de la VM y el namespace /node
    const socket = io.connect(window.socketIoServerUrl + '/node', { // <--- MODIFICADO
        transports: ['websocket'], // Forzar websocket es una buena práctica
        query: {
            node_id: String(window.nodeId),
            username: window.username
        }
    });

    let mediaRecorder;
    let audioChunks = [];
    let isRecording = false;
    let isTransmittingAudio = false;

    const volumeMeter = document.getElementById('volume_meter');
    const volumeThreshold = 5;

    function updateVolumeMeter(volume) {
        if (volumeMeter) {
            volumeMeter.style.width = `${volume}%`;
            volumeMeter.style.backgroundColor = volume >= 50 ? 'red' : 'green';
        }
    }

    async function captureAudioAutomatically() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);

            mediaRecorder.ondataavailable = (event) => {
                audioChunks.push(event.data);
            };

            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                audioChunks = [];
                const reader = new FileReader();
                reader.onload = function () {
                    const base64AudioMessage = reader.result.split(',')[1];
                    console.log(`Enviando audio desde ${username} al nodo ${nodeId}`);

                    socket.emit('transmit_audio', {
                        audio: base64AudioMessage,
                        user: username,
                        node_id: String(nodeId)  // Forzamos a cadena
                    });
                };
                reader.readAsDataURL(audioBlob);
            };

            const audioContext = new AudioContext();
            const source = audioContext.createMediaStreamSource(stream);
            const analyser = audioContext.createAnalyser();
            source.connect(analyser);

            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            const bufferLength = analyser.frequencyBinCount;

            const checkVolume = () => {
                analyser.getByteFrequencyData(dataArray);
                let sum = 0;
                for (let i = 0; i < bufferLength; i++) {
                    sum += dataArray[i];
                }
                const averageVolume = sum / bufferLength;
                updateVolumeMeter(averageVolume);

                if (!isTransmittingAudio) {
                    if (averageVolume > volumeThreshold && mediaRecorder.state === 'inactive') {
                        console.log("Volumen alto detectado, iniciando grabación...");
                        mediaRecorder.start();
                        isRecording = true;
                    } else if (averageVolume <= volumeThreshold && mediaRecorder.state === 'recording') {
                        console.log("Volumen bajo detectado, deteniendo grabación...");
                        mediaRecorder.stop();
                        isRecording = false;
                    }
                } else {
                    console.log("Ignorando la grabación ya que el usuario está transmitiendo audio.");
                }
            };

            setInterval(checkVolume, 100);
        } catch (error) {
            console.error('Error al acceder al micrófono:', error);
        }
    }

    captureAudioAutomatically();

    socket.on('audio_start', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha comenzado a transmitir audio. Evitando grabación...`);
            isTransmittingAudio = true;
        }
    });

    socket.on('audio_end', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha terminado la transmisión de audio.`);
            isTransmittingAudio = false;
        }
    });

    socket.on('transmit_audio', (data) => {
        console.log('Recibiendo audio', data);
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);
            return;
        }
        console.log('Reproduciendo audio de otro usuario...');
        // Aquí iría la lógica para reproducir el audio recibido
    });
});
