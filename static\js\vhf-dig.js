// /static/js/vhf-dig.js

document.addEventListener('DOMContentLoaded', function () {
    const nodeId = window.nodeId;
    const username = window.username;

    console.log(`Conectado como ${username} al nodo ${nodeId}`);  // Log inicial para verificar el usuario y nodo

    const socket = io.connect(`https://patagoniaservers.com.ar:5000/node`, {
        transports: ['websocket'],
        query: {
            node_id: String(nodeId),  // Forzar a cadena
            username: username
        }
    });

    let mediaRecorder;
    let audioChunks = [];
    let isRecording = false;
    let isTransmittingAudio = false;

    const volumeMeter = document.getElementById('volume_meter');
    const volumeThreshold = 5;

    function updateVolumeMeter(volume) {
        if (volumeMeter) {
            volumeMeter.style.width = `${volume}%`;
            volumeMeter.style.backgroundColor = volume >= 50 ? 'red' : 'green';
        }
    }

    async function captureAudioAutomatically() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);

            mediaRecorder.ondataavailable = (event) => {
                audioChunks.push(event.data);
            };

            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                audioChunks = [];
                const reader = new FileReader();
                reader.onload = function () {
                    const base64AudioMessage = reader.result.split(',')[1];
                    console.log(`Enviando audio desde ${username} al nodo ${nodeId}`);

                    socket.emit('transmit_audio', {
                        audio: base64AudioMessage,
                        user: username,
                        node_id: String(nodeId)  // Forzamos a cadena
                    });
                };
                reader.readAsDataURL(audioBlob);
            };

            const audioContext = new AudioContext();
            const source = audioContext.createMediaStreamSource(stream);
            const analyser = audioContext.createAnalyser();
            source.connect(analyser);

            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            const bufferLength = analyser.frequencyBinCount;

            const checkVolume = () => {
                analyser.getByteFrequencyData(dataArray);
                let sum = 0;
                for (let i = 0; i < bufferLength; i++) {
                    sum += dataArray[i];
                }
                const averageVolume = sum / bufferLength;
                updateVolumeMeter(averageVolume);

                if (!isTransmittingAudio) {
                    if (averageVolume > volumeThreshold && mediaRecorder.state === 'inactive') {
                        console.log("Volumen alto detectado, iniciando grabación...");
                        mediaRecorder.start();
                        isRecording = true;
                    } else if (averageVolume <= volumeThreshold && mediaRecorder.state === 'recording') {
                        console.log("Volumen bajo detectado, deteniendo grabación...");
                        mediaRecorder.stop();
                        isRecording = false;
                    }
                } else {
                    console.log("Ignorando la grabación ya que el usuario está transmitiendo audio.");
                }
            };

            setInterval(checkVolume, 100);
        } catch (error) {
            console.error('Error al acceder al micrófono:', error);
        }
    }

    captureAudioAutomatically();

    socket.on('audio_start', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha comenzado a transmitir audio. Evitando grabación...`);
            isTransmittingAudio = true;
        }
    });

    socket.on('audio_end', (data) => {
        if (data.user === username) {
            console.log(`El usuario ${username} ha terminado la transmisión de audio.`);
            isTransmittingAudio = false;
        }
    });

    socket.on('transmit_audio', (data) => {
        console.log('Recibiendo audio', data);
        if (data.user === username) {
            console.log(`Ignorando el audio propio de ${data.user}`);
            return;
        }
        if (!data.audio || data.audio.trim() === "") {
            console.error("Error: Audio vacío recibido.");
            return;
        }

        console.log('Activando PTT y reproduciendo audio de otro usuario...');

        // Activar PTT
        fetch('/ptt_event', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ptt_state: true })
        })
        .then(response => {
            if (!response.ok) { throw new Error('Error en la solicitud de activación del PTT'); }
            return response.json();
        })
        .then(responseData => {
            console.log('PTT activado:', responseData);

            // Reproducir el audio después de activar el PTT
            try {
                const audioBlob = new Blob(
                    [new Uint8Array(atob(data.audio).split("").map(char => char.charCodeAt(0)))],
                    { type: 'audio/wav' }
                );
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);

                // Reproducir el audio
                audio.play().then(() => {
                    console.log('Reproduciendo audio...');
                }).catch((error) => {
                    console.error('Error al reproducir el audio:', error);
                });

                // Desactivar PTT cuando termine el audio
                audio.addEventListener('ended', () => {
                    console.log('Audio finalizado. Desactivando PTT...');
                    fetch('/ptt_event', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ ptt_state: false })
                    })
                    .then(response => {
                        if (!response.ok) { throw new Error('Error en la solicitud de desactivación del PTT'); }
                        return response.json();
                    })
                    .then(data => { console.log('PTT desactivado:', data); })
                    .catch((error) => { console.error('Error al desactivar el PTT:', error); });
                });
            } catch (error) {
                console.error('Error al decodificar o reproducir el audio:', error);
                // Desactivar PTT en caso de error
                fetch('/ptt_event', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ptt_state: false })
                }).catch(err => console.error('Error al desactivar PTT después de error:', err));
            }
        })
        .catch((error) => {
            console.error('Error al activar el PTT:', error);
        });
    });
});
