{% extends "base_layout.html" %}

{% block title %}Gestión de Viajes{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0 text-primary">Gestión de Viajes</h1>
        </div>
        <div class="card-body">
            <!-- Estadísticas -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h5 class="card-title">Total Viajes</h5>
                            <h2>{{ total_trips }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5 class="card-title">Viajes Activos</h5>
                            <h2>{{ active_trips }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h5 class="card-title">Viajes Completados</h5>
                            <h2>{{ completed_trips }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h5 class="card-title">Viajes Cancelados</h5>
                            <h2>{{ cancelled_trips }}</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtros y búsqueda -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Buscar por ID, pasajero o conductor..." id="searchInput">
                        <button class="btn btn-primary" type="button" id="searchButton">
                            <i class="bi bi-search"></i> Buscar
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="filter-buttons d-flex flex-wrap justify-content-end">
                        <button class="btn btn-outline-primary filter-btn active" data-filter="all">Todos</button>
                        <button class="btn btn-outline-info filter-btn" data-filter="solicitado">Solicitados</button>
                        <button class="btn btn-outline-primary filter-btn" data-filter="aceptado">Aceptados</button>
                        <button class="btn btn-outline-success filter-btn" data-filter="en_viaje">En Viaje</button>
                        <button class="btn btn-outline-warning filter-btn" data-filter="completado">Completados</button>
                        <button class="btn btn-outline-danger filter-btn" data-filter="cancelado">Cancelados</button>
                    </div>
                </div>
            </div>

            <!-- Tabla de viajes -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="tripsTable">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Pasajero</th>
                            <th>Conductor</th>
                            <th>Origen</th>
                            <th>Destino</th>
                            <th>Estado</th>
                            <th>Fecha</th>
                            <th>Tarifa</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trip in trips %}
                        <tr data-status="{{ trip.status }}">
                            <td>{{ trip.id }}</td>
                            <td>{{ trip.passenger_name }}</td>
                            <td>{{ trip.driver_name|default('Sin asignar') }}</td>
                            <td>{{ trip.origin_address|default(trip.origin_latitude + ', ' + trip.origin_longitude) }}</td>
                            <td>{{ trip.destination_address|default(trip.destination_latitude + ', ' + trip.destination_longitude) }}</td>
                            <td>
                                {% if trip.status == 'solicitado' %}
                                <span class="badge bg-info">Solicitado</span>
                                {% elif trip.status == 'aceptado' %}
                                <span class="badge bg-primary">Aceptado</span>
                                {% elif trip.status == 'en_camino_pasajero' %}
                                <span class="badge bg-primary">En camino al pasajero</span>
                                {% elif trip.status == 'en_destino_pasajero' %}
                                <span class="badge bg-primary">En punto de recogida</span>
                                {% elif trip.status == 'en_viaje' %}
                                <span class="badge bg-success">En viaje</span>
                                {% elif trip.status == 'completado' %}
                                <span class="badge bg-warning text-dark">Completado</span>
                                {% elif trip.status == 'cancelado_pasajero' %}
                                <span class="badge bg-danger">Cancelado por pasajero</span>
                                {% elif trip.status == 'cancelado_conductor' %}
                                <span class="badge bg-danger">Cancelado por conductor</span>
                                {% elif trip.status == 'no_disponible' %}
                                <span class="badge bg-secondary">No disponible</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ trip.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ trip.requested_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>
                                {% if trip.actual_fare %}
                                ${{ trip.actual_fare }}
                                {% elif trip.estimated_fare %}
                                ${{ trip.estimated_fare }} (est.)
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-info view-trip" data-id="{{ trip.id }}">
                                    <i class="bi bi-eye"></i>
                                </button>
                                {% if trip.status == 'solicitado' %}
                                <button class="btn btn-sm btn-success assign-driver" data-id="{{ trip.id }}">
                                    <i class="bi bi-person-check"></i>
                                </button>
                                {% endif %}
                                {% if trip.status not in ['completado', 'cancelado_pasajero', 'cancelado_conductor', 'no_disponible'] %}
                                <button class="btn btn-sm btn-warning update-status" data-id="{{ trip.id }}">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal de detalles del viaje -->
<div class="modal fade" id="tripDetailsModal" tabindex="-1" aria-labelledby="tripDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tripDetailsModalLabel">Detalles del Viaje</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Información General</h6>
                        <p><strong>ID:</strong> <span id="trip_id"></span></p>
                        <p><strong>Estado:</strong> <span id="trip_status"></span></p>
                        <p><strong>Fecha de solicitud:</strong> <span id="trip_requested_at"></span></p>
                        <p><strong>Fecha de aceptación:</strong> <span id="trip_accepted_at"></span></p>
                        <p><strong>Fecha de inicio:</strong> <span id="trip_started_at"></span></p>
                        <p><strong>Fecha de finalización:</strong> <span id="trip_completed_at"></span></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Participantes</h6>
                        <p><strong>Pasajero:</strong> <span id="trip_passenger"></span></p>
                        <p><strong>Conductor:</strong> <span id="trip_driver"></span></p>
                        <p><strong>Vehículo:</strong> <span id="trip_vehicle"></span></p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Ubicaciones</h6>
                        <p><strong>Origen:</strong> <span id="trip_origin"></span></p>
                        <p><strong>Destino:</strong> <span id="trip_destination"></span></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Información de Pago</h6>
                        <p><strong>Tarifa estimada:</strong> $<span id="trip_estimated_fare"></span></p>
                        <p><strong>Tarifa final:</strong> $<span id="trip_actual_fare"></span></p>
                        <p><strong>Distancia estimada:</strong> <span id="trip_estimated_distance"></span> km</p>
                        <p><strong>Distancia real:</strong> <span id="trip_actual_distance"></span> km</p>
                        <p><strong>Duración estimada:</strong> <span id="trip_estimated_duration"></span> min</p>
                        <p><strong>Duración real:</strong> <span id="trip_actual_duration"></span> min</p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Calificaciones</h6>
                        <p><strong>Calificación del pasajero:</strong> <span id="trip_passenger_rating"></span>/5</p>
                        <p><strong>Comentario del pasajero:</strong> <span id="trip_passenger_comment"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Calificación del conductor:</strong> <span id="trip_driver_rating"></span>/5</p>
                        <p><strong>Comentario del conductor:</strong> <span id="trip_driver_comment"></span></p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div id="trip_map" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de asignación de conductor -->
<div class="modal fade" id="assignDriverModal" tabindex="-1" aria-labelledby="assignDriverModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignDriverModalLabel">Asignar Conductor</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="assignDriverForm">
                    <input type="hidden" id="assign_trip_id">
                    <div class="mb-3">
                        <label for="driver_id" class="form-label">Conductor</label>
                        <select class="form-select" id="driver_id" required>
                            <option value="">Seleccionar conductor...</option>
                            {% for driver in drivers %}
                            <option value="{{ driver.id }}">{{ driver.full_name }} ({{ driver.vehicle_info|default('Sin vehículo') }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="assignDriverBtn">Asignar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de actualización de estado -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">Actualizar Estado del Viaje</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    <input type="hidden" id="update_trip_id">
                    <div class="mb-3">
                        <label for="new_status" class="form-label">Nuevo Estado</label>
                        <select class="form-select" id="new_status" required>
                            <option value="">Seleccionar estado...</option>
                            <option value="aceptado">Aceptado</option>
                            <option value="en_camino_pasajero">En camino al pasajero</option>
                            <option value="en_destino_pasajero">En punto de recogida</option>
                            <option value="en_viaje">En viaje</option>
                            <option value="completado">Completado</option>
                            <option value="cancelado_pasajero">Cancelado por pasajero</option>
                            <option value="cancelado_conductor">Cancelado por conductor</option>
                        </select>
                    </div>
                    <div class="mb-3" id="fareInputGroup" style="display: none;">
                        <label for="actual_fare" class="form-label">Tarifa Final ($)</label>
                        <input type="number" class="form-control" id="actual_fare" step="0.01" min="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="updateStatusBtn">Actualizar</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts_extra %}
<script>
    // Filtrar viajes por estado
    document.querySelectorAll('.filter-btn').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            const rows = document.querySelectorAll('#tripsTable tbody tr');

            // Resaltar botón activo
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');

            // Filtrar filas
            rows.forEach(row => {
                if (filter === 'all') {
                    row.style.display = '';
                } else if (filter === 'cancelado') {
                    // Mostrar ambos tipos de cancelación
                    const status = row.getAttribute('data-status');
                    if (status === 'cancelado_pasajero' || status === 'cancelado_conductor') {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                } else {
                    if (row.getAttribute('data-status') === filter) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        });
    });

    // Búsqueda
    document.getElementById('searchButton').addEventListener('click', function() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const rows = document.querySelectorAll('#tripsTable tbody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // Ver detalles del viaje
    document.querySelectorAll('.view-trip').forEach(button => {
        button.addEventListener('click', function() {
            const tripId = this.getAttribute('data-id');

            // En una implementación real, aquí se haría una llamada a la API
            // para obtener los detalles del viaje
            fetch(`/api/v1/trips/${tripId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Error al obtener detalles del viaje');
                    }
                    return response.json();
                })
                .then(trip => {
                    // Llenar el modal con los datos del viaje
                    document.getElementById('trip_id').textContent = trip.id;
                    document.getElementById('trip_status').textContent = getStatusText(trip.status);
                    document.getElementById('trip_requested_at').textContent = formatDate(trip.requested_at);
                    document.getElementById('trip_accepted_at').textContent = formatDate(trip.accepted_at);
                    document.getElementById('trip_started_at').textContent = formatDate(trip.started_at);
                    document.getElementById('trip_completed_at').textContent = formatDate(trip.completed_at);

                    // Mostrar el modal
                    const modal = new bootstrap.Modal(document.getElementById('tripDetailsModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error al obtener detalles del viaje');
                });
        });
    });

    // Asignar conductor
    document.querySelectorAll('.assign-driver').forEach(button => {
        button.addEventListener('click', function() {
            const tripId = this.getAttribute('data-id');
            document.getElementById('assign_trip_id').value = tripId;

            const modal = new bootstrap.Modal(document.getElementById('assignDriverModal'));
            modal.show();
        });
    });

    // Actualizar estado
    document.querySelectorAll('.update-status').forEach(button => {
        button.addEventListener('click', function() {
            const tripId = this.getAttribute('data-id');
            document.getElementById('update_trip_id').value = tripId;

            const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
            modal.show();
        });
    });

    // Mostrar/ocultar campo de tarifa final cuando se selecciona "Completado"
    document.getElementById('new_status').addEventListener('change', function() {
        const fareInputGroup = document.getElementById('fareInputGroup');
        if (this.value === 'completado') {
            fareInputGroup.style.display = 'block';
        } else {
            fareInputGroup.style.display = 'none';
        }
    });

    // Enviar formulario de asignación de conductor
    document.getElementById('assignDriverBtn').addEventListener('click', function() {
        const tripId = document.getElementById('assign_trip_id').value;
        const driverId = document.getElementById('driver_id').value;

        if (!driverId) {
            alert('Por favor, selecciona un conductor');
            return;
        }

        // En una implementación real, aquí se haría una llamada a la API
        // para asignar el conductor al viaje
        fetch(`/api/v1/trips/${tripId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'aceptado',
                driver_id: parseInt(driverId)
            }),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al asignar conductor');
            }
            return response.json();
        })
        .then(data => {
            // Cerrar el modal y recargar la página
            bootstrap.Modal.getInstance(document.getElementById('assignDriverModal')).hide();
            window.location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al asignar conductor');
        });
    });

    // Enviar formulario de actualización de estado
    document.getElementById('updateStatusBtn').addEventListener('click', function() {
        const tripId = document.getElementById('update_trip_id').value;
        const newStatus = document.getElementById('new_status').value;

        if (!newStatus) {
            alert('Por favor, selecciona un estado');
            return;
        }

        const updateData = {
            status: newStatus
        };

        // Si el estado es "Completado", incluir la tarifa final
        if (newStatus === 'completado') {
            const actualFare = document.getElementById('actual_fare').value;
            if (actualFare) {
                updateData.actual_fare = parseFloat(actualFare);
            }
        }

        // En una implementación real, aquí se haría una llamada a la API
        // para actualizar el estado del viaje
        fetch(`/api/v1/trips/${tripId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al actualizar estado');
            }
            return response.json();
        })
        .then(data => {
            // Cerrar el modal y recargar la página
            bootstrap.Modal.getInstance(document.getElementById('updateStatusModal')).hide();
            window.location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al actualizar estado');
        });
    });

    // Función para formatear fechas
    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    // Función para obtener texto de estado
    function getStatusText(status) {
        switch(status) {
            case 'solicitado': return 'Solicitado';
            case 'aceptado': return 'Aceptado';
            case 'en_camino_pasajero': return 'En camino al pasajero';
            case 'en_destino_pasajero': return 'En punto de recogida';
            case 'en_viaje': return 'En viaje';
            case 'completado': return 'Completado';
            case 'cancelado_pasajero': return 'Cancelado por pasajero';
            case 'cancelado_conductor': return 'Cancelado por conductor';
            case 'no_disponible': return 'No disponible';
            default: return status;
        }
    }
</script>
{% endblock %}
