#!/bin/bash
# Script para ejecutar el servidor de desarrollo FastAPI

# Verifica si el entorno virtual esta activado, si no, intenta activarlo
if [ -z "$VIRTUAL_ENV" ]; then
    if [ -d "venv" ]; then
        echo "Activando entorno virtual..."
        source venv/bin/activate
    else
        echo "Entorno virtual 'venv' no encontrado. Por favor, crealo e instale las dependencias."
        exit 1
    fi
fi

echo "Iniciando servidor de desarrollo Uvicorn en puerto 5008..."
# Ejecuta uvicorn. --reload es para desarrollo.
# --host 0.0.0.0 permite acceso desde otras maquinas en la red.
uvicorn app.main:app --reload --host 0.0.0.0 --port 5008
