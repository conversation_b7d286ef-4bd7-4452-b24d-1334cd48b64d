# performance_improvements.py
# Mejoras de rendimiento para Intranet IMPA

from flask_caching import Cache
from sqlalchemy import Index, text
from functools import wraps
import time
import redis
from flask import current_app, request, g

# 1. CONFIGURACIÓN DE CACHE
class CacheConfig:
    """Configuración de cache"""
    CACHE_TYPE = "redis"
    CACHE_REDIS_URL = "redis://localhost:6379/0"
    CACHE_DEFAULT_TIMEOUT = 300  # 5 minutos
    
    # Configuraciones específicas por tipo de dato
    CACHE_TIMEOUTS = {
        'churches': 600,      # 10 minutos
        'users': 300,         # 5 minutos
        'statistics': 1800,   # 30 minutos
        'reports': 3600,      # 1 hora
    }

# 2. INICIALIZACIÓN DE CACHE
def setup_cache(app):
    """Configurar sistema de cache"""
    cache = Cache()
    cache.init_app(app, config=CacheConfig.__dict__)
    return cache

# 3. DECORADORES DE CACHE
def cached_query(timeout=300, key_prefix='query'):
    """Decorador para cachear consultas de base de datos"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from app import cache
            
            # Generar clave única basada en función y argumentos
            cache_key = f"{key_prefix}:{f.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Intentar obtener del cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Si no está en cache, ejecutar función y guardar resultado
            result = f(*args, **kwargs)
            cache.set(cache_key, result, timeout=timeout)
            return result
        
        return decorated_function
    return decorator

# 4. SERVICIOS OPTIMIZADOS
class OptimizedChurchService:
    """Servicio optimizado para iglesias"""
    
    @staticmethod
    @cached_query(timeout=600, key_prefix='churches')
    def get_all_churches():
        """Obtener todas las iglesias (cacheado)"""
        from app.models import Church
        return Church.query.all()
    
    @staticmethod
    @cached_query(timeout=300, key_prefix='church_stats')
    def get_church_statistics():
        """Obtener estadísticas de iglesias"""
        from app.models import Church, Member
        from app import db
        
        stats = db.session.execute(text("""
            SELECT 
                COUNT(DISTINCT c.id) as total_churches,
                COUNT(DISTINCT m.id) as total_members,
                AVG(member_count.count) as avg_members_per_church
            FROM churches c
            LEFT JOIN (
                SELECT church_id, COUNT(*) as count 
                FROM members 
                GROUP BY church_id
            ) member_count ON c.id = member_count.church_id
            LEFT JOIN members m ON c.id = m.church_id
        """)).fetchone()
        
        return {
            'total_churches': stats[0] or 0,
            'total_members': stats[1] or 0,
            'avg_members_per_church': round(stats[2] or 0, 2)
        }
    
    @staticmethod
    def invalidate_church_cache():
        """Invalidar cache de iglesias"""
        from app import cache
        cache.delete_memoized(OptimizedChurchService.get_all_churches)
        cache.delete_memoized(OptimizedChurchService.get_church_statistics)

# 5. PAGINACIÓN OPTIMIZADA
class OptimizedPagination:
    """Clase para paginación optimizada"""
    
    @staticmethod
    def paginate_query(query, page, per_page=20, error_out=False):
        """Paginación optimizada con conteo eficiente"""
        # Para consultas grandes, usar LIMIT/OFFSET sin COUNT
        if per_page > 100:
            per_page = 100  # Límite máximo
        
        # Obtener elementos de la página actual
        items = query.offset((page - 1) * per_page).limit(per_page + 1).all()
        
        # Verificar si hay página siguiente
        has_next = len(items) > per_page
        if has_next:
            items = items[:-1]  # Remover el elemento extra
        
        has_prev = page > 1
        
        return {
            'items': items,
            'has_next': has_next,
            'has_prev': has_prev,
            'page': page,
            'per_page': per_page,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None
        }

# 6. ÍNDICES DE BASE DE DATOS
def create_database_indexes():
    """Crear índices para mejorar rendimiento"""
    from app import db
    
    indexes = [
        # Índices para tabla users
        Index('idx_users_role', 'users.role'),
        Index('idx_users_church_id', 'users.church_id'),
        Index('idx_users_email', 'users.email'),
        Index('idx_users_active', 'users.is_active'),
        
        # Índices para tabla churches
        Index('idx_churches_pastor_id', 'churches.pastor_id'),
        Index('idx_churches_district', 'churches.district'),
        
        # Índices para tabla members
        Index('idx_members_church_id', 'members.church_id'),
        Index('idx_members_user_id', 'members.user_id'),
        
        # Índices para tabla documents
        Index('idx_documents_category', 'documents.category'),
        Index('idx_documents_upload_date', 'documents.upload_date'),
        
        # Índices para tabla transactions
        Index('idx_transactions_account_id', 'transactions.account_id'),
        Index('idx_transactions_date', 'transactions.transaction_date'),
        Index('idx_transactions_type', 'transactions.transaction_type'),
        
        # Índices compuestos
        Index('idx_users_role_church', 'users.role', 'users.church_id'),
        Index('idx_members_church_active', 'members.church_id', 'members.is_active'),
    ]
    
    # Crear índices si no existen
    for index in indexes:
        try:
            index.create(db.engine, checkfirst=True)
            print(f"Índice creado: {index.name}")
        except Exception as e:
            print(f"Error creando índice {index.name}: {e}")

# 7. OPTIMIZACIÓN DE CONSULTAS
class QueryOptimizer:
    """Optimizador de consultas"""
    
    @staticmethod
    def get_churches_with_member_count():
        """Obtener iglesias con conteo de miembros optimizado"""
        from app.models import Church, Member
        from app import db
        
        return db.session.query(
            Church,
            db.func.count(Member.id).label('member_count')
        ).outerjoin(Member).group_by(Church.id).all()
    
    @staticmethod
    def get_user_with_relationships(user_id):
        """Obtener usuario con relaciones optimizado"""
        from app.models import User
        from sqlalchemy.orm import joinedload
        
        return User.query.options(
            joinedload(User.church),
            joinedload(User.pastor),
            joinedload(User.member)
        ).filter_by(id=user_id).first()
    
    @staticmethod
    def get_church_dashboard_data(church_id):
        """Obtener datos del dashboard de iglesia optimizado"""
        from app.models import Church, Member, Transaction, CalendarEvent
        from app import db
        
        # Una sola consulta para obtener todos los datos necesarios
        result = db.session.execute(text("""
            SELECT 
                c.name as church_name,
                COUNT(DISTINCT m.id) as total_members,
                COUNT(DISTINCT CASE WHEN m.is_active = 1 THEN m.id END) as active_members,
                COUNT(DISTINCT t.id) as total_transactions,
                COALESCE(SUM(CASE WHEN t.transaction_type = 'ingreso' THEN t.amount ELSE 0 END), 0) as total_income,
                COALESCE(SUM(CASE WHEN t.transaction_type = 'egreso' THEN t.amount ELSE 0 END), 0) as total_expenses,
                COUNT(DISTINCT ce.id) as upcoming_events
            FROM churches c
            LEFT JOIN members m ON c.id = m.church_id
            LEFT JOIN accounts a ON c.id = a.church_id
            LEFT JOIN transactions t ON a.id = t.account_id
            LEFT JOIN calendar_events ce ON ce.event_date >= CURDATE() AND ce.user_id IN (
                SELECT u.id FROM users u WHERE u.church_id = c.id
            )
            WHERE c.id = :church_id
            GROUP BY c.id, c.name
        """), {'church_id': church_id}).fetchone()
        
        if result:
            return {
                'church_name': result[0],
                'total_members': result[1] or 0,
                'active_members': result[2] or 0,
                'total_transactions': result[3] or 0,
                'total_income': float(result[4] or 0),
                'total_expenses': float(result[5] or 0),
                'balance': float(result[4] or 0) - float(result[5] or 0),
                'upcoming_events': result[6] or 0
            }
        return None

# 8. MIDDLEWARE DE PERFORMANCE
def setup_performance_monitoring(app):
    """Configurar monitoreo de rendimiento"""
    
    @app.before_request
    def before_request():
        g.start_time = time.time()
    
    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            
            # Log requests lentos
            if duration > 2.0:  # Más de 2 segundos
                current_app.logger.warning(
                    f"Slow request: {request.method} {request.path} "
                    f"took {duration:.2f}s from {request.remote_addr}"
                )
            
            # Añadir header con tiempo de respuesta
            response.headers['X-Response-Time'] = f"{duration:.3f}s"
        
        return response

# 9. CONFIGURACIÓN DE CONNECTION POOL
def setup_database_pool(app):
    """Configurar pool de conexiones de base de datos"""
    
    # Configuración optimizada para MySQL
    app.config.update({
        'SQLALCHEMY_ENGINE_OPTIONS': {
            'pool_size': 10,
            'pool_recycle': 3600,  # 1 hora
            'pool_pre_ping': True,
            'max_overflow': 20,
            'pool_timeout': 30,
        }
    })

# 10. FUNCIÓN DE INICIALIZACIÓN
def initialize_performance_improvements(app):
    """Inicializar todas las mejoras de rendimiento"""
    
    # Configurar cache
    cache = setup_cache(app)
    app.cache = cache
    
    # Configurar pool de conexiones
    setup_database_pool(app)
    
    # Configurar monitoreo
    setup_performance_monitoring(app)
    
    # Crear índices (ejecutar una vez)
    with app.app_context():
        try:
            create_database_indexes()
        except Exception as e:
            app.logger.error(f"Error creating indexes: {e}")
    
    return cache

# 11. EJEMPLO DE USO
"""
# En routes.py, usar así:

from mejoras.performance_improvements import (
    OptimizedChurchService, 
    OptimizedPagination,
    QueryOptimizer
)

@routes_bp.route('/churches')
@login_required
def list_churches():
    page = request.args.get('page', 1, type=int)
    
    # Usar servicio optimizado
    if page == 1:
        # Primera página desde cache
        churches = OptimizedChurchService.get_all_churches()
        pagination_data = OptimizedPagination.paginate_query(
            Church.query, page, per_page=20
        )
    else:
        # Páginas siguientes con paginación optimizada
        pagination_data = OptimizedPagination.paginate_query(
            Church.query, page, per_page=20
        )
    
    return render_template('churches/list.html', 
                         churches=pagination_data['items'],
                         pagination=pagination_data)
"""
