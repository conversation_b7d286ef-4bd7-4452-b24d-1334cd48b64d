#!/usr/bin/env python3
# --- Archivo: find_original_data.py ---
# Script para buscar los datos originales de la aplicación

import os
import sqlite3
import subprocess
import glob

def search_database_files():
    """Buscar archivos de base de datos en el sistema."""
    print("🔍 Buscando archivos de base de datos...")
    
    # Buscar archivos .db
    db_files = []
    
    # Buscar en directorios comunes
    search_paths = [
        '/home',
        '/var',
        '/opt',
        '/tmp'
    ]
    
    for path in search_paths:
        try:
            result = subprocess.run(['find', path, '-name', '*.db', '-type', 'f'], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                files = result.stdout.strip().split('\n')
                db_files.extend([f for f in files if f and 'app.db' in f])
        except:
            continue
    
    print(f"📁 Archivos .db encontrados: {len(db_files)}")
    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f"  📄 {db_file} ({size:,} bytes)")
    
    return db_files

def analyze_database_content(db_file):
    """Analizar el contenido de una base de datos."""
    print(f"\n🔍 Analizando: {db_file}")
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Obtener tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas: {tables}")
        
        # Verificar datos importantes
        data_found = False
        
        for table in tables:
            if table == 'sqlite_sequence':
                continue
                
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f"  📊 {table}: {count} registros")
                    data_found = True
                    
                    # Mostrar muestras de datos importantes
                    if table == 'point' and count > 0:
                        cursor.execute("SELECT name, city, source FROM point LIMIT 5;")
                        points = cursor.fetchall()
                        print(f"    📍 Puntos ejemplo: {points}")
                    
                    elif table == 'image' and count > 0:
                        cursor.execute("SELECT filename, point_id FROM image LIMIT 5;")
                        images = cursor.fetchall()
                        print(f"    🖼️  Imágenes ejemplo: {images}")
                    
                    elif table == 'camera' and count > 0:
                        cursor.execute("SELECT type, point_id FROM camera LIMIT 5;")
                        cameras = cursor.fetchall()
                        print(f"    📷 Cámaras ejemplo: {cameras}")
                    
                    elif table == 'user' and count > 0:
                        cursor.execute("SELECT username FROM user;")
                        users = cursor.fetchall()
                        print(f"    👥 Usuarios: {[u[0] for u in users]}")
                
            except Exception as e:
                print(f"    ❌ Error leyendo {table}: {e}")
        
        conn.close()
        
        return data_found
        
    except Exception as e:
        print(f"❌ Error analizando {db_file}: {e}")
        return False

def search_in_other_directories():
    """Buscar en otros directorios de aplicaciones."""
    print("\n🔍 Buscando en otros directorios...")
    
    # Buscar directorios que puedan contener aplicaciones
    potential_dirs = []
    
    try:
        # Buscar en /home
        for item in os.listdir('/home'):
            item_path = f'/home/<USER>'
            if os.path.isdir(item_path):
                potential_dirs.append(item_path)
    except:
        pass
    
    print(f"📁 Directorios encontrados: {len(potential_dirs)}")
    
    for directory in potential_dirs:
        print(f"\n📂 Explorando: {directory}")
        
        # Buscar archivos .db en el directorio
        try:
            db_files = glob.glob(f"{directory}/**/app.db", recursive=True)
            db_files.extend(glob.glob(f"{directory}/**/*.db", recursive=True))
            
            for db_file in db_files:
                if os.path.exists(db_file):
                    size = os.path.getsize(db_file)
                    if size > 1000:  # Solo archivos con contenido
                        print(f"  📄 {db_file} ({size:,} bytes)")
                        
                        # Analizar si tiene datos relevantes
                        has_data = analyze_database_content(db_file)
                        if has_data:
                            print(f"  ✅ Contiene datos relevantes")
                        
        except Exception as e:
            print(f"  ❌ Error explorando {directory}: {e}")

def check_running_applications():
    """Verificar aplicaciones en ejecución que puedan tener datos."""
    print("\n🔍 Verificando aplicaciones en ejecución...")
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        python_processes = [line for line in lines if 'python' in line and 'run.py' in line]
        
        print(f"🐍 Procesos Python encontrados: {len(python_processes)}")
        
        for process in python_processes:
            print(f"  🔄 {process}")
            
            # Extraer directorio de trabajo
            parts = process.split()
            for i, part in enumerate(parts):
                if 'run.py' in part:
                    work_dir = os.path.dirname(part)
                    if work_dir and work_dir != '.':
                        print(f"    📂 Directorio: {work_dir}")
                        
                        # Buscar base de datos en ese directorio
                        db_path = os.path.join(work_dir, 'app.db')
                        instance_db_path = os.path.join(work_dir, 'instance', 'app.db')
                        
                        for db_file in [db_path, instance_db_path]:
                            if os.path.exists(db_file):
                                size = os.path.getsize(db_file)
                                print(f"    📄 {db_file} ({size:,} bytes)")
                                
                                if size > 10000:  # Archivos con contenido significativo
                                    analyze_database_content(db_file)
        
    except Exception as e:
        print(f"❌ Error verificando procesos: {e}")

def suggest_recovery_options():
    """Sugerir opciones de recuperación."""
    print("\n💡 Opciones de recuperación:")
    print("1. 📥 Importar datos desde archivos DBF/CSV si los tienes")
    print("2. 🔄 Restaurar desde backup externo si existe")
    print("3. 🆕 Empezar con datos de prueba para testing")
    print("4. 📞 Contactar al administrador anterior para backup")
    print("5. 🔍 Buscar en otros servidores o ubicaciones")

def main():
    """Función principal."""
    print("🔍 Buscador de Datos Originales")
    print("=" * 40)
    
    # Buscar archivos de base de datos
    db_files = search_database_files()
    
    # Analizar archivos encontrados
    if db_files:
        print("\n📊 Analizando archivos encontrados...")
        for db_file in db_files:
            if os.path.exists(db_file):
                analyze_database_content(db_file)
    
    # Buscar en otros directorios
    search_in_other_directories()
    
    # Verificar aplicaciones en ejecución
    check_running_applications()
    
    # Sugerir opciones
    suggest_recovery_options()
    
    print("\n🎯 Resumen:")
    print("Si no se encontraron datos originales, podemos:")
    print("1. Crear datos de prueba para testing")
    print("2. Configurar importación desde archivos externos")
    print("3. Empezar con la aplicación limpia")

if __name__ == "__main__":
    main()
