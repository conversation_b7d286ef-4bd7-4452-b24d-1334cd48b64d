# File: backend/apps/incidents/models.py
# -----------------------------------------------


from django.contrib.gis.db import models as gis_models
from django.db import models
from django.conf import settings
# from apps.locations.models import Ciudad # Descomentar

class Incidencia(models.Model):
    class EstadoChoices(models.TextChoices):
        NUEVA = "NUEVA", "Nueva"
        ASIGNADA_OPERADOR = "ASIGNADA_OPERADOR", "Asignada a Operador"
        EN_PROCESO_OPERADOR = "EN_PROCESO_OPERADOR", "En Proceso por Operador"
        DERIVADA_BRIGADA = "DERIVADA_BRIGADA", "Derivada a Brigada"
        EN_PROCESO_BRIGADA = "EN_PROCESO_BRIGADA", "En Proceso por Brigada"
        CERRADA_RESUELTA = "CERRADA_RESUELTA", "<PERSON><PERSON><PERSON> - <PERSON>suelta"
        CERRADA_NO_RESUELTA = "CERRADA_NO_RESUELTA", "Cerrada - No Resuelta"
        CANCELADA = "CANCELADA", "Cancelada por Usuario"

    usuario_reporta = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="incidencias_reportadas",
        limit_choices_to={'role': 'CIUDADANO'}
    )
    descripcion_texto = models.TextField(blank=True, null=True)
    audio_url = models.URLField(max_length=500, blank=True, null=True)
    foto_url = models.URLField(max_length=500, blank=True, null=True)
    ubicacion_incidencia = gis_models.PointField(srid=4326)
    # ciudad_reportada = models.ForeignKey('locations.Ciudad', on_delete=models.SET_NULL, null=True, blank=True)
    estado = models.CharField(max_length=30, choices=EstadoChoices.choices, default=EstadoChoices.NUEVA)
    fecha_creacion = models.DateTimeField(auto_now_add=True)
    fecha_actualizacion = models.DateTimeField(auto_now=True)
    operador_asignado = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name="incidencias_asignadas_operador",
        limit_choices_to={'role': 'OPERADOR'}
    )
    brigada_asignada = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, null=True, blank=True,
        related_name="incidencias_asignadas_brigada",
        limit_choices_to={'role': 'BRIGADA'}
    )
    objects = gis_models.Manager()

    def __str__(self):
        return f"Incidencia #{self.id} por {self.usuario_reporta.username} - {self.estado}"

    class Meta:
        ordering = ['-fecha_creacion']
        indexes = [
            gis_models.Index(fields=['ubicacion_incidencia']),
        ]
