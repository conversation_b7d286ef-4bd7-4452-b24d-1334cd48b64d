# File: README.md
# -----------------------------------------------


# RN-Rural Project

Proyecto para la aplicación RN-Rural.

## Estructura

- `backend/`: Contiene el backend Django.
- `mobile_app/`: (Placeholder) Contendrá el código de la aplicación móvil.
- `web_panel/`: (Placeholder) Contendrá el código del panel web para operadores.

## Próximos Pasos (Backend)

1.  Navega a la carpeta `backend`:
    `cd rn_rural/backend`
2.  Crea y activa un entorno virtual:
    `python -m venv venv`
    `source venv/bin/activate` (Linux/macOS) o `venv\Scripts\activate` (Windows)
3.  Instala las dependencias:
    `pip install -r requirements.txt`
4.  Copia `.env.example` a `.env` y configura tus variables de entorno (especialmente `DATABASE_URL` y `SECRET_KEY`).
    `cp .env.example .env`
5.  Asegúrate de que tu base de datos PostgreSQL con PostGIS esté en funcionamiento y accesible.
6.  Ejecuta las migraciones:
    `python manage.py makemigrations`
    `python manage.py migrate`
7.  Crea un superusuario:
    `python manage.py createsuperuser`
8.  Inicia el servidor de desarrollo:
    `python manage.py runserver`

Y para los WebSockets (con Daphne):
    `daphne -p 8001 rn_rural_project.asgi:application`
    (Asegúrate de que Daphne esté instalado: `pip install daphne`)

