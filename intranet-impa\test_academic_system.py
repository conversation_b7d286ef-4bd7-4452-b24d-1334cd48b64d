# test_academic_system.py
# Script para probar el sistema académico sin configurar BD completa

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from flask import url_for

def test_academic_routes():
    """Probar que las rutas académicas están configuradas correctamente"""
    app = create_app()
    
    with app.app_context():
        print("🧪 TESTING SISTEMA ACADÉMICO")
        print("=" * 50)
        
        # Lista de rutas académicas a probar
        academic_routes = [
            'routes.academic_dashboard',
            'routes.academic_schools', 
            'routes.create_academic_school',
            'routes.pastoral_programs',
            'routes.create_pastoral_program',
            'routes.academic_reports'
        ]
        
        print("📋 Verificando rutas académicas...")
        
        for route_name in academic_routes:
            try:
                url = url_for(route_name)
                print(f"✅ {route_name}: {url}")
            except Exception as e:
                print(f"❌ {route_name}: Error - {e}")
        
        print(f"\n🔍 Verificando importaciones...")
        
        # Verificar importaciones de modelos
        try:
            from app.models import (AcademicSchool, AcademicCurriculum, AcademicSubject, 
                                   AcademicEnrollment, AcademicGrade, PastoralInstitute, 
                                   PastoralProgram, PastoralEnrollment, PastoralGrade)
            print("✅ Modelos académicos importados correctamente")
        except ImportError as e:
            print(f"❌ Error importando modelos: {e}")
        
        # Verificar importaciones de servicios
        try:
            from app.academic_services import AcademicService, PastoralAcademicService
            print("✅ Servicios académicos importados correctamente")
        except ImportError as e:
            print(f"❌ Error importando servicios: {e}")
        
        # Verificar importaciones de formularios
        try:
            from app.academic_forms import (AcademicSchoolForm, PastoralProgramForm, 
                                          AcademicGradeForm, PastoralGradeForm)
            print("✅ Formularios académicos importados correctamente")
        except ImportError as e:
            print(f"❌ Error importando formularios: {e}")
        
        print(f"\n📁 Verificando templates...")
        
        # Verificar que existen los templates
        template_files = [
            'academic/dashboard.html',
            'academic/schools.html', 
            'academic/create_school.html'
        ]
        
        for template in template_files:
            template_path = os.path.join(app.template_folder, template)
            if os.path.exists(template_path):
                print(f"✅ Template encontrado: {template}")
            else:
                print(f"❌ Template faltante: {template}")
        
        print(f"\n🎯 RESUMEN:")
        print(f"   - Rutas académicas configuradas")
        print(f"   - Modelos, servicios y formularios listos")
        print(f"   - Templates básicos creados")
        print(f"   - Menú de navegación actualizado")
        
        print(f"\n📝 PRÓXIMOS PASOS:")
        print(f"   1. Ejecutar setup_academic_database.py para crear tablas")
        print(f"   2. Asignar rol 'instituto' a un usuario")
        print(f"   3. Probar la aplicación en el navegador")
        print(f"   4. Crear escuelas y programas de prueba")

def check_database_status():
    """Verificar estado de la base de datos"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🗄️ VERIFICANDO BASE DE DATOS...")
        
        try:
            from app import db
            from app.models import User, Church
            
            # Verificar conexión básica
            user_count = User.query.count()
            church_count = Church.query.count()
            
            print(f"✅ Conexión a BD exitosa")
            print(f"   - Usuarios: {user_count}")
            print(f"   - Iglesias: {church_count}")
            
            # Verificar si existen tablas académicas
            try:
                from app.models import AcademicSchool, PastoralInstitute
                
                # Intentar hacer query simple
                academic_schools = AcademicSchool.query.count()
                pastoral_institutes = PastoralInstitute.query.count()
                
                print(f"✅ Tablas académicas existentes")
                print(f"   - Escuelas académicas: {academic_schools}")
                print(f"   - Institutos pastorales: {pastoral_institutes}")
                
                return True
                
            except Exception as e:
                print(f"⚠️ Tablas académicas no encontradas: {e}")
                print(f"   Ejecuta setup_academic_database.py para crearlas")
                return False
                
        except Exception as e:
            print(f"❌ Error de conexión a BD: {e}")
            return False

def show_user_roles():
    """Mostrar usuarios y sus roles"""
    app = create_app()
    
    with app.app_context():
        print(f"\n👥 USUARIOS Y ROLES ACTUALES:")
        
        try:
            from app.models import User
            
            users = User.query.all()
            role_counts = {}
            
            for user in users:
                role = user.role
                if role not in role_counts:
                    role_counts[role] = 0
                role_counts[role] += 1
                
                # Mostrar primeros 5 usuarios de cada rol
                if role_counts[role] <= 3:
                    print(f"   - {user.first_name} {user.last_name} ({user.email}) - {role}")
            
            print(f"\n📊 RESUMEN POR ROLES:")
            for role, count in role_counts.items():
                print(f"   - {role}: {count} usuario(s)")
                
            # Verificar si hay usuarios con roles académicos
            academic_roles = ['instituto', 'rector', 'profesor_corporativo']
            academic_users = [u for u in users if u.role in academic_roles]
            
            if academic_users:
                print(f"\n🎓 USUARIOS CON ROLES ACADÉMICOS:")
                for user in academic_users:
                    print(f"   - {user.first_name} {user.last_name} - {user.role}")
            else:
                print(f"\n⚠️ NO HAY USUARIOS CON ROLES ACADÉMICOS")
                print(f"   Asigna rol 'instituto' a un usuario para gestión académica")
                
        except Exception as e:
            print(f"❌ Error obteniendo usuarios: {e}")

if __name__ == '__main__':
    print("🚀 VERIFICACIÓN DEL SISTEMA ACADÉMICO")
    print("=" * 60)
    
    # 1. Probar rutas y configuración
    test_academic_routes()
    
    # 2. Verificar estado de BD
    db_ready = check_database_status()
    
    # 3. Mostrar usuarios y roles
    show_user_roles()
    
    print(f"\n🎉 VERIFICACIÓN COMPLETADA")
    
    if db_ready:
        print(f"✅ Sistema académico listo para usar")
        print(f"🌐 Accede a: http://localhost:5000/academic")
    else:
        print(f"⚠️ Ejecuta setup_academic_database.py primero")
        print(f"📝 Luego asigna roles académicos a usuarios")
