<!-- /app/templates/admin/list_pastores.html -->
{% extends "base.html" %}
{# Importa la nueva macro #}
{% from '_sort_helpers.html' import render_sortable_header %}

{% block title %}Listado de Pastores{% endblock %}

{% block content %}
  <h1>Listado de Pastor<PERSON></h1>

  {# Incluir búsqueda (no necesita cambios) #}
  {% include '_search_form.html' %}

  <table class="table table-striped">
    <thead>
      <tr>
        {# Usa la macro para los encabezados ordenables #}
        {# Parámetros: endpoint, key_columna, texto_mostrar, sort_by_actual, sort_dir_actual, search_term_actual #}
        {{ render_sortable_header('routes.list_pastores', 'first_name', 'Nombre', sort_by, sort_dir, search_term) }}
        {{ render_sortable_header('routes.list_pastores', 'last_name', 'Apellido', sort_by, sort_dir, search_term) }}
        {{ render_sortable_header('routes.list_pastores', 'grado', 'Grado', sort_by, sort_dir, search_term) }}
        {{ render_sortable_header('routes.list_pastores', 'matricula', 'Matrícula', sort_by, sort_dir, search_term) }}
        {# Encabezados no ordenables (puedes hacerlos ordenables si unes la tabla Church) #}
        <th>Iglesia</th>
        <th>Teléfono</th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% for pastor_user in pastores %} {# Cambiado a pastor_user para claridad #}
      <tr>
        <td>{{ pastor_user.first_name }}</td>
        <td>{{ pastor_user.last_name }}</td>
        <td>
          {# Acceder a pastor.grado directamente ya que hicimos join/outerjoin #}
          {% if pastor_user.pastor and pastor_user.pastor.grado %}
            {{ pastor_user.pastor.grado|title }}
          {% else %}
            -
          {% endif %}
        </td>
        <td>
          {% if pastor_user.pastor and pastor_user.pastor.matricula %}
            {{ pastor_user.pastor.matricula }}
          {% else %}
            -
          {% endif %}
        </td>
        <td>{{ pastor_user.church.name if pastor_user.church else '-' }}</td>
        <td>{{ pastor_user.phone_number if pastor_user.phone_number else '-' }}</td>
        <td>
          <a href="{{ url_for('routes.edit_profile', user_id=pastor_user.id) }}" class="btn btn-primary btn-sm">Editar</a>
          <a href="{{ url_for('routes.user_detail', user_id=pastor_user.id) }}" class="btn btn-info btn-sm">Ver Detalles</a>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>

   {# Incluir paginación si la tienes (la macro de ordenación ya pasa 'page') #}
   {# {% include '_pagination.html' %} #}

{% endblock %}