#!/usr/bin/env python3
"""
Script para corregir los valores del enum roleenum en la base de datos.
"""

import psycopg2
from app.core.config import settings

def fix_enum_values():
    try:
        # Conectar a la base de datos
        conn = psycopg2.connect(settings.DATABASE_URL)
        cursor = conn.cursor()
        
        # 1. Verificar los valores actuales del enum
        cursor.execute("""
        SELECT typname, enumlabel
        FROM pg_enum e
        JOIN pg_type t ON e.enumtypid = t.oid
        WHERE typname = 'roleenum'
        ORDER BY enumsortorder;
        """)
        
        enum_values = cursor.fetchall()
        print("Valores actuales del enum roleenum:")
        for row in enum_values:
            print(f"  - '{row[1]}'")
        
        # 2. Crear un nuevo tipo de enum con los valores en MAYÚSCULAS
        print("\nCreando nuevo tipo de enum con valores en MAYÚSCULAS...")
        
        # Primero, crear un tipo temporal
        cursor.execute("CREATE TYPE roleenum_new AS ENUM ('USUARIO', 'TAXI', 'OPERADOR', 'TITULAR', 'BASE', 'ADMINISTRADOR');")
        
        # 3. Actualizar la tabla roles para usar valores en MAYÚSCULAS
        print("\nActualizando valores en la tabla roles...")
        cursor.execute("SELECT id, name FROM roles;")
        roles = cursor.fetchall()
        
        for role_id, role_name in roles:
            new_name = role_name.upper()
            print(f"  - Actualizando rol {role_id}: '{role_name}' -> '{new_name}'")
            
            # Actualizar directamente usando SQL para evitar problemas con el enum
            cursor.execute("""
            UPDATE roles SET name = %s::text WHERE id = %s;
            """, (new_name, role_id))
        
        # 4. Modificar la columna para usar el nuevo tipo
        print("\nModificando la estructura de la tabla roles...")
        
        # Eliminar la restricción de enum en la columna name
        cursor.execute("""
        ALTER TABLE roles 
        ALTER COLUMN name TYPE text;
        """)
        
        # Eliminar el tipo enum antiguo
        cursor.execute("DROP TYPE roleenum;")
        
        # Renombrar el nuevo tipo
        cursor.execute("ALTER TYPE roleenum_new RENAME TO roleenum;")
        
        # Convertir la columna de nuevo a enum
        cursor.execute("""
        ALTER TABLE roles 
        ALTER COLUMN name TYPE roleenum USING name::roleenum;
        """)
        
        # 5. Verificar los cambios
        cursor.execute("""
        SELECT typname, enumlabel
        FROM pg_enum e
        JOIN pg_type t ON e.enumtypid = t.oid
        WHERE typname = 'roleenum'
        ORDER BY enumsortorder;
        """)
        
        new_enum_values = cursor.fetchall()
        print("\nNuevos valores del enum roleenum:")
        for row in new_enum_values:
            print(f"  - '{row[1]}'")
        
        cursor.execute("SELECT id, name FROM roles;")
        new_roles = cursor.fetchall()
        
        print("\nValores actualizados en la tabla roles:")
        for role_id, role_name in new_roles:
            print(f"  - ID: {role_id}, Nombre: '{role_name}'")
        
        # Confirmar los cambios
        conn.commit()
        print("\n¡Cambios aplicados correctamente!")
        
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Corrigiendo valores del enum roleenum en la base de datos...")
    fix_enum_values()
    print("Proceso completado.")
