<!-- /app/templates/list_documents.html -->
{% extends "base.html" %}
{% block title %}Lista de Documentos{% endblock %}

{% block content %}
<div class="container my-4">
  <h1>Documentos</h1>
  <a href="{{ url_for('routes.upload_document') }}" class="btn btn-outline-primary btn-block mb-3">Cargar Documento</a>
  <table class="table table-striped">
    {% include '_search_form.html' %}
    {% include '_pagination.html' %}  <!-- ¡Asegúrate de tener paginación! -->
    <thead>
      <tr>
        <th>Título</th>
        <th>Categoría</th>
        <th>Tópico</th>
        <th><PERSON><PERSON> Carga</th>
        <th>Status</th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% for doc in documents %}
      <tr>
        <td>{{ doc.title }}</td>
        <td>{{ doc.category }}</td>
        <td>{{ doc.topic or '-' }}</td>
        <td>{{ doc.upload_date|to_local }}</td>
        <td>{{ doc.status }}</td>
        <td>
          <a href="{{ url_for('routes.download_document', doc_id=doc.id) }}" class="btn btn-sm btn-info">Descargar</a>
          {% if current_user.role in ['administrador', 'secretaria'] and doc.status != 'aprobado' %}
            <form action="{{ url_for('routes.approve_document', doc_id=doc.id) }}" method="POST" style="display:inline;">
              <button type="submit" class="btn btn-sm btn-success">Aprobar</button>
            </form>
          {% endif %}
            <!-- Botón para eliminar el documento. -->
            {% if current_user.role in ['administrador', 'secretaria'] %}
            <form action="{{ url_for('routes.delete_document', doc_id=doc.id) }}" method="POST" style="display:inline;">
                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('¿Estás seguro de que quieres eliminar este documento?')">Eliminar</button>
            </form>
            {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}