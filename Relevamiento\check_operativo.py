#!/usr/bin/env python3
# --- Archivo: check_operativo.py ---
# Script para verificar la aplicación en /home/<USER>

import os
import sqlite3
import shutil
from datetime import datetime

def analyze_operativo_app():
    """Analizar la aplicación en /home/<USER>"""
    print("🔍 Analizando aplicación en /home/<USER>")
    
    operativo_dir = "/home/<USER>"
    
    if not os.path.exists(operativo_dir):
        print("❌ Directorio /home/<USER>")
        return False
    
    print(f"📂 Contenido de {operativo_dir}:")
    try:
        items = os.listdir(operativo_dir)
        for item in items:
            item_path = os.path.join(operativo_dir, item)
            if os.path.isfile(item_path):
                size = os.path.getsize(item_path)
                print(f"  📄 {item} ({size:,} bytes)")
            elif os.path.isdir(item_path):
                print(f"  📁 {item}/")
    except Exception as e:
        print(f"❌ Error listando directorio: {e}")
        return False
    
    # Buscar bases de datos
    db_paths = [
        os.path.join(operativo_dir, "app.db"),
        os.path.join(operativo_dir, "instance", "app.db")
    ]
    
    found_data = False
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"\n📊 Analizando: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Obtener tablas
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [table[0] for table in cursor.fetchall()]
                
                print(f"📋 Tablas: {tables}")
                
                # Verificar datos en tablas importantes
                for table in ['user', 'point', 'image', 'camera']:
                    if table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table};")
                        count = cursor.fetchone()[0]
                        print(f"  📊 {table}: {count} registros")
                        
                        if count > 0:
                            found_data = True
                            
                            # Mostrar muestras
                            if table == 'point':
                                cursor.execute("SELECT name, city, source FROM point LIMIT 5;")
                                points = cursor.fetchall()
                                print(f"    📍 Puntos ejemplo: {points}")
                            
                            elif table == 'image':
                                cursor.execute("SELECT filename FROM image LIMIT 5;")
                                images = cursor.fetchall()
                                print(f"    🖼️  Imágenes ejemplo: {[i[0] for i in images]}")
                            
                            elif table == 'camera':
                                cursor.execute("SELECT type, point_id FROM camera LIMIT 5;")
                                cameras = cursor.fetchall()
                                print(f"    📷 Cámaras ejemplo: {cameras}")
                            
                            elif table == 'user':
                                cursor.execute("SELECT username FROM user;")
                                users = cursor.fetchall()
                                print(f"    👥 Usuarios: {[u[0] for u in users]}")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ Error analizando {db_path}: {e}")
    
    return found_data

def copy_data_from_operativo():
    """Copiar datos desde la aplicación Operativo."""
    print("\n🔄 Copiando datos desde /home/<USER>")
    
    operativo_dbs = [
        "/home/<USER>/app.db",
        "/home/<USER>/instance/app.db"
    ]
    
    best_db = None
    max_size = 0
    
    # Encontrar la base de datos con más datos
    for db_path in operativo_dbs:
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            if size > max_size:
                max_size = size
                best_db = db_path
    
    if not best_db:
        print("❌ No se encontró base de datos en /home/<USER>")
        return False
    
    print(f"📁 Usando: {best_db} ({max_size:,} bytes)")
    
    try:
        # Hacer backup de la base de datos actual
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"backup_before_operativo_{timestamp}.db"
        
        if os.path.exists('instance/app.db'):
            shutil.copy2('instance/app.db', backup_name)
            print(f"✅ Backup actual guardado como: {backup_name}")
        
        # Copiar base de datos desde Operativo
        shutil.copy2(best_db, 'instance/app.db')
        print("✅ Base de datos copiada desde Operativo")
        
        # Verificar la copia
        conn = sqlite3.connect('instance/app.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tablas copiadas: {tables}")
        
        # Mostrar estadísticas
        for table in ['user', 'point', 'image', 'camera']:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = cursor.fetchone()[0]
                print(f"  📊 {table}: {count} registros")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error copiando datos: {e}")
        return False

def update_user_permissions():
    """Actualizar la estructura de permisos en la base de datos copiada."""
    print("\n🔧 Actualizando estructura de permisos...")
    
    try:
        conn = sqlite3.connect('instance/app.db')
        cursor = conn.cursor()
        
        # Verificar si la tabla user tiene las columnas de permisos
        cursor.execute("PRAGMA table_info(user);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        # Agregar columnas de permisos si no existen
        if 'role' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN role VARCHAR(20) DEFAULT 'administrador';")
            print("✅ Columna 'role' agregada")
        
        if 'is_active' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFAULT TRUE;")
            print("✅ Columna 'is_active' agregada")
        
        if 'created_by' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN created_by INTEGER;")
            print("✅ Columna 'created_by' agregada")
        
        if 'created_at' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;")
            print("✅ Columna 'created_at' agregada")
        
        if 'updated_at' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;")
            print("✅ Columna 'updated_at' agregada")
        
        # Crear tablas de permisos si no existen
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        if 'user_city_permissions' not in tables:
            cursor.execute('''
                CREATE TABLE user_city_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    city VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, city)
                );
            ''')
            print("✅ Tabla user_city_permissions creada")
        
        if 'user_source_permissions' not in tables:
            cursor.execute('''
                CREATE TABLE user_source_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    source VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, source)
                );
            ''')
            print("✅ Tabla user_source_permissions creada")
        
        if 'user_permissions' not in tables:
            cursor.execute('''
                CREATE TABLE user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    permission_type VARCHAR(50) NOT NULL,
                    permission_value BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE,
                    UNIQUE(user_id, permission_type)
                );
            ''')
            print("✅ Tabla user_permissions creada")
        
        # Actualizar usuarios existentes
        cursor.execute("UPDATE user SET role = 'administrador' WHERE role IS NULL OR role = '';")
        cursor.execute("UPDATE user SET is_active = TRUE WHERE is_active IS NULL;")
        cursor.execute("UPDATE user SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL;")
        cursor.execute("UPDATE user SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;")
        
        conn.commit()
        conn.close()
        
        print("✅ Estructura de permisos actualizada")
        return True
        
    except Exception as e:
        print(f"❌ Error actualizando permisos: {e}")
        return False

def main():
    """Función principal."""
    print("🔍 Verificador de Aplicación Operativo")
    print("=" * 45)
    
    # Analizar aplicación Operativo
    has_data = analyze_operativo_app()
    
    if has_data:
        print("\n✅ Se encontraron datos en /home/<USER>")
        
        response = input("\n¿Copiar datos desde /home/<USER>/n): ").strip().lower()
        
        if response in ['s', 'si', 'y', 'yes']:
            if copy_data_from_operativo():
                if update_user_permissions():
                    print("\n🎉 ¡Datos copiados y actualizados exitosamente!")
                    print("\n🚀 Próximos pasos:")
                    print("   1. Reiniciar aplicación: systemctl restart relevamiento")
                    print("   2. Verificar datos: python3 check_db.py")
                    print("   3. Probar aplicación web")
                    print("   4. Crear templates HTML para gestión de usuarios")
                else:
                    print("\n⚠️  Datos copiados pero error actualizando permisos")
            else:
                print("\n❌ Error copiando datos")
        else:
            print("❌ Operación cancelada")
    else:
        print("\n❌ No se encontraron datos relevantes en /home/<USER>")
        print("\n💡 Opciones:")
        print("1. Crear datos de prueba")
        print("2. Importar desde archivos externos")
        print("3. Empezar con aplicación limpia")

if __name__ == "__main__":
    main()
