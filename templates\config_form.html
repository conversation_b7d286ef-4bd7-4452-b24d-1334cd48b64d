<!-- /static/app-ws/templates/config_form.html -->

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuración de Usuario</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">
</head>
<body class="body-background">
    <header>
        <h1>Cliente VHF Digital - Configuración de Usuario</h1>
        <p>Formulario de configuración para la aplicación Cliente VHF Digital.</p>
    </header>

    <main class="container">
        <!-- Sección de formulario de configuración -->
        <div class="node-section">
            <h3>Configuración de Usuario</h3>
        </div>

        <form action="{{ url_for('save_user_config') }}" method="POST" class="config-form">
            <div class="form-group">
                <label for="username">Usuario:</label>
                <input type="text" id="username" name="username" class="input-field" required>
            </div>

            <div class="form-group">
                <label for="password">Contraseña:</label>
                <input type="password" id="password" name="password" class="input-field" required>
            </div>

            <div class="form-group">
                <label for="node_id">ID del Nodo:</label>
                <input type="text" id="node_id" name="node_id" class="input-field" required>
            </div>

            <div class="form-group">
                <label for="input_device_index">Dispositivo de entrada de audio:</label>
                <select id="input_device_index" name="input_device_index" class="input-field">
                    {% if input_devices %}
                        {% for index, name in input_devices %}
                            <option value="{{ index }}">{{ name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="0">Dispositivo predeterminado</option>
                    {% endif %}
                </select>
            </div>

            <div class="form-group">
                <label for="volume_level">Nivel de Volumen:</label>
                <input type="number" id="volume_level" name="volume_level" class="input-field" min="1" max="10" required>
            </div>

            <div class="form-group">
                <label for="port_number">Número de Puerto:</label>
                <input type="text" id="port_number" name="port_number" class="input-field" required>
            </div>

            <div class="form-group">
                <button type="submit" class="submit-btn">Guardar Configuración</button>
            </div>
        </form>
    </main>

    <footer>
        <p>&copy; 2024 Cliente VHF Digital</p>
    </footer>
</body>
</html>
