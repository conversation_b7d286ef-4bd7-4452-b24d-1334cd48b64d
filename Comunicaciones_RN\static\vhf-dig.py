# vhf-dig.py

import json
import serial
import time
import os
import ctypes
import pyaudio
import wave
import io
import base64
import numpy as np
import threading
import hashlib
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

# Función para cargar la configuración del usuario
def load_config():
    try:
        with open('ptt_client_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: No se encontró el archivo de configuración.")
        return {}

# Función para calcular el hash del archivo de configuración
def file_checksum(file_path):
    md5_hash = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            # Leer el archivo en bloques para no cargar todo el archivo en memoria
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
    except FileNotFoundError:
        print("Error: No se encontró el archivo de configuración.")
        return None
    return md5_hash.hexdigest()

# Función para monitorear el archivo de configuración
def monitor_config_file(config, config_lock):
    config_path = 'ptt_client_config.json'
    last_checksum = file_checksum(config_path)
    
    while True:
        time.sleep(2)  # Monitorea cada 2 segundos (ajusta este valor según sea necesario)
        current_checksum = file_checksum(config_path)
        
        if current_checksum != last_checksum:
            print("Cambios detectados en el archivo de configuración. Recargando...")
            with config_lock:
                new_config = load_config()
                config.update(new_config)
            last_checksum = current_checksum

# Función para iniciar sesión y navegar al nodo usando Selenium
def login_and_navigate(config):
    chrome_options = Options()
    chrome_options.add_argument("--use-fake-ui-for-media-stream")  # Permitir el acceso a la cámara y el micrófono sin preguntar
    chrome_options.add_argument("--disable-gpu")  # Deshabilitar la aceleración por hardware
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-software-rasterizer")
    chrome_options.add_argument("--headless")  # Ejecutar en modo headless
    chrome_options.add_argument("--window-size=1920,1080")  # Tamaño de la ventana
    chrome_options.add_argument("--autoplay-policy=no-user-gesture-required")

    # Iniciar el driver de Chrome con webdriver_manager
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

    # Navegar al sitio web
    driver.get(config['node_url'])

    # Iniciar sesión en el sitio web
    username = driver.find_element(By.NAME, 'username')
    password = driver.find_element(By.NAME, 'password')

    username.send_keys(config['username'])
    password.send_keys(config['password'])

    password.send_keys(Keys.RETURN)  # Usando correctamente Keys.RETURN para enviar la contraseña

    # Esperar un momento para que el inicio de sesión se complete
    time.sleep(5)

    # Navegar a la página del nodo específico
    driver.get(config['node_url'])

    # Esperar a que la página cargue completamente
    time.sleep(5)
    
    return driver

# Función para controlar el PTT en el puerto COM
def set_ptt(state, config, ser=None):
    com_port = f"COM{config.get('port_number', '1')}"
    try:
        if state:
            # Abre el puerto si no está abierto ya
            if ser is None or not ser.is_open:
                ser = serial.Serial(com_port, baudrate=9600, timeout=1)
                print(f"Puerto {com_port} abierto con éxito.")
            
            ser.setDTR(True)
            print(f"PTT activado en {com_port}. Mide el voltaje en el pin 7.")
            return ser  # Devuelve el objeto Serial para uso futuro
        else:
            if ser is not None and ser.is_open:
                ser.setDTR(False)
                print(f"PTT desactivado en {com_port}. Verifica si el voltaje vuelve a 0.")
                ser.close()
                print(f"Puerto {com_port} cerrado.")
            return None  # Devuelve None cuando el puerto está cerrado
    except serial.SerialException as e:
        print(f"Error al intentar abrir el puerto {com_port}: {e}")
        return None

# Función para capturar y enviar audio del micrófono al sitio web
def capture_and_send_audio(driver, config, config_lock):
    p = pyaudio.PyAudio()

    # Configuración del stream de audio
    stream = p.open(format=pyaudio.paInt16,
                    channels=1,
                    rate=8000,  # Frecuencia de muestreo para voz
                    input=True,
                    input_device_index=config.get('input_device_index', 0),
                    frames_per_buffer=512)  # Tamaño de buffer ajustado

    volume_level_threshold = config.get('volume_level', 5)
    is_transmitting = False

    def send_audio(data):
        # Crear un archivo WAV en memoria
        wav_buffer = io.BytesIO()
        wf = wave.open(wav_buffer, 'wb')
        wf.setnchannels(1)
        wf.setsampwidth(p.get_sample_size(pyaudio.paInt16))
        wf.setframerate(8000)
        wf.writeframes(data)
        wf.close()

        # Convertir el archivo WAV a base64
        wav_buffer.seek(0)
        audio_base64 = base64.b64encode(wav_buffer.read()).decode('utf-8')

        # Enviar el audio al servidor
        script = f"""
            const socket = io.connect('https://' + document.domain + ':' + location.port + '/node', {{
                query: {{
                    node_id: '{config['node_id']}',
                    username: '{config['username']}'
                }}
            }});

            socket.emit('transmit_audio', {{
                audio: '{audio_base64}',
                user: '{config['username']}',
                node_id: '{config['node_id']}'
            }});
        """
        driver.execute_script(script)

    print("Monitoreando el audio...")

    try:
        while True:
            data = stream.read(2048)
            audio_data = np.frombuffer(data, dtype=np.int16)
            volume = np.linalg.norm(audio_data) / len(audio_data)

            with config_lock:
                volume_level_threshold = config.get('volume_level', 5)

            if volume > volume_level_threshold and not is_transmitting:
                print(f"Volumen {volume} excede el umbral {volume_level_threshold}. Iniciando transmisión...")
                is_transmitting = True

            if is_transmitting:
                send_audio(data)

            if is_transmitting and volume <= volume_level_threshold:
                print(f"Volumen {volume} por debajo del umbral {volume_level_threshold}. Deteniendo transmisión...")
                is_transmitting = False

            time.sleep(0.1)  # Pequeña pausa para no sobrecargar el servidor
    except KeyboardInterrupt:
        print("Interrumpido por el usuario, deteniendo el envío de audio.")
    finally:
        stream.stop_stream()
        stream.close()
        p.terminate()
        print("Transmisión de audio detenida.")

if __name__ == '__main__':
    config = load_config()
    config_lock = threading.Lock()

    if 'username' not in config or 'password' not in config or 'node_url' not in config or 'node_id' not in config:
        print("Error: Configuración incompleta. Por favor, asegúrate de tener usuario, contraseña y nodo configurados.")
        exit(1)

    # Iniciar un hilo para monitorear el archivo de configuración
    threading.Thread(target=monitor_config_file, args=(config, config_lock), daemon=True).start()

    # Iniciar sesión y navegar al nodo usando Selenium
    driver = login_and_navigate(config)

    # Iniciar la captura y monitoreo de audio
    capture_and_send_audio(driver, config, config_lock)
