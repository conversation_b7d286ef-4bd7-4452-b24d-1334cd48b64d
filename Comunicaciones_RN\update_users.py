# update_users.py
from app import create_app
from extensions import db
from models import User
import uuid

app = create_app()

with app.app_context():
    users = User.query.all()
    for user in users:
        if user.regional_units is None:
            user.regional_units = ''
        if user.has_police_access is None:
            user.has_police_access = False
        if user.session_token is None:
            user.session_token = str(uuid.uuid4())
        db.session.add(user)
    db.session.commit()
    print("Usuarios actualizados correctamente")
