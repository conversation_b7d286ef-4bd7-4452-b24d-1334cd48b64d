<!-- app/templates/points/list_points.html -->
{% extends "base.html" %}

{% block title %}Gestionar Puntos - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Gestionar Puntos</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">{{ total_points }} punto{{ 's' if total_points != 1 else '' }} encontrado{{ 's' if total_points != 1 else '' }}</span>
    </div>
</div>

<!-- Bo<PERSON> de Acción -->
<div class="d-flex justify-content-start align-items-center mb-3 flex-wrap gap-2">
    <a href="{{ url_for('points.create') }}" class="btn btn-success btn-sm">
        <i class="bi bi-plus-circle-fill me-1"></i> Nuevo Punto
    </a>

    <a href="{{ url_for('points.export_csv') }}" class="btn btn-outline-secondary btn-sm">
        <i class="bi bi-download me-1"></i> Exportar CSV
    </a>

    <form method="POST" action="{{ url_for('points.import_csv') }}" enctype="multipart/form-data" class="d-inline">
        {{ import_form.hidden_tag() }}
        {{ import_form.csv_file(class="form-control form-control-sm d-inline w-auto") }}
        {{ import_form.submit_csv(class="btn btn-secondary btn-sm ms-1") }}
    </form>

    <form method="POST" action="{{ url_for('points.import_dbf') }}" enctype="multipart/form-data" class="d-inline">
        {{ import_form.hidden_tag() }}
        {{ import_form.dbf_file(class="form-control form-control-sm d-inline w-auto") }}
        {{ import_form.submit_dbf(class="btn btn-secondary btn-sm ms-1") }}
    </form>
</div>

<!-- Formulario de Filtros Avanzados -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">🔍 Filtros de Búsqueda Avanzados</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('points.list_all') }}">
            <div class="row g-3">
                <!-- Primera fila -->
                <div class="col-md-2">
                    {{ filter_form.point_id.label(class="form-label") }}
                    {{ filter_form.point_id(class="form-control form-control-sm", placeholder="123") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.name.label(class="form-label") }}
                    {{ filter_form.name(class="form-control form-control-sm", placeholder="Nombre del punto") }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.city.label(class="form-label") }}
                    {{ filter_form.city(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.status.label(class="form-label") }}
                    {{ filter_form.status(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.source.label(class="form-label") }}
                    {{ filter_form.source(class="form-select form-select-sm") }}
                </div>

                <!-- Segunda fila -->
                <div class="col-md-3">
                    {{ filter_form.description.label(class="form-label") }}
                    {{ filter_form.description(class="form-control form-control-sm", placeholder="Texto en descripción") }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.camera_type.label(class="form-label") }}
                    {{ filter_form.camera_type(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.has_images.label(class="form-label") }}
                    {{ filter_form.has_images(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-2">
                    {{ filter_form.has_cameras.label(class="form-label") }}
                    {{ filter_form.has_cameras(class="form-select form-select-sm") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.cameras_count.label(class="form-label") }}
                    {{ filter_form.cameras_count(class="form-select form-select-sm") }}
                </div>

                <!-- Tercera fila - Coordenadas -->
                <div class="col-md-3">
                    {{ filter_form.lat_min.label(class="form-label") }}
                    {{ filter_form.lat_min(class="form-control form-control-sm", placeholder="-90.0") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.lat_max.label(class="form-label") }}
                    {{ filter_form.lat_max(class="form-control form-control-sm", placeholder="90.0") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.lon_min.label(class="form-label") }}
                    {{ filter_form.lon_min(class="form-control form-control-sm", placeholder="-180.0") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.lon_max.label(class="form-label") }}
                    {{ filter_form.lon_max(class="form-control form-control-sm", placeholder="180.0") }}
                </div>

                <!-- Cuarta fila - Fechas -->
                <div class="col-md-3">
                    {{ filter_form.date_from.label(class="form-label") }}
                    {{ filter_form.date_from(class="form-control form-control-sm") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.date_to.label(class="form-label") }}
                    {{ filter_form.date_to(class="form-control form-control-sm") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.modified_from.label(class="form-label") }}
                    {{ filter_form.modified_from(class="form-control form-control-sm") }}
                </div>
                <div class="col-md-3">
                    {{ filter_form.modified_to.label(class="form-label") }}
                    {{ filter_form.modified_to(class="form-control form-control-sm") }}
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="bi bi-search"></i> Aplicar Filtros
                    </button>
                    <a href="{{ url_for('points.list_all') }}" class="btn btn-outline-secondary btn-sm ms-2">
                        <i class="bi bi-x-circle"></i> Limpiar Filtros
                    </a>
                    <!-- Mantener ordenamiento actual -->
                    <input type="hidden" name="sort_by" value="{{ sort_by }}">
                    <input type="hidden" name="order" value="{{ order }}">
                </div>
            </div>
        </form>
    </div>
</div>
<!-- Tabla de Resultados -->
{% if points %}
<div class="table-responsive">
    <table class="table table-striped table-hover table-sm">
        <thead class="table-dark">
            <tr>
                {# Cabecera ID con enlace para ordenar #}
                {% set sort_order_id = 'desc' if sort_by == 'id' and order == 'asc' else 'asc' %}
                <th scope="col">
                    <a href="{{ url_for('points.list_all', sort_by='id', order=sort_order_id) }}" class="text-white text-decoration-none">ID
                        {% if sort_by == 'id' %}
                            <i class="bi bi-arrow-{{ 'down' if order == 'asc' else 'up' }}"></i>
                        {% endif %}
                    </a>
                </th>
                {# Cabecera Nombre con enlace para ordenar #}
                {% set sort_order_name = 'desc' if sort_by == 'name' and order == 'asc' else 'asc' %}
                <th scope="col">
                    <a href="{{ url_for('points.list_all', sort_by='name', order=sort_order_name) }}" class="text-white text-decoration-none">Nombre
                        {% if sort_by == 'name' %}
                            <i class="bi bi-arrow-{{ 'down' if order == 'asc' else 'up' }}"></i>
                        {% endif %}
                    </a>
                </th>
                <th scope="col">Ciudad</th>
                <th scope="col">📍 Coordenadas</th>
                <th scope="col">Origen</th>
                {# Cabecera Estado con enlace para ordenar #}
                {% set sort_order_status = 'desc' if sort_by == 'status' and order == 'asc' else 'asc' %}
                <th scope="col">
                    <a href="{{ url_for('points.list_all', sort_by='status', order=sort_order_status) }}" class="text-white text-decoration-none">Estado
                        {% if sort_by == 'status' %}
                            <i class="bi bi-arrow-{{ 'down' if order == 'asc' else 'up' }}"></i>
                        {% endif %}
                    </a>
                </th>
                <th scope="col">📷 Cámaras</th>
                <th scope="col">🖼️ Imágenes</th>
                <th scope="col">Acciones</th>
            </tr>
        </thead>
        <tbody>
            {% for point in points %}
            <tr>
                <td><strong>{{ point.id }}</strong></td>
                <td>
                    <a href="{{ url_for('points.detail', point_id=point.id) }}" class="text-decoration-none">
                        <strong>{{ point.name or 'Sin nombre' }}</strong>
                    </a>
                    {% if point.description %}
                        <br><small class="text-muted">{{ point.description[:50] }}{% if point.description|length > 50 %}...{% endif %}</small>
                    {% endif %}
                </td>
                <td>{{ point.city or 'N/A' }}</td>
                <td>
                    <small class="font-monospace">
                        {{ "%.6f"|format(point.latitude) }},<br>
                        {{ "%.6f"|format(point.longitude) }}
                    </small>
                </td>
                <td>
                    {% if point.source %}
                        <span class="badge bg-secondary">{{ point.source }}</span>
                    {% else %}
                        <span class="text-muted">N/A</span>
                    {% endif %}
                </td>
                <td>
                    <span class="status-indicator status-{{ point.status }}" title="{{ point.status.capitalize() }}"></span>
                    {{ point.status.capitalize() }}
                </td>
                <td>
                    {% if point.cameras_count > 0 %}
                        <span class="badge bg-primary">{{ point.cameras_count }}</span>
                    {% else %}
                        <span class="text-muted">0</span>
                    {% endif %}
                </td>
                <td>
                    {% if point.images_count > 0 %}
                        <span class="badge bg-success">{{ point.images_count }}</span>
                        {% if point.has_annotations %}
                            <i class="bi bi-pencil-square text-warning" title="Con anotaciones"></i>
                        {% endif %}
                    {% else %}
                        <span class="text-muted">0</span>
                    {% endif %}
                </td>
                <td>
                    <a href="{{ url_for('points.detail', point_id=point.id) }}" class="btn btn-info btn-sm me-1" title="Ver Detalles">
                        <i class="bi bi-eye-fill"></i>
                    </a>
                    <a href="{{ url_for('points.edit', point_id=point.id) }}" class="btn btn-warning btn-sm me-1" title="Editar Punto">
                        <i class="bi bi-pencil-fill"></i>
                    </a>
                    <form action="{{ url_for('points.delete', point_id=point.id) }}" method="POST" class="d-inline"
                          onsubmit="return confirm('¿Estás SEGURO de que quieres eliminar el punto {{ point.id }} ({{ point.name or '' }})?\n¡Esta acción borrará también sus imágenes y anotaciones y no se puede deshacer!');">
                        <button type="submit" class="btn btn-danger btn-sm" title="Eliminar Punto">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="alert alert-info mt-3">
    <i class="bi bi-info-circle"></i>
    {% if request.args %}
        No se encontraron puntos que coincidan con los filtros aplicados.
    {% else %}
        No hay puntos registrados todavía. <a href="{{ url_for('points.create') }}">Crea el primero</a>.
    {% endif %}
</div>
{% endif %}

<!-- Información adicional -->
{% if points %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">📊 Estadísticas</h6>
                <p class="card-text">
                    <strong>Total de puntos:</strong> {{ total_points }}<br>
                    <strong>Con cámaras:</strong> {{ points|selectattr('cameras_count', 'greaterthan', 0)|list|length }}<br>
                    <strong>Con imágenes:</strong> {{ points|selectattr('images_count', 'greaterthan', 0)|list|length }}<br>
                    <strong>Con anotaciones:</strong> {{ points|selectattr('has_annotations')|list|length }}
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">🎯 Estados</h6>
                <p class="card-text">
                    {% set azul_count = points|selectattr('status', 'equalto', 'azul')|list|length %}
                    {% set amarillo_count = points|selectattr('status', 'equalto', 'amarillo')|list|length %}
                    {% set verde_count = points|selectattr('status', 'equalto', 'verde')|list|length %}
                    {% set rojo_count = points|selectattr('status', 'equalto', 'rojo')|list|length %}
                    <span class="badge bg-primary">Por Hacer: {{ azul_count }}</span>
                    <span class="badge bg-warning">En Curso: {{ amarillo_count }}</span>
                    <span class="badge bg-success">Hecho: {{ verde_count }}</span>
                    <span class="badge bg-danger">Incompleto: {{ rojo_count }}</span>
                </p>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block head_extra %} {# Añadir iconos Bootstrap si no están en base.html #}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
<style>
    th a { text-decoration: none; color: inherit; }
    th a:hover { text-decoration: underline; }
    th i { font-size: 0.8em; } /* Icono de ordenamiento más pequeño */
</style>
{% endblock %}
{% block scripts_extra %}
<script src="{{ url_for('static', filename='js/filter.js') }}"></script>
{% endblock %}