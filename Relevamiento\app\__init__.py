# --- Archivo: app/__init__.py ---
import os
import re  # <-- Importar re para expresiones regulares
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
from config import Config
from logging.config import dictConfig
from datetime import datetime
from markupsafe import Markup, escape  # <-- Importar Markup y escape

# Inicializar extensiones
db = SQLAlchemy()
migrate = Migrate()
csrf = CSRFProtect()
login = LoginManager()
login.login_view = 'auth.login'  # Referenciar blueprint 'auth' y vista 'login'
login.login_message = "Por favor, inicia sesión para acceder a esta página."
login.login_message_category = "info"

# --- Configuración de logs (NIVEL CAMBIADO A DEBUG) ---
dictConfig({
    'version': 1,
    'formatters': {'default': {
        'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
    }},
    'handlers': {'wsgi': {
        'class': 'logging.StreamHandler',
        'stream': 'ext://flask.logging.wsgi_errors_stream',
        'formatter': 'default'
    }},
    'root': {
        'level': 'DEBUG',  # <--- MODIFICADO DE INFO A DEBUG ---
        'handlers': ['wsgi']
    }
})
# --- FIN CONFIGURACIÓN LOGS ---


# --- DEFINICIÓN DEL FILTRO nl2br ---
def nl2br(value):
    """Convierte saltos de línea en etiquetas <br> HTML."""
    if not value:
        return ""
    # Escapar HTML en el valor original para prevenir XSS
    escaped_value = escape(value)
    # Reemplazar varios tipos de saltos de línea (\r\n, \r, \n) por <br>\n
    # Usar Markup para indicar que el resultado con <br> es seguro para renderizar
    result = Markup(re.sub(r'\r\n|\r|\n', '<br>\n', escaped_value))
    return result
# --- FIN DEFINICIÓN FILTRO ---


# Función factoría de la aplicación
def create_app(config_class=Config):
    app = Flask(__name__, instance_relative_config=True)
    app.config.from_object(config_class)

    # Usar el logger después de configurar dictConfig o usar el logger por defecto de Flask
    # app.logger se configura *después* de dictConfig si se usa así
    # Es mejor loguear DESPUÉS de la configuración inicial
    # ---> Loguear el nivel configurado <---
    current_log_level = app.logger.getEffectiveLevel()
    app.logger.info(f'Iniciando aplicación GeoApp... Nivel de log configurado: {current_log_level}')


    # Crear carpeta de instancia si no existe
    try:
        os.makedirs(app.instance_path)
        app.logger.info(f"Carpeta de instancia creada en: {app.instance_path}")
    except OSError:
        app.logger.debug(f"Carpeta de instancia ya existe en: {app.instance_path}") # Cambiado a DEBUG

    # Crear carpeta de subidas si no existe
    upload_folder = app.config['UPLOAD_FOLDER']
    try:
        os.makedirs(upload_folder)
        app.logger.info(f"Carpeta de subidas creada en: {upload_folder}")
    except OSError:
        app.logger.debug(f"Carpeta de subidas ya existe en: {upload_folder}") # Cambiado a DEBUG

    # Inicializar extensiones con la app
    db.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)
    login.init_app(app)

    # --- REGISTRAR EL FILTRO PERSONALIZADO ---
    app.jinja_env.filters['nl2br'] = nl2br
    # --- FIN REGISTRO ---

    # Registrar procesador de contexto para 'now' y 'csrf_token'
    @app.context_processor
    def inject_now():
        from flask_wtf.csrf import generate_csrf
        return {
            'now': datetime.utcnow,
            'csrf_token': generate_csrf
        }

    # Registrar Blueprints
    app.logger.debug("Registrando blueprints...") # Log DEBUG
    from .routes import bp as main_bp
    app.register_blueprint(main_bp)

    from .auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from .points import bp as points_bp
    app.register_blueprint(points_bp, url_prefix='/points')

    from .users import bp as users_bp
    app.register_blueprint(users_bp, url_prefix='/users')

    from .audit import bp as audit_bp
    app.register_blueprint(audit_bp, url_prefix='/audit')

    app.logger.debug("Blueprints registrados.") # Log DEBUG

    # Registrar Comandos CLI
    app.logger.debug("Registrando comandos CLI...") # Log DEBUG
    # Asume que el archivo se llama importer.py ahora
    try:
        from . import importer
        app.cli.add_command(importer.import_dbf_command)
        app.cli.add_command(importer.import_csv_command) # Si añadiste el comando CSV
        app.cli.add_command(importer.init_db_command)
        app.logger.debug("Comandos CLI desde importer.py registrados.") # Log DEBUG
    except ImportError:
        # Fallback al nombre antiguo si renombrar falló o no se hizo
        app.logger.warning("No se encontró importer.py, intentando con dbf_importer.py para comandos CLI")
        from . import dbf_importer
        app.cli.add_command(dbf_importer.import_dbf_command)
        # Considera añadir el comando CSV aquí también si está en dbf_importer.py
        app.cli.add_command(dbf_importer.init_db_command)
        app.logger.debug("Comandos CLI desde dbf_importer.py registrados.") # Log DEBUG


    # Importar modelos para registrar user_loader y crear tablas
    from app import models

    app.logger.info("Aplicación GeoApp configurada exitosamente.")
    return app