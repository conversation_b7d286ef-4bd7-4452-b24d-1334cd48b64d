import psutil
import time
import os
import socketio
from datetime import datetime, timedelta
from threading import Thread, Event, Lock
import logging
import signal
import sys
import urllib3

# -----------------------------------------------------------------------------
# Su<PERSON><PERSON>ir advertencias de urllib3 (Solo para Desarrollo)
# -----------------------------------------------------------------------------
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# -----------------------------------------------------------------------------
# Configuración del logger
# -----------------------------------------------------------------------------
logging.basicConfig(
    filename='monitor_debug.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# -----------------------------------------------------------------------------
# Configuración del cliente SocketIO
# -----------------------------------------------------------------------------
sio = socketio.Client(
    reconnection_attempts=5,
    reconnection_delay=2,
    ssl_verify=False  # O especifica la ruta al certificado, e.g., '/ruta/a/cert.pem'
)

# -----------------------------------------------------------------------------
# Variables y constantes
# -----------------------------------------------------------------------------
BASE_URL = 'https://192.168.1.81:5000'  # URL base con los namespaces /monitor y /audio_events

# Archivos de registro
METRICS_LOG_FILE = 'monitor_log.txt'
AUDIO_EVENTS_LOG_FILE = 'audio_events.log'

# Intervalo para generar informes en segundos (24 horas = 24 * 60 * 60)
REPORT_INTERVAL = 24 * 60 * 60  # 24 horas

# Tiempo del último informe
last_report_time = datetime.now()

# Eventos para controlar la captura de métricas
transmission_event = Event()
save_event = Event()
shutdown_event = Event()  # Evento para señalizar el cierre del script

# Listas para almacenar métricas durante eventos
transmission_metrics = []
save_metrics = []

# Lock para asegurar la exclusión mutua
metrics_lock = Lock()

# Diccionarios para almacenar la información de inicio de transmisión y guardado
transmissions_data = {}  # { node_id: { 'start_time': datetime, 'user': str } }
saves_data = {}          # { node_id: { 'start_time': datetime, 'user': str } }

# -----------------------------------------------------------------------------
# Funciones para obtener métricas del sistema
# -----------------------------------------------------------------------------
def get_cpu_usage():
    """
    Obtiene el porcentaje de uso de la CPU de forma casi inmediata (interval=0).
    Esto evita bloqueos de 1 segundo.
    """
    return psutil.cpu_percent(interval=0)

def get_memory_usage():
    """Obtiene el uso de memoria RAM en MB."""
    return psutil.virtual_memory().used / (1024 * 1024)

def get_disk_usage():
    """Obtiene el porcentaje de uso del disco principal (/)."""
    return psutil.disk_usage('/').percent

def get_network_speed():
    """
    Mide la velocidad de subida y bajada de la red en MB/s en un intervalo de 0.5s,
    para capturar más datos sin bloquear por un segundo completo.
    """
    net_io_counters_start = psutil.net_io_counters()
    sent_bytes_start = net_io_counters_start.bytes_sent
    recv_bytes_start = net_io_counters_start.bytes_recv

    # Esperamos 0.5 segundos
    time.sleep(0.5)

    net_io_counters_end = psutil.net_io_counters()
    sent_bytes_end = net_io_counters_end.bytes_sent
    recv_bytes_end = net_io_counters_end.bytes_recv

    # Escalamos la medición (x2) ya que el intervalo fue 0.5s
    upload_speed = (sent_bytes_end - sent_bytes_start) * 2 / (1024 * 1024)  # MB/s
    download_speed = (recv_bytes_end - recv_bytes_start) * 2 / (1024 * 1024)  # MB/s

    return upload_speed, download_speed

# -----------------------------------------------------------------------------
# Función para escribir en un archivo de log (con manejo de errores y logs)
# -----------------------------------------------------------------------------
def append_to_log(file_path, content):
    """
    Agrega contenido a un archivo de log en modo 'append', usando UTF-8
    para evitar problemas con caracteres.
    
    Se añade manejo de excepciones mejorado y logs de depuración para
    verificar el contenido que se intenta escribir y el resultado de la operación.
    """
    try:
        directory = os.path.dirname(file_path)
        if directory.strip():
            if not os.path.exists(directory):
                os.makedirs(directory)
                logging.debug(f"Directorio creado: {directory}")
        else:
            logging.debug("No se especificó un directorio en la ruta del archivo, se usará el directorio actual.")

        logging.debug(f"Intentando abrir el archivo {file_path} para escribir en modo 'append'.")
        logging.debug(f"Contenido a escribir:\n{content}")

        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(content)
        
        logging.debug(f"Contenido escrito correctamente en {file_path}")

    except Exception as e:
        logging.error(f"Error al escribir en el archivo {file_path}: {e}", exc_info=True)

# -----------------------------------------------------------------------------
# Función para generar y guardar informe diario de métricas
# -----------------------------------------------------------------------------
def finalize_metrics_report():
    """
    Genera y guarda un informe diario de monitoreo de métricas en 'monitor_log.txt'.
    Se registran CPU, RAM, Disco, velocidad de Red y la hora actual.
    """
    header = (
        "\n--------------------------------------------------\n"
        f"Informe de monitorizacion - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        "--------------------------------------------------\n"
    )
    append_to_log(METRICS_LOG_FILE, header)

    cpu = get_cpu_usage()
    ram = get_memory_usage()
    disk = get_disk_usage()
    upload_speed, download_speed = get_network_speed()
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    system_metrics = (
        f"\nMetricas del Sistema:\n"
        f"- Uso de CPU: {cpu}%\n"
        f"- Uso de RAM: {ram:.2f} MB\n"
        f"- Uso de Disco: {disk}%\n"
        f"- Velocidad de Subida de Red: {upload_speed:.2f} MB/s\n"
        f"- Velocidad de Bajada de Red: {download_speed:.2f} MB/s\n"
        f"- Hora del Informe: {timestamp}\n"
    )
    append_to_log(METRICS_LOG_FILE, system_metrics)
    append_to_log(METRICS_LOG_FILE, "\n----------------------------------------\n\n")

    global last_report_time
    last_report_time = datetime.now()

# -----------------------------------------------------------------------------
# Funciones para la captura de métricas en transmisión y guardado
# -----------------------------------------------------------------------------
def transmission_metrics_collector():
    """
    Captura métricas relacionadas con la transmisión mientras dure el evento
    o hasta 30 segundos como máximo. Se registran logs de depuración.
    """
    logging.debug("Iniciando la captura de métricas de transmisión.")
    start_time = time.time()
    while not transmission_event.is_set() and (time.time() - start_time) < 30:
        try:
            with metrics_lock:
                cpu = get_cpu_usage()
                ram = get_memory_usage()
                disk = get_disk_usage()
                upload_speed, download_speed = get_network_speed()
                transmission_metrics.append({
                    'cpu': cpu,
                    'ram': ram,
                    'disk': disk,
                    'upload_speed': upload_speed,
                    'download_speed': download_speed
                })
            time.sleep(0.5)
        except Exception as e:
            logging.error(f"Error en la captura de métricas de transmisión: {e}")
            break
    logging.debug("Finalizando la captura de métricas de transmisión.")

def save_metrics_collector():
    """
    Captura métricas relacionadas con el guardado mientras dure el evento
    o hasta 30 segundos como máximo. Se registran logs de depuración.
    """
    logging.debug("Iniciando la captura de métricas de guardado.")
    start_time = time.time()
    while not save_event.is_set() and (time.time() - start_time) < 30:
        try:
            with metrics_lock:
                ram = get_memory_usage()
                upload_speed, download_speed = get_network_speed()
                save_metrics.append({
                    'ram': ram,
                    'upload_speed': upload_speed,
                    'download_speed': download_speed
                })
            time.sleep(0.5)
        except Exception as e:
            logging.error(f"Error en la captura de métricas de guardado: {e}")
            break
    logging.debug("Finalizando la captura de métricas de guardado.")

# -----------------------------------------------------------------------------
# Función para conectarse al servidor SocketIO
# -----------------------------------------------------------------------------
def connect_to_server():
    """
    Intenta conectarse al servidor SocketIO con los namespaces /monitor y /audio_events.
    Si ya está conectado, no intenta reconectar.
    Si no puede conectar, se registra el error y se finaliza la ejecución.
    """
    try:
        if not sio.connected:
            sio.connect(BASE_URL, namespaces=['/monitor', '/audio_events'], wait_timeout=10)
            print(f"Conectado al servidor en {BASE_URL} con namespaces /monitor y /audio_events")
            logging.info(f"Conectado al servidor en {BASE_URL}")
        else:
            logging.warning("Ya estás conectado al servidor SocketIO.")
    except socketio.exceptions.ConnectionError as e:
        error_msg = f"Error al conectar con el servidor: {e}"
        print(error_msg)
        logging.error(error_msg)
        raise SystemExit(error_msg) from e

# -----------------------------------------------------------------------------
# Eventos SocketIO (namespace general y /monitor)
# -----------------------------------------------------------------------------
@sio.event
def connect():
    """
    Evento que se dispara al conectarse exitosamente al servidor SocketIO.
    Enviamos un conjunto inicial de métricas apenas nos conectamos.
    """
    print('Conexión establecida con el servidor SocketIO.')
    logging.info('Conexión establecida con el servidor SocketIO.')

    try:
        initial_metrics = {
            'cpu': get_cpu_usage(),
            'ram': get_memory_usage(),
            'disk': get_disk_usage(),
            'network': {
                'upload_speed': get_network_speed()[0],
                'download_speed': get_network_speed()[1]
            },
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        sio.emit('monitor_metrics', initial_metrics, namespace='/monitor')
        append_to_log(
            METRICS_LOG_FILE,
            f"Métricas iniciales enviadas a las {initial_metrics['timestamp']}:\n{initial_metrics}\n"
        )
        print(f"Métricas iniciales enviadas al servidor a las {initial_metrics['timestamp']}")
    except Exception as e:
        logging.error(f"Error al enviar métricas iniciales: {e}")

@sio.event(namespace='/monitor')
def disconnect():
    """Evento al desconectarse del servidor SocketIO en /monitor."""
    print('Desconectado del servidor SocketIO (/monitor).')
    logging.info('Desconectado del servidor SocketIO (/monitor).')

@sio.on('monitor_response', namespace='/monitor')
def on_monitor_response(data):
    """
    Evento que recibe la respuesta del servidor tras enviar métricas
    en el namespace /monitor.
    """
    try:
        message = (
            f"Respuesta del servidor (/monitor) a las "
            f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {data}"
        )
        print(message)
        append_to_log(METRICS_LOG_FILE, message + '\n')
        logging.info(message)
    except Exception as e:
        logging.error(f"Error al procesar monitor_response: {e}")

# -----------------------------------------------------------------------------
# Eventos SocketIO para /audio_events
# -----------------------------------------------------------------------------

@sio.on('audio_transmission_received', namespace='/audio_events')
def on_audio_transmission_received(data):
    """
    Evento que se dispara cuando se detecta (por cualquier medio) una transmisión de audio.
    
    Ahora solo registramos el evento sin modificar la transmisión en curso:
    - Escribimos en audio_events.log la información básica de la transmisión detectada.
    - No invocamos on_transmission_started (para evitar bloqueos o duplicados).
    """
    try:
        node_id = data.get('node_id')
        user = data.get('user', 'Desconocido')
        logging.debug(f"on_audio_transmission_received para el nodo {node_id}, usuario: {user}")

        # Registramos que se recibió la transmisión sin interferir con la lógica ya existente.
        detection_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        message = (
            "[Detección de Transmisión de Audio]\n"
            f"Nodo: {node_id}\n"
            f"Usuario: {user}\n"
            f"Hora de detección: {detection_time}\n"
            "=============================================================\n\n"
        )
        append_to_log(AUDIO_EVENTS_LOG_FILE, message)
        logging.info(f"Transmisión detectada para el nodo {node_id}, usuario: {user}.")

    except Exception as e:
        logging.error(f"Error en on_audio_transmission_received: {e}", exc_info=True)

@sio.on('transmission_started', namespace='/audio_events')
def on_transmission_started(data):
    """
    Maneja el inicio de la transmisión de audio en /audio_events.
    - Limpia la lista de métricas y arranca el hilo de captura.
    - Almacena la hora de inicio y el usuario.
    """
    node_id = data.get('node_id')
    user = data.get('user')

    # Si el nodo ya está en transmissions_data, significa que ya hay una transmisión en curso
    if node_id in transmissions_data:
        logging.warning(f"Ya existe una transmisión en curso para el nodo {node_id}. No se inicia otra.")
        return

    start_time = datetime.now()
    transmissions_data[node_id] = {
        'start_time': start_time,
        'user': user
    }
    logging.info(
        f"Transmisión iniciada. Nodo: {node_id}, Usuario: {user}, "
        f"Hora de inicio: {start_time} (almacenado en transmissions_data)."
    )

    with metrics_lock:
        transmission_metrics.clear()
        transmission_event.clear()

    transmission_thread = Thread(target=transmission_metrics_collector)
    transmission_thread.start()

@sio.on('transmission_ended', namespace='/audio_events')
def on_transmission_ended(data):
    """
    Maneja el fin de la transmisión de audio en /audio_events.
    - Detiene la captura de métricas.
    - Registra la información en audio_events.log.
    """
    try:
        node_id = data.get('node_id')
        logging.debug(f"on_transmission_ended triggered para el nodo {node_id}. Datos recibidos: {data}")

        # Obtenemos user y hora de inicio de transmissions_data, si existe
        if node_id in transmissions_data:
            start_time = transmissions_data[node_id]['start_time']
            user = transmissions_data[node_id]['user']
            del transmissions_data[node_id]
        else:
            start_time = None
            user = 'Desconocido'
            logging.warning(f"No se encontró información de inicio para el nodo {node_id} en transmissions_data.")
        
        # Detener la captura
        transmission_event.set()
        end_time = datetime.now()

        if start_time:
            duration_seconds = (end_time - start_time).total_seconds()
        else:
            duration_seconds = data.get('duration', 0)

        # Resumen de métricas
        with metrics_lock:
            if transmission_metrics:
                avg_cpu = sum(m['cpu'] for m in transmission_metrics) / len(transmission_metrics)
                avg_ram = sum(m['ram'] for m in transmission_metrics) / len(transmission_metrics)
                avg_disk = sum(m['disk'] for m in transmission_metrics) / len(transmission_metrics)
                avg_upload = sum(m['upload_speed'] for m in transmission_metrics) / len(transmission_metrics)
                avg_download = sum(m['download_speed'] for m in transmission_metrics) / len(transmission_metrics)

                metrics_summary = (
                    "[Transmisión de Audio]\n"
                    f"Usuario: {user}\n"
                    f"Hora de inicio: {start_time.strftime('%Y-%m-%d %H:%M:%S') if start_time else 'Desconocida'}\n"
                    f"Hora de fin: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"Duración: {duration_seconds:.2f} segundos\n"
                    f"- Uso promedio de CPU: {avg_cpu:.2f}%\n"
                    f"- Uso promedio de RAM: {avg_ram:.2f} MB\n"
                    f"- Uso promedio de Disco: {avg_disk:.2f}%\n"
                    f"- Velocidad promedio de Subida: {avg_upload:.2f} MB/s\n"
                    f"- Velocidad promedio de Bajada: {avg_download:.2f} MB/s\n\n"
                    "=============================================================\n\n"
                )
                logging.debug(f"Resumen de métricas (transmisión) para nodo {node_id}:\n{metrics_summary}")
                append_to_log(AUDIO_EVENTS_LOG_FILE, metrics_summary)
            else:
                metrics_summary = (
                    "[Transmisión de Audio]\n"
                    f"Usuario: {user}\n"
                    f"Hora de inicio: {start_time.strftime('%Y-%m-%d %H:%M:%S') if start_time else 'Desconocida'}\n"
                    f"Hora de fin: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"Duración: {duration_seconds:.2f} segundos\n"
                    f"- No se recopilaron métricas durante la transmisión.\n\n"
                    "=============================================================\n\n"
                )
                logging.warning("No se recopilaron métricas durante la transmisión.")
                append_to_log(AUDIO_EVENTS_LOG_FILE, metrics_summary)

    except Exception as e:
        logging.error(f"Error en on_transmission_ended: {e}", exc_info=True)

# -----------------------------------------------------------------------------
# Eventos de guardado
# -----------------------------------------------------------------------------
@sio.on('save_started', namespace='/audio_events')
def on_save_started(data):
    """
    Evento que indica el inicio de la operación de guardado de audio en /audio_events.
    - Limpia la lista de métricas y arranca el hilo de captura.
    - Almacena la hora de inicio y el usuario.
    """
    node_id = data.get('node_id')
    user = data.get('user')

    start_time = datetime.now()
    saves_data[node_id] = {
        'start_time': start_time,
        'user': user
    }
    logging.info(
        f"Guardado de audio iniciado. Nodo: {node_id}, Usuario: {user}, "
        f"Hora de inicio: {start_time} (almacenado en saves_data)."
    )

    with metrics_lock:
        save_metrics.clear()
        save_event.clear()

    save_thread = Thread(target=save_metrics_collector)
    save_thread.start()

@sio.on('save_ended', namespace='/audio_events')
def on_save_ended(data):
    """
    Evento que indica el fin del guardado de audio en /audio_events.
    - Detiene la captura de métricas.
    - No se escriben datos en audio_events.log (solo logs internos).
    """
    try:
        node_id = data.get('node_id')
        logging.debug(f"on_save_ended triggered para el nodo {node_id}. Datos recibidos: {data}")

        if node_id in saves_data:
            start_time = saves_data[node_id]['start_time']
            user = saves_data[node_id]['user']
            del saves_data[node_id]
        else:
            start_time = None
            user = 'Desconocido'
            logging.warning(f"No se encontró información de inicio para el nodo {node_id} en saves_data.")

        save_event.set()
        end_time = datetime.now()
        
        if start_time:
            duration_seconds = (end_time - start_time).total_seconds()
        else:
            duration_seconds = data.get('duration', 0)

        filename = data.get('filename')
        if filename and os.path.exists(filename):
            audio_size_mb = os.path.getsize(filename) / (1024 * 1024)
        else:
            audio_size_mb = 0.0
            logging.warning("No se encontró el archivo de audio o no se proporcionó el nombre de archivo.")

        with metrics_lock:
            if save_metrics:
                avg_ram = sum(m['ram'] for m in save_metrics) / len(save_metrics)
                avg_upload = sum(m['upload_speed'] for m in save_metrics) / len(save_metrics)
                avg_download = sum(m['download_speed'] for m in save_metrics) / len(save_metrics)

                metrics_summary = (
                    "[Guardado de Transmisión]\n"
                    f"Usuario: {user}\n"
                    f"Hora de inicio: {start_time}\n"
                    f"Hora de fin: {end_time}\n"
                    f"Duración: {duration_seconds:.2f} segundos\n"
                    f"Tamaño del archivo: {audio_size_mb:.2f} MB\n"
                    f"- Uso promedio de RAM: {avg_ram:.2f} MB\n"
                    f"- Velocidad promedio de Subida: {avg_upload:.2f} MB/s\n"
                    f"- Velocidad promedio de Bajada: {avg_download:.2f} MB/s\n"
                )
                logging.debug(f"Resumen de métricas (guardado) para nodo {node_id}:\n{metrics_summary}")
            else:
                metrics_summary = (
                    "[Guardado de Transmisión]\n"
                    f"Usuario: {user}\n"
                    f"Hora de inicio: {start_time}\n"
                    f"Hora de fin: {end_time}\n"
                    f"Duración: {duration_seconds:.2f} segundos\n"
                    f"Tamaño del archivo: {audio_size_mb:.2f} MB\n"
                    f"- No se recopilaron métricas durante el guardado.\n"
                )
                logging.warning("No se recopilaron métricas durante el guardado.")
                logging.debug(f"Resumen de métricas (guardado) para nodo {node_id}:\n{metrics_summary}")

    except Exception as e:
        logging.error(f"Error en on_save_ended: {e}", exc_info=True)

# -----------------------------------------------------------------------------
# Manejo de Señales para una Terminación Graceful
# -----------------------------------------------------------------------------
def signal_handler(signum, frame):
    """
    Maneja señales de terminación para desconectar el cliente SocketIO y
    finalizar el script de manera ordenada.
    """
    logging.info(f"Señal de terminación recibida: {signum}. Cerrando el script de manera ordenada.")
    print("\nCerrando el script de manera ordenada...")
    shutdown_event.set()

signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # Señal de terminación

# -----------------------------------------------------------------------------
# EJECUCIÓN PRINCIPAL DEL SCRIPT
# -----------------------------------------------------------------------------
if __name__ == '__main__':
    # Aseguramos la existencia de archivos de log
    for log_file in [METRICS_LOG_FILE, AUDIO_EVENTS_LOG_FILE, 'monitor_debug.log']:
        if not os.path.exists(log_file):
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("")

    # Conectarnos al servidor SocketIO
    connect_to_server()

    # Bucle principal:
    # - Cada 1 minuto se envían métricas al servidor
    # - Cada 24 horas se genera un informe de métricas
    try:
        while not shutdown_event.is_set():
            try:
                time.sleep(60)  # Espera 1 minuto
                current_time = datetime.now()

                # Informe diario
                if (current_time - last_report_time) >= timedelta(seconds=REPORT_INTERVAL):
                    finalize_metrics_report()
                else:
                    # Enviar métricas del sistema al servidor
                    cpu = get_cpu_usage()
                    ram = get_memory_usage()
                    disk = get_disk_usage()
                    upload_speed, download_speed = get_network_speed()
                    timestamp = current_time.strftime('%Y-%m-%d %H:%M:%S')

                    metrics = {
                        'cpu': cpu,
                        'ram': ram,
                        'disk': disk,
                        'network': {
                            'upload_speed': upload_speed,
                            'download_speed': download_speed
                        },
                        'timestamp': timestamp
                    }

                    sio.emit('monitor_metrics', metrics, namespace='/monitor')
                    append_to_log(
                        METRICS_LOG_FILE,
                        f"Métricas enviadas al servidor a las {timestamp}:\n{metrics}\n"
                    )
                    print(f"Métricas enviadas al servidor a las {timestamp}")

            except Exception as e:
                logging.error(f"Error en el bucle principal de envío de métricas: {e}")
                # raise  # Descomentar si quieres forzar la detención ante errores
    finally:
        # Desconexión ordenada
        try:
            if sio.connected:
                sio.disconnect()
                logging.info("Desconectado del servidor SocketIO de manera ordenada.")
                print("Desconectado del servidor SocketIO de manera ordenada.")
        except Exception as e:
            logging.error(f"Error al desconectar del servidor SocketIO: {e}", exc_info=True)
        sys.exit(0)
