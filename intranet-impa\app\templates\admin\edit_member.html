<!-- /app/templates/admin/edit_member.html -->
{% extends 'base.html' %}
{% from "_formhelpers.html" import render_field %}

{% block title %}Editar Miembro{% endblock %}

{% block content %}
<h1>Editar Miembro</h1>
<form method="POST" action="">
    {{ form.hidden_tag() }}
    <!-- Reemplazar full_name por first_name y last_name -->
    <div class="form-group">
        {{ render_field(form.first_name, class="form-control", placeholder="Nombre") }}
    </div>
    <div class="form-group">
        {{ render_field(form.last_name, class="form-control", placeholder="Apellido") }}
    </div>
    <div class="form-group">
        {{ render_field(form.email, class="form-control", placeholder="Correo Electrónico") }}
    </div>
    <div class="form-group">
        {{ render_field(form.phone_number, class="form-control", placeholder="Teléfono") }}
    </div>
    <div class="form-group">
        {{ render_field(form.address, class="form-control", placeholder="Dirección") }}
    </div>
    <div class="form-group">
        {{ render_field(form.date_of_birth, class="form-control") }}
    </div>
    <div class="form-group">
        {{ render_field(form.alergies, class="form-control", placeholder="Alergias") }}
    </div>
    <div class="form-group">
        {{ render_field(form.emergency_contact, class="form-control", placeholder="Contacto de Emergencia") }}
    </div>
    <div class="form-group">
        <label for="member_functions">Funciones asignadas</label>
        {% for subfield in form.member_functions %}
          <div class="form-check">
            {{ subfield(class="form-check-input") }}
            {{ subfield.label(class="form-check-label") }}
          </div>
        {% endfor %}
    </div>
    <button type="submit" class="btn btn-primary">{{ form.submit.label }}</button>
</form>
{% endblock %}
