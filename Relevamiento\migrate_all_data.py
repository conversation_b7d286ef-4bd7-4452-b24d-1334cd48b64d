#!/usr/bin/env python3
# --- Archivo: migrate_all_data.py ---
# Script para migrar todos los datos del backup a la nueva estructura

import os
import sqlite3
import shutil
from datetime import datetime

def migrate_all_data():
    """Migrar todos los datos del backup a la nueva base de datos."""
    print("🔄 Migrando todos los datos del backup...")

    backup_path = "instance/app.db.bkp"
    current_path = "instance/app.db"

    # Hacer backup de seguridad
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    safety_backup = f"safety_backup_{timestamp}.db"

    if os.path.exists(current_path):
        shutil.copy2(current_path, safety_backup)
        print(f"✅ Backup de seguridad: {safety_backup}")

    try:
        # Conectar a ambas bases de datos
        backup_conn = sqlite3.connect(backup_path)
        backup_cursor = backup_conn.cursor()

        current_conn = sqlite3.connect(current_path)
        current_cursor = current_conn.cursor()

        print("📊 Extrayendo datos del backup...")

        # Extraer usuarios del backup
        backup_cursor.execute("SELECT * FROM user;")
        backup_users = backup_cursor.fetchall()

        # Obtener estructura de usuarios del backup
        backup_cursor.execute("PRAGMA table_info(user);")
        backup_user_columns = [col[1] for col in backup_cursor.fetchall()]

        print(f"👥 Usuarios en backup: {len(backup_users)}")
        print(f"📋 Columnas user backup: {backup_user_columns}")

        # Extraer puntos
        backup_cursor.execute("SELECT * FROM point;")
        backup_points = backup_cursor.fetchall()

        backup_cursor.execute("PRAGMA table_info(point);")
        backup_point_columns = [col[1] for col in backup_cursor.fetchall()]

        print(f"📍 Puntos en backup: {len(backup_points)}")
        print(f"📋 Columnas point backup: {backup_point_columns}")

        # Extraer imágenes
        backup_cursor.execute("SELECT * FROM image;")
        backup_images = backup_cursor.fetchall()

        backup_cursor.execute("PRAGMA table_info(image);")
        backup_image_columns = [col[1] for col in backup_cursor.fetchall()]

        print(f"🖼️  Imágenes en backup: {len(backup_images)}")
        print(f"📋 Columnas image backup: {backup_image_columns}")

        # Extraer cámaras
        backup_cursor.execute("SELECT * FROM camera;")
        backup_cameras = backup_cursor.fetchall()

        backup_cursor.execute("PRAGMA table_info(camera);")
        backup_camera_columns = [col[1] for col in backup_cursor.fetchall()]

        print(f"📷 Cámaras en backup: {len(backup_cameras)}")
        print(f"📋 Columnas camera backup: {backup_camera_columns}")

        # Limpiar tablas actuales
        print("\n🧹 Limpiando tablas actuales...")
        current_cursor.execute("DELETE FROM user_permissions;")
        current_cursor.execute("DELETE FROM user_city_permissions;")
        current_cursor.execute("DELETE FROM user_source_permissions;")
        current_cursor.execute("DELETE FROM camera;")
        current_cursor.execute("DELETE FROM image;")
        current_cursor.execute("DELETE FROM point;")
        current_cursor.execute("DELETE FROM user;")

        # Migrar usuarios
        print("\n👥 Migrando usuarios...")

        for user_row in backup_users:
            user_data = dict(zip(backup_user_columns, user_row))

            # Insertar usuario con nueva estructura
            current_cursor.execute('''
                INSERT INTO user (id, username, email, password_hash, role, is_active, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, 'administrador', TRUE, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (
                user_data.get('id'),
                user_data.get('username'),
                user_data.get('email'),
                user_data.get('password_hash')
            ))

        print(f"✅ {len(backup_users)} usuarios migrados")

        # Migrar puntos
        print("\n📍 Migrando puntos...")

        for point_row in backup_points:
            point_data = dict(zip(backup_point_columns, point_row))

            # Insertar punto
            current_cursor.execute('''
                INSERT INTO point (id, name, latitude, longitude, status, city, source, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (
                point_data.get('id'),
                point_data.get('name'),
                point_data.get('latitude'),
                point_data.get('longitude'),
                point_data.get('status', 'azul'),
                point_data.get('city'),
                point_data.get('source'),
                point_data.get('description')
            ))

        print(f"✅ {len(backup_points)} puntos migrados")

        # Migrar imágenes
        print("\n🖼️  Migrando imágenes...")

        for image_row in backup_images:
            image_data = dict(zip(backup_image_columns, image_row))

            # Insertar imagen
            current_cursor.execute('''
                INSERT INTO image (id, filename, point_id, user_id, annotations_json, created_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                image_data.get('id'),
                image_data.get('filename'),
                image_data.get('point_id'),
                image_data.get('user_id'),
                image_data.get('annotations_json')
            ))

        print(f"✅ {len(backup_images)} imágenes migradas")

        # Migrar cámaras
        print("\n📷 Migrando cámaras...")

        for camera_row in backup_cameras:
            camera_data = dict(zip(backup_camera_columns, camera_row))

            # Insertar cámara
            current_cursor.execute('''
                INSERT INTO camera (id, point_id, type, direction, photo_filename, latitude, longitude, location_source, location_accuracy, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (
                camera_data.get('id'),
                camera_data.get('point_id'),
                camera_data.get('type', 'otra'),
                camera_data.get('direction'),
                camera_data.get('photo_filename'),
                camera_data.get('latitude'),
                camera_data.get('longitude'),
                camera_data.get('location_source'),
                camera_data.get('location_accuracy')
            ))

        print(f"✅ {len(backup_cameras)} cámaras migradas")

        # Crear usuario admin adicional si no existe
        current_cursor.execute("SELECT COUNT(*) FROM user WHERE username = 'admin';")
        admin_exists = current_cursor.fetchone()[0] > 0

        if not admin_exists:
            from werkzeug.security import generate_password_hash
            password_hash = generate_password_hash('isaias52')
            current_cursor.execute('''
                INSERT INTO user (username, password_hash, role, is_active, created_at, updated_at)
                VALUES ('admin', ?, 'administrador', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (password_hash,))
            print("✅ Usuario admin adicional creado")

        # Actualizar secuencias
        current_cursor.execute("UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM user) WHERE name = 'user';")
        current_cursor.execute("UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM point) WHERE name = 'point';")
        current_cursor.execute("UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM image) WHERE name = 'image';")
        current_cursor.execute("UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM camera) WHERE name = 'camera';")

        # Commit cambios
        current_conn.commit()

        # Verificar migración
        print("\n📊 Verificando migración...")

        current_cursor.execute("SELECT COUNT(*) FROM user;")
        user_count = current_cursor.fetchone()[0]

        current_cursor.execute("SELECT COUNT(*) FROM point;")
        point_count = current_cursor.fetchone()[0]

        current_cursor.execute("SELECT COUNT(*) FROM image;")
        image_count = current_cursor.fetchone()[0]

        current_cursor.execute("SELECT COUNT(*) FROM camera;")
        camera_count = current_cursor.fetchone()[0]

        print(f"  👥 Usuarios: {user_count}")
        print(f"  📍 Puntos: {point_count}")
        print(f"  🖼️  Imágenes: {image_count}")
        print(f"  📷 Cámaras: {camera_count}")

        # Mostrar algunos usuarios
        current_cursor.execute("SELECT username, role FROM user;")
        users = current_cursor.fetchall()
        print(f"\n👥 Usuarios migrados:")
        for user in users:
            print(f"  - {user[0]} ({user[1]})")

        # Mostrar algunas ciudades
        current_cursor.execute("SELECT DISTINCT city FROM point WHERE city IS NOT NULL LIMIT 10;")
        cities = current_cursor.fetchall()
        print(f"\n🏙️  Ciudades encontradas:")
        for city in cities:
            print(f"  - {city[0]}")

        # Mostrar algunas fuentes
        current_cursor.execute("SELECT DISTINCT source FROM point WHERE source IS NOT NULL LIMIT 10;")
        sources = current_cursor.fetchall()
        print(f"\n📡 Fuentes encontradas:")
        for source in sources:
            print(f"  - {source[0]}")

        # Cerrar conexiones
        backup_conn.close()
        current_conn.close()

        return True

    except Exception as e:
        print(f"❌ Error en migración: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal."""
    print("🔄 Migrador Completo de Datos")
    print("=" * 35)

    print("⚠️  Este proceso va a:")
    print("  1. Hacer backup de la base de datos actual")
    print("  2. Migrar TODOS los datos del backup:")
    print("     - 6 usuarios")
    print("     - 1118 puntos")
    print("     - 14 imágenes")
    print("     - 18 cámaras")
    print("  3. Mantener la nueva estructura de permisos")
    print("  4. Agregar usuario admin con contraseña isaias52")

    response = input("\n¿Continuar con la migración? (s/n): ").strip().lower()

    if response in ['s', 'si', 'y', 'yes']:
        if migrate_all_data():
            print("\n🎉 ¡Migración completada exitosamente!")
            print("\n🚀 Próximos pasos:")
            print("   1. Reiniciar aplicación: systemctl restart relevamiento")
            print("   2. Verificar datos: python3 check_db.py")
            print("   3. Probar aplicación web")
            print("   4. Crear templates HTML para gestión de usuarios")
            print("\n🔑 Credenciales disponibles:")
            print("   - Usuarios originales con sus contraseñas")
            print("   - admin / isaias52 (nuevo usuario administrador)")
        else:
            print("\n❌ Error en la migración")
    else:
        print("❌ Migración cancelada")

if __name__ == "__main__":
    main()
