// audio.js

import { socket, safeEmit } from './socketConnection.js';

// Obtener elementos del DOM
const transmitAudioButton = document.getElementById('transmitAudio');
const username = document.body.dataset.username;
const nodeId = parseInt(document.body.dataset.nodeId);

// --- Función para mostrar notificaciones (SIMPLIFICADA) ---
function showNotification(title, body, icon) {
    if (Notification.permission === "granted") { // SOLO si ya tenemos permiso
        new Notification(title, { body, icon });
    }
}

// Variables
let mediaRecorder;
let audioChunks = [];

// Importar URLs de las imágenes (asumiendo que están en el mismo directorio)
const rojoImageUrl = "/static/rojo.png";  // Ajusta la ruta si es necesario
const verdeImageUrl = "/static/verde.png"; // Ajusta la ruta si es necesario

// --- Grabación y Transmisión de Audio ---
async function startRecording() {
    transmitAudioButton.src = rojoImageUrl;
    // Convertir nodeId a string para evitar problemas de coincidencia de room
    safeEmit('audio_start', { user: username, node_id: String(nodeId) });
    if (!navigator.mediaDevices?.getUserMedia) {
        console.error('getUserMedia is not supported.');
        alert('getUserMedia is not supported.');
        return;
    }
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorder = new MediaRecorder(stream);
        console.log("Mime type del MediaRecorder:", mediaRecorder.mimeType);
        mediaRecorder.ondataavailable = function (event) {
            if (event.data.size > 0) {
                audioChunks.push(event.data);
            }
        };
        mediaRecorder.onstop = async function () {
            if (audioChunks.length > 0) {
                const mimeType = mediaRecorder.mimeType || 'audio/wav';
                const audioBlob = new Blob(audioChunks, { type: mimeType });
                audioChunks = [];
                const reader = new FileReader();
                reader.onload = function () {
                    const base64AudioMessage = reader.result.split(',')[1];
                    // Asegurarse de enviar node_id como string
                    safeEmit('transmit_audio', {
                        audio: base64AudioMessage,
                        user: username,
                        node_id: String(nodeId),
                        mimeType: mimeType
                    });
                    transmitAudioButton.src = verdeImageUrl;
                    // También convertir nodeId a string aquí
                    safeEmit('audio_end', { user: username, node_id: String(nodeId) });
                };
                reader.readAsDataURL(audioBlob);
            }
        };
        mediaRecorder.start();
    } catch (error) {
        console.error('Error accessing media devices:', error);
        alert('Error accessing media devices: ' + error.message);
    }
}

function stopRecording() {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
        mediaRecorder.stop();
        // Eliminamos emisión redundante de audio_end, ya que se emite en onstop
    }
}

// --- Event listeners para el audio ---
if (transmitAudioButton) { // Verificar que el botón exista antes de añadir event listeners
    transmitAudioButton.addEventListener('mousedown', startRecording);
    transmitAudioButton.addEventListener('mouseup', stopRecording);
    transmitAudioButton.addEventListener('touchstart', function(event) {
        event.preventDefault();
        startRecording();
    });
    transmitAudioButton.addEventListener('touchend', function(event) {
        event.preventDefault();
        stopRecording();
    });
}
// --- Listener para reproducir audios recibidos de otros usuarios ---
socket.on('receive_audio', function(data) {
    console.log("Evento receive_audio recibido:", data);
    const messagesDiv = document.getElementById('messages'); // Obtener messagesDiv DENTRO del listener.
    if (!messagesDiv) {
        console.error("messagesDiv no encontrado en el DOM.");
        return;
    }


    if (data.user !== username) {
        if (!data.audio || data.audio.length === 0) {
            console.error("No se recibió audio válido.");
            return;
        }
        try {
            const byteCharacters = atob(data.audio);
            console.log("Longitud del string decodificado:", byteCharacters.length);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            // Usa el mimeType enviado, o 'audio/wav' por defecto
            const mimeType = data.mimeType || 'audio/wav';
            const audioBlob = new Blob([byteArray], { type: mimeType });
            const audioUrl = URL.createObjectURL(audioBlob);
            console.log("URL del audio generado:", audioUrl);
            const audio = new Audio(audioUrl);
            audio.controls = true; //  Asegurar que los controles sean visibles.

            // Intentar la reproducción, y *después* añadir al DOM:
            audio.play().catch(err => console.error("Error al reproducir el audio:", err));

             const newMessage = document.createElement('p');
             newMessage.className = 'message';
             newMessage.innerHTML = `<strong>${data.user}:</strong> (audio) <span class="timestamp">${new Date().toLocaleTimeString()}</span><br>`;
             newMessage.appendChild(audio); // Añadir el elemento <audio>
             messagesDiv.appendChild(newMessage);
             messagesDiv.scrollTop = messagesDiv.scrollHeight;


        } catch (e) {
            console.error("Error en la conversión del audio:", e);
        }
    }
});

// --- Eventos de audio (inicio/fin/ocupado) ---
socket.on('audio_start', function(data) {
    const audioIcon = document.getElementById(`audio-${data.user}`);
    if (audioIcon) { audioIcon.classList.add('active'); }
    if (data.user !== username) {
        showNotification(
            "Inicio de audio",
            `El usuario ${data.user} ha iniciado transmisión.`,
            "/static/RNCom.ico"
        );
    }
});

socket.on('audio_end', function(data) {
    const audioIcon = document.getElementById(`audio-${data.user}`);
    if (audioIcon) { audioIcon.classList.remove('active'); }
    if (data.user !== username) {
        showNotification(
            "Fin de audio",
            `El usuario ${data.user} ha finalizado transmisión.`,
            "/static/RNCom.ico"
        );
    }
});

socket.on('audio_busy', function (data) {
    alert(`La transmisión de Audio está ocupada por el usuario ${data.user}. Por favor espere.`);
});

socket.on('global_audio_start', function(data) {
    if (data.node_id === String(nodeId)) {
        console.log(`Audio iniciado por ${data.user} en el nodo ${data.node_id}`);
        const userAudioIcon = document.getElementById(`audio-${data.user}`);
        if (userAudioIcon) { userAudioIcon.style.display = 'inline'; }
    }
});

socket.on('global_audio_end', function(data) {
    if (data.node_id === String(nodeId)) {
        console.log(`Audio finalizado por ${data.user} en el nodo ${data.node_id}`);
        const userAudioIcon = document.getElementById(`audio-${data.user}`);
        if (userAudioIcon) { userAudioIcon.style.display = 'none'; }
    }
});

// Este módulo no exporta nada, ya que su funcionalidad
// se ejecuta al cargarse y se maneja internamente.