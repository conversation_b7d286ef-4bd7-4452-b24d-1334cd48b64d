PRAGMA foreign_keys=OFF;
BEGIN TRANSACTION;
CREATE TABLE node (
	id INTEGER NOT NULL, 
	name VARCHAR(150) NOT NULL, 
	parent_id INTEGER, 
	listeners INTEGER, 
	PRIMARY KEY (id), 
	FOREIGN KEY(parent_id) REFERENCES node (id)
);
INSERT INTO node VALUES(1,'Comunicaciones RN',NULL,0);
INSERT INTO node VALUES(2,'Agencia',1,0);
INSERT INTO node VALUES(3,'Policia',1,0);
INSERT INTO node VALUES(4,'Unidad Regional 1',3,0);
INSERT INTO node VALUES(5,'Unidad Regional 2',3,0);
INSERT INTO node VALUES(6,'Unidad Regional 3',3,0);
INSERT INTO node VALUES(7,'Unidad Regional 4',3,0);
INSERT INTO node VALUES(8,'Unidad Regional 5',3,0);
INSERT INTO node VALUES(9,'Unidad Regional 6',3,0);
INSERT INTO node VALUES(10,'<PERSON><PERSON>',8,0);
INSERT INTO node VALUES(11,'Campo Grande',8,0);
INSERT INTO node VALUES(12,'Cinco Saltos',8,0);
INSERT INTO node VALUES(13,'Cipolletti',8,0);
INSERT INTO node VALUES(14,'Fernández Oro',8,0);
INSERT INTO node VALUES(15,'General Roca',5,0);
INSERT INTO node VALUES(16,'Allen',5,0);
INSERT INTO node VALUES(17,'Cervantes',5,0);
INSERT INTO node VALUES(18,'Ingeniero Huergo',5,0);
INSERT INTO node VALUES(19,'Mainqué',5,0);
INSERT INTO node VALUES(20,'General Enrique Godoy',5,0);
INSERT INTO node VALUES(21,'Villa Regina',5,0);
INSERT INTO node VALUES(22,'Chichinales',5,0);
INSERT INTO node VALUES(23,'Choele Choel',7,0);
INSERT INTO node VALUES(24,'Pomona',7,0);
INSERT INTO node VALUES(25,'Luis Beltrán',7,0);
INSERT INTO node VALUES(26,'Lamarque',7,0);
INSERT INTO node VALUES(27,'Belisle',7,0);
INSERT INTO node VALUES(28,'Chimpay',7,0);
INSERT INTO node VALUES(29,'San Carlos de Bariloche',6,0);
INSERT INTO node VALUES(30,'Villa Mascardi',6,0);
INSERT INTO node VALUES(31,'Villa Llanquín',6,0);
INSERT INTO node VALUES(32,'Dina Huapi',6,0);
INSERT INTO node VALUES(33,'Pilcaniyeu',6,0);
INSERT INTO node VALUES(34,'Comallo',6,0);
INSERT INTO node VALUES(35,'Ramos Mexía',9,0);
INSERT INTO node VALUES(36,'Valcheta',9,0);
INSERT INTO node VALUES(37,'Sierra Colorada',9,0);
INSERT INTO node VALUES(38,'Los Menucos',9,0);
INSERT INTO node VALUES(39,'Maquinchao',9,0);
INSERT INTO node VALUES(40,'Ingeniero Jacobacci',9,0);
INSERT INTO node VALUES(41,'Viedma',4,0);
INSERT INTO node VALUES(42,'General Conesa',4,0);
INSERT INTO node VALUES(43,'San Antonio Oeste',4,0);
INSERT INTO node VALUES(44,'Las Grutas',4,0);
INSERT INTO node VALUES(45,'Puerto San Antonio Este',4,0);
INSERT INTO node VALUES(46,'Guardia Mitre',4,0);
CREATE TABLE IF NOT EXISTS "user" (
	id INTEGER NOT NULL, 
	username VARCHAR(150) NOT NULL, 
	password VARCHAR(150) NOT NULL, 
	role VARCHAR(50) NOT NULL, 
	node_access VARCHAR(500) NOT NULL, 
	first_name VARCHAR(150) NOT NULL, 
	last_name VARCHAR(150) NOT NULL, 
	badge_number VARCHAR(150) NOT NULL, 
	organization VARCHAR(150) NOT NULL, 
	regional_units VARCHAR(500), 
	has_police_access BOOLEAN, 
	session_token VARCHAR(150), 
	PRIMARY KEY (id), 
	CONSTRAINT uq_user_session_token UNIQUE (session_token), 
	UNIQUE (username)
);
INSERT INTO user VALUES(1,'admin','pbkdf2:sha256:1000000$yh2mXHD7fKOVL4vt$59ed7be78b33461ee1e4bae6d9e3f25d990b607c5618fdaf24eb76b686d3c2c6','admin','Comunicaciones RN','Admin','User','0001','Policia','',0,'26c78a64-fc38-4289-8654-88f3f82b23ef');
INSERT INTO user VALUES(2,'joacoabe','pbkdf2:sha256:600000$snaEe3Fj86YY5Gob$3d5fd555502dc7476c5d46230b828b7c33e00ea8681999387003ef465641d6ca','admin','Comunicaciones RN,Agencia,Policia','Joaquin Abel','Gonzalez','8080','Policia','',1,'fe4b6667-398a-472c-9400-5dcba38f4387');
INSERT INTO user VALUES(3,'viedma','pbkdf2:sha256:600000$5X8d5syWstJV0nhN$6b503b4bf4d6c069a6b29b447e11046e700d082cb7419fc12bbb3fc55bbc5925','standard_user','Viedma','viedma','123','123','Policia','',0,'8dd04e99-3f95-4c64-a8c7-281baabe4562');
INSERT INTO user VALUES(4,'ur1','pbkdf2:sha256:600000$9EkNogt499TDJn9M$f838a6a4d1574c4d11bfb6f8f11b0eaf3f44132cd77b0d65206fcc94280e274f','standard_user','Unidad Regional 1','ur1','123','123','Policia','Unidad Regional 1',0,'b2b26d20-4623-476a-88f7-56835fd56286');
INSERT INTO user VALUES(5,'ministro','pbkdf2:sha256:1000000$zEh0s8dE3zZJjUjr$f78567a36296f29e8e5898ae9acd0169d914c7468adcce51e9a33d050628a27a','standard_user','Comunicaciones RN,Policia','Ministro','Seguridad','1111','Policia','',1,NULL);
INSERT INTO user VALUES(6,'jefa','pbkdf2:sha256:1000000$N3TnnXkk42mc9SpZ$511fb24b8f67f7ccc28ef18e16c29c0ef8d7d8414fd4b9b9fafd3b95f17f01a3','standard_user','Policia','jefa','policia','2222','Policia','',1,NULL);
INSERT INTO user VALUES(7,'subjefe','pbkdf2:sha256:1000000$iHynsqHCgL6YBkrN$c8082042f03f8bf8139c54d470456e46e503effec73fc46d34db45bc37bd540f','standard_user','Policia','subjefe','policia','3333','Policia','',1,NULL);
INSERT INTO user VALUES(8,'Arcajo','pbkdf2:sha256:1000000$EScFAU83oX8wdX7A$ebe74e79f852bde63cf59207a23ab0603687ff748a664a3b1f1454a9578a0486','admin','Comunicaciones RN,Policia,Viedma','Jose','Arcajo','55555','Policia','Unidad Regional 1,Unidad Regional 2,Unidad Regional 3,Unidad Regional 4,Unidad Regional 5,Unidad Regional 6',1,NULL);
INSERT INTO user VALUES(9,'dfuertes','pbkdf2:sha256:1000000$DJHUJ1lhXW4vhGIc$893006410c1701c0bb08c4818af448e96fa706a4c809ac9bfc5f7469e8805827','standard_user','Comunicaciones RN,Agencia,Policia','Diego','Fuertes','1234567','Policia','Unidad Regional 1,Unidad Regional 2,Unidad Regional 3,Unidad Regional 4,Unidad Regional 5,Unidad Regional 6',1,NULL);
INSERT INTO user VALUES(10,'wmorel','pbkdf2:sha256:1000000$mcOgYJtTc5CIHv6b$88c71555e2e6842d29b880b010b32b072fb8c204613aaba47e16ef363202d00a','standard_user','Comunicaciones RN,Agencia,Policia','Wenceslao Javier','Morel Hereria','123456','Policia','Unidad Regional 1,Unidad Regional 2,Unidad Regional 3,Unidad Regional 4,Unidad Regional 5,Unidad Regional 6',1,NULL);
INSERT INTO user VALUES(11,'mfarid','pbkdf2:sha256:1000000$Y0gjihzNBhCWOUg1$8ec292504681ee63d6ee1d96bcb08cd521205e4cd94bc9301a543e8374c538e9','standard_user','Comunicaciones RN,Agencia,Policia','Farid','Mercado','1234567','Policia','Unidad Regional 1,Unidad Regional 2,Unidad Regional 3,Unidad Regional 4,Unidad Regional 5,Unidad Regional 6',1,NULL);
INSERT INTO user VALUES(12,'broman','pbkdf2:sha256:1000000$j4QERGT7kZjg5Qbj$bb9f6eb07dd44871c1bf77644fdbf256cd62fb6251b2a28d3f5a905686b17fcb','standard_user','Comunicaciones RN,Agencia,Policia','Bruno Fabricio','Roman','12345678','Policia','Unidad Regional 1,Unidad Regional 2,Unidad Regional 3,Unidad Regional 4,Unidad Regional 5,Unidad Regional 6',1,NULL);
INSERT INTO user VALUES(13,'jbruno','pbkdf2:sha256:1000000$x7kExXZFXtsLjNLT$393203933a8c88d6b8605e6bfac89717849b79e7ebb3166d2ec7074c235d1951','standard_user','Comunicaciones RN,Agencia,Policia','Jorge','Bruno','1234567','Policia','Unidad Regional 1,Unidad Regional 2,Unidad Regional 3,Unidad Regional 4,Unidad Regional 5,Unidad Regional 6',1,NULL);
CREATE TABLE alembic_version (
	version_num VARCHAR(32) NOT NULL, 
	CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);
INSERT INTO alembic_version VALUES('c9236737a1de');
COMMIT;
