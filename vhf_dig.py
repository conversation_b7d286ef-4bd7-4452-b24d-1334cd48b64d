#!/usr/bin/env python3
# vhf_dig.py

import json
import pyaudio
import wave
import io
import base64
import numpy as np
import time
import socketio
import threading
import os

sio = socketio.Client()

def load_config():
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config
    except FileNotFoundError:
        print("Error: No se encontró ptt_client_config.json.")
        return {}

def monitor_config_file(config, config_lock):
    last_modified = os.path.getmtime('config.json')
    while True:
        time.sleep(2)
        current_modified = os.path.getmtime('config.json')
        if current_modified != last_modified:
            with config_lock:
                new_config = load_config()
                config.update(new_config)
            last_modified = current_modified

def capture_and_send_audio(config, config_lock):
    p = pyaudio.PyAudio()
    stream = p.open(format=pyaudio.paInt16,
                    channels=1,
                    rate=8000,
                    input=True,
                    input_device_index=config.get('input_device_index', 0),
                    frames_per_buffer=512)
    volume_level_threshold = config.get('volume_level', 5)
    is_transmitting = False

    def send_audio(data):
        wav_buffer = io.BytesIO()
        wf = wave.open(wav_buffer, 'wb')
        wf.setnchannels(1)
        wf.setsampwidth(p.get_sample_size(pyaudio.paInt16))
        wf.setframerate(8000)
        wf.writeframes(data)
        wf.close()
        wav_buffer.seek(0)
        audio_base64 = base64.b64encode(wav_buffer.read()).decode('utf-8')
        sio.emit('transmit_audio', {
            'audio': audio_base64,
            'user': config['username'],
            'node_id': config['node_id']
        })

    try:
        while True:
            data = stream.read(2048)
            audio_data = np.frombuffer(data, dtype=np.int16)
            volume = np.linalg.norm(audio_data) / len(audio_data)
            with config_lock:
                volume_level_threshold = config.get('volume_level', 5)
            if volume > volume_level_threshold and not is_transmitting:
                is_transmitting = True
            if is_transmitting:
                send_audio(data)
            if is_transmitting and volume <= volume_level_threshold:
                is_transmitting = False
            time.sleep(0.1)
    except KeyboardInterrupt:
        pass
    finally:
        stream.stop_stream()
        stream.close()
        p.terminate()

def connect_to_socket(config):
    @sio.event
    def connect():
        pass
    @sio.event
    def disconnect():
        pass
    @sio.event
    def connect_error(data):
        print("Error al conectar con el servidor:", data)
    sio.connect(f"wss://{config['node_url']}",
                headers={'node_id': config['node_id'], 'username': config['username']},
                namespaces=['/node'])

if __name__ == '__main__':
    config = load_config()
    config_lock = threading.Lock()
    if 'username' not in config or 'password' not in config or 'node_url' not in config or 'node_id' not in config:
        print("Error: Configuración incompleta en ptt_client_config.json.")
        exit(1)
    threading.Thread(target=monitor_config_file, args=(config, config_lock), daemon=True).start()
    connect_to_socket(config)
    capture_and_send_audio(config, config_lock)
