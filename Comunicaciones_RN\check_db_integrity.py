import sqlite3

# Ruta a la base de datos
db_path = 'instance/vhf.db'

def check_db_integrity(db_path):
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Verificar la integridad de la base de datos
        cursor.execute("PRAGMA integrity_check;")
        result = cursor.fetchone()

        if result[0] == "ok":
            print("La base de datos está íntegra.")
        else:
            print("Problemas de integridad encontrados:", result[0])

        # Listar las tablas en la base de datos
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()

        if tables:
            print("Tablas en la base de datos:")
            for table in tables:
                print(table[0])
        else:
            print("No se encontraron tablas en la base de datos.")

        # Cerrar la conexión
        conn.close()

    except sqlite3.Error as e:
        print(f"Error al conectar a la base de datos: {e}")

# Verificar la integridad de la base de datos
check_db_integrity(db_path)
