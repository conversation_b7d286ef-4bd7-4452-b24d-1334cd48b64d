# monitor.py

import threading
from vhf_dig import capture_audio
from dig_vhf import listen_to_events

def monitor_audio(config):
    # Inicia hilos para monitorizar tanto el micrófono como los eventos
    audio_thread = threading.Thread(target=capture_audio, args=(config,))
    events_thread = threading.Thread(target=listen_to_events, args=(config,))
    
    audio_thread.start()
    events_thread.start()

    audio_thread.join()  # Asegura que los hilos corran indefinidamente
    events_thread.join()
