<!-- /app/templates/_formhelpers.html -->
{% macro render_field(field) %}
  <dt>{{ field.label }}
  <dd>{{ field(**kwargs)|safe }}
  {% if field.errors %}
    <ul class=errors>
    {% for error in field.errors %}
      <li>{{ error }}</li>
    {% endfor %}
    </ul>
  {% endif %}
  </dd>
{% endmacro %}

{% macro format_participant(participant, current_user) %}
  {% if participant.role == 'pastorado' and participant.pastor and participant.pastor.grado %}
    Pastor {{ participant.pastor.grado }} {{ participant.full_name }}
  {% elif participant.church and current_user.church and participant.church.id == current_user.church.id and participant.member %}
    {% set ns = namespace(is_pastora=false) %}
    {% for func in participant.member.functions %}
      {% if func.name|lower == 'pastora' %}
        {% set ns.is_pastora = true %}
      {% endif %}
    {% endfor %}
    {% if ns.is_pastora %}
      Pastora {{ participant.full_name }}
    {% else %}
      <PERSON><PERSON>/a {{ participant.full_name }}
    {% endif %}
  {% else %}
    {{ participant.full_name }}
  {% endif %}
{% endmacro %}
