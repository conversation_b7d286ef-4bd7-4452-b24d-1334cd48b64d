#!/usr/bin/env python3
# client.py

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import SocketIO, emit # Añadir emit si lo usas directamente desde el servidor Flask
from flask_cors import CORS
import json
import os

# Intenta importar ptt_control, pero maneja si no está
try:
    from ptt_control import set_ptt
    PTT_CONTROL_AVAILABLE = True
    print("INFO: Módulo 'ptt_control' cargado exitosamente.")
except ImportError:
    PTT_CONTROL_AVAILABLE = False
    print("ADVERTENCIA: El módulo 'ptt_control' no se encontró o no se pudo importar. La funcionalidad PTT no estará disponible.")
    def set_ptt(state, config, ser=None):
        print(f"ADVERTENCIA: set_ptt llamado con estado {state} pero ptt_control no está disponible.")
        return None

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_FILE_PATH = os.path.join(BASE_DIR, 'config.json')
print(f"INFO (cliente.py): El archivo de configuración se buscará/guardará en: {CONFIG_FILE_PATH}")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key_change_me_please'

# Configuración CORS para Flask HTTP (rutas normales)
CORS(app, resources={r"/*": {"origins": "*"}})

# Configuración CORS para Flask-SocketIO (conexiones WebSocket)
# Este socketio es para si ESTE MISMO servidor Flask fuera a manejar conexiones Socket.IO.
# Si solo sirve el frontend y el frontend se conecta a OTRO servidor Socket.IO (la VM),
# entonces esta configuración de cors_allowed_origins aquí es menos crítica para ESA conexión externa.
# Sin embargo, es buena práctica tenerla si alguna vez usas Socket.IO en este servidor Flask.
socketio = SocketIO(app, cors_allowed_origins="*")


serial_connection = None

def load_config():
    print(f"DEBUG (cliente.py): Intentando cargar configuración desde {CONFIG_FILE_PATH}")
    if not os.path.exists(CONFIG_FILE_PATH):
        print(f"DEBUG (cliente.py): El archivo de configuración {CONFIG_FILE_PATH} no existe.")
        return None
    try:
        with open(CONFIG_FILE_PATH, 'r', encoding='utf-8') as file:
            config = json.load(file)
            print(f"DEBUG (cliente.py): Configuración cargada exitosamente desde {CONFIG_FILE_PATH}")
            return config
    except Exception as e: # Captura más general para cualquier error de carga/parseo
        print(f"ERROR (cliente.py): Error al cargar/parsear config desde {CONFIG_FILE_PATH}: {e}")
        return None

def save_config_to_file(config_data):
    print(f"DEBUG (cliente.py): Intentando guardar configuración en {CONFIG_FILE_PATH}")
    try:
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=4)
        print(f"DEBUG (cliente.py): Configuración guardada exitosamente en {CONFIG_FILE_PATH}")
        return True
    except PermissionError as e_perm:
        print(f"ERROR CRÍTICO - PERMISSION_ERROR (cliente.py): No se pudo guardar {CONFIG_FILE_PATH}. Detalle: {e_perm}")
        return False
    except Exception as e:
        print(f"ERROR (cliente.py): Ocurrió un error inesperado al guardar la configuración en {CONFIG_FILE_PATH}: {e}")
        return False

@app.route('/')
def index():
    config = load_config()
    if config:
        # Esta es la URL del SERVIDOR SOCKET.IO en la VM
        vm_server_ip_port = config.get('node_url', '************:5000') # Tomar de config.json o default
        socket_io_vm_url = f"https://{vm_server_ip_port}" # El servidor VM usa HTTPS/WSS

        return render_template('index.html',
                               node_id=config.get('node_id', 'N/A'),
                               username=config.get('username', 'N/A'),
                               node_name=config.get('node_name', config.get('node_id', 'N/A')), # Asumir node_name o usar node_id
                               socket_io_server_url=socket_io_vm_url) # <--- PASAR ESTA URL
    else:
        return redirect(url_for('config_form_route')) # Asegúrate que el nombre de la función es correcto

@app.route('/config_form')
def config_form_route():
    # Si necesitas cargar 'input_devices' desde config.py, tendrías que importar esa función.
    # Por ahora, se asume que config_form.html puede manejar input_devices vacío o lo obtienes de otra forma.
    # from config import list_audio_devices # Ejemplo
    # input_devices_list = list_audio_devices() if 'list_audio_devices' in locals() else []
    return render_template('config_form.html', input_devices=[])

@app.route('/save_config', methods=['POST'])
def save_user_config_route():
    print("DEBUG (cliente.py): Recibida solicitud POST en /save_config")
    try:
        current_config = load_config() or {} # Carga config existente o usa dict vacío
        config_data = {
            "username": request.form['username'],
            "password": request.form['password'],
            "node_id": request.form['node_id'],
            "input_device_index": int(request.form['input_device_index']),
            "volume_level": int(request.form['volume_level']),
            "port_number": request.form['port_number'],
            # Preservar node_url de la config existente, o usar el de la VM como default si no existe
            "node_url": current_config.get('node_url', '************:5000')
        }
        if save_config_to_file(config_data):
            return redirect(url_for('index'))
        else:
            return "Error al guardar la configuración. Revise los logs del servidor Flask (consola).", 500
    except Exception as e:
        print(f"ERROR (cliente.py): Error en /save_config: {e}")
        return "Error interno del servidor.", 500

@app.route('/ptt_event', methods=['POST'])
def handle_ptt_event():
    global serial_connection
    data = request.json
    config = load_config()
    if config and data and 'ptt_state' in data:
        if PTT_CONTROL_AVAILABLE:
            serial_connection = set_ptt(data['ptt_state'], config, serial_connection)
        return jsonify({"status": "success", "ptt_state": data['ptt_state']})
    else:
        return jsonify({"status": "error", "message": "Invalid request or config missing"}), 400

# Este es un manejador Socket.IO para ESTE servidor Flask.
# Si el objetivo es solo conectar el frontend a la VM, este manejador aquí
# no se usará para esa conexión VM-frontend.
@socketio.on('connect', namespace='/node') # Ejemplo si tuvieras un namespace '/node' local
def handle_local_connect():
    print('INFO (cliente.py): Cliente conectado al Socket.IO local de este servidor Flask.')
    # emit('my response', {'data': 'Connected to local Flask SocketIO'})

@socketio.on('disconnect', namespace='/node')
def handle_local_disconnect():
    print('INFO (cliente.py): Cliente desconectado del Socket.IO local de este servidor Flask.')

# Este manejador es genérico para 'receive_audio' sin namespace.
# Si tus JS se conectan a la VM, la VM es la que recibirá 'transmit_audio'
# y la VM es la que emitiría 'receive_audio'.
# Este manejador aquí solo se activaría si un cliente Socket.IO se conecta a ESTE servidor Flask
# y emite 'receive_audio'.
@socketio.on('receive_audio')
def handle_local_receive_audio_event(data):
    print(f"INFO (cliente.py): Evento 'receive_audio' recibido en Socket.IO local: {data}")
    # Lógica para procesar/reproducir el audio si este servidor Flask es el destino.
    # Si el audio va a/desde la VM, esta función no es para eso.
    pass

if __name__ == '__main__':
    print("INFO (cliente.py): Iniciando servidor Flask de desarrollo...")
    try:
        # Este socketio.run hace que ESTE servidor Flask sea también un servidor Socket.IO.
        # Los JS de tu frontend se conectarán a la VM, no a este, según tu requerimiento.
        socketio.run(app, debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e_startup:
        print(f"ERROR CRÍTICO INESPERADO AL INICIAR (cliente.py): {e_startup}")