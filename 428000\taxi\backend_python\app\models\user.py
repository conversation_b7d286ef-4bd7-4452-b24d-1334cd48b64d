# Versión corregida de app/models/user.py
import enum # Asegúrate de importar enum de Python
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base

# 1. Definir el Enum de Python PRIMERO
class RoleEnum(str, enum.Enum): # Este es tu Enum de Python para los roles
    USUARIO = "usuario"
    TAXI = "taxi" # Conductor
    OPERADOR = "operador"
    TITULAR = "titular" # Dueño del vehículo
    BASE = "base" # Dueño de la base/central
    ADMINISTRADOR = "administrador"  # Corregido para coincidir con el valor en la base de datos

# 2. Luego definir los modelos que usan este Enum

class User(Base):
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, index=True)
    hashed_password = Column(String, nullable=False)
    phone_number = Column(String, unique=True, index=True, nullable=True)
    is_active = Column(Boolean(), default=True)
    is_superuser = Column(Boolean(), default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # La relación ahora apunta a la clase UserRoleModel
    roles = relationship("UserRoleModel", secondary="user_roles_association", back_populates="users") # Cambié el nombre de la tabla de asociación

    vehicles_owned = relationship("Vehicle", back_populates="owner", foreign_keys="Vehicle.owner_id")
    driven_vehicle = relationship("Vehicle", back_populates="current_driver", foreign_keys="Vehicle.driver_id", uselist=False)
    base_station_managed = relationship("BaseStation", back_populates="manager", foreign_keys="BaseStation.manager_id", uselist=False)

# 3. Modelo para la tabla 'roles' que almacena los tipos de roles
class UserRoleModel(Base):
    __tablename__ = 'roles' # El nombre de la tabla en la BD sigue siendo 'roles'
    id = Column(Integer, primary_key=True, index=True)

    # AQUÍ: Ahora 'RoleEnum' (el Enum de Python) está definido y en ámbito
    name = Column(SQLAlchemyEnum(RoleEnum), unique=True, nullable=False)
    description = Column(String, nullable=True)

    # La relación inversa desde UserRoleModel a User
    users = relationship("User", secondary="user_roles_association", back_populates="roles") # Cambié el nombre de la tabla de asociación

# 4. Modelo para la tabla de asociación muchos-a-muchos entre User y UserRoleModel
class UserRolesAssociation(Base): # Renombré la tabla de asociación para claridad
    __tablename__ = 'user_roles_association' # Nombre explícito para la tabla de unión
    user_id = Column(Integer, ForeignKey('users.id'), primary_key=True)
    role_id = Column(Integer, ForeignKey('roles.id'), primary_key=True) # 'roles.id' se refiere a la tabla 'roles' (UserRoleModel)