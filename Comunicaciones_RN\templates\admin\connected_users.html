<!-- /templates/admin/connected_users.html -->

<!doctype html>
<html lang="es">
<head>
  <meta charset="utf-8">
  <title>Usuarios Conectados</title>
  <!-- Cargamos tu hoja de estilos principal -->
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
  <link rel="icon" href="{{ url_for('static', filename='RNCom.ico') }}" type="image/x-icon">

  <!-- Estilos adicionales u overrides para esta vista -->
  <style>
    /* Mantener el fondo del sitio (body-background) en lugar de forzar #f8f9fa */
    /* Quitamos padding extra en body y confiamos en los estilos de tu CSS principal */
    
    /* Contenedor de cada nodo */
    .node-container {
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      margin-bottom: 20px;
      padding: 20px;
    }
    /* Título de cada nodo */
    .node-header {
      font-size: 1.3em;
      color: #006994; /* Mantén tu tono de azul principal */
      margin-bottom: 10px;
    }
    /* Lista de usuarios */
    .user-list {
      list-style-type: none;
      padding-left: 0;
    }
    /* Cada usuario */
    .user-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #e9ecef;
    }
    .user-item:last-child {
      border-bottom: none;
    }
    /* Botón para desconectar */
    .disconnect-button {
      background-color: #dc3545;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 0.9em;
    }
    .disconnect-button:hover {
      background-color: #c82333;
    }
  </style>
</head>
<body class="body-background">
  <!-- Cabecera con la misma estética que el dashboard -->
  <header>
    <h1>Bienvenido, {{ current_user.username }}</h1>
    <nav class="navigation-links">
      <a href="{{ url_for('views.private_chats') }}">Chats Privados</a>
      {% if current_user.role == 'admin' %}
        <a href="{{ url_for('admin.list_users') }}">Administrar Usuarios</a>
        <a href="{{ url_for('admin.list_nodes') }}">Administrar Nodos</a>
        <a href="{{ url_for('admin.view_connected_users') }}">Ver Usuarios Conectados</a>
      {% endif %}
      <a href="{{ url_for('auth.logout') }}">Logout</a>
    </nav>
  </header>

  <main>
    <h2>Usuarios Conectados por Nodo</h2>
    <div id="userListDiv">
      <!-- Aquí se pintará dinámicamente la lista de nodos y usuarios -->
    </div>

    <!-- Mantén el mismo estilo de tus enlaces -->
    <a href="{{ url_for('views.dashboard') }}">Volver al Dashboard</a>
  </main>

  <footer>
    <p>© 2024 Tu Sitio Web</p>
  </footer>

  <!-- Carga Socket.IO (versión de ejemplo) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.0/socket.io.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const userListDiv = document.getElementById('userListDiv');
      const socket = io(); // Conexión Socket.IO global (sin namespace especial)

      // Solicita lista completa de usuarios conectados al conectar
      socket.on('connect', function() {
        console.log('Conectado a Socket.IO en la vista de Usuarios Conectados');
        socket.emit('get_all_connected_users');
      });

      // Escucha actualización de usuarios conectados
      socket.on('update_all_users', function(data) {
        console.log("Datos recibidos (update_all_users):", data);
        displayUsers(data);
      });

      // Forzar logout si el servidor lo indica
      socket.on('force_logout', function(data) {
        alert(data.message);
        window.location.href = '/logout';
      });

      // Función para renderizar la lista de nodos y usuarios
      function displayUsers(data) {
        // Limpia el contenedor
        userListDiv.innerHTML = '';

        // Verifica si no hay datos
        if (typeof data !== 'object' || data === null || Object.keys(data).length === 0) {
          userListDiv.innerHTML = "<p>No hay usuarios conectados.</p>";
          return;
        }

        // data: { node_id: { node_name: 'XYZ', users: [user1, user2] }, ... }
        for (const node_id in data) {
          if (data.hasOwnProperty(node_id)) {
            const info = data[node_id];
            const nodeContainer = document.createElement('div');
            nodeContainer.className = 'node-container';

            const nodeHeader = document.createElement('h3');
            nodeHeader.className = 'node-header';
            nodeHeader.textContent = `Nodo ${info.node_name}`;
            nodeContainer.appendChild(nodeHeader);

            const userList = document.createElement('ul');
            userList.className = 'user-list';

            if (!Array.isArray(info.users)) continue;

            // Crea un <li> por cada usuario
            info.users.forEach(username => {
              const userItem = document.createElement('li');
              userItem.className = 'user-item';
              userItem.innerHTML = `
                <span>${username}</span>
                <button class="disconnect-button" data-nodeid="${node_id}" data-username="${username}">
                  Desconectar
                </button>
              `;
              userList.appendChild(userItem);
            });

            nodeContainer.appendChild(userList);
            userListDiv.appendChild(nodeContainer);
          }
        }

        // Asigna eventos a los botones de desconexión
        userListDiv.querySelectorAll('.disconnect-button').forEach(button => {
          button.addEventListener('click', function() {
            const username = this.dataset.username;
            const node_id = this.dataset.nodeid;
            fetch(`/admin/disconnect_user/${node_id}/${username}`, { method: 'POST' })
              .then(response => response.json())
              .then(data => {
                if (data.message) {
                  alert(data.message);
                } else {
                  alert(data.error);
                }
              })
              .catch(err => {
                console.error("Error en la desconexión:", err);
              });
          });
        });
      }
    });
  </script>
</body>
</html>
