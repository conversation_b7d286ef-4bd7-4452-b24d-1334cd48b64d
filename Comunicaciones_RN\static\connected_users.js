// /static/connected_users.js

document.addEventListener('DOMContentLoaded', function () {
    const userListDiv = document.getElementById('userListDiv');

    // Conéctate al namespace '/node' de SocketIO.
    const socket = io('/node', {
        query: {
            node_id: nodeId, // Asegúrate de que nodeId esté definido
            username: username // Asegúrate de que username esté definido
        }
    });

    function updateConnectedUsers(users) {
        if (userListDiv) {
            userListDiv.innerHTML = '<h3>Usuarios Conectados por Nodo</h3>';

            if (users.length === 0) {
                const messageElement = document.createElement('p');
                messageElement.textContent = 'No hay usuarios conectados en este nodo.';
                userListDiv.appendChild(messageElement);
                return;
            }

            users.forEach(username => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.innerHTML = `<strong>Usuario:</strong> ${username}`;
                userListDiv.appendChild(userItem);
            });

        } else {
            console.error('userListDiv no encontrado en el DOM.');
        }
    }



    // Escucha el evento 'update_users' del servidor (namespace /node).
    socket.on('update_users', function(users) {
        console.log('Datos recibidos a través de SocketIO (update_users):', users);
        updateConnectedUsers(users);
    });


    // Manejo de conexión/desconexión (opcional, pero útil)
    socket.on('connect', () => {
        console.log('Conectado a SocketIO (namespace /node)');
    });

    socket.on('disconnect', () => {
        console.log('Desconectado de SocketIO (namespace /node)');
    });

    // --- IMPORTANTE:  Solicitar la lista inicial al conectarse ---
    socket.on('connect', () => {
        console.log('Conectado a SocketIO (namespace /node), solicitando lista inicial');
        socket.emit('get_connected_users', { node_id: nodeId }); // Solicita al conectarse
    });
});