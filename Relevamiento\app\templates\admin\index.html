<!-- app/templates/admin/index.html -->
{% extends "base.html" %}

{% block title %}Administración - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">🛡️ Panel de Administración</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.list_users') }}" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-people"></i> Usuarios
            </a>
            <a href="{{ url_for('admin.list_roles') }}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-shield-check"></i> Roles
            </a>
            <a href="{{ url_for('admin.bulk_permissions') }}" class="btn btn-sm btn-outline-info">
                <i class="bi bi-gear"></i> Permisos Masivos
            </a>
        </div>
    </div>
</div>

<!-- Estadísticas Generales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ total_users }}</h5>
                <p class="card-text">Usuarios Totales</p>
                <a href="{{ url_for('admin.list_users') }}" class="btn btn-sm btn-outline-primary">Ver Todos</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ users_with_permissions }}</h5>
                <p class="card-text">Con Permisos</p>
                <small class="text-muted">{{ "%.1f"|format((users_with_permissions/total_users*100) if total_users > 0 else 0) }}%</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ total_roles }}</h5>
                <p class="card-text">Roles Definidos</p>
                <a href="{{ url_for('admin.list_roles') }}" class="btn btn-sm btn-outline-info">Gestionar</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ total_users - users_with_permissions }}</h5>
                <p class="card-text">Sin Permisos</p>
                {% if total_users - users_with_permissions > 0 %}
                    <form method="POST" action="{{ url_for('admin.initialize_system') }}" class="d-inline">
                        <button type="submit" class="btn btn-sm btn-warning" 
                                onclick="return confirm('¿Inicializar permisos para usuarios sin configurar?')">
                            Inicializar
                        </button>
                    </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Usuarios Recientes -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">👥 Usuarios Recientes</h5>
            </div>
            <div class="card-body">
                {% if recent_users %}
                    <div class="list-group list-group-flush">
                        {% for user in recent_users %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ user.username }}</strong>
                                <br>
                                <small class="text-muted">{{ user.email }}</small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{{ user.created_at.strftime('%d/%m/%Y') }}</small>
                                <br>
                                <a href="{{ url_for('admin.manage_user_permissions', user_id=user.id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    Permisos
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">No hay usuarios registrados.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Roles Más Utilizados -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">🏆 Roles Más Utilizados</h5>
            </div>
            <div class="card-body">
                {% if role_usage %}
                    <div class="list-group list-group-flush">
                        {% for role_name, count in role_usage %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>{{ role_name }}</span>
                            <span class="badge bg-primary rounded-pill">{{ count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">No hay datos de uso de roles.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Acciones Rápidas -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">⚡ Acciones Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="{{ url_for('admin.create_role') }}" class="btn btn-success">
                                <i class="bi bi-plus-circle"></i> Crear Nuevo Rol
                            </a>
                        </div>
                        <small class="text-muted">Definir un nuevo nivel de acceso</small>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <a href="{{ url_for('admin.bulk_permissions') }}" class="btn btn-info">
                                <i class="bi bi-gear-fill"></i> Permisos Masivos
                            </a>
                        </div>
                        <small class="text-muted">Asignar permisos a múltiples usuarios</small>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-grid">
                            <form method="POST" action="{{ url_for('admin.initialize_system') }}" class="d-inline w-100">
                                <button type="submit" class="btn btn-warning w-100" 
                                        onclick="return confirm('¿Inicializar sistema de permisos?\n\nEsto creará roles predeterminados y asignará permisos básicos a usuarios sin configurar.')">
                                    <i class="bi bi-bootstrap-reboot"></i> Inicializar Sistema
                                </button>
                            </form>
                        </div>
                        <small class="text-muted">Configurar roles y permisos predeterminados</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Información del Sistema -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">ℹ️ Información del Sistema de Permisos</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🎯 Niveles de Usuario:</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-danger">100</span> <strong>Administrador:</strong> Acceso completo</li>
                            <li><span class="badge bg-warning">80</span> <strong>Supervisor:</strong> Múltiples ciudades y capas</li>
                            <li><span class="badge bg-info">60</span> <strong>Usuario Regional:</strong> Ciudades específicas</li>
                            <li><span class="badge bg-primary">40</span> <strong>Usuario Básico:</strong> Capas específicas</li>
                            <li><span class="badge bg-secondary">20</span> <strong>Solo Lectura:</strong> Solo visualización</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🔐 Permisos Disponibles:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-geo-alt text-primary"></i> <strong>Puntos:</strong> Ver, crear, editar, eliminar</li>
                            <li><i class="bi bi-camera text-success"></i> <strong>Cámaras:</strong> Ver, crear, editar, eliminar</li>
                            <li><i class="bi bi-image text-info"></i> <strong>Imágenes:</strong> Ver, subir, anotar, eliminar</li>
                            <li><i class="bi bi-graph-up text-warning"></i> <strong>Reportes:</strong> Ver, exportar datos</li>
                            <li><i class="bi bi-people text-danger"></i> <strong>Administración:</strong> Gestionar usuarios</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
{% endblock %}
