# client.py

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import <PERSON><PERSON><PERSON>
from flask_cors import CORS
from ptt_control import set_ptt
import json
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'

CORS(app, resources={r"/*": {"origins": "*"}})

socketio = SocketIO(app, cors_allowed_origins="*")

serial_connection = None

# Función para cargar la configuración del PTT
def load_config():
    try:
        if not os.path.exists('config.json'):
            return None  # No se encontró el archivo de configuración
        with open('config.json', 'r', encoding='utf-8') as file:
            return json.load(file)
    except FileNotFoundError:
        return None

# Ruta principal para cargar la interfaz
@app.route('/')
def index():
    config = load_config()
    if config:
        return render_template('index.html', node_id=config['node_id'], username=config['username'])
    else:
        # Si no hay configuración, redirige al formulario de configuración
        return redirect(url_for('config_form'))

# Ruta para mostrar el formulario de configuración
@app.route('/config_form')
def config_form():
    return render_template('config_form.html')

# Nueva ruta para guardar la configuración enviada desde el formulario
@app.route('/save_config', methods=['POST'])
def save_user_config():
    """Guarda la configuración enviada desde el formulario web"""
    config = {
        "username": request.form['username'],
        "password": request.form['password'],
        "node_id": request.form['node_id'],
        "input_device_index": int(request.form['input_device_index']),
        "volume_level": int(request.form['volume_level']),
        "port_number": request.form['port_number']
    }
    save_config(config)
    return redirect(url_for('index'))

def save_config(config):
    """Guarda la configuración en el archivo config.json"""
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    print("Configuración guardada correctamente.")

# Ruta para manejar eventos de activación/desactivación de PTT desde el cliente
@app.route('/ptt_event', methods=['POST'])
def handle_ptt_event():
    global serial_connection
    data = request.json
    config = load_config()
    
    if config and 'ptt_state' in data:
        if data['ptt_state']:
            print(f"Activando PTT para {config['username']} en el nodo {config['node_id']}")
            serial_connection = set_ptt(True, config, serial_connection)
        else:
            print(f"Desactivando PTT para {config['username']} en el nodo {config['node_id']}")
            serial_connection = set_ptt(False, config, serial_connection)

        return jsonify({"status": "success", "ptt_state": data['ptt_state']})
    else:
        return jsonify({"status": "error", "message": "Invalid request"}), 400

# WebSocket para recibir eventos de audio
@socketio.on('receive_audio')
def handle_receive_audio(data):
    config = load_config()
    if config:
        print("Recibiendo audio desde el servidor...")

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
