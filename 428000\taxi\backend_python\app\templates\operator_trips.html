{% extends "base_layout_operator.html" %}

{% block title %}Gestión de Viajes - Panel de Operador{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    #map {
        height: 400px;
        border-radius: 10px;
    }
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    .trip-card {
        border-left: 4px solid #0d6efd;
        margin-bottom: 10px;
        transition: all 0.3s;
    }
    .trip-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .trip-card.pending {
        border-left-color: #dc3545;
    }
    .trip-card.in-progress {
        border-left-color: #ffc107;
    }
    .trip-card.completed {
        border-left-color: #198754;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5 mb-4">Gestión de Viajes</h1>
        <p class="lead">Administra y supervisa todos los viajes del sistema.</p>
    </div>
</div>

<!-- Filtros -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Filtros</h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-3">
                        <label for="status-filter" class="form-label">Estado</label>
                        <select id="status-filter" class="form-select">
                            <option value="">Todos</option>
                            <option value="solicitado">Solicitado</option>
                            <option value="aceptado">Aceptado</option>
                            <option value="en_camino_pasajero">En camino al pasajero</option>
                            <option value="en_destino_pasajero">En destino del pasajero</option>
                            <option value="en_viaje">En viaje</option>
                            <option value="completado">Completado</option>
                            <option value="cancelado_pasajero">Cancelado por pasajero</option>
                            <option value="cancelado_conductor">Cancelado por conductor</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date-filter" class="form-label">Fecha</label>
                        <input type="date" id="date-filter" class="form-control">
                    </div>
                    <div class="col-md-3">
                        <label for="driver-filter" class="form-label">Conductor</label>
                        <select id="driver-filter" class="form-select">
                            <option value="">Todos</option>
                            {% for driver in drivers|default([]) %}
                            <option value="{{ driver.id }}">{{ driver.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="passenger-filter" class="form-label">Pasajero</label>
                        <select id="passenger-filter" class="form-select">
                            <option value="">Todos</option>
                            {% for passenger in passengers|default([]) %}
                            <option value="{{ passenger.id }}">{{ passenger.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">Limpiar</button>
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">Aplicar Filtros</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Mapa y Lista de Viajes -->
<div class="row">
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Mapa de Viajes</h5>
            </div>
            <div class="card-body">
                <div id="map"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Viajes</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshTrips()">
                        <i class="bi bi-arrow-clockwise"></i> Actualizar
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Pasajero</th>
                                <th>Conductor</th>
                                <th>Estado</th>
                                <th>Origen</th>
                                <th>Destino</th>
                                <th>Fecha</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="trips-table-body">
                            {% for trip in trips|default([]) %}
                            <tr>
                                <td>{{ trip.id }}</td>
                                <td>{{ trip.passenger_name|default('') }}</td>
                                <td>{{ trip.driver_name|default('Sin asignar') }}</td>
                                <td>
                                    <span class="badge {% if trip.status == 'completado' %}bg-success{% elif trip.status == 'cancelado_pasajero' or trip.status == 'cancelado_conductor' %}bg-danger{% elif trip.status == 'en_viaje' %}bg-primary{% else %}bg-warning{% endif %}">
                                        {{ trip.status|upper }}
                                    </span>
                                </td>
                                <td>{{ trip.origin_address|truncate(15) }}</td>
                                <td>{{ trip.destination_address|truncate(15) }}</td>
                                <td>{{ trip.requested_at|default('') }}</td>
                                <td>
                                    <a href="{{ url_for('trip_details_route', trip_id=trip.id) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center">No hay viajes disponibles</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span class="text-muted">Mostrando {{ trips|default([])|length }} viajes</span>
                    </div>
                    <nav aria-label="Paginación de viajes">
                        <ul class="pagination pagination-sm">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                            </li>
                            <li class="page-item active" aria-current="page">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Siguiente</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
    // Inicializar el mapa
    const map = L.map('map').setView([-34.6037, -58.3816], 12); // Buenos Aires como ejemplo

    // Añadir capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Iconos personalizados
    const originIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    const destinationIcon = L.icon({
        iconUrl: 'https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/images/marker-icon-2x.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34]
    });

    // Función para cargar viajes
    async function loadTrips() {
        try {
            const response = await fetchWithAuth('/api/v1/trips');
            if (response.ok) {
                const trips = await response.json();
                
                // Limpiar marcadores existentes
                map.eachLayer(layer => {
                    if (layer instanceof L.Marker || layer instanceof L.Polyline) {
                        map.removeLayer(layer);
                    }
                });
                
                // Añadir marcadores para cada viaje
                trips.forEach(trip => {
                    if (trip.origin_latitude && trip.origin_longitude) {
                        const originMarker = L.marker([trip.origin_latitude, trip.origin_longitude], {icon: originIcon})
                            .addTo(map)
                            .bindPopup(`
                                <strong>Origen del Viaje #${trip.id}</strong><br>
                                ${trip.origin_address || 'No especificado'}<br>
                                Estado: ${trip.status}<br>
                                <a href="/web/operator/trip/${trip.id}" class="btn btn-sm btn-primary mt-2">Ver detalles</a>
                            `);
                    }
                    
                    if (trip.destination_latitude && trip.destination_longitude) {
                        const destinationMarker = L.marker([trip.destination_latitude, trip.destination_longitude], {icon: destinationIcon})
                            .addTo(map)
                            .bindPopup(`
                                <strong>Destino del Viaje #${trip.id}</strong><br>
                                ${trip.destination_address || 'No especificado'}<br>
                                Estado: ${trip.status}<br>
                                <a href="/web/operator/trip/${trip.id}" class="btn btn-sm btn-primary mt-2">Ver detalles</a>
                            `);
                    }
                    
                    // Dibujar línea entre origen y destino
                    if (trip.origin_latitude && trip.origin_longitude && trip.destination_latitude && trip.destination_longitude) {
                        const routeLine = L.polyline([
                            [trip.origin_latitude, trip.origin_longitude],
                            [trip.destination_latitude, trip.destination_longitude]
                        ], {
                            color: getColorByStatus(trip.status),
                            weight: 3,
                            opacity: 0.7,
                            dashArray: '5, 10'
                        }).addTo(map);
                    }
                });
                
                // Actualizar tabla
                updateTripsTable(trips);
            }
        } catch (error) {
            console.error('Error al cargar viajes:', error);
        }
    }

    // Función para obtener color según estado
    function getColorByStatus(status) {
        switch(status) {
            case 'solicitado': return '#dc3545'; // Rojo
            case 'aceptado': return '#ffc107'; // Amarillo
            case 'en_camino_pasajero': return '#fd7e14'; // Naranja
            case 'en_destino_pasajero': return '#0dcaf0'; // Cyan
            case 'en_viaje': return '#0d6efd'; // Azul
            case 'completado': return '#198754'; // Verde
            case 'cancelado_pasajero': 
            case 'cancelado_conductor': return '#6c757d'; // Gris
            default: return '#6c757d'; // Gris por defecto
        }
    }

    // Función para actualizar la tabla de viajes
    function updateTripsTable(trips) {
        const tableBody = document.getElementById('trips-table-body');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        if (trips.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">No hay viajes disponibles</td>
                </tr>
            `;
            return;
        }
        
        trips.forEach(trip => {
            const statusClass = trip.status === 'completado' ? 'bg-success' : 
                               (trip.status === 'cancelado_pasajero' || trip.status === 'cancelado_conductor') ? 'bg-danger' :
                               trip.status === 'en_viaje' ? 'bg-primary' : 'bg-warning';
            
            tableBody.innerHTML += `
                <tr>
                    <td>${trip.id}</td>
                    <td>${trip.passenger_name || ''}</td>
                    <td>${trip.driver_name || 'Sin asignar'}</td>
                    <td>
                        <span class="badge ${statusClass}">
                            ${trip.status.toUpperCase()}
                        </span>
                    </td>
                    <td>${trip.origin_address ? trip.origin_address.substring(0, 15) + '...' : ''}</td>
                    <td>${trip.destination_address ? trip.destination_address.substring(0, 15) + '...' : ''}</td>
                    <td>${trip.requested_at || ''}</td>
                    <td>
                        <a href="/web/operator/trip/${trip.id}" class="btn btn-sm btn-primary">
                            <i class="bi bi-eye"></i>
                        </a>
                    </td>
                </tr>
            `;
        });
    }

    // Función para aplicar filtros
    function applyFilters() {
        const statusFilter = document.getElementById('status-filter').value;
        const dateFilter = document.getElementById('date-filter').value;
        const driverFilter = document.getElementById('driver-filter').value;
        const passengerFilter = document.getElementById('passenger-filter').value;
        
        // Construir URL con parámetros de filtro
        let url = '/api/v1/trips?';
        if (statusFilter) url += `status=${statusFilter}&`;
        if (dateFilter) url += `date=${dateFilter}&`;
        if (driverFilter) url += `driver_id=${driverFilter}&`;
        if (passengerFilter) url += `passenger_id=${passengerFilter}&`;
        
        // Eliminar el último '&' si existe
        url = url.endsWith('&') ? url.slice(0, -1) : url;
        
        // Cargar viajes filtrados
        loadFilteredTrips(url);
    }

    // Función para cargar viajes filtrados
    async function loadFilteredTrips(url) {
        try {
            const response = await fetchWithAuth(url);
            if (response.ok) {
                const trips = await response.json();
                updateTripsTable(trips);
                
                // Actualizar mapa
                map.eachLayer(layer => {
                    if (layer instanceof L.Marker || layer instanceof L.Polyline) {
                        map.removeLayer(layer);
                    }
                });
                
                // Añadir marcadores para cada viaje filtrado
                // (código similar a loadTrips pero con los viajes filtrados)
            }
        } catch (error) {
            console.error('Error al cargar viajes filtrados:', error);
        }
    }

    // Función para resetear filtros
    function resetFilters() {
        document.getElementById('status-filter').value = '';
        document.getElementById('date-filter').value = '';
        document.getElementById('driver-filter').value = '';
        document.getElementById('passenger-filter').value = '';
        
        // Recargar todos los viajes
        loadTrips();
    }

    // Función para actualizar viajes
    function refreshTrips() {
        loadTrips();
    }

    // Cargar viajes al iniciar
    document.addEventListener('DOMContentLoaded', function() {
        loadTrips();
    });
</script>
{% endblock %}
