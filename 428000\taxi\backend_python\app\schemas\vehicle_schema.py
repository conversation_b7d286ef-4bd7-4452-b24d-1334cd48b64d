from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.vehicle import VehicleStatus, VehicleCategory

class VehicleBase(BaseModel):
    plate_number: Optional[str] = None
    brand: Optional[str] = None
    model: Optional[str] = None
    year: Optional[int] = None
    color: Optional[str] = None
    # is_active: Optional[bool] = True # Eliminamos esta propiedad ya que no existe en la base de datos
    status: Optional[VehicleStatus] = None
    category: Optional[VehicleCategory] = None
    last_latitude: Optional[str] = None
    last_longitude: Optional[str] = None
    driver_id: Optional[int] = None

class VehicleCreate(VehicleBase):
    plate_number: str
    brand: str
    model: str
    year: int
    color: str

class VehicleUpdate(VehicleBase):
    pass

class VehicleInDBBase(VehicleBase):
    id: int
    owner_id: Optional[int] = None
    driver_id: Optional[int] = None
    base_station_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class Vehicle(VehicleInDBBase):
    pass

class VehicleWithRelations(Vehicle):
    owner: Optional["UserBase"] = None
    current_driver: Optional["UserBase"] = None

class VehicleLocationUpdate(BaseModel):
    """Esquema para actualizar la ubicación de un vehículo."""
    latitude: str
    longitude: str

# Para evitar referencias circulares
from app.schemas.user_schema import UserBase
VehicleWithRelations.update_forward_refs()
