{% extends "base.html" %}

{% block title %}Crear Escuela Bíblica - Sistema Académico{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-plus"></i> Crear Nueva Escuela Bíblica
            </h1>
        </div>
    </div>
    
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('routes.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('routes.academic_dashboard') }}">Sistema Académico</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('routes.academic_schools') }}">Escuelas Bíblicas</a></li>
            <li class="breadcrumb-item active" aria-current="page"><PERSON>rear Escuela</li>
        </ol>
    </nav>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-school"></i> Información de la Escuela
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <!-- Información básica -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Ejemplo: "Escuela Bíblica Básica - Iglesia Central"
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.description.label(class="form-label") }}
                                    {{ form.description(class="form-control", rows="3") }}
                                    <small class="form-text text-muted">
                                        Descripción breve del propósito y contenido de la escuela.
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Asignación -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.church_id.label(class="form-label") }}
                                    {{ form.church_id(class="form-control" + (" is-invalid" if form.church_id.errors else "")) }}
                                    {% if form.church_id.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.church_id.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.director_id.label(class="form-label") }}
                                    {{ form.director_id(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Opcional. Puedes asignar un director después.
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fechas -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.start_date.label(class="form-label") }}
                                    {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else "")) }}
                                    {% if form.start_date.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.start_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.end_date.label(class="form-label") }}
                                    {{ form.end_date(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Opcional. Dejar vacío para escuela permanente.
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Configuración -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.max_students.label(class="form-label") }}
                                    {{ form.max_students(class="form-control" + (" is-invalid" if form.max_students.errors else "")) }}
                                    {% if form.max_students.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.max_students.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Capacidad máxima de estudiantes.
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="form-check">
                                        {{ form.auto_enrollment(class="form-check-input") }}
                                        {{ form.auto_enrollment.label(class="form-check-label") }}
                                    </div>
                                    <small class="form-text text-muted">
                                        Los nuevos miembros se matricularán automáticamente.
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Botones -->
                        <div class="form-group">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('routes.academic_schools') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Cancelar
                                </a>
                                {{ form.submit(class="btn btn-primary") }}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Panel de ayuda -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Ayuda
                    </h6>
                </div>
                <div class="card-body">
                    <h6>¿Qué es una Escuela Bíblica?</h6>
                    <p class="text-muted small">
                        Las escuelas bíblicas son programas de formación para miembros de la iglesia local. 
                        Incluyen materias como fundamentos de fe, historia bíblica, y vida cristiana práctica.
                    </p>
                    
                    <h6>Matrícula Automática</h6>
                    <p class="text-muted small">
                        Si activas esta opción, todos los nuevos miembros de la iglesia se matricularán 
                        automáticamente en esta escuela, siempre que haya cupo disponible.
                    </p>
                    
                    <h6>Director de Escuela</h6>
                    <p class="text-muted small">
                        El director supervisa el funcionamiento de la escuela, puede asignar calificaciones 
                        y gestionar las matrículas de estudiantes.
                    </p>
                </div>
            </div>
            
            <!-- Plantillas predefinidas -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-magic"></i> Plantillas Rápidas
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Usa estas plantillas para crear escuelas comunes:</p>
                    
                    <button type="button" class="btn btn-outline-success btn-sm btn-block mb-2" 
                            onclick="fillTemplate('basic')">
                        <i class="fas fa-book"></i> Escuela Bíblica Básica
                    </button>
                    
                    <button type="button" class="btn btn-outline-primary btn-sm btn-block mb-2" 
                            onclick="fillTemplate('leadership')">
                        <i class="fas fa-users"></i> Escuela de Liderazgo
                    </button>
                    
                    <button type="button" class="btn btn-outline-warning btn-sm btn-block" 
                            onclick="fillTemplate('teachers')">
                        <i class="fas fa-chalkboard-teacher"></i> Escuela de Maestros
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function fillTemplate(type) {
    const templates = {
        'basic': {
            name: 'Escuela Bíblica Básica',
            description: 'Fundamentos de la fe cristiana para nuevos creyentes y miembros recientes',
            max_students: 50,
            auto_enrollment: true
        },
        'leadership': {
            name: 'Escuela de Liderazgo',
            description: 'Formación para líderes y servidores de la iglesia local',
            max_students: 25,
            auto_enrollment: false
        },
        'teachers': {
            name: 'Escuela de Maestros',
            description: 'Capacitación para maestros de escuela dominical y grupos pequeños',
            max_students: 20,
            auto_enrollment: false
        }
    };
    
    const template = templates[type];
    if (template) {
        // Obtener el nombre de la iglesia seleccionada
        const churchSelect = document.getElementById('church_id');
        const churchName = churchSelect.options[churchSelect.selectedIndex].text;
        
        // Llenar los campos
        document.getElementById('name').value = template.name + (churchName !== 'Seleccione...' ? ' - ' + churchName : '');
        document.getElementById('description').value = template.description;
        document.getElementById('max_students').value = template.max_students;
        document.getElementById('auto_enrollment').checked = template.auto_enrollment;
        
        // Establecer fecha de inicio como hoy
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('start_date').value = today;
    }
}

// Actualizar nombre cuando cambie la iglesia
document.getElementById('church_id').addEventListener('change', function() {
    const nameField = document.getElementById('name');
    const currentName = nameField.value;
    
    // Solo actualizar si el nombre contiene " - " (indicando que usa plantilla)
    if (currentName.includes(' - ')) {
        const baseName = currentName.split(' - ')[0];
        const churchName = this.options[this.selectedIndex].text;
        
        if (churchName !== 'Seleccione...') {
            nameField.value = baseName + ' - ' + churchName;
        }
    }
});

// Establecer fecha de inicio por defecto
document.addEventListener('DOMContentLoaded', function() {
    const startDateField = document.getElementById('start_date');
    if (!startDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        startDateField.value = today;
    }
});
</script>
{% endblock %}
