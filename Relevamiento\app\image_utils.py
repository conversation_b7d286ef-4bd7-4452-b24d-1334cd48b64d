# app/image_utils.py
from PIL import Image as PILImage, ImageDraw, UnidentifiedImageError
import os
from flask import current_app

# --- Configuración del Círculo ---
DEFAULT_CIRCLE_RADIUS = 10
DEFAULT_CIRCLE_COLOR = 'red'
DEFAULT_CIRCLE_WIDTH = 3 # Grosor de la línea del círculo
# ---------------------------------

def draw_circle_on_image(image_filename, x, y, radius=DEFAULT_CIRCLE_RADIUS, color=DEFAULT_CIRCLE_COLOR, width=DEFAULT_CIRCLE_WIDTH):
    """
    Dibuja un círculo EN UNA COPIA de la imagen original y la guarda.
    Retorna el nombre de la imagen modificada (o None si hay error).
    Las imágenes modificadas tendrán un sufijo '_marked'.
    """
    logger = current_app.logger
    upload_folder = current_app.config['UPLOAD_FOLDER']
    original_path = os.path.join(upload_folder, image_filename)

    if not os.path.exists(original_path):
        logger.error(f"No se puede dibujar círculo: Imagen original no encontrada en {original_path}")
        return None

    # Crear nombre para la imagen modificada
    name, ext = os.path.splitext(image_filename)
    marked_filename = f"{name}_marked{ext}"
    marked_path = os.path.join(upload_folder, marked_filename)

    try:
        logger.info(f"Abriendo imagen {original_path} para dibujar círculo en ({x},{y})")
        with PILImage.open(original_path) as img:
            # Crear una copia para no modificar la original directamente
            img_copy = img.convert("RGB").copy() # Asegurar formato RGB y hacer copia
            draw = ImageDraw.Draw(img_copy)

            # Coordenadas para el bounding box del círculo
            left = x - radius
            top = y - radius
            right = x + radius
            bottom = y + radius

            # Dibuja el círculo (elipse con bounding box cuadrado)
            draw.ellipse((left, top, right, bottom), outline=color, width=width)
            logger.info(f"Círculo dibujado en la copia de la imagen.")

            # Guarda la imagen modificada
            img_copy.save(marked_path)
            logger.info(f"Imagen marcada guardada como: {marked_path}")
            return marked_filename # Devuelve el nombre del archivo modificado

    except UnidentifiedImageError:
        logger.error(f"Error dibujando círculo: No se pudo identificar el formato de imagen en {original_path}")
        return None
    except FileNotFoundError:
         logger.error(f"Error dibujando círculo: Archivo no encontrado {original_path}")
         return None
    except Exception as e:
        logger.exception(f"Error inesperado dibujando círculo en {original_path}: {e}")
        return None

# Nota: Esta función `draw_circle_on_image` NO se usa en la implementación actual
# (que usa superposición CSS/JS). Se deja aquí por si decides cambiar a la
# modificación de imagen en el backend. Si la usaras, tendrías que:
# 1. Llamarla desde la ruta `set_image_circle` en `app/points.py`.
# 2. Guardar el `marked_filename` en la base de datos (quizás en un nuevo campo).
# 3. Modificar `point_detail.html` para mostrar la imagen marcada en lugar de la original.
# 4. Considerar cómo manejar múltiples marcas o la eliminación/actualización de marcas.